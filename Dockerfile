#FROM liferay/jdk21
FROM apache/skywalking-java-agent:9.3.0-java21

LABEL maintainer="<PERSON><PERSON>uanY<PERSON>" \
        version="0.0.1" \
        description="LookSky-SkyGirls-Agents应用"

# 设置时区为上海
ENV TZ=UTC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN apt-get update && apt-get install -y tini && rm -rf /var/lib/apt/lists/*

WORKDIR /looksky-skygirls-agents
RUN mkdir -p /looksky-skygirls-agents && chmod 755 /looksky-skygirls-agents

COPY ai-skygirls-start/target/ai-skygirls-start.jar /looksky-skygirls-agents/app.jar

# Arthas追踪工具
RUN curl -o /looksky-skygirls-agents/arthas-boot.jar https://arthas.aliyun.com/arthas-boot.jar

# SkyWalking 配置
ENV SW_AGENT_NAME=looksky_skygirls_agents
ENV SW_AGENT_COLLECTOR_BACKEND_SERVICE=skywalking-skywalking-helm-oap.skywalking.svc.cluster.local:11800
ENV SW_LOGGING_LEVEL=INFO

ENV JAVA_OPTS="-Xms256m \
                -Xmx1024m \
                -XX:MetaspaceSize=128M \
                -XX:MaxNewSize=256m \
                -XX:MaxMetaspaceSize=256m \
                -Dskywalking.agent.service_name=$SW_AGENT_NAME \
                -Dskywalking.collector.backend_service=$SW_AGENT_COLLECTOR_BACKEND_SERVICE \
                -Dskywalking.logging.level=$SW_LOGGING_LEVEL \
                -Djava.security.egd=file:/dev/./urandom"

EXPOSE 11462
# ENTRYPOINT 处理pid=1的问题
ENTRYPOINT ["/usr/bin/tini", "--", "/bin/sh", "-c", "java $JAVA_OPTS -jar /looksky-skygirls-agents/app.jar"]