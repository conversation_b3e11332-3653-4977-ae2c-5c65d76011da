#-------------------------------------------------------------------------------#
#               Qodana analysis is configured by qodana.yaml file               #
#             https://www.jetbrains.com/help/qodana/qodana-yaml.html            #
#-------------------------------------------------------------------------------#
version: "1.0"

#Specify inspection profile for code analysis
profile:
  name: qodana.starter

#Enable inspections
#include:
#  - name: <SomeEnabledInspectionId>

#Disable inspections
#exclude:
#  - name: <SomeDisabledInspectionId>
#    paths:
#      - <path/where/not/run/inspection>

projectJDK: "21" #(Applied in CI/CD pipeline)

#Execute shell command before Qodana execution (Applied in CI/CD pipeline)
#bootstrap: sh ./prepare-qodana.sh

#Install IDE plugins before Qodana execution (Applied in CI/CD pipeline)
#plugins:
#  - id: <plugin.id> #(plugin id can be found at https://plugins.jetbrains.com)

#Specify Qodana linter for analysis (Applied in CI/CD pipeline)
linter: jetbrains/qodana-jvm:2024.3

exclude:
  - name: All
    paths:
      - ai-skygirls-models/src/main/java/org/springframework/ai/bedrock/converse/api/ConverseApiUtils.java
      - ai-skygirls-models/src/main/java/org/springframework/ai/bedrock/converse/BedrockProxyChatModel.java
      - ai-skygirls-models/src/main/java/org/springframework/ai/azure/openai/AzureOpenAiChatOptions.java
      - ai-skygirls-models/src/main/java/org/springframework/ai/azure/openai/AzureOpenAiChatModel.java
      - ai-skygirls-models/src/main/java/org/springframework/ai/azure/openai/AzureOpenAiResponseFormat.java
      - ai-skygirls-data/src/main/java/ai/fal/client/http/HttpClient.java
      - ai-skygirls-data/src/main/java/ai/fal/client/FalClientImpl.java
      - ai-skygirls-application/src/main/java/com/looksky/agents/application/tryon/swapcloth/changecolor/Signer.java
