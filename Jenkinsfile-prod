def getGitBranches() {
    def branches = []
    try {
        branches = sh(
            script: "git ls-remote --heads origin origin https://git.code.tencent.com/westyle_global_repos/business/ai-agent/looksky_skygirls_agents.git | awk '{print \$2}' | sed 's|refs/heads/||'",
            returnStdout: true
        ).trim().tokenize('\n')
    } catch (Exception e) {
        branches = ['dev','test','main'] // Default fallback if branch fetching fails
    }
    return branches
}

pipeline {

    agent any

    parameters {
        choice(name: 'BRANCH_NAME', choices: getGitBranches(), description: '请选择要构建和发布的 Git 分支')
    }

    environment {
        SSH_CREDENTIALS_ID = 'eks-rancher-ec2'
        EC2_HOST = '*************'
        DOCKER_IMAGE = "lookskysvc/micro:looksky-skygirls-agents-prod-${env.BUILD_NUMBER}"
        MAVEN_HOME = '/usr/local/apache-maven-3.9.8'
        MAVEN_PACKAGE_PATH = '/opt/looksky/looksky_skygirls_agents'
        DOCKER_BUILD_PATH = '/opt/looksky/looksky_skygirls_agents'
        SPRING_PROFILES_ACTIVE = 'prod'
        JDK21_HOME = '/usr/local/java/jdk-21.0.5'
    }

    stages {

        stage('Setup JDK 21') {
            steps {
                sshagent([SSH_CREDENTIALS_ID]) {
                    sh """
                        ssh -o StrictHostKeyChecking=no ec2-user@${EC2_HOST} bash -c '
                            if [ ! -d "${JDK21_HOME}" ]; then
                                echo "Installing JDK 21..."
                                sudo mkdir -p /usr/local/java
                                sudo wget -q https://download.oracle.com/java/21/latest/jdk-21_linux-x64_bin.tar.gz -O /tmp/jdk21.tar.gz
                                sudo tar -xzf /tmp/jdk21.tar.gz -C /usr/local/java
                                sudo rm -f /tmp/jdk21.tar.gz
                                echo "JDK 21 installed at ${JDK21_HOME}"
                            else
                                echo "JDK 21 already installed."
                            fi
                        '
                    """
                }
            }
        }

        stage('Compile & Build') {
            steps {
                sshagent([SSH_CREDENTIALS_ID]) {
                    sh """
                        ssh -o StrictHostKeyChecking=no ec2-user@${EC2_HOST} bash -c '
                            export JAVA_HOME=${JDK21_HOME} &&
                            export PATH=\${JAVA_HOME}/bin:${MAVEN_HOME}/bin:\$PATH &&
                            export SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE} &&
                            cd ${MAVEN_PACKAGE_PATH} &&
                            git pull &&
                            git checkout ${params.BRANCH_NAME} &&
                            mvn clean &&
                            mvn install package -P${SPRING_PROFILES_ACTIVE} -Dmaven.test.skip=true -Dspring.profiles.active=${SPRING_PROFILES_ACTIVE} -T 4 -U
                        '
                    """
                }
            }
        }

        stage('Docker Build & Push') {
            steps {
                sshagent([SSH_CREDENTIALS_ID]) {
                    sh """
                        ssh -o StrictHostKeyChecking=no ec2-user@${EC2_HOST} '
                            cd ${DOCKER_BUILD_PATH} &&
                            docker build -t ${DOCKER_IMAGE} . &&
                            docker push ${DOCKER_IMAGE}
                        '
                    """
                }
            }
        }
    }
}