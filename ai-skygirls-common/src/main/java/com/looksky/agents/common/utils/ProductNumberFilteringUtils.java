package com.looksky.agents.common.utils;

import com.looksky.agents.sdk.agent.search.dto.FilterStepDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 模拟商品过滤过程，确保早期轮次过滤更多，后期轮次过滤较少，
 * 最后一轮采用特定的过滤逻辑，最终剩余数量不低于20。
 */
@Slf4j
public class ProductNumberFilteringUtils {

    private final static Map<String, int[]> categoryRanges = new HashMap<>();
    private final static int MIN_COUNT = 20;

    static {
        categoryRanges.put("sweater", new int[]{18868, 25527});
        categoryRanges.put("sweatshirt", new int[]{13488, 18249});
        categoryRanges.put("t-shirt", new int[]{18135, 24535});
        categoryRanges.put("blouse", new int[]{16326, 22088});
        categoryRanges.put("vest", new int[]{8821, 11934});
        categoryRanges.put("bra", new int[]{3971, 5373});
        categoryRanges.put("camisole", new int[]{7972, 10786});
        categoryRanges.put("corset", new int[]{3917, 5299});
        categoryRanges.put("tank_top", new int[]{11816, 15986});
        categoryRanges.put("tube", new int[]{6688, 9048});
        categoryRanges.put("tunic", new int[]{1504, 2035});
        categoryRanges.put("maxi_dresses", new int[]{13255, 17933});
        categoryRanges.put("midi_dresses", new int[]{17617, 23835});
        categoryRanges.put("short_dresses", new int[]{26083, 35288});
        categoryRanges.put("pants", new int[]{27210, 36813});
        categoryRanges.put("skirt", new int[]{24405, 33019});
        categoryRanges.put("shorts", new int[]{11018, 14907});
        categoryRanges.put("skort", new int[]{3982, 5388});
        categoryRanges.put("anorak", new int[]{3794, 5134});
        categoryRanges.put("bomber", new int[]{5436, 7354});
        categoryRanges.put("shacket", new int[]{5535, 7489});
        categoryRanges.put("motorcycle", new int[]{4008, 5422});
        categoryRanges.put("car_coat", new int[]{1768, 2392});
        categoryRanges.put("trench", new int[]{4274, 5783});
        categoryRanges.put("puffer_jacket", new int[]{12309, 16654});
        categoryRanges.put("other_jacket", new int[]{13688, 18520});
        categoryRanges.put("teddy_bear", new int[]{3726, 5042});
        categoryRanges.put("blazer", new int[]{6818, 9224});
        categoryRanges.put("capes", new int[]{2285, 3091});
        categoryRanges.put("jumpsuit", new int[]{5467, 7397});
        categoryRanges.put("romper", new int[]{4223, 5713});
        categoryRanges.put("bodysuit", new int[]{3419, 4626});
        categoryRanges.put("pants_set", new int[]{5508, 7452});
        categoryRanges.put("shorts_set", new int[]{3482, 4710});
        categoryRanges.put("skirt_set", new int[]{4896, 6624});
    }





    /**
     * 模拟商品过滤过程
     *
     * @param initialCount 初始商品数量
     * @param rounds       过滤轮数
     * @return 每轮过滤的结果列表
     */
    public static List<FilterStepDTO> simulateFiltering(int initialCount, int rounds) {
        Random random = new Random();
        List<FilterStepDTO> results = new ArrayList<>();
        int remainingCount = initialCount;

        // 计算总的需要减少的数量（目标是至少剩余minCount）
        int totalReductionNeeded = initialCount - MIN_COUNT;

        // 分配权重，早期轮次权重较高
        int[] weights = new int[rounds];
        int totalWeight = 0;
        for (int i = 0; i < rounds; i++) {
            weights[i] = rounds - i; // 例如，5轮时，权重为5,4,3,2,1
            totalWeight += weights[i];
        }

        // 处理前 rounds - 1 轮
        for (int i = 0; i < rounds - 1; i++) {
            int roundNum = i + 1;
            int beforeCount = remainingCount;

            // 计算本轮的期望减少数量
            double weight = weights[i];
            double expectedReduction = (double) totalReductionNeeded * weight / totalWeight;

            // 应用 +/-30%的波动
            double fluctuationFactor = 0.7 + (0.6 * random.nextDouble()); // 0.7到1.3之间
            int actualReduction = (int) Math.round(expectedReduction * fluctuationFactor);

            // 确保不会过滤掉过多，导致剩余数量低于minCount
            if (remainingCount - actualReduction < MIN_COUNT + (rounds - i - 1)) {
                actualReduction = remainingCount - MIN_COUNT - (rounds - i - 1);
            }

            // 更新剩余数量
            remainingCount -= actualReduction;

            // 记录当前轮结果
            results.add(new FilterStepDTO(roundNum, beforeCount, remainingCount));

            // 更新总权重和总需要减少的数量
            totalWeight -= weights[i];
            totalReductionNeeded -= actualReduction;
        }

        // 处理最后一轮
        if (rounds > 0) {

            // 按照用户提供的逻辑计算最后一轮的减少量
            double temp = remainingCount - 20;
            // 确保 temp 不为负数
            if (temp < 0) {
                temp = 0;
            }
            // 生成0.9到0.95之间的随机数
            double randomFactor = 0.9 + (0.05 * random.nextDouble());
            // 计算应该减掉的值
            int reductionAmount = (int) Math.round(temp * randomFactor);
            // 计算最后一轮的剩余数量
            int finalCount = remainingCount - reductionAmount;
            // 确保最后一轮的剩余数量不低于20
            if (finalCount < MIN_COUNT) {
                finalCount = MIN_COUNT;
            }

            results.add(new FilterStepDTO(rounds, remainingCount, finalCount));
        }

        return results;
    }

    /**
     * 根据商品品类随机生成初始商品总数
     *
     * @param category        商品品类
     * @param categoryRanges  品类对应的商品数量范围
     * @return 随机生成的初始商品总数
     */
    public static int getInitialCountForCategory(String category, Map<String, int[]> categoryRanges) {
        Random random = new Random();
        int[] range;
        if (!categoryRanges.containsKey(category)) {
            range = new int[]{5000, 10000};
            log.error("无效的商品品类: {}", category);
        } else {
            range = categoryRanges.get(category);
        }
        return range[0] + random.nextInt(range[1] - range[0] + 1);
    }


    public static List<FilterStepDTO> getFilterStepDTOs(int numberOfRounds, String subCategory) {
        int totalCount = getInitialCountForCategory(subCategory, categoryRanges);
        return simulateFiltering(totalCount, numberOfRounds);
    }


    public static void main(String[] args) {

        // 选择一个品类
        String selectedCategory = "skirt_set";

        // 根据品类随机生成初始商品数量
        int totalCount = getInitialCountForCategory(selectedCategory, categoryRanges);

        // 设置过滤轮数，可以根据需要调整
        int numberOfRounds = 3; // 例如5轮

        // 模拟过滤过程
        List<FilterStepDTO> results = simulateFiltering(totalCount, numberOfRounds);

        // 输出结果
        System.out.println("商品品类: " + selectedCategory);
        System.out.println("初始商品数量: " + totalCount);
        for (FilterStepDTO step : results) {
            System.out.println(step);
        }
    }
}
