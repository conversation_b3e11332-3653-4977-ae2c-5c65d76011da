package com.looksky.agents.common.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.graecove.common.ABTestFlagResp;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ABTestFlagUtils {

    private ABTestFlagUtils() {}

     public static ABTestFlagResp parseMap(Map<String, Object> abTestFlagMap) {
        try {
            if (abTestFlagMap == null || abTestFlagMap.isEmpty()) {
                return null;
            }
            Map<String, ABTestFlagResp.ABTestVariantResp> variantRespMap = new HashMap<>();

            abTestFlagMap.forEach((key, value) -> {
                if (value instanceof Map<?, ?> variantMap) {
                    ABTestFlagResp.ABTestVariantResp abTestVariantResp = BeanUtil.toBean(variantMap, ABTestFlagResp.ABTestVariantResp.class);
                    variantRespMap.put(key, abTestVariantResp);
                }
            });

            return new ABTestFlagResp(variantRespMap);
        } catch (Exception e) {
            log.error("ABTestFlagResp 解析错误, abTestFlagMap 的内容{}", JSONUtil.toJsonStr(abTestFlagMap), e);
            return null;
        }
    }

    /**
     * {
     *     "test": {
     *         "variantID": "test",
     *         "variantName": "test",
     *         "experimentName": "test"
     *     }
     * }
     */
    public static ABTestFlagResp parse(Object abTestFlagObj) {
        try {
            if (ObjectUtil.isEmpty(abTestFlagObj)) {
                return null;
            }
            if (abTestFlagObj instanceof Map<?, ?> abTestMap) {
                @SuppressWarnings("unchecked")
                Map<String, Object> typedMap = (Map<String, Object>) abTestMap;
                return parseMap(typedMap);

            } else if (abTestFlagObj instanceof String abTestFlagStr) {
                @SuppressWarnings("unchecked")
                Map<String, Object> abTestMap = JSONUtil.toBean(abTestFlagStr, Map.class);
                return parseMap(abTestMap);
            }
            log.error("ABTestFlagResp 解析错误, abTestFlagObj 的内容{}", abTestFlagObj);
            return null;
        } catch (Exception e) {
            log.error("ABTestFlagResp 解析错误, abTestFlagObj 的内容{}", abTestFlagObj, e);
            return null;
        }
    }
}
