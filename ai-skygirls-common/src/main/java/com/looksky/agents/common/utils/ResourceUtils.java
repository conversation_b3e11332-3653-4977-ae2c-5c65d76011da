package com.looksky.agents.common.utils;

import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * @ClassName ResourceUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/30 下午5:05
 * @Version 1.0
 **/
public class ResourceUtils {
    public static String innerTagModelOutputStructure(String labelType) {
        String filePath = String.format("schema/tag/%s.json", labelType);
        return loadConfig(filePath);
    }


    public static String workflow(String fileName) {
        String filePath = String.format("workflow/%s.json", fileName);
        return loadConfig(filePath);
    }

    public static String loadConfig(String filePath) {
        try {
            // 构建文件路径
            ClassPathResource resource = new ClassPathResource(filePath);
            // 读取文件内容
            return new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new RuntimeException("无法读取配置文件: " + filePath, e);
        }
    }
}
