package com.looksky.agents.common.utils;

import org.springframework.util.StringUtils;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.regex.Pattern;

public class ImageUrlValidator {
    private static final Pattern IMAGE_PATTERN = Pattern.compile(".*\\.(jpg|jpeg|png|gif|bmp|webp)$", Pattern.CASE_INSENSITIVE);
    
    /**
     * 验证URL是否为有效的图片URL
     *
     * @param imageUrl 待验证的URL
     * @return 如果是有效的图片URL返回true，否则返回false
     */
    public static boolean isValidImageUrl(String imageUrl) {
        if (!StringUtils.hasText(imageUrl)) {
            return false;
        }

        try {
            // 验证URL格式
            new URL(imageUrl);
            
            // 验证是否为图片格式
            if (!IMAGE_PATTERN.matcher(imageUrl).matches()) {
                return false;
            }
            
            return true;
        } catch (MalformedURLException e) {
            return false;
        }
    }
} 