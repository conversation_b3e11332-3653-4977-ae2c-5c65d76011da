package com.looksky.agents.common.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 字符串限制工具类
 */
public class StringLimitUtils {

    private static final int LIMIT_LENGTH = 60;
    
    /**
     * 截取字符串的前 LIMIT_LENGTH 个单词（标点符号也计入单词数，空格不计入）
     *
     * @param input 输入字符串
     * @return 截取后的字符串，如果输入为null或空字符串则返回原字符串
     */
    public static String limitWords(String input) {
        if (StringUtils.isBlank(input)) {
            return input;
        }

        int count = 0;
        int lastPos = 0;
        boolean inWord = false;
        
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            
            if (Character.isWhitespace(c)) {
                if (inWord) {
                    inWord = false;
                }
                continue;
            }
            
            // 如果是字母、数字或连字符，且不在单词中，开始新单词
            if ((Character.isLetterOrDigit(c) || c == '-') && !inWord) {
                count++;
                inWord = true;
            }
            // 如果是标点符号（除了连字符）
            else if (!Character.isLetterOrDigit(c) && c != '-') {
                count++;
                inWord = false;
            }
            
            if (count > LIMIT_LENGTH) {
                return input.substring(0, lastPos);
            }
            
            lastPos = i + 1;
        }
        
        return input;
    }
}