package com.looksky.agents.common.utils;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.reflect.TypeToken;
import com.lark.oapi.Client;
import com.lark.oapi.core.response.RawResponse;
import com.lark.oapi.core.token.AccessTokenType;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.sheets.v3.model.GetSpreadsheetSheetReq;
import com.lark.oapi.service.sheets.v3.model.GetSpreadsheetSheetResp;
import com.lark.oapi.service.sheets.v3.model.GetSpreadsheetSheetRespBody;
import com.lark.oapi.service.sheets.v3.model.MergeRange;
import com.lark.oapi.service.sheets.v3.model.Sheet;
import com.looksky.agents.sdk.feishu.dto.LarkResultDTO;
import com.looksky.agents.sdk.feishu.dto.SpreadsheetDataDTO;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 飞书表格工具
 *
 * @since  1.1.0
 * <AUTHOR>
 **/
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class FeiShuSheetUtil {

    public static <T> List<T> getFeiShuExcelData(String spreadsheetToken, String sheetId, Class<T> clazz) {

        Client client = Client.newBuilder("cli_a5f1001bb9731013", "D4e6CevO6CAQmyxXnXS2OgQSPZLiZHEb").build();
        MergeRange[] merges = queryFeishuExcelMerge(client, spreadsheetToken, sheetId);

        return queryFeishuExcelData(client, merges,  spreadsheetToken, sheetId, 1, clazz);


    }



    public static MergeRange[] queryFeishuExcelMerge(Client client,String spreadsheetToken, String sheetId){

        try {
            GetSpreadsheetSheetResp getSpreadsheetSheetResp = client.sheets().spreadsheetSheet().get(GetSpreadsheetSheetReq.newBuilder().spreadsheetToken(spreadsheetToken).sheetId(sheetId).build());
            GetSpreadsheetSheetRespBody data = getSpreadsheetSheetResp.getData();
            Sheet sheet = data.getSheet();
            return sheet.getMerges();
        }catch (Exception e){
            log.error("解析飞书excel合并单元格异常", e);
        }
        return new MergeRange[]{};
    }

    public static <T> List<T> queryFeishuExcelData(Client client, MergeRange[] merges, String spreadsheetToken,
                                                   String sheetId, Integer headRowNumber, Class<T> clazz) {
        try {
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/values_batch_get?ranges=" + sheetId;
            RawResponse resp = client.get(url, null, AccessTokenType.Tenant);
            Type type = new TypeToken<LarkResultDTO<SpreadsheetDataDTO>>() {}.getType();
            LarkResultDTO<SpreadsheetDataDTO> result = Jsons.DEFAULT.fromJson(new String(resp.getBody()), type);
            List<SpreadsheetDataDTO.ValueRange> valueRanges = result.getData().getValueRanges();
            List<List<Object>> values = valueRanges.getFirst().getValues();

            return convertExcelData(values, clazz);
        } catch (Exception e) {
            log.error("解析飞书excel数据异常", e);
        }
        return new ArrayList<>();
    }

    private static <T> List<T> convertExcelData(List<List<Object>> values, Class<T> clazz) {
        List<T> dataList = new ArrayList<>();
        if (values.isEmpty() || values.getFirst().isEmpty()) {
            return dataList;
        }

        // 获取表头
        List<Object> headers = values.getFirst();

        // 获取类的所有字段
        Field[] fields = clazz.getDeclaredFields();
        Map<String, Field> fieldMap = Arrays.stream(fields)
                .collect(Collectors.toMap(Field::getName, field -> field));

        // 处理数据行
        for (int i = 1; i < values.size(); i++) {
            List<Object> row = values.get(i);
            try {
                T instance = clazz.getDeclaredConstructor().newInstance();

                for (int j = 0; j < headers.size() && j < row.size(); j++) {
                    String headerName = convertToString(headers.get(j));
                    Field field = fieldMap.get(headerName);

                    if (field != null) {
                        field.setAccessible(true);
                        Object value = convertFieldValue(row.get(j), field.getType());
                        field.set(instance, value);
                    }
                }

                dataList.add(instance);
            } catch (Exception e) {
                log.error("转换数据行异常", e);
            }
        }

        return dataList;
    }

    private static String convertToString(Object value) {
        switch (value) {
            case null -> {
                return "";
            }
            case String s -> {
                return s;
            }
            case List<?> list -> {
                List<SpreadsheetDataDTO.SegmentNode> nodes = JSON.parseArray(JSON.toJSONString(value), SpreadsheetDataDTO.SegmentNode.class);
                return nodes.stream().map(SpreadsheetDataDTO.SegmentNode::getText).collect(Collectors.joining());
            }
            case Map<?, ?> map -> {
                SpreadsheetDataDTO.SegmentNode node = JSON.parseObject(JSON.toJSONString(value), SpreadsheetDataDTO.SegmentNode.class);
                return node.getText();
            }
            default -> {
                return value.toString();
            }
        }
    }

    private static Object convertFieldValue(Object value, Class<?> targetType) {
        if (value == null) {
            return null;
        }

        if (targetType == String.class) {
            return convertToString(value);
        } else if (targetType == Integer.class || targetType == int.class) {
            if (value instanceof Number number) {
                return number.intValue();
            } else {
                try {
                    return Integer.parseInt(convertToString(value));
                } catch (NumberFormatException e) {
                    log.error("无法将值转换为 Integer: {}", value, e);
                    return null;
                }
            }
        } else if (targetType == Long.class || targetType == long.class) {
            if (value instanceof Number number) {
                return number.longValue();
            } else {
                try {
                    return Long.parseLong(convertToString(value));
                } catch (NumberFormatException e) {
                    log.error("无法将值转换为 Long: {}", value, e);
                    return null;
                }
            }
        } else if (targetType == Double.class || targetType == double.class) {
            if (value instanceof Number number) {
                return number.doubleValue();
            } else {
                try {
                    return Double.parseDouble(convertToString(value));
                } catch (NumberFormatException e) {
                    log.error("无法将值转换为 Double: {}", value, e);
                    return null;
                }
            }
        }
        // 可以根据需要添加更多类型的转换

        // 默认返回字符串表示
        return convertToString(value);
    }

    /**
     * 处理合并单元格
     *
     * @param data          解析数据
     * @param merges        合并单元格信息
     * @param headRowNumber 起始行
     */
    protected static void explainMergeData(Map<Integer, Map<Integer,String>> data, MergeRange[] merges, Integer headRowNumber) {

        if(ArrayUtil.isEmpty(merges)){
            return;
        }

        //循环所有合并单元格信息
        Arrays.stream(merges).forEach(merge -> {

            if(merge.getStartRowIndex() >= headRowNumber){

                int firstRowIndex = merge.getStartRowIndex();
                int lastRowIndex = merge.getEndRowIndex();
                int firstColumnIndex = merge.getStartColumnIndex();
                int lastColumnIndex = merge.getEndColumnIndex();

                // 获取初始值
                String initValue = data.get(firstRowIndex).get(firstColumnIndex);

                //设置值
                for (int i = firstRowIndex; i <= lastRowIndex; i++) {
                    for (int j = firstColumnIndex; j <= lastColumnIndex; j++) {
                        data.get(i).put(j, initValue);
                    }
                }
            }
        });
    }

    // 如果你想要返回Map格式
    public static List<Map<String, Object>> queryFeishuExcelDataAsMap(String spreadsheetToken, String sheetId) {
        try {
            Client client = Client.newBuilder("cli_a5f1001bb9731013", "D4e6CevO6CAQmyxXnXS2OgQSPZLiZHEb").build();

            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/values_batch_get?ranges=" + sheetId;
            RawResponse resp = client.get(url, null, AccessTokenType.Tenant);
            Type type = new TypeToken<LarkResultDTO<SpreadsheetDataDTO>>() {}.getType();
            LarkResultDTO<SpreadsheetDataDTO> result = Jsons.DEFAULT.fromJson(new String(resp.getBody()), type);
            List<SpreadsheetDataDTO.ValueRange> valueRanges = result.getData().getValueRanges();
            List<List<Object>> values = valueRanges.getFirst().getValues();

            return convertExcelDataToMap(values);
        } catch (Exception e) {
            log.error("解析飞书excel数据异常", e);
        }
        return new ArrayList<>();
    }

    private static List<Map<String, Object>> convertExcelDataToMap(List<List<Object>> values) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        if (values.isEmpty() || values.getFirst().isEmpty()) {
            return dataList;
        }

        List<String> headers = values.getFirst().stream()
                .map(FeiShuSheetUtil::convertToString)
                .toList();

        for (int i = 1; i < values.size(); i++) {
            List<Object> row = values.get(i);
            Map<String, Object> rowMap = new HashMap<>();

            for (int j = 0; j < headers.size() && j < row.size(); j++) {
                rowMap.put(headers.get(j), convertToString(row.get(j)));
            }

            dataList.add(rowMap);
        }

        return dataList;
    }

}
