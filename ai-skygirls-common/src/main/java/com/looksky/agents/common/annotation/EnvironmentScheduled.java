package com.looksky.agents.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记只在指定环境执行的定时任务
 * 使用方式与@Scheduled注解相同，但只在指定环境生效
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface EnvironmentScheduled {
    /**
     * 固定速率执行，单位毫秒
     */
    long fixedRate() default -1;
    
    /**
     * 固定延迟执行，单位毫秒
     */
    long fixedDelay() default -1;
    
    /**
     * 初始延迟时间，单位毫秒
     */
    long initialDelay() default -1;
    
    /**
     * Cron表达式
     */
    String cron() default "";
    
    /**
     * Cron表达式的时区
     */
    String zone() default "";
    
    /**
     * 指定在哪些环境中执行定时任务
     * 默认为test和dev环境
     */
    String[] environments() default {"test"};
} 