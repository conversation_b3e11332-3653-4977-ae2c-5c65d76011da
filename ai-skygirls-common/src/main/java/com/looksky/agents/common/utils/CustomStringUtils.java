package com.looksky.agents.common.utils;

import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * @ClassName StringUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/8 上午10:44
 * @Version 1.0
 **/
public class CustomStringUtils {
    public static String replaceTemplate(String template, Map<String, Object> variables) {
        if (!StringUtils.hasText(template)) {
            return null;
        }

        return variables.entrySet().stream()
                .reduce(template,
                        (t, entry) -> t.replace("{" + entry.getKey() + "}", String.valueOf(entry.getValue())),
                        (s1, s2) -> s1);
    }
}
