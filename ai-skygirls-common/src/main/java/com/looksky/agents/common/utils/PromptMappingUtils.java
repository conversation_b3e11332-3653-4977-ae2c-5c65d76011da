package com.looksky.agents.common.utils;


import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.graecove.common.BusinessException;
import java.util.List;
import java.util.Optional;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Prompt 配置映射工具
 *
 * <AUTHOR>
 * @since 1.1.14
 **/
@Component
public class PromptMappingUtils {

    private static final Logger log = LoggerFactory.getLogger(PromptMappingUtils.class);
    private static final String STORE_KEY = "common:prompt";
    private final RBucket<List<PromptMapping>> bucket;
    private final ObjectMapper objectMapper;

    public PromptMappingUtils(RedissonClient redissonClient, ObjectMapper objectMapper) {
        this.bucket = redissonClient.getBucket(STORE_KEY, new TypedJsonJacksonCodec(new TypeReference<List<PromptMapping>>() {
        }, objectMapper));
        this.objectMapper = objectMapper;

        // 加载 Prompt 配置数据
        loadConfigToRedis();
    }


    /**
     * 加载 Prompt 配置到 redis
     */
    public void loadConfigToRedis() {
        bucket.set(loadConfig());
    }

    /**
     * 获取本地的 prompt mapping 配置文件
     * @return PromptMapping 列表
     */
    private List<PromptMapping> loadConfig() {
        String prompt = ResourceUtils.loadConfig("cache/prompt.json");
        try {
            return objectMapper.readValue(prompt, new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            log.error("加载Prompt配置失败", e);
            throw new BusinessException("加载Prompt配置失败");
        }
    }

    /**
     * 通过 key 获取 Prompt
     * @param key 业务 key
     * @return prompt name
     */
    public String get(String key) {
        // 获取缓存配置，如果为空则重新加载
        List<PromptMapping> promptMappings = Optional.ofNullable(bucket.get())
                .filter(list -> !list.isEmpty())
                .orElseGet(() -> {
                    log.warn("Redis Prompt配置为空，请检查加载是否正常");
                    List<PromptMapping> loadedMappings = loadConfig();
                    bucket.set(loadedMappings);
                    return loadedMappings;
                });

        // 尝试从Redis配置中获取prompt
        return Optional.ofNullable(get(promptMappings, key))
                // 如果Redis中没有，尝试从最新配置中获取
                .orElseGet(() -> Optional.ofNullable(get(loadConfig(), key))
                // 如果还是没有，抛出异常
                .orElseThrow(() -> new BusinessException("无法获取 " + key + " 对应的 Prompt")));
    }

    /**
     * 在特定数据里, 获取 prompt
     * @param promptMappings 数据列表
     * @param key 业务 key
     * @return prompt name
     */
    private String get(List<PromptMapping> promptMappings, String key) {
        return promptMappings.stream()
                .filter(mapping -> key.equals(mapping.key()))
                .map(PromptMapping::prompt)
                .filter(CharSequenceUtil::isNotBlank) // 过滤空值
                .findFirst()
                .orElse(null);
    }


    record PromptMapping(String key, String prompt, String description) {
    }
}
