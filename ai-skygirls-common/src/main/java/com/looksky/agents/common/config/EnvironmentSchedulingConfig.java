package com.looksky.agents.common.config;

import com.looksky.agents.common.annotation.EnvironmentScheduled;
import org.jetbrains.annotations.NotNull;
import java.lang.reflect.Method;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.TimeZone;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ConcurrentTaskScheduler;
import org.springframework.scheduling.config.CronTask;
import org.springframework.scheduling.config.ScheduledTask;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.config.TriggerTask;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.scheduling.support.PeriodicTrigger;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;

/**
 * 测试环境定时任务配置类
 * 用于处理TestEnvironmentScheduled注解，只在指定环境生效
 * 使用虚拟线程执行定时任务
 */
@Configuration
public class EnvironmentSchedulingConfig {

    @Value("${spring.profiles.active:}")
    private String activeProfile;

    /**
     * 判断当前环境是否在指定的环境列表中
     * @param environments 指定的环境列表
     * @return 当前环境是否在指定的环境列表中
     */
    private boolean isInSpecifiedEnvironment(String[] environments) {
        if (environments == null || environments.length == 0) {
            return false;
        }
        return Arrays.asList(environments).contains(activeProfile);
    }

    /**
     * 创建使用虚拟线程的调度器执行服务
     * 使用虚拟线程可以更高效地处理大量并发任务
     */
    @Bean(destroyMethod = "shutdown")
    public ScheduledExecutorService testEnvironmentScheduledExecutorService() {
        // 使用虚拟线程工厂创建线程池
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(1, Thread.ofVirtual().factory());
        executor.setRemoveOnCancelPolicy(true);
        return executor;
    }

    /**
     * 创建任务调度器
     * 使用ConcurrentTaskScheduler包装ScheduledExecutorService
     */
    @Bean
    public TaskScheduler testEnvironmentTaskScheduler(ScheduledExecutorService testEnvironmentScheduledExecutorService) {
        // 使用ConcurrentTaskScheduler包装ScheduledExecutorService
        return new ConcurrentTaskScheduler(testEnvironmentScheduledExecutorService);
    }

    /**
     * 创建Bean后处理器，用于处理TestEnvironmentScheduled注解
     */
    @Bean
    public BeanPostProcessor testEnvironmentScheduledAnnotationProcessor(
            TaskScheduler testEnvironmentTaskScheduler) {
        
        return new BeanPostProcessor() {
            private final ScheduledTaskRegistrar registrar = new ScheduledTaskRegistrar();
            private final List<ScheduledTask> scheduledTasks = new ArrayList<>();

            @Override
            public Object postProcessAfterInitialization(@NotNull Object bean, @NotNull String beanName) {
                // 设置任务调度器
                registrar.setTaskScheduler(testEnvironmentTaskScheduler);
                
                // 获取bean的所有方法
                Method[] methods = ReflectionUtils.getAllDeclaredMethods(bean.getClass());
                for (Method method : methods) {
                    // 查找带有TestEnvironmentScheduled注解的方法
                    EnvironmentScheduled annotation = method.getAnnotation(EnvironmentScheduled.class);
                    if (annotation != null) {
                        // 检查当前环境是否在指定的环境列表中
                        if (isInSpecifiedEnvironment(annotation.environments())) {
                            // 注册定时任务
                            registerTask(bean, method, annotation);
                        }
                    }
                }
                
                return bean;
            }

            /**
             * 注册定时任务
             */
            private void registerTask(Object bean, Method method, EnvironmentScheduled annotation) {
                // 确保方法可访问
                ReflectionUtils.makeAccessible(method);
                
                // 创建任务
                Runnable task = () -> ReflectionUtils.invokeMethod(method, bean);
                
                // 根据注解配置创建触发器
                if (StringUtils.hasText(annotation.cron())) {
                    // 使用Cron表达式
                    String cron = annotation.cron();
                    String zone = annotation.zone();
                    TimeZone timeZone = StringUtils.hasText(zone) ? 
                            TimeZone.getTimeZone(ZoneId.of(zone)) : TimeZone.getDefault();
                    
                    // 注册Cron任务
                    scheduledTasks.add(registrar.scheduleCronTask(
                            new CronTask(task, new CronTrigger(cron, timeZone))));
                    
                } else if (annotation.fixedRate() >= 0) {
                    // 使用固定速率
                    long initialDelay = annotation.initialDelay() >= 0 ? annotation.initialDelay() : 0;
                    PeriodicTrigger trigger = new PeriodicTrigger(annotation.fixedRate(), TimeUnit.MILLISECONDS);
                    trigger.setInitialDelay(initialDelay);
                    
                    // 注册固定速率任务
                    scheduledTasks.add(registrar.scheduleTriggerTask(
                            new TriggerTask(task, trigger)));
                    
                } else if (annotation.fixedDelay() >= 0) {
                    // 使用固定延迟
                    long initialDelay = annotation.initialDelay() >= 0 ? annotation.initialDelay() : 0;
                    PeriodicTrigger trigger = new PeriodicTrigger(annotation.fixedDelay(), TimeUnit.MILLISECONDS);
                    trigger.setInitialDelay(initialDelay);
                    trigger.setFixedRate(false);
                    
                    // 注册固定延迟任务
                    scheduledTasks.add(registrar.scheduleTriggerTask(
                            new TriggerTask(task, trigger)));
                }
            }
        };
    }
} 