package com.looksky.agents.common.utils;

import java.util.Map;
import java.util.Properties;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 获取系统环境变量的工具
 *
 * <AUTHOR>
 * @since 1.2.0
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class EnvUtil {

    // Spring 激活的环境配置名
    private static final String SPRING_ACTIVE_ENV = "SPRING_PROFILES_ACTIVE";

    /**
     * 获取所有系统环境变量
     */
    public static Map<String, String> getAllEnv() {
        return System.getenv();
    }

    /**
     * 获取指定的环境变量
     */
    public static String getEnv(String name) {
        return System.getenv(name);
    }

    /**
     * 获取系统属性
     */
    public static String getSystemProperty(String key) {
        return System.getProperty(key);
    }

    /**
     * 获取所有系统属性
     */
    public static Properties getAllSystemProperties() {
        return System.getProperties();
    }

    /**
     * 判断是否为开发环境
     */
    public static boolean isDev() {
        return "dev".equalsIgnoreCase(getSpringActiveEnv());
    }

    /**
     * 判断是否为测试环境
     */
    public static boolean isTest() {
        return "test".equalsIgnoreCase(getSpringActiveEnv());
    }

    /**
     * 判断是否为正式环境
     */
    public static boolean isProd() {
        return "prod".equalsIgnoreCase(getSpringActiveEnv());
    }

    /**
     * 获取 Spring 激活的环境
     */
    public static String getSpringActiveEnv() {
        return getEnv(SPRING_ACTIVE_ENV);
    }
}