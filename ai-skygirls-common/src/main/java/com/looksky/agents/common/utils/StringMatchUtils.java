package com.looksky.agents.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 字符串匹配工具类
 */
public class StringMatchUtils {


    /**
     * 过滤掉双编码的表情符号，保留单编码的字符
     *
     * @param text 原始文本
     * @return 过滤后的文本
     */
    public static String filterDoubleCodedEmojis(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);

            // 检查是否是高代理项字符（双编码表情符号的第一部分）
            if (Character.isHighSurrogate(c)) {
                // 跳过这个字符和下一个低代理项字符
                i++; // 跳过低代理项
                continue;
            }

            // 保留非代理对字符
            result.append(c);
        }

        return result.toString();
    }

    /**
     * 过滤掉字符串中的三个连续短横线"---"
     *
     * @param text 原始文本
     * @return 过滤后的文本，其中所有的"---"被移除
     */
    public static String filterTripleDash(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        // 将三个连续短横线替换为空字符串
        return text.replace("---", "");
    }

    public static String filterMarkdown(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        if (text.contains("```")) {
            return "";
        }
        return text;
    }

    public static String filterThinkEnd(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        if (text.contains("</think>")) {
            return "";
        }
        return text;
    }

    public static String filterDeepSeekOutput(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        if (text.contains("```")) {
            return "";
        }

        if (text.contains("</think>")) {
            return "";
        }

        if (text.contains("---")) {
            return "";
        }

        return filterDoubleCodedEmojis(text);
    }

    /**
     * 判断目标字符串中是否包含指定的文本（支持单词或短语）
     *
     * @param source 源文本，可以是一段话
     * @param target 目标文本，可以是单词或短语
     * @return true if matched, false otherwise
     */
    public static boolean matches(String source, String target) {
        if (StringUtils.isBlank(source) || StringUtils.isBlank(target)) {
            return false;
        }

        // 标准化空白字符
        source = normalizeText(source);
        target = normalizeText(target);

        // 如果target是单个单词，直接在source的单词列表中查找
        if (!target.contains(" ")) {
            return Arrays.asList(source.split("\\s+")).contains(target);
        }

        // 如果是短语，进行短语匹配
        return containsPhrase(source, target);
    }

    /**
     * 判断目标字符串中是否包含完整的短语
     *
     * @param text   目标文本
     * @param phrase 要匹配的短语
     * @return true if matched, false otherwise
     */
    private static boolean containsPhrase(String text, String phrase) {
        if (StringUtils.isBlank(text) || StringUtils.isBlank(phrase)) {
            return false;
        }

        // 标准化空白字符
        text = normalizeText(text);
        phrase = normalizeText(phrase);

        // 如果短语比文本长，直接返回false
        if (phrase.split("\\s+").length > text.split("\\s+").length) {
            return false;
        }

        // 将文本按空格分割成单词数组
        String[] textWords = text.split("\\s+");
        String[] phraseWords = phrase.split("\\s+");

        // 在文本中查找连续的匹配
        for (int i = 0; i <= textWords.length - phraseWords.length; i++) {
            boolean match = true;
            for (int j = 0; j < phraseWords.length; j++) {
                if (!textWords[i + j].equals(phraseWords[j])) {
                    match = false;
                    break;
                }
            }
            if (match) {
                return true;
            }
        }

        return false;
    }

    /**
     * 标准化文本
     * 1. 将标点符号转换为空格
     * 2. 将多个连续空白字符替换为单个空格
     * 3. 去除首尾空白
     */
    private static String normalizeText(String input) {
        if (input == null) {
            return "";
        }
        // 将标点符号和特殊字符转换为空格
        String normalized = input.replaceAll("[,\\-\\.\\'\\\"\\:\\;\\!\\?]", " ");
        // 将多个空格替换为单个空格并去除首尾空白
        return normalized.trim().replaceAll("\\s+", " ");
    }

    /**
     * 获取目标字符串中所有匹配的文本
     *
     * @param source  源文本
     * @param targets 要匹配的文本集合（可以是单词或短语）
     * @return 匹配到的文本集合
     */
    public static Set<String> findMatches(String source, Set<String> targets) {
        if (StringUtils.isBlank(source) || targets == null || targets.isEmpty()) {
            return new HashSet<>();
        }

        Set<String> matches = new HashSet<>();
        for (String target : targets) {
            if (matches(source, target)) {
                matches.add(target);
            }
        }

        return matches;
    }
} 