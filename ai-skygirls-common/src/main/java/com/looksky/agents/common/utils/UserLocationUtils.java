package com.looksky.agents.common.utils;

import cn.hutool.core.util.ObjectUtil;
import com.skygirls.biz.user.dto.UserLocationInfoDTO;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class UserLocationUtils {

    public static UserLocationInfoDTO getLocation(HttpServletRequest request) {

        if (ObjectUtil.isEmpty(request)) {
            return null;
        }
        UserLocationInfoDTO dto = new UserLocationInfoDTO();
        try {
            dto.setHost(request.getHeader("host"))
                .setUserAgent(request.getHeader("user-agent"))
                .setViewerTimeZone(request.getHeader("cloudfront-viewer-time-zone"))
                .setViewerAddress(request.getHeader("cloudfront-viewer-address"))
                .setViewerCountry(request.getHeader("cloudfront-viewer-country"))
                .setIsIosViewer(request.getHeader("cloudfront-is-ios-viewer"))
                .setIsTabletViewer(request.getHeader("cloudfront-is-tablet-viewer"))
                .setForwardedProto(request.getHeader("cloudfront-forwarded-proto"))
                .setViewerCountryName(request.getHeader("cloudfront-viewer-country-name"))
                .setIsMobileViewer(request.getHeader("cloudfront-is-mobile-viewer"))
                .setIsSmartTVViewer(request.getHeader("cloudfront-is-smarttv-viewer"))
                .setViewerCountryRegion(request.getHeader("cloudfront-viewer-country-region"))
                .setIsAndroidViewer(request.getHeader("cloudfront-is-android-viewer"))
                .setViewerCountryRegionName(request.getHeader("cloudfront-viewer-country-region-name"))
                .setViewerCity(request.getHeader("cloudfront-viewer-city"))
                .setViewerLatitude(request.getHeader("cloudfront-viewer-latitude"))
                .setViewerLongitude(request.getHeader("cloudfront-viewer-longitude"))
                .setViewerHttpVersion(request.getHeader("cloudfront-viewer-http-version"))
                .setViewerPostalCode(request.getHeader("cloudfront-viewer-postal-code"))
                .setViewerASN(request.getHeader("cloudfront-viewer-asn"))
                .setIsDesktopViewer(request.getHeader("cloudfront-is-desktop-viewer"))
                .setViewerMetroCode(request.getHeader("cloudfront-viewer-metro-code"))
                .setViewerTLS(request.getHeader("cloudfront-viewer-tls"));
        } catch (Exception e) {
            log.error("获取用户位置信息失败", e);
        }

        return dto;
    }
}
