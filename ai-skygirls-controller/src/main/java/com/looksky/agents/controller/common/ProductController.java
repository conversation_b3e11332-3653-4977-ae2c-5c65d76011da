package com.looksky.agents.controller.common;

import com.looksky.agents.data.opensearch.SkcSearchService;
import com.looksky.agents.infrastructure.response.ResponseResultBody;
import com.looksky.agents.sdk.product.ProductInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 商品相关的 Controller
 *
 * <AUTHOR>
 * @since 1.1.14
 **/
@Tag(name = "商品服务")
@RequestMapping("/product")
@ResponseResultBody
@RequiredArgsConstructor
public class ProductController {
    private final SkcSearchService skcSearchService;

    @SneakyThrows
    @Operation(description = "根据 skcId 查找商品信息")
    @PostMapping("/search")
    public List<ProductInfo> commonRequestModel(@RequestBody ProductIds productIds) {
        return skcSearchService.searchSkc(productIds.ids());
    }

    public record ProductIds(List<String> ids) {}

}
