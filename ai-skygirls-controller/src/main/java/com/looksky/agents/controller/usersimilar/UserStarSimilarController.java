package com.looksky.agents.controller.usersimilar;


import com.looksky.agents.application.userSimilar.UserStarSimilarService;
import com.looksky.agents.sdk.recommend.star.dto.UserStarBodySimilarParam;
import com.looksky.agents.sdk.recommend.star.dto.UserStarBodySimilarResultDTO;
import com.looksky.agents.infrastructure.logs.Log;
import com.looksky.agents.infrastructure.response.ResponseResultBody;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 *用户和明星相似度 Controller
 *
 * @since  1.1.0
 * <AUTHOR>
 **/
@Tag(name = "相似度计算")
@RequestMapping("/similar")
@ResponseResultBody
@RequiredArgsConstructor
public class UserStarSimilarController {

    private final UserStarSimilarService userStarSimilarService;

    @Log
    @Operation(description = "用户和明星身形相似度计算")
    @PostMapping("/userStarBodySimilar")
    public UserStarBodySimilarResultDTO userStarBodySimilar(@RequestBody UserStarBodySimilarParam userStarBodySimilarReq) {
        return userStarSimilarService.userStarBodySimilar(userStarBodySimilarReq);
    }

}
