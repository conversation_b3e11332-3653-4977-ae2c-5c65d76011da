package com.looksky.agents.controller.recommend;

import com.looksky.agents.application.foryou.PartitionRecommendService;
import com.looksky.agents.infrastructure.logs.Log;
import com.looksky.agents.sdk.recommend.foryou.dto.ForYouParam;
import com.looksky.agents.infrastructure.response.ResponseResultBody;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@Tag(name = "forYou")
@RequestMapping("/foryou")
@ResponseResultBody
@RequiredArgsConstructor
public class ForYouController {

    private final PartitionRecommendService partitionRecommendService;


    @Log(logResp = true)
    @PostMapping("/partitionVector")
    @Operation(description = "获取 foryou 不同分区的向量词")
    public ForYouParam forYouPartitionVectorQuery(@RequestBody ForYouParam forYouParam) {
        return partitionRecommendService.forYouVectorQuery(forYouParam);
    }

}
