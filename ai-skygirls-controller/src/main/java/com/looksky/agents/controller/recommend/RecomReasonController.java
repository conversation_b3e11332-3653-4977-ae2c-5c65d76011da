package com.looksky.agents.controller.recommend;

import com.looksky.agents.application.recomReason.ForYouRecomReasonService;
import com.looksky.agents.application.recomReason.GirlsRecomReasonService;
import com.looksky.agents.sdk.recommend.reason.dto.RecommendReasonDTO;
import com.looksky.agents.sdk.recommend.reason.dto.RecommendReasonParam;
import com.looksky.agents.infrastructure.logs.Log;
import com.looksky.agents.infrastructure.response.ResponseResultBody;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author：ch
 * @Date：2024/12/23 14:46
 * @Description
 */
@Tag(name = "推荐理由服务")
@RequestMapping("/recomReason")
@ResponseResultBody
@RequiredArgsConstructor
public class RecomReasonController {

    private final GirlsRecomReasonService girlsRecomReasonService;
    private final ForYouRecomReasonService forYouRecomReasonService;

    @Log
    @Deprecated(since = "2025-3-13", forRemoval = true)
    @Operation(description = "获取girls版本 推荐理由")
    @PostMapping("/findGirlsRecomReason")
    public void findGirlsRecomReasonAsync(@RequestBody RecommendReasonParam req) {
        girlsRecomReasonService.getRecomReasonAsync(req.getUserId(), req.getSeason(), req.getCity(), req.getItemIds());
    }

    @Log
    @Operation(description = "获取girls版本 daily100推荐理由")
    @PostMapping("/findGirlsRecomReasonDaily100")
    public List<RecommendReasonDTO> findGirlsRecomReasonSync(@RequestBody RecommendReasonParam req) {

        if (req.getScene() == null) {
            return girlsRecomReasonService.buildRecomReasonByDaily100(req.getUserId(), req.getSeason(), req.getCity(), req.getItemIds(), req.getBatchSize());
        }
        return forYouRecomReasonService.buildRecomReasonByForYou(req.getUserId(), req.getSeason(), req.getCity(), req.getItemIds(), req.getBatchSize(), req.getScene());
    }


    @Log
    @Operation(description = "for you 板块的推荐理由")
    @PostMapping("/findForYouRecomReason")
    public List<RecommendReasonDTO> findForYouRecomReason(@RequestBody RecommendReasonParam req) {
        return forYouRecomReasonService.buildRecomReasonByForYou(req.getUserId(), req.getSeason(), req.getCity(), req.getItemIds(), req.getBatchSize(), req.getScene());
    }


}
