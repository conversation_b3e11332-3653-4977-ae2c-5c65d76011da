package com.looksky.agents.controller.test;

import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.ChatModelFallbackService;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequestMapping("/test")
@RestController
@RequiredArgsConstructor
public class TestOpenAiAzureCost {
    private final ChatModelFallbackService chatModelFallbackService;

    private static final String FEI_SHU_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/6227fd46-4273-4141-a362-2c1764d13a5e";


    @SneakyThrows
    @GetMapping("/testOpenAiAzureCost")
    public void testOpenAiAzureCost() {
        VirtualCompletableFuture.runAsync(this::execute);
    }


    @SneakyThrows
    public void execute() {

        List<CostResult> costResults = new ArrayList<>();

        for (int i = 1; i <= 50; i++) {
            log.info("开始执行第 {} 次测试", i);
            RequestParamBO prompt = getPrompt();
            VirtualCompletableFuture<CostResult> cost = cost(prompt, i);
            costResults.add(cost.join());
            TimeUnit.SECONDS.sleep(2);
        }

        log.info("耗时结果: {}", JSONUtil.toJsonStr(costResults));

        // 将结果发送到飞书
        sendToFeiShu(costResults);


    }

    private void sendToFeiShu(List<CostResult> costResults) {
        try {
            // 构建消息内容
            StringBuilder messageBuilder = new StringBuilder();
            messageBuilder.append("OpenAI 和 Azure 性能对比测试结果:\n\n");

            for (CostResult result : costResults) {
                messageBuilder.append("序号: ").append(result.getOpenAiCost().getIndex()).append("\n");
                messageBuilder.append("OpenAI 耗时: ").append(result.getOpenAiCost().getCost()).append("ms\n");
                messageBuilder.append("Azure 耗时: ").append(result.getAzureCost().getCost()).append("ms\n");
                messageBuilder.append("-------------------\n");
            }

            // 计算平均耗时
            double openAiAvgCost = costResults.stream()
                .mapToLong(r -> r.getOpenAiCost().getCost())
                .average()
                .orElse(0);
            double azureAvgCost = costResults.stream()
                .mapToLong(r -> r.getAzureCost().getCost())
                .average()
                .orElse(0);

            messageBuilder.append("\n统计结果:\n");
            messageBuilder.append("OpenAI 平均耗时: ").append(String.format("%.2f", openAiAvgCost)).append("ms\n");
            messageBuilder.append("Azure 平均耗时: ").append(String.format("%.2f", azureAvgCost)).append("ms");

            // 构建飞书消息格式
            Map<String, Object> message = new HashMap<>();
            message.put("msg_type", "text");
            message.put("content", Map.of("text", messageBuilder.toString()));

            try (HttpResponse response = HttpRequest.post(FEI_SHU_URL)
                .header("Content-Type", "application/json")
                .body(JSONUtil.toJsonStr(message))
                .execute()) {


                //// 发送 HTTP 请求
                //HttpRequest request = HttpRequest.newBuilder()
                //    .uri(URI.create(feiShuUrl))
                //    .header("Content-Type", "application/json")
                //    .POST(HttpRequest.BodyPublishers.ofString(JSONUtil.toJsonStr(message)))
                //    .build();
                //
                //HttpClient client = HttpClient.newHttpClient();
                //HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

                if (!response.isOk()) {
                    log.error("发送飞书消息失败: {}", response.body());
                }
            }
            Map<String, Object> message2 = new HashMap<>();
            message2.put("msg_type", "text");
            message2.put("content", Map.of("text", JSONUtil.toJsonStr(costResults)));

            try (HttpResponse response1 = HttpRequest.post(FEI_SHU_URL)
                .header("Content-Type", "application/json")
                .body(JSONUtil.toJsonStr(message2))
                .execute()) {

                if (!response1.isOk()) {
                    log.error("发送飞书消息1失败: {}", response1.body());
                }
            }


        } catch (Exception e) {
            log.error("发送飞书消息异常", e);
        }
    }


    private VirtualCompletableFuture<CostResult> cost(RequestParamBO prompt, Integer index) {
        VirtualCompletableFuture<Result> openAiFuture = executeOpenAi(prompt, index);
        VirtualCompletableFuture<Result> azureFuture = executeAzure(prompt, index);
        return openAiFuture.thenCombine(azureFuture, (openAiCost, azureCost) -> {
            CostResult costResult = new CostResult();
            costResult.setOpenAiCost(openAiCost);
            costResult.setAzureCost(azureCost);
            return costResult;
        });
    }


    private VirtualCompletableFuture<Result> executeOpenAi(RequestParamBO prompt, Integer index) {
        return VirtualCompletableFuture.supplyAsync(() -> {
            Result result = new Result();
            result.setIndex(index);
            long startTime = System.currentTimeMillis();
            String content = "OpenAI 请求超时";
            try {
                content = chatModelFallbackService.execute(prompt).getContent();
            } catch (Exception e) {
                log.error("OpenAI 请求异常", e);
            }
            long endTime = System.currentTimeMillis();
            long cost = endTime - startTime;
            result.setStartTime(startTime);
            result.setEndTime(endTime);
            result.setCost(cost);
            result.setContent(content);
            return result;
        });
    }


    private VirtualCompletableFuture<Result> executeAzure(RequestParamBO prompt, Integer index) {
        return VirtualCompletableFuture.supplyAsync(() -> {
            Result result = new Result();
            result.setIndex(index);
            long startTime = System.currentTimeMillis();
            String content = "Azure 请求超时";
            try {
                content = chatModelFallbackService.executeAzure(prompt).getContent();
            } catch (Exception e) {
                log.error("Azure 请求异常", e);
            }
            long endTime = System.currentTimeMillis();
            long cost = endTime - startTime;
            result.setStartTime(startTime);
            result.setEndTime(endTime);
            result.setCost(cost);
            result.setContent(content);
            return result;
        });
    }

    private RequestParamBO getPrompt() {
        String idStr = IdUtil.getSnowflakeNextIdStr();
        RequestParamBO requestParamBO = JSONUtil.toBean(PROMPT, RequestParamBO.class);
        requestParamBO.setSystemPrompt(idStr + "---\n" + requestParamBO.getSystemPrompt());
        requestParamBO.setUserPrompt(idStr + "---\n" + requestParamBO.getUserPrompt());

        ArrayList<String> strings = new ArrayList<>();
        strings.add("https://cdn.lookskyai.com/upload/agent/bdbb8ed99fb6c7844faf507e09cbd587.jpeg");
        strings.add("https://cdn.lookskyai.com/upload/agent/bdbb8ed99fb6c7844faf507e09cbd587.jpeg");
        strings.add("https://cdn.lookskyai.com/upload/agent/8fee9c231a3bb01fc8f745c3fef297e9.jpeg");

        requestParamBO.setImageUrl(strings);

        return requestParamBO;
    }

    @Data
    static class CostResult {
        private Result openAiCost;
        private Result azureCost;
    }


    @Data
    static class Result {
        private Integer index;
        private Long startTime;
        private Long endTime;
        private Long cost;
        private String model;
        private String content;
    }

    private static final String PROMPT = """
        {
            "systemPrompt": "# Skylar: Advanced Fashion, Makeup, and Hairstyle Consultant\\n﻿\\n## Role:\\n﻿\\nYou are Skylar, the ultimate cool big sister and style guru for American Gen Z women. Your vibe is fun, chic, and effortlessly relatable—like the friend who always has the best style tips and knows what’s trending. Your mission is to guide users through their beauty and fashion journey with positivity, a touch of humor, and actionable suggestions they can actually use. Think cool, approachable, and practical—no generic or unattainable advice here!  \\n﻿\\n## Audience:\\n﻿\\nYoung women (ages 14–22) in the U.S. who are enhancing their personal style, confidence, and attractiveness.\\n﻿\\n## Goals:\\nGenerate a **personalized styling report** covering **fashion**, **makeup**, and **hairstyles**. Provide **specific, actionable feedback** for each section that feels like it’s coming from a trusted friend. Keep the tone playful and engaging, with relatable pop-culture references, trendy language, and a casual vibe.  \\n﻿\\n## Task:\\n### **User Information**:\\n﻿\\n        age: 24\\n        height: 6' 1\\"\\n        weight: 200 lbs\\n        skinDepth: LIGHT\\n        skinTone: yellow\\n        hairColor: BLACK\\n        faceShape: OVAL\\n        pupilColor: DARK_BROWN\\n        girlsEyeShape: Almond_Eyes\\n        girlsNose: Button_Nose\\n        girlsMouth: full_lips\\n        eyebrowDistribution: MEDIUM_DENSITY\\n        eyebrow: Arched_Brows\\n        colorSeason: DARK_WINTER\\n        kibbeStyle: NATURAL\\nAnalyze the user's profile and uploaded photos. Perform the following:\\n﻿\\n1. **Outfit Scoring**: Assess all aspects, including color coordination, proportions, trends, and accessorizing.\\n﻿\\n2. **Makeup Scoring**: Evaluate base makeup, eye makeup precision, eyebrow grooming, lip coordination, and blush.\\n﻿\\n3. **Hair Scoring**: Analyze the harmony of hair color with skin tone, and the suitability of the hairstyle for the user’s face shape.\\n﻿\\n## Scoring Structure:\\n﻿\\n### Scoring Standards:\\n﻿\\n- **Initial Scores**: **60–89** for the first evaluation—leaving room for improvement.\\n﻿\\n- **Subsequent Evaluations**: **1–10 scale** to reflect progress or decline. Each new score is compared to the previous one, with changes not exceeding **5 points**.\\n﻿\\n### feedbackTemplates :\\n﻿\\n- **90–100 (Excellent)**text: You’re absolutely nailing it! Keep up the great work. 💖\\n﻿\\n- **80–89 (Great)**text: Almost perfect! Just a little tweak and you’ll be there! ✨\\n﻿\\n- **70–79 (Good)**text: Solid look, but there are areas to refine. Let’s make it shine! 💫\\n﻿\\n- **60–69 (Needs Improvement)**text: On track, but there are key areas to focus on. Let’s work together to improve. 👏\\n﻿\\n- **Below 60 (Poor)**text: No worries! You’ve got the foundation, let’s work on those key areas for improvement. 💪\\n﻿\\n####note: \\n﻿- Keep feedback short, fun, and relatable. Z Gen users don’t want a lecture or overly robotic responses.  \\n- Output the text of 'feedbackTemplates' in the' feedback 'section based on the score range.\\n- Please do not always score as a multiple of 5.\\n﻿\\n- Please reply to the user in the second person, as it will appear more friendly.\\n﻿\\n- Please note that this analysis is intended for users and needs to be conducted in the second person as' you '.\\n﻿\\n- In the **reason** sections of **styleAndTrends** and **colorConference**, ensure the content focuses exclusively on clothing-related suggestions. Avoid including recommendations or references to accessories, shoes, or other non-clothing items.\\n﻿",
            "userPrompt": "##Rating example:\\n﻿\\n### **Outfit Scoring (40%)**\\n﻿\\n- **Color Coordination (30%)**:\\n﻿\\n- **Score**: 70\\n﻿\\n- **Reason**: “Your pink cardigan works beautifully with your light spring palette, but the gray top feels slightly off, muting your warm undertones. Consider a cream or pale peach blouse to restore balance.”\\n﻿\\n- **Fit and Proportion (30%)**:\\n﻿\\n- **Score**: 85\\n﻿\\n- **Reason**: “Your wide-leg pants are comfy, though they don’t define your shape as nicely as your last outfit did. Try high-waisted trousers or a belted A-line skirt to maintain comfort and highlight your silhouette. A cinched waist or subtle flare can help bring back that flattering balance.”\\n﻿\\n- **Style and Trends (25%)**:\\n﻿\\n- **Score**: 75\\n﻿\\n- **Reason**: Last time, structured pieces like a softly detailed dress framed your face beautifully. This time, the casual outfit feels a bit too relaxed. Try tops with soft necklines to better highlight your facial features.\\n### **Makeup Scoring (30%)**\\n﻿\\n- **Face and Outfit Coordination**  \\n- **Score**: 75\\n**Reason:** “The casual outfit feels a bit too relaxed. Tops with soft necklines, like a scoop neck or a gentle V-neck, can frame your face more effectively and draw attention to your features.”  \\n﻿\\n- **Accessories and Integrity**  \\n- **Score**: 74\\n**Reason:** “Consider adding a lightweight scarf or a minimalist tote to tie your outfit together. These subtle additions can create a cohesive, polished look without overpowering your style.”  \\n﻿\\n--- ﻿\\n﻿\\n- **Base Makeup (20%)**:\\n﻿\\n- **Score**: 80\\n﻿\\n- **Reason**: Great job! The base is flawless. Adding a soft highlighter will make your face glow even more.\\n﻿\\n- **Eye Makeup Precision (20%)**:\\n﻿\\n- **Score**: 65\\n﻿\\n- **Reason**: Looks good, but you need to sharpen up that eyeliner to really make your eyes pop. Try a firmer eyeliner pencil for better precision.\\n﻿\\n- **Lip Coordination (15%)**:\\n﻿\\n- **Score**: 90\\n﻿\\n- **Reason**: Absolutely perfect! Your lip color is on point. Keep it up!\\n﻿\\n### **Hair Scoring (30%)**\\n﻿\\n- **Hair Color & Skin Tone Harmony (25%)**:\\n﻿\\n- **Score**: 70\\n﻿\\n- **Reason**: Your hair color is looking good, but adding some highlights could create a more radiant appearance.\\n﻿\\n- **Face Shape & Hair Coordination (25%)**:\\n﻿\\n- **Score**: 60\\n﻿\\n- **Reason**: Your hairstyle suits your face shape, but adding some volume on top or a side part could help elongate your face more.\\n﻿\\n---\\n﻿\\n### Scoring & Improvement Recommendations:\\n﻿\\n- **Initial Scores (60-89 Range)**: You’ve got the foundation, but there's still room for improvement. Here are some suggestions to help you level up your style!\\n﻿\\n- **Subsequent Scores**: Follow-up evaluations will track changes. We’ll compare your scores and highlight areas where you've made progress or need more attention.\\n﻿\\n- **Actionable Tips**: Based on the Reason, here are the key areas to focus on for a quick improvement! 💪\\n﻿\\n---\\n﻿\\n### Reason Based on Score Ranges:\\n﻿\\n#### **High Scores (90–100)**\\n﻿\\n- **Example**: *Lip Coordination: 90 (Excellent)*\\n﻿\\n- **Reason**: You’re absolutely nailing it! Your lip color complements your skin tone and the overall look perfectly. Keep rocking this shade!\\n﻿\\n#### **Medium Scores (70–89)**\\n﻿\\n- **Example**: *Eye Makeup Precision: 65 (Good)*\\n﻿\\n- **Reason**: Looks good, but you need to sharpen up that eyeliner. A firmer pencil will help create a more defined line and really make your eyes pop. ✨\\n﻿\\n#### **Low Scores (Below 60)**\\n﻿\\n- **Example**: *Face Shape & Hair Coordination: 60 (Needs Improvement)*\\n﻿\\n- **Reason**: Your hairstyle is a good start, but try adding some volume or a side part to elongate your face and create a more balanced look. It will make a big difference! 💪\\n﻿\\n﻿\\n﻿\\n## Key Notes:\\n﻿\\n1. **Direct, Actionable Feedback & Improvement Tips**:\\n﻿\\nThe prompt should focus on giving **direct and actionable feedback** with **specific improvement tips**. Avoid generic comments and instead, offer clear guidance on what can be done to improve the look or style.\\n﻿\\n2. **Friendly, Approachable Tone**:\\n﻿\\nUse a **friendly, approachable tone** while maintaining **expertise**. This ensures that the feedback is not only helpful but also feels relatable and encouraging to the user, especially since we're addressing younger audiences.\\n﻿\\n3. **Consistent Scoring & Guidelines**:\\n﻿\\nEnsure **consistent scoring** across all categories (Outfit, Makeup, Hair), and provide clear **guidelines for each score range** (e.g., Excellent, Great, Good, On Track, Needs Improvement). This makes it easier for users to understand where they stand and how they can improve.\\n﻿\\n4. **Use of Second Person (\\"You\\")**:\\n﻿\\nUse **second person (\\"you\\")** to make the feedback more personalized and relatable. This helps create a connection with the user, making the feedback feel like it’s tailored specifically for them.\\n﻿\\n5. **Be Specific About Items**:\\n﻿\\nAlways be specific about the items being discussed. For example, instead of saying \\"this outfit,\\" refer to \\"this white lace dress\\" or \\"these black platform heels.\\" It makes the feedback more precise and clear.\\n﻿\\n6. **Resonate with Gen Z Preferences**:\\n﻿\\nIncorporate elements that resonate with **American Gen Z women**. This includes using trendy language, pop culture references, and understanding their shopping habits. Mentioning influencers or trends they admire can make the feedback feel more on-point.\\n﻿\\n7. **Avoid Redundancy**:\\n﻿\\nEnsure that each piece of feedback is unique and relevant. Avoid repeating the same information in multiple sections, as it can make the feedback feel repetitive and less engaging.\\n﻿\\n8. **Maintain a Professional Yet Relatable Tone**:\\n﻿\\nKeep the tone **professional** but still **relatable**. Users should feel that the feedback is from someone who understands style, but also feels approachable and human.\\n﻿\\n9. **Use Expressive Elements**:\\n﻿\\nIntroduce **expressive elements** like emojis to make the feedback more engaging and fun. This can enhance the user experience and make the feedback feel more lively and less formal.\\n﻿\\n10. **Clear Clothing Descriptions**:\\n﻿\\nWhen describing the user’s clothing, use clear language. Instead of saying “this set of clothes,” refer to “this white lace dress” or “this black velvet jacket.” It ensures clarity and precision in the feedback.\\n11. ﻿**Reason Fields:**\\n- Ensure each reason is succinct, ranging from 100 to 150 characters.\\n- Provide only constructive feedback, avoiding excessive praise.",
            "modelName": "gpt-4o-2024-11-20",
            "temperature": 1,
            "jsonSchema": "{\\n  \\"$schema\\": \\"http://json-schema.org/draft-07/schema#\\",\\n  \\"title\\": \\"Comprehensive_Personal_Style_Evaluation\\",\\n  \\"description\\": \\"Unified schema for evaluating the user's outfit, hairstyle, and makeup. Each category is scored on a 0-100 scale with personalized feedback based on the score and user's specific attributes.\\",\\n  \\"type\\": \\"object\\",\\n  \\"properties\\": {\\n    \\"fitsMatch\\": {\\n      \\"type\\": \\"object\\",\\n      \\"title\\": \\"Outfit Assessment\\",\\n      \\"properties\\": {\\n        \\"description\\": {\\n          \\"type\\": \\"string\\",\\n          \\"description\\": \\"Provide a general description of the user's outfit, including its components (e.g., top, bottom, accessories) and overall style. Assess how well the outfit enhances the user's body shape, color season, and personal style.\\"\\n        },\\n        \\"colorCoordination\\": {\\n          \\"type\\": \\"object\\",\\n          \\"properties\\": {\\n            \\"score\\": {\\n              \\"type\\": \\"integer\\",\\n              \\"description\\": \\"Score for color coordination in the outfit (0-100 scale).\\"\\n            },\\n            \\"feedback\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Use 'feedbackTemplates' to generate corresponding types based on scores..\\"\\n            },\\n            \\"reason\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Feedback based on the color coordination performance.\\"\\n            }\\n          },\\n          \\"additionalProperties\\": false,\\n          \\"required\\": [\\n            \\"score\\",\\n            \\"feedback\\",\\n            \\"reason\\"\\n          ]\\n        },\\n        \\"fitAndProportion\\": {\\n          \\"type\\": \\"object\\",\\n          \\"properties\\": {\\n            \\"score\\": {\\n              \\"type\\": \\"integer\\",\\n              \\"description\\": \\"Score for the fit and proportion of the outfit (0-100 scale).\\"\\n            },\\n            \\"feedback\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Use 'feedbackTemplates' to generate corresponding types based on scores..\\"\\n            },\\n            \\"reason\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Feedback based on the fit and proportion performance.\\"\\n            }\\n          },\\n          \\"additionalProperties\\": false,\\n          \\"required\\": [\\n            \\"score\\",\\n            \\"feedback\\",\\n            \\"reason\\"\\n          ]\\n        },\\n        \\"styleAndTrendiness\\": {\\n          \\"type\\": \\"object\\",\\n          \\"properties\\": {\\n            \\"score\\": {\\n              \\"type\\": \\"integer\\",\\n              \\"description\\": \\"Score for the style and trendiness of the outfit (0-100 scale).\\"\\n            },\\n            \\"feedback\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Use 'feedbackTemplates' to generate corresponding types based on scores..\\"\\n            },\\n            \\"reason\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Feedback based on the style and trends performance.\\"\\n            }\\n          },\\n          \\"additionalProperties\\": false,\\n          \\"required\\": [\\n            \\"score\\",\\n            \\"feedback\\",\\n            \\"reason\\"\\n          ]\\n        },\\n        \\"faceCoordination\\": {\\n          \\"type\\": \\"object\\",\\n          \\"properties\\": {\\n            \\"score\\": {\\n              \\"type\\": \\"integer\\",\\n              \\"description\\": \\"Score for face and outfit coordination (0-100 scale).\\"\\n            },\\n            \\"feedback\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Use 'feedbackTemplates' to generate corresponding types based on scores..\\"\\n            },\\n            \\"reason\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Feedback based on the face and outfit coordination performance.\\"\\n            }\\n          },\\n          \\"additionalProperties\\": false,\\n          \\"required\\": [\\n            \\"score\\",\\n            \\"feedback\\",\\n            \\"reason\\"\\n          ]\\n        },\\n        \\"accessories\\": {\\n          \\"type\\": \\"object\\",\\n          \\"properties\\": {\\n            \\"score\\": {\\n              \\"type\\": \\"integer\\",\\n              \\"description\\": \\"Score for accessories and their role in completing the outfit (0-100 scale).\\"\\n            },\\n            \\"feedback\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Use 'feedbackTemplates' to generate corresponding types based on scores..\\"\\n            },\\n            \\"reason\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Feedback based on the accessories and integrity performance.\\"\\n            }\\n          },\\n          \\"additionalProperties\\": false,\\n          \\"required\\": [\\n            \\"score\\",\\n            \\"feedback\\",\\n            \\"reason\\"\\n          ]\\n        }\\n      },\\n      \\"additionalProperties\\": false,\\n      \\"required\\": [\\n        \\"description\\",\\n        \\"colorCoordination\\",\\n        \\"fitAndProportion\\",\\n        \\"styleAndTrendiness\\",\\n        \\"faceCoordination\\",\\n        \\"accessories\\"\\n      ]\\n    },\\n    \\"hairMatch\\": {\\n      \\"type\\": \\"object\\",\\n      \\"title\\": \\"Hairstyle Assessment\\",\\n      \\"properties\\": {\\n        \\"description\\": {\\n          \\"type\\": \\"string\\",\\n          \\"description\\": \\"General description of the user's hairstyle, including length, texture, and style. Assess how well the hairstyle complements facial features, color season, and personal style.\\"\\n        },\\n        \\"colorMatch\\": {\\n          \\"type\\": \\"object\\",\\n          \\"properties\\": {\\n            \\"score\\": {\\n              \\"type\\": \\"integer\\",\\n              \\"description\\": \\"Score for color coordination in the hairstyle (0-100 scale).\\"\\n            },\\n            \\"feedback\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Use 'feedbackTemplates' to generate corresponding types based on scores..\\"\\n            },\\n            \\"reason\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Personalized feedback based on the color coordination performance.\\"\\n            }\\n          },\\n          \\"additionalProperties\\": false,\\n          \\"required\\": [\\n            \\"score\\",\\n            \\"feedback\\",\\n            \\"reason\\"\\n          ]\\n        },\\n        \\"hairAndFaceShapeMatch\\": {\\n          \\"type\\": \\"object\\",\\n          \\"properties\\": {\\n            \\"score\\": {\\n              \\"type\\": \\"integer\\",\\n              \\"description\\": \\"Score for hairstyle and face shape coordination (0-100 scale).\\"\\n            },\\n            \\"feedback\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Use 'feedbackTemplates' to generate corresponding types based on scores..\\"\\n            },\\n            \\"reason\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Personalized feedback based on the hairstyle and face shape coordination performance.\\"\\n            }\\n          },\\n          \\"additionalProperties\\": false,\\n          \\"required\\": [\\n            \\"score\\",\\n            \\"feedback\\",\\n            \\"reason\\"\\n          ]\\n        }\\n      },\\n      \\"additionalProperties\\": false,\\n      \\"required\\": [\\n        \\"description\\",\\n        \\"colorMatch\\",\\n        \\"hairAndFaceShapeMatch\\"\\n      ]\\n    },\\n    \\"makeupMatch\\": {\\n      \\"type\\": \\"object\\",\\n      \\"title\\": \\"Makeup Assessment\\",\\n      \\"properties\\": {\\n        \\"description\\": {\\n          \\"type\\": \\"string\\",\\n          \\"description\\": \\"General description of the user's makeup look, including base makeup, eye makeup, eyebrow grooming, lip makeup, and blush. This should assess how well the makeup enhances facial features and complements style.\\"\\n        },\\n        \\"effectFoundation\\": {\\n          \\"type\\": \\"object\\",\\n          \\"properties\\": {\\n            \\"score\\": {\\n              \\"type\\": \\"integer\\",\\n              \\"description\\": \\"Score for base makeup effect (0-100 scale).\\"\\n            },\\n            \\"feedback\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Use 'feedbackTemplates' to generate corresponding types based on scores..\\"\\n            },\\n            \\"reason\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Feedback based on the base makeup's performance.\\"\\n            }\\n          },\\n          \\"additionalProperties\\": false,\\n          \\"required\\": [\\n            \\"score\\",\\n            \\"feedback\\",\\n            \\"reason\\"\\n          ]\\n        },\\n        \\"eyeMakeupEffect\\": {\\n          \\"type\\": \\"object\\",\\n          \\"properties\\": {\\n            \\"score\\": {\\n              \\"type\\": \\"integer\\",\\n              \\"description\\": \\"Score for eye makeup effect (0-100 scale).\\"\\n            },\\n            \\"feedback\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Use 'feedbackTemplates' to generate corresponding types based on scores..\\"\\n            },\\n            \\"reason\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Feedback based on the eye makeup's performance.\\"\\n            }\\n          },\\n          \\"additionalProperties\\": false,\\n          \\"required\\": [\\n            \\"score\\",\\n            \\"feedback\\",\\n            \\"reason\\"\\n          ]\\n        },\\n        \\"browShape\\": {\\n          \\"type\\": \\"object\\",\\n          \\"properties\\": {\\n            \\"score\\": {\\n              \\"type\\": \\"integer\\",\\n              \\"description\\": \\"Score for eyebrow grooming effect (0-100 scale).\\"\\n            },\\n            \\"feedback\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Use 'feedbackTemplates' to generate corresponding types based on scores..\\"\\n            },\\n            \\"reason\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Feedback on the eyebrow grooming.\\"\\n            }\\n          },\\n          \\"additionalProperties\\": false,\\n          \\"required\\": [\\n            \\"score\\",\\n            \\"feedback\\",\\n            \\"reason\\"\\n          ]\\n        },\\n        \\"lipStyle\\": {\\n          \\"type\\": \\"object\\",\\n          \\"properties\\": {\\n            \\"score\\": {\\n              \\"type\\": \\"integer\\",\\n              \\"description\\": \\"Score for lip makeup effect (0-100 scale).\\"\\n            },\\n            \\"feedback\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Use 'feedbackTemplates' to generate corresponding types based on scores..\\"\\n            },\\n            \\"reason\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Feedback on the lip makeup.\\"\\n            }\\n          },\\n          \\"additionalProperties\\": false,\\n          \\"required\\": [\\n            \\"score\\",\\n            \\"feedback\\",\\n            \\"reason\\"\\n          ]\\n        },\\n        \\"blushGlow\\": {\\n          \\"type\\": \\"object\\",\\n          \\"properties\\": {\\n            \\"score\\": {\\n              \\"type\\": \\"integer\\",\\n              \\"description\\": \\"Score for blush makeup effect (0-100 scale).\\"\\n            },\\n            \\"feedback\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Use 'feedbackTemplates' to generate corresponding types based on scores..\\"\\n            },\\n            \\"reason\\": {\\n              \\"type\\": \\"string\\",\\n              \\"description\\": \\"Feedback on the blush effect.\\"\\n            }\\n          },\\n          \\"additionalProperties\\": false,\\n          \\"required\\": [\\n            \\"score\\",\\n            \\"feedback\\",\\n            \\"reason\\"\\n          ]\\n        }\\n      },\\n      \\"additionalProperties\\": false,\\n      \\"required\\": [\\n        \\"description\\",\\n        \\"effectFoundation\\",\\n        \\"eyeMakeupEffect\\",\\n        \\"browShape\\",\\n        \\"lipStyle\\",\\n        \\"blushGlow\\"\\n      ]\\n    }\\n  },\\n  \\"additionalProperties\\": false,\\n  \\"required\\": [\\n    \\"fitsMatch\\",\\n    \\"hairMatch\\",\\n    \\"makeupMatch\\"\\n  ]\\n}",
            "outputType": "FUNCTION_CALL",
            "streaming": false,
            "size": 512
        }
        """;
}
