package com.looksky.agents.controller.tryon.colorseason;

import com.graecove.common.ApiResp;
import com.looksky.agents.application.chat.reply.stream.TryOnDetailOpeningMessageSender;
import com.looksky.agents.application.tryon.colorseason.TryOnColorSeasonFacade;
import com.looksky.agents.application.tryon.colorseason.impl.TryOnColorSeasonServiceV1;
import com.looksky.agents.data.client.service.LookSkyDataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.logs.Log;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.tryon.dto.TryOnReplayDTO;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonParam;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonParamV1;
import com.skygirls.biz.im.dto.MessageRestDTO;
import com.skygirls.biz.user.tryon.model.TryOnReportModel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "换装服务")
@RequestMapping("/tryon")
@RestController
@RequiredArgsConstructor
public class TryOnColorSeasonController {
    private final TryOnColorSeasonServiceV1 tryOnColorSeasonServiceV1;
    private final TryOnColorSeasonFacade tryOnColorSeasonFacade;
    private final TryOnDetailOpeningMessageSender tryOnDetailOpeningMessageSender;
    private final LookSkyDataService lookSkyDataService;


    @Log(logResp = true)
    @Operation(description = "colorSeason换装")
    @PostMapping("/colorSeason")
    public ApiResp<String> colorSeason(@RequestBody TryOnColorSeasonParam colorSeasonParam) {
        String result = tryOnColorSeasonServiceV1.tryOnColorSeason(colorSeasonParam);
        return ApiResp.ok(result);
    }

    @Log(logResp = true)
    @Operation(description = "colorSeason换装 - 四步版本- 换白 T (带容错功能)")
    @PostMapping("/v1/colorSeason")
    public ApiResp<String> tryOnWhiteT(@Valid @RequestBody TryOnColorSeasonParamV1 colorSeasonParam) {
        String result = tryOnColorSeasonFacade.tryOnWhiteT(colorSeasonParam);
        return ApiResp.ok(result);
    }

    @Log(logResp = true)
    @Deprecated(since = "1.1.9", forRemoval = true)
    @Operation(description = "colorSeason换装 - 通知有新用户进入 app")
    @GetMapping("/v1/colorSeason/notify")
    public ApiResp<Void> notifyStart() {
        return ApiResp.ok(null);
    }


    @Log(logResp = true)
    @Operation(description = "tryOn 点评穿搭")
    @PostMapping("/pointsToWear")
    public ApiResp<String> pointsToWear(@RequestBody TryOnReportModel tryOnReportModel) {
        lookSkyDataService.getUserInfo(tryOnReportModel.getUserId());

        MessageRestDTO.EventDict eventDict = new MessageRestDTO.EventDict();
        eventDict.setSkcId(tryOnReportModel.getSkcId());
        eventDict.setSkuId(tryOnReportModel.getSkuId());
        RequestInputDTO requestInput = RequestInputDTO.builder()
            .userId(tryOnReportModel.getUserId())
            .eventDict(eventDict)
            .build();

        Context.put(Context.Name.REQUEST_INPUT.getName(), requestInput);

        TryOnReplayDTO praise = tryOnDetailOpeningMessageSender.getPraise(tryOnReportModel.getUploadImage(), tryOnReportModel.getResultImage());
        return ApiResp.ok(praise.getThreeLineReview());
    }
}
