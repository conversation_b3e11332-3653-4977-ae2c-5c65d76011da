package com.looksky.agents.controller.schedule;

import com.looksky.agents.application.schedule.PreRequestService;
import com.looksky.agents.infrastructure.response.ResponseResultBody;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@Slf4j
@Tag(name = "定时任务")
@RequestMapping("/schedule")
@ResponseResultBody
@RequiredArgsConstructor
public class PreRequestSchemaController {

    private final PreRequestService preRequestService;


    @Operation(description = "预请求 json schema")
    // 每 6 个小时执行一次
    @GetMapping("/jsonSchema")
    public void request() {
        preRequestService.buildRequest();
    }
}
