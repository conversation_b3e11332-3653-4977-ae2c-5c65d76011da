package com.looksky.agents.controller.test;

import cn.hutool.json.JSONObject;
import com.graecove.common.ApiResp;
import com.looksky.agents.data.client.business.LookSkyClient;
import com.looksky.agents.data.grpc.SearchGrpcClient;
import com.looksky.agents.sdk.agent.search.dto.TestSearchRequestDTO;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import com.looksky.agents.sdk.recommend.search.dto.response.SearchResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.bedrock.converse.BedrockProxyChatModel;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@Slf4j
@RequestMapping("/test")
@RestController
@Tag(name = "测试接口")
public class TestSearchController {

    @Resource
    private LookSkyClient lookSkyClient;

    @Resource
    private SearchGrpcClient searchGrpcClient;

    @Resource
    private BedrockProxyChatModel chatModel;


    // https://www.cnblogs.com/vipstone/p/18712440
    @GetMapping("/chat")
    public SseEmitter testDeepSeekOutput(@RequestParam String message) {
        // 测试 DeepSeek 的输出
        // 创建 SSE 发射器，设置超时时间（例如 1 分钟）
        SseEmitter emitter = new SseEmitter(60_000L);
        // 创建 Prompt 对象
        Prompt prompt = new Prompt(new UserMessage(message), ChatOptions.builder().maxTokens(32768).build());
        // 订阅流式响应
        // 异常处理
        // 完成处理
        chatModel.stream(prompt).subscribe(response -> {
                try {
                    Generation result = response.getResult();
                    if (result == null) {
                        return;
                    }
                    String content = result.getOutput().getText().replace("<think>", "");

                    System.out.println(content);

                    // 发送 SSE 事件
                    emitter.send(SseEmitter.event()
                        .data(content)
                        .build());
                } catch (Exception e) {
                    emitter.completeWithError(e);
                }
            },
            emitter::completeWithError,
            emitter::complete
        );
        // 处理客户端断开连接
        emitter.onCompletion(() -> {
            // 可在此处释放资源
            log.info("SSE connection completed");
        });
        emitter.onTimeout(() -> {
            emitter.complete();
            log.info("SSE connection timed out");
        });
        return emitter;
    }


    @Operation(summary = "search 的测试接口, 直接传入向量词, 供测评系统使用")
    @PostMapping("/search")
    public ApiResp<Object> search(@RequestBody SearchRequestDTO requestDTO) {
        TestSearchRequestDTO testSearchRequestDTO = TestSearchRequestDTO.fromGirlsAgentSearchRequestDTO(requestDTO);
        JSONObject entries = lookSkyClient.girlsSearchTestEvaluation(testSearchRequestDTO);
        return ApiResp.ok(entries.getObj("data"));
    }

    @Operation(summary = "daily100 直接请求推荐")
    @PostMapping("/daily100")
    public ApiResp<SearchResponseDTO> daily100(@RequestBody SearchRequestDTO requestDTO) {
        SearchResponseDTO search = searchGrpcClient.search(requestDTO);
        return ApiResp.ok(search);
    }
}
