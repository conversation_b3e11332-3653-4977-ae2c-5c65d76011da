package com.looksky.agents.controller.chat;

import com.looksky.agents.application.chat.ChatBaseService;
import com.looksky.agents.infrastructure.response.ResponseResultBody;
import com.looksky.agents.infrastructure.versioncompat.annotation.VersionCompatController;
import com.looksky.agents.sdk.agent.ext.RequestVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @ClassName ChatController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/28 下午1:43
 * @Version 1.0
 **/
@Tag(name = "对话")
@RequestMapping("/chat")
@ResponseResultBody
@VersionCompatController
@RequiredArgsConstructor
public class ChatController {

    private final ChatBaseService chatBaseService;


    @PostMapping
    public void chat(@RequestBody RequestVo requestVo) {
        chatBaseService.chat(requestVo);
    }


}
