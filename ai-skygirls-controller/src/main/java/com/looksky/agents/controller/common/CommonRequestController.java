package com.looksky.agents.controller.common;


import com.graecove.common.ApiResp;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.logs.Log;
import com.looksky.agents.sdk.common.dto.CommonModelRequestDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "通用能力")
@RequestMapping("/common")
@RestController
@RequiredArgsConstructor
public class CommonRequestController {

    private final CommonRequestService commonRequestService;

    @Log(logResp = true)
    @Operation(description = "执行策略")
    @PostMapping(value = "/strategy", produces = MediaType.APPLICATION_JSON_VALUE)
    @CollectEvent(typeExpression = "#commonModelRequestDTO.strategyName")
    public ApiResp<Object> commonRequestModel(@RequestBody CommonModelRequestDTO commonModelRequestDTO) {
        Object obj = commonRequestService.commonRequestModelWithObj(commonModelRequestDTO);
        return ApiResp.ok(obj);
    }
}
