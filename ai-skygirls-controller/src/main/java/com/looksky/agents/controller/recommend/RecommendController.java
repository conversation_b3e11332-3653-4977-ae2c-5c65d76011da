package com.looksky.agents.controller.recommend;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.common.utils.UserLocationUtils;
import com.looksky.agents.application.chat.recommend.SimilarRecommendService;
import com.looksky.agents.application.colorSeason.ColorSeasonV1Service;
import com.looksky.agents.data.grpc.SearchGrpcClient;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.logs.Log;
import com.looksky.agents.infrastructure.response.ResponseResultBody;
import com.looksky.agents.sdk.recommend.colorseason.request.ColorSeasonRequestDTO;
import com.looksky.agents.sdk.recommend.colorseason.response.ColorSeasonResponseDTO;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import com.looksky.agents.sdk.recommend.search.dto.response.SearchResponseDTO;
import com.looksky.agents.sdk.recommend.similar.dto.request.SimilarRequestDTO;
import com.looksky.agents.sdk.recommend.similar.dto.response.SimilarResponseDTO;
import com.looksky.agents.sdk.utils.date.DateUtils;
import com.skygirls.biz.user.dto.UserLocationInfoDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 推荐服务
 *
 * @since  1.1.0
 * <AUTHOR>
 **/
@Slf4j
@Tag(name = "推荐服务")
@RequestMapping("/recommend")
@ResponseResultBody
@RequiredArgsConstructor
public class RecommendController {
    private final ColorSeasonV1Service colorSeasonV1Service;
    private final SimilarRecommendService similarRecommendService;
    private final SearchGrpcClient searchGrpcClient;

    @Resource
    private HttpServletRequest httpServletRequest;

    @Log
    @Operation(description = "获取 color season 推荐商品")
    @PostMapping("/colorSeason")
    public ColorSeasonResponseDTO daily(@RequestBody ColorSeasonRequestDTO colorSeasonRequestDTO) {

        if (StrUtil.isBlankIfStr(colorSeasonRequestDTO.getDate())) {
            UserLocationInfoDTO location = UserLocationUtils.getLocation(httpServletRequest);
            log.info("cloudfront设置的请求头数据: {}", JSONUtil.toJsonStr(location));
            colorSeasonRequestDTO.setDate(DateUtils.getNowDate(location == null ? null : location.getViewerTimeZone()));
        }

        log.info("color season 请求体: {}", JSONUtil.toJsonStr(colorSeasonRequestDTO));
        // 初始化上下文并记录开始时间
        Context.put("requestStartTime", System.currentTimeMillis());
        try {
            return colorSeasonV1Service.seasonColor(colorSeasonRequestDTO);
        } finally {
            Context.clear();
        }
    }



    @Log
    @Operation(description = "获取相似商品")
    @PostMapping("/similar")
    public SimilarResponseDTO similar(@RequestBody SimilarRequestDTO similarRequestDTO) {
        log.info("similar 请求体: {}", JSONUtil.toJsonStr(similarRequestDTO));
        return similarRecommendService.similarRecommend(similarRequestDTO);
    }


    @Log
    @Operation(description = "直连推荐的 search api")
    @PostMapping("/search")
    public SearchResponseDTO searchApi(@RequestBody SearchRequestDTO request) {
        return searchGrpcClient.search(request);
    }


}
