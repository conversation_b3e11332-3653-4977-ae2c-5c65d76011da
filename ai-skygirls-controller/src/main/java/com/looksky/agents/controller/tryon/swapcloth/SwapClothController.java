package com.looksky.agents.controller.tryon.swapcloth;

import com.graecove.common.ApiResp;
import com.looksky.agents.application.tryon.swapcloth.SwapClothServiceV4;
import com.looksky.agents.application.tryon.swapcloth.background.remove.RemoveBackgroundFacade;
import com.looksky.agents.application.tryon.swapcloth.background.ReplaceBackgroundService;
import com.looksky.agents.application.tryon.swapcloth.changecolor.RunPodServerlessChangeClotheColorServiceV3;
import com.looksky.agents.infrastructure.logs.Log;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothParam;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.SwapClothChangeClothColorRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "换装服务")
@RequestMapping("/swapCloth")
@RestController
@RequiredArgsConstructor
public class SwapClothController {
    private final SwapClothServiceV4 swapClothBaseService;
    private final RemoveBackgroundFacade removeBackgroundFacade;
    private final ReplaceBackgroundService replaceBackgroundService;
    private final RunPodServerlessChangeClotheColorServiceV3 runPodServerlessChangeClotheColorServiceV3;


    @Log
    @Operation(description = "请求换装 - 异步接口")
    @PostMapping()
    public ApiResp<Boolean> swapCloth(@RequestBody SwapClothParam swapClothParam) {
        Boolean result = swapClothBaseService.asyncSwapCloth(swapClothParam);
        return ApiResp.ok(result);
    }

    @Log
    @Operation(description = "请求换装同步接口")
    @PostMapping("/sync")
    public ApiResp<String> swapClothSync(@RequestBody SwapClothParam swapClothParam) {
        String result = swapClothBaseService.create(swapClothParam);
        return ApiResp.ok(result);
    }


    @Log(logResp = true)
    @Operation(description = "去除图片背景")
    @GetMapping("/removeBackground")
    public ApiResp<String> removeBackground(@RequestParam String imageUrl) {
        String result = removeBackgroundFacade.removeBackground(imageUrl);
        return ApiResp.ok(result);
    }

    @Log(logResp = true)
    @Operation(description = "更换图片背景")
    @GetMapping("/replaceBackground")
    public ApiResp<String> replaceBackground(@RequestParam String imageUrl) {
        String result = replaceBackgroundService.replaceBackground(imageUrl);
        return ApiResp.ok(result);
    }


    /**
     * 换色
     * @param request 请求参数
     * @return 换色后的图片地址
     */
    @Log()
    @Operation(description = "colorSeason - 换色")
    @PostMapping("/changeClothColor")
    public ApiResp<String> changeClothColor(@Valid @RequestBody SwapClothChangeClothColorRequest request) {
        String result = runPodServerlessChangeClotheColorServiceV3.changeClothColor(request);
        return ApiResp.ok(result);
    }
}
