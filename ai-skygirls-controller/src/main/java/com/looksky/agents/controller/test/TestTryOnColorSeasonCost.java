package com.looksky.agents.controller.test;

import com.graecove.common.ApiResp;
import com.looksky.agents.application.tryon.colorseason.impl.TryOnColorSeasonServiceV3;
import com.looksky.agents.application.tryon.colorseason.impl.TryOnColorSeasonServiceV4;
import com.looksky.agents.application.tryon.colorseason.impl.TryOnColorSeasonServiceV5;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonParamV1;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 1.1.14
 **/
@Slf4j
@RequestMapping("/experiment")
@RestController
@RequiredArgsConstructor
public class TestTryOnColorSeasonCost {
    private final TryOnColorSeasonServiceV3 tryOnColorSeasonServiceV3;
    private final TryOnColorSeasonServiceV4 tryOnColorSeasonServiceV4;
    private final TryOnColorSeasonServiceV5 tryOnColorSeasonServiceV5;

    @PostMapping("/tryOnColorSeasonCost")
    public ApiResp<String> testTryOnColorSeason(@RequestBody TryOnColorSeasonParamV1 param) {
        return switch (param.getId()) {
            case "v3" -> ApiResp.ok(tryOnColorSeasonServiceV3.tryOnWhiteT(param));
            case "v4" -> ApiResp.ok(tryOnColorSeasonServiceV4.tryOnWhiteT(param));
            case "v5" -> ApiResp.ok(tryOnColorSeasonServiceV5.tryOnWhiteT(param));
            default -> throw new IllegalArgumentException("Unsupported version: " + param.getId());
        };
    }
}
