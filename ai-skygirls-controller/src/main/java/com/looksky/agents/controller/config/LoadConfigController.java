package com.looksky.agents.controller.config;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.common.utils.FeiShuSheetUtil;
import com.looksky.agents.common.utils.PromptMappingUtils;
import com.looksky.agents.common.utils.ResourceUtils;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.infrastructure.response.ResponseResultBody;
import com.looksky.agents.sdk.agent.common.dto.OpeningCacheDTO;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.product.enums.SearchQuestionType;
import com.looksky.agents.sdk.tryon.colorseason.dto.TryOnColorSeasonDescriptionsDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@Slf4j
@Tag(name = "配置加载")
@RequestMapping("/config")
@ResponseResultBody
@RequiredArgsConstructor
public class LoadConfigController {

    @Value("${spring.profiles.active}")
    private String env;

    private final RedissonClient redissonClient;
    private final CacheManager cacheManager;
    private static final HashSet<String> CACHE_NAMES = new HashSet<>(Set.of("event:eventModel", "prompt:macros", "prompt:metadata", "prompt:promptModel"));

    private final PromptMappingUtils promptMappingUtils;

    @Operation(description = "重置 Prompt Mapping 配置")
    @GetMapping("resetPromptConfig")
    public void resetPromptConfig() {
        promptMappingUtils.loadConfigToRedis();
    }

    @Operation(description = "清除本地缓存")
    @GetMapping("clearLocalCache")
    public void clearLocalCache() {
        log.info("开始清除本地缓存");
        Collection<String> cacheNames = cacheManager.getCacheNames();
        for (String cacheName : cacheNames) {
            if (CACHE_NAMES.contains(cacheName)) {
                Cache cache = cacheManager.getCache(cacheName);
                if (cache != null) {
                    cache.clear();
                    log.info("已清除缓存: {}", cacheName);
                }
            }

        }
        log.info("本地缓存清除完成");
    }


    @PostConstruct
    public void load() {
        if ("dev".equals(env)) {
            return;
        }
        loadAdviceQuestion();
        loadAdviceQuestionNextStep();
        commonRequestModel();
        matchTitle();
        opening();
    }


    @Operation(description = "加载建议提问的下一步 prompt")
    @GetMapping("/loadAdviceQuestionNextStep")
    public void loadAdviceQuestionNextStep() {
        List<Map<String, Object>> listMaps = FeiShuSheetUtil.queryFeishuExcelDataAsMap("RG6ZslEjShNwSptrrjOcts7vnSb", "sXUVHk");

        listMaps.forEach(map -> {
            String key = (String) map.get("英文原句（绿色的需要写变量）");
            // 计算 key 的 md5
            String md5Key = DigestUtil.md5Hex(key);

            String temp = (String) map.get("推荐的策略");
            String value;

            if ("推荐单品".equals(temp)) {
                value = SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
            } else {
                value = SearchQuestionType.SUBJECTIVE_WITHOUT_ASK_SOLUTION.getValue();

            }
            redissonClient.getBucket(RedisKeyConstants.adviceQuestionNextStep(md5Key)).set(value);

        });

    }


    @Operation(description = "加载建议的提问")
    @GetMapping("/loadAdviceQuestion")
    public void loadAdviceQuestion() {

        redissonClient.getSet(EventTypeEnum.ADVICE_QUESTIONS.getType() + ":search:dressing:opening").clear();
        redissonClient.getSet(EventTypeEnum.ADVICE_QUESTIONS.getType() + ":search:dressing:season_style").clear();
        redissonClient.getSet(EventTypeEnum.ADVICE_QUESTIONS.getType() + ":search:dressing:occasion_outfit").clear();
        redissonClient.getSet(EventTypeEnum.ADVICE_QUESTIONS.getType() + ":search:dressing:find_by_category").clear();

        List<Map<String, Object>> listMaps = FeiShuSheetUtil.queryFeishuExcelDataAsMap("RG6ZslEjShNwSptrrjOcts7vnSb", "sXUVHk");
        List<String> seasonList = new ArrayList<>();
        List<String> occassionList = new ArrayList<>();
        List<String> categoryList = new ArrayList<>();
        List<String> homeList = new ArrayList<>();

        listMaps.forEach(map -> {
            String keys = (String) map.get("标签分类（最后14个是gpt创造，加了不喜欢什么款式）");
            Object value = map.get("英文原句（绿色的需要写变量）");
            String[] split = keys.split(",");
            for (String key : split) {
                if ("场合页".equals(key)) {
                    occassionList.add((String) value);
                } else if ("季节页".equals(key)) {
                    seasonList.add((String) value);
                } else if ("放首页".equals(key)) {
                    homeList.add((String) value);
                } else if ("品类页".equals(key)) {
                    categoryList.add((String) value);
                }
            }
        });

        redissonClient.getSet(EventTypeEnum.ADVICE_QUESTIONS.getType() + ":search:dressing:opening").addAll(homeList);
        redissonClient.getSet(EventTypeEnum.ADVICE_QUESTIONS.getType() + ":search:dressing:season_style").addAll(seasonList);
        redissonClient.getSet(EventTypeEnum.ADVICE_QUESTIONS.getType() + ":search:dressing:occasion_outfit").addAll(occassionList);
        redissonClient.getSet(EventTypeEnum.ADVICE_QUESTIONS.getType() + ":search:dressing:find_by_category").addAll(categoryList);
    }


    @Operation(description = "tryon descriptions 数据")
    @GetMapping("/tryon-descriptions")
    public void commonRequestModel() {
        String result = ResourceUtils.loadConfig("tryon/colorSeason-descriptions.json");
        redissonClient.getBucket(RedisKeyConstants.TRY_ON_COLOR_SEASON_DESCRIPTION, new TypedJsonJacksonCodec(TryOnColorSeasonDescriptionsDTO.class))
            .set(JSONUtil.toBean(result, TryOnColorSeasonDescriptionsDTO.class));
    }

    @Operation(description = "opening 数据")
    @GetMapping("/opening")
    public void opening() {
        OpeningCacheDTO homeOpening = new OpeningCacheDTO();
        String havaRegisterStr = """
        {user_name}! You totally won this one \uD83C\uDF89
        Saved your friend $49.99 and got your Color Season for free? Iconic.
        Tap to see the shades made for you \uD83D\uDC47✨""";
        homeOpening.setHaveRegisterCodeLock(havaRegisterStr);

        String noRegisterStr = """
        Hi,{user_name}! I’ve got a little surprise for you \uD83C\uDF81
        Your Color Season is unlocked—just to get your style glow-up started \uD83D\uDC96
        Tap to see your best colors\uD83D\uDC47\uD83C\uDF80""";
        homeOpening.setNoRegisterCodeLock(noRegisterStr);
        redissonClient.getBucket("opening:home:home", new TypedJsonJacksonCodec(OpeningCacheDTO.class)).set(homeOpening);


        OpeningCacheDTO vibeScanOpening = new OpeningCacheDTO();
        vibeScanOpening.setHaveRegisterCodeLock("Snap a photo. Let Skylar do a Vibe Scan-outfit, makeup, hair-then give you pro tips to level up.");
        vibeScanOpening.setNoRegisterCodeLock("Snap a photo. Let Skylar do a Vibe Scan-outfit, makeup, hair-then give you pro tips to level up.");
        vibeScanOpening.setUnlock("Today, you’ve got an exclusive chance for Skylar’s Vibe scan—analyzing your outfit, makeup, and hair—with pro tips to level up your style!");
        redissonClient.getBucket("opening:vibe_scan:vibe_scan", new TypedJsonJacksonCodec(OpeningCacheDTO.class)).set(vibeScanOpening);


        String closetStr = "Style rut? Not anymore. Closet Remix is coming—upload a fav & get 3 iconic outfit ideas. 🚀";
        OpeningCacheDTO closetOpening = new OpeningCacheDTO();
        closetOpening.setUnlock(closetStr);
        closetOpening.setHaveRegisterCodeLock(closetStr);
        closetOpening.setNoRegisterCodeLock(closetStr);

        redissonClient.getBucket("opening:search:closet", new TypedJsonJacksonCodec(OpeningCacheDTO.class)).set(closetOpening);
        redissonClient.getBucket("opening:closet:closet", new TypedJsonJacksonCodec(OpeningCacheDTO.class)).set(closetOpening);

        String tryOnStr = "Virtual Try-On’s dropping soon. See yourself in your fav looks–Stay tuned! 👀✨";

        String tryOnStrNotUpload = """
        Hey {user_name}, ready to try on your dream ’fits? Upload a full-body pic in fitted clothes.
        Everything you try auto-saves to your Wishlist.""";

        String tryOnStrUpload = "Hey {user_name}, For the most accurate try-on experience, please upload a clear, full-body photo in fitted attire.";

        String analysisStr = """
        Your 1-minute VIP fitting sesh starts now! 🪐
        We’re deep-diving into your style DNA + stitching every detail (like a digital tailor 🧵) to craft your ~dream fit preview~.
        Pro tip: AI’s 95% lit (thanks to 20k+ real outfit scans!), but collars might do their own TikTok dance. Blame the 1% glitch-core charm 😜
        Check your Try-On in 60 sec—your future flex is baking there! 🧁✨
        P.S. You’re literally beta-testing fashion’s future. NBD. 💅""";

        OpeningCacheDTO tryOnOpeningCache = new OpeningCacheDTO();
        tryOnOpeningCache.setNotUploaded(tryOnStrNotUpload);
        tryOnOpeningCache.setUploaded(tryOnStrUpload);
        tryOnOpeningCache.setAnalysis(analysisStr);
        tryOnOpeningCache.setUnlock(tryOnStr);
        tryOnOpeningCache.setHaveRegisterCodeLock(tryOnStr);
        tryOnOpeningCache.setNoRegisterCodeLock(tryOnStr);
        redissonClient.getBucket("opening:try_on:home", new TypedJsonJacksonCodec(OpeningCacheDTO.class)).set(tryOnOpeningCache);
        redissonClient.getBucket("opening:try_on:tryOn_uploadPhoto", new TypedJsonJacksonCodec(OpeningCacheDTO.class)).set(tryOnOpeningCache);
        redissonClient.getBucket("opening:try_on:tryOn_processing", new TypedJsonJacksonCodec(OpeningCacheDTO.class)).set(tryOnOpeningCache);
        redissonClient.getBucket("opening:try_on:tryOn_detail", new TypedJsonJacksonCodec(OpeningCacheDTO.class)).set(tryOnOpeningCache);



        String homeFirst = """
        Nailed it, {user_name}!
        You snagged your color season. So good 👏. Check it out.""";
        redissonClient.getBucket(RedisKeyConstants.HOME_FIRST_OPENING, StringCodec.INSTANCE).set(homeFirst);

        String homeSecond = """
        Not a Pro yet...
        Find your best ’fits. Try on your faves.
        Tap to Unlock Now.""";
        redissonClient.getBucket(RedisKeyConstants.HOME_SECOND_OPENING, StringCodec.INSTANCE).set(homeSecond);

        String homeUnlock = """
        Hey, I’m Skylar—your AI stylist in your pocket.
        Spot something you love? Tap to try it on.
        Jump into Fit Finder or explore For You.""";
        redissonClient.getBucket(RedisKeyConstants.HOME_UNLOCK_OPENING, StringCodec.INSTANCE).set(homeUnlock);

        String forYouFirstDay = """
        Hi, {user_name}! Scanning 430k+ items from 300+ brands!
        Your unique data + Color Season = chef's kiss 🤌""";
        redissonClient.getBucket(RedisKeyConstants.FOR_YOU_FIRST_DAY_OPENING, StringCodec.INSTANCE).set(forYouFirstDay);


        String forYouSecondDay = """
        Hello again!
        Curating only flawless matches from 327 brands.
        Your It-girl vibes + color season =  perfection😘""";
        redissonClient.getBucket(RedisKeyConstants.FOR_YOU_SECOND_DAY_OPENING, StringCodec.INSTANCE).set(forYouSecondDay);

        String forYouCompletePreferenceSettings = "Preferences saved! Now scanning 430K+ items. From here on out, every item you see will match your settings, your vibe, and your Color Season. 🤌";
        redissonClient.getBucket(RedisKeyConstants.FOR_YOU_COMPLETE_SETTINGS, StringCodec.INSTANCE).set(forYouCompletePreferenceSettings);



    }
    @Operation(description = "最佳匹配主题数据")
    @GetMapping("/matchTitle")
    public void matchTitle() {
        String firstStr = """
        Omg, this is SO you! 💖
        Your style, but better. ✨
        Handpicked just for you. 💕
        A match made in style heaven. 😍
        Your vibe? Nailed it. 🎯
        Your vibe? Hailey would approve. ✨
        This is giving main character energy. 🎬
        If Pinterest made outfits just for you… 💖
        Your wardrobe just found a new BFF. 🛍️
        She’s stylish, she’s cool… she’s YOU. 😉
        """;
        String[] firstSplit = firstStr.split("\n");
        ArrayList<String> firstList = new ArrayList<>(Arrays.asList(firstSplit));
        RList<String> firstRedisList = redissonClient.getList(RedisKeyConstants.forYouMatchThemesFirstKey());
        firstRedisList.clear();
        firstRedisList.addAll(firstList);
        log.info(JSONUtil.toJsonStr(firstList));


        String dailyStr = """
            New drops, same YOU. 💃
            Fresh picks, just for you! 🔥
            Another banger fit? Say less. 😉
            Upgraded just for you. 🚀
            We see your vibe, and we love it. 😘
            A fresh slay, just for you. 🔥
            Updated picks = upgraded wardrobe. 🚀
            You’re evolving. Your style should too. 😉
            Your dream closet is getting real. ✨
            Back again? You’ve got taste. 💅
            Still your vibe, still a slay. 🔥
            We just know you too well. 😉
            Styled for YOU, and only you. 💖
            Your closet is about to level up. 🚀
            We get you. And we love your vibe. 😘
            Another round of 🔥 outfits? Say less.
            The style gods are in your favor. 💖
            Okay, but this fit was made for you. 😉
            If you don’t get this, we might cry. 😭
            You again? We love a loyal queen. 👑
            One step closer to your dream wardrobe. 😉
            You keep coming back, we keep serving looks. 🔥
            More styles, more slays. 💃
            Besties don’t let besties miss a great fit. 💕
            Back again? Your taste is IMMACULATE. ✨
            Your closet is about to be iconic. 😉
            You + these fits = main character energy. 🎬
            """;
        String[] dailySplit = dailyStr.split("\n");
        ArrayList<String> dailyList = new ArrayList<>(Arrays.asList(dailySplit));
        RList<String> dailyRedisList = redissonClient.getList(RedisKeyConstants.forYouMatchThemesDailyKey());
        // 先把这个列表里面的所有元素删除, 再添加
        dailyRedisList.clear();
        dailyRedisList.addAll(dailyList);
        log.info(JSONUtil.toJsonStr(dailyList));

    }
}
