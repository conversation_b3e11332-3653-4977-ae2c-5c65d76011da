package com.looksky.agents.controller.schedule;

import com.looksky.agents.application.schedule.OpenAIMonitoringService;
import com.looksky.agents.infrastructure.response.ResponseResultBody;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * OpenAI监控控制器
 * 提供查询OpenAI请求耗时数据的API接口
 */
@Slf4j
@Tag(name = "OpenAI监控")
@RestController
@RequestMapping("/monitoring/openai")
@ResponseResultBody
@RequiredArgsConstructor
public class OpenAIMonitoringController {

    private final OpenAIMonitoringService openAIMonitoringService;

    /**
     * 获取指定日期的OpenAI请求耗时数据
     * @param date 日期，格式为yyyy-MM-dd，如果为null则获取当天数据
     * @return 耗时数据，key为时间（HH:mm:ss），value为耗时（毫秒）
     */
    @Operation(description = "获取指定日期的OpenAI请求耗时数据")
    @GetMapping("/latency")
    public Map<String, Long> getLatencyData(
            @Parameter(description = "日期，格式为yyyy-MM-dd，不传则获取当天数据")
            @RequestParam(required = false) String date) {
        return openAIMonitoringService.getLatencyData(date);
    }

    /**
     * 获取最近7天的OpenAI请求耗时数据
     * @return 平均耗时数据，key为日期（yyyy-MM-dd），value为平均耗时（毫秒）
     */
    @Operation(description = "获取最近7天的OpenAI请求耗时数据")
    @GetMapping("/7day-latency")
    public Map<String, Map<String, Long>> getLatencyByDay() {
        return openAIMonitoringService.getAverageLatencyByDay();
    }
} 