package com.looksky.agents.controller.evaluate;


import com.looksky.agents.application.evaluate.recommend.ColorSeasonHttpTestAtomComponent;
import com.looksky.agents.application.evaluate.recommend.ForYouHttpTestAtomComponent;
import com.looksky.agents.application.evaluate.search.CallExternalSearchAtomComponent;
import com.looksky.agents.application.evaluate.tryon.GeminiTryOnAtomComponent;
import com.looksky.agents.application.evaluate.tryon.RunPodChangeColorAtomComponent;
import com.looksky.agents.application.evaluate.tryon.RunPodServerlessTryOnAtomComponent;
import com.looksky.agents.application.evaluate.prompt.OptimizePromptService;
import com.looksky.agents.infrastructure.logs.Log;
import com.looksky.agents.infrastructure.response.ResponseResultBody;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import com.looksky.agents.sdk.optimize.dto.OptimizePromptResultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 评估服务
 *
 * @since  1.1.0
 * <AUTHOR>
 **/
@Tag(name = "评估")
@RequestMapping("/run")
@ResponseResultBody
@RequiredArgsConstructor
public class EvaluateController {

    private final ColorSeasonHttpTestAtomComponent colorSeasonHttpTestAtomComponent;
    private final CallExternalSearchAtomComponent callExternalSearchAtomComponent;
    private final ForYouHttpTestAtomComponent forYouHttpTestAtomComponent;
    private final GeminiTryOnAtomComponent geminiTryOnAtomComponent;
    private final RunPodServerlessTryOnAtomComponent runPodServerlessTryOnAtomComponent;
    private final RunPodChangeColorAtomComponent runPodChangeColorAtomComponent;

    @Log
    @Operation(description = "分区推荐商品")
    @PostMapping("/process/{name}")
    public OptimizePromptResultDTO common(@PathVariable String name, @RequestBody DatasetRecordDTO datasetRecord) {
        return matchName(name, datasetRecord);
    }


    private OptimizePromptResultDTO matchName(String name, DatasetRecordDTO datasetRecord) {
        return switch (name) {
            case "colorSeasonHttpTest" -> colorSeasonHttpTestAtomComponent.run(datasetRecord);
            case "callExternalSearch" -> callExternalSearchAtomComponent.run(datasetRecord);
            case "girlsPartitionMatch", "girlsPartitionTry", "girlsPartitionBrand", "girlsPartitionFestival", "girlsPartitionBasic", "girlsPartitionTrend", "girlsPartitionDiscount" ->
                forYouHttpTestAtomComponent.run(datasetRecord, name);
            case "geminiTryOn" -> geminiTryOnAtomComponent.run(datasetRecord);
            case "runPodServerlessTryOn" -> runPodServerlessTryOnAtomComponent.run(datasetRecord);
            case "runPodChangeColor" , "runPodServerlessChangeColor" -> runPodChangeColorAtomComponent.run(datasetRecord, name);
            default -> null;
        };
    }

    private final OptimizePromptService optimizePromptService;

    @Operation(description = "优化/调试 prompt")
    @PostMapping("/optimizePrompt")
    public OptimizePromptResultDTO optimizePrompt(@RequestBody RequestParamBO requestParamBO) {
        return optimizePromptService.optimizePrompt(requestParamBO);
    }

    @Operation(description = "批跑 prompt")
    @PostMapping("/batchRunPrompt/{promptName}")
    public OptimizePromptResultDTO batchRunPrompt(@PathVariable String promptName, @RequestBody DatasetRecordDTO datasetRecord) {
        return optimizePromptService.batchRunPrompt(promptName, datasetRecord);
    }



}