package com.looksky.agents.controller.content;

import com.looksky.agents.application.content.user.avatar.ContentUserAvatarService;
import com.looksky.agents.application.content.user.username.UserNameGenerateService;
import com.looksky.agents.infrastructure.logs.Log;
import com.looksky.agents.infrastructure.response.ResponseResultBody;
import com.looksky.agents.sdk.content.user.avatar.UserAvatarParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 内容平台, 用户相关的服务
 *
 * <AUTHOR>
 * @since 1.1.14
 **/
@Tag(name = "内容 - 用户相关服务")
@RequestMapping("/content/user")
@ResponseResultBody
@RequiredArgsConstructor
public class ContentUserController {
    private final ContentUserAvatarService contentUserAvatarService;
    private final UserNameGenerateService userNameGenerateService;


    @Log(logResp = true)
    @Operation(description = "生成用户头像")
    @PostMapping("/avatar")
    public UserList commonRequestModel(@RequestBody @Valid @Size(max = 50, message = "列表长度不能超过50")  List<UserAvatarParam> params) {
        List<UserAvatarParam> userAvatarParams = contentUserAvatarService.buildUserAvatar(params);
        return new UserList(userAvatarParams);
    }

    public record UserList(List<UserAvatarParam> userList){}


    @Log
    @Operation(description = "生成用户名")
    @PostMapping("/generateUserName")
    public Set<String> generateUserNameToPool() {
        return userNameGenerateService.generateUserNameToPool();
    }

    @Log
    @Operation(description = "随机获取一个用户名")
    @PostMapping("/randomUserName")
    public String randomUserName() {
        return userNameGenerateService.randomUserName();
    }
}
