#!/bin/bash

# 获取当前版本号
image_tag=$(grep 'tag:' helmCharts/helm-prod/values.yaml | awk '{print $2}')
# 提取版本号中的数字部分
version=$(echo $image_tag | grep -o '[0-9.]*')
# 将版本号数字部分分割为数组
IFS='.' read -r -a version_array <<< "$version"
# 将最后一位数字自增
((version_array[${#version_array[@]}-1]++))
# 组装新的版本号
new_version=$(IFS='.'; echo "${version_array[*]}")
echo "previous_version=$version, new_version=$new_version"

# 替换yaml文件中的版本号
perl -pi -e "s/$version/$new_version/g" helmCharts/helm-prod/values.yaml
echo "New image tag: looksky-skygirls-agents-prod-v$new_version"

cd ..
git pull
mvn clean install package -Pprod -Dmaven.test.skip=true -T 10C || exit

docker build --platform=linux/amd64 --tag lookskysvc/micro:looksky-skygirls-agents-prod-v$new_version .
# shellcheck disable=SC2140
docker push lookskysvc/micro:looksky-skygirls-agents-prod-v$new_version
