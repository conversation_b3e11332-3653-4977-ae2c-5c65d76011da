server:
  port: 11462
  servlet:
    context-path: '/skygirls-agents'
  tomcat:
    relaxed-query-chars: '|,{,},[,],^'   # 如果url中包含这些字符，会被tomcat容器接受, 不加这个会报错
  shutdown: graceful  # 开启优雅停机
#  tomcat:
#    connection-timeout: 30s
#    keep-alive-timeout: 60s
#    threads:
#      max: 200
#      min-spare: 10

spring:
#  cloud:
#    compatibility-verifier:
#      enabled: false
  profiles:
    active: dev
#    active: @spring.profiles.active@
  application:
    name: 'looksky-skygirls-agents'
  lifecycle:
    timeout-per-shutdown-phase: 120s  # 设置优雅停机超时时间

  ai:
    bedrock:
      aws:
        region: us-east-1
        access-key: ********************
        secret-key: LvkVYdQDvarPLGJFuC5+tC4DYjSJIXSphdUhAW51
      converse:
        chat:
          options:
            model: us.deepseek.r1-v1:0
    azure:
      openai:
        api-key: Dogxi9bo2EPU3PK4JrqMiuRrrXHIWcKxMtacN336AkJKjTyIpLA0JQQJ99BAACfhMk5XJ3w3AAAAACOGQ64G
#        endpoint: https://admin-m5up6k6a-eastus.cognitiveservices.azure.com
        endpoint: https://admin-m5rs622a-swedencentral.cognitiveservices.azure.com
        chat:
          options:
#            deployment-name: gpt-4o
            deployment-name: gpt-4.1
    retry:
      max-attempts: 2      # 重试1数
      backoff:
        initial-interval: 2000            # 等待时间2秒 (单位是毫秒)
        multiplier: 2                     # 等待间隔倍数为 2
        max-interval: 3000                # 最大等待时间2秒
      on-client-errors: false              # 允许重试客户端错误(4xx)
      on-http-codes: # 设置需要重试的HTTP状态码
#        - 400
#        - 401
#        - 402
#        - 403
#        - 404
#        - 429
        - 500
        - 502
        - 503
        - 504

  jackson:
    time-zone: 'GMT+8'
#    date-format: yyyy-MM-dd HH:mm:ss
    locale: 'zh_CN'
  threads:
    virtual:
      enabled: true


  freemarker:
    charset: 'UTF-8'
    cache: true
    settings:
      number_format: '0.##'
      datetime_format: 'yyyy-MM-dd HH:mm:ss'     # 日期时间格式
      date_format: 'yyyy-MM-dd'                  # 日期格式
      time_format: 'HH:mm:ss'                    # 时间格式
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

looksky:
  signature: '225a44b332f0492bbbc04b4ab5378f42'
  retry:
    daily100-expected-duration: 10000


logging:
  config: 'classpath:logback-spring.xml'
  file:
    path: 'logs'
#  level:
#    org.springframework.ai.bedrock: 'debug'

management:
  endpoints:
    web:
      exposure:
        include: 'health,info,metrics,prometheus'
      base-path: '/actuator'
      path-mapping:
        health: 'health'
  endpoint:
    health:
      show-components: always
      show-details: always
      probes:
        enabled: true
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true

grpc:
  client:
    girlsAgentSearchRecommendService:
      address: recsys-grpc.graecove.svc.cluster.local:9090
      negotiationType: PLAINTEXT
      enableKeepAlive: true
      keepAliveTime: 60s
      keepAliveTimeout: 5s
      keepAliveWithoutCalls: false

    girlsDailyRecommendService:
      address: recsys-grpc.graecove.svc.cluster.local:9090
      negotiationType: PLAINTEXT
      enableKeepAlive: true
      keepAliveTime: 60s
      keepAliveTimeout: 5s
      keepAliveWithoutCalls: false

    girlsAgentSimilarRecommendService:
      address: recsys-grpc.graecove.svc.cluster.local:9090
      negotiationType: PLAINTEXT
      enableKeepAlive: true
      keepAliveTime: 60s
      keepAliveTimeout: 5s
      keepAliveWithoutCalls: false

    girlsColorSeasonRecommendService:
      address: recsys-grpc.graecove.svc.cluster.local:9090
      negotiationType: PLAINTEXT
      enableKeepAlive: true
      keepAliveTime: 60s
      keepAliveTimeout: 5s
      keepAliveWithoutCalls: false

    girlsPartitionDailyRecommendService:
      address: recsys-grpc.graecove.svc.cluster.local:9090
      negotiationType: PLAINTEXT
      enableKeepAlive: true
      keepAliveTime: 60s
      keepAliveTimeout: 5s
      keepAliveWithoutCalls: false


http:
  client:
    connect-timeout: '5s'
    read-timeout: '60s'
    log-requests: true
    log-responses: true
  third-party:
    base-urls:
      looksky-api: 'https://test-api.looksky.ai'
      skygirls-api: 'https://test-api.skygirls.ai'
      open-perplex: 'https://44c57909-d9e2-41cb-9244-9cd4a443cb41.app.bhs.ai.cloud.ovh.net'
      other-service: ''
      py-agent-api: 'http://looksky-skygirls-fashion-agent-rest:8501'
      localhost: 'https://test-api.looksky.ai'  # 辉哥本地地址
      data-hub-api: 'https://test-api.looksky.ai'
      runpod-api: 'https://rest.runpod.io/v1'
      data-collect-api: 'https://test-api.looksky.ai'
    headers:
      open-perplex:
        api-key: '3ScBYqCEUtsuwDHJmCoerS2i7WxsN49cHq26KiSZbU8'
      runpod-api:
        api-key: 'rpa_5CTPO24848NZNJ6CH34RHS1LX98NAAWHWZ7UOVJS1chd92'
      feiShu:
        secret: '6227fd46-4273-4141-a362-2c1764d13a5e'

mybatis-plus:
  global-config:
    banner: false
    db-config:
      id-type: ASSIGN_ID # 默认是这个
      table-prefix: 't_'
      logic-delete-field: 'deleted'
      logic-delete-value: 'true'
  mapper-locations: 'classpath*:/mapper/**/*.xml'
  type-aliases-package: 'com.looksky.agents.*'
  configuration:
    map-underscore-to-camel-case: true

springdoc:
  api-docs:
    enabled: true
    path: '/api-docs'
  swagger-ui:
    path: '/swagger-ui.html'
    tags-sorter: 'alpha'
    operations-sorter: 'alpha'
  packages-to-scan: 'com.looksky.agents.**'
  paths-to-match: '/**'


opensearch:
  aws:
    accessKeyId: ********************
    secretAccessKey: cGermX8c6OAvC373S1rR+hxPuGkRu+eK5vyq1S9w
    endpoint: search-looksky-es-test-pyzf6ondtfyjiiwrxn35v653ra.aos.us-east-1.on.aws
    signingServiceName: es
    region: us-east-1
    connectionTimeout: 6000
    socketTimeout: 6000
    maxConnections: 100

try-on:
  fal:
    apiKeyList:
      - 684ab851-003c-40e6-b26b-e6e1845c7069:1ede052e5b34304fe1fded9305320955 # looksky-test1
  fashn:
    apiKeyList:
      - Bearer fa-MjnZQdX64WSe-ZD1K9HmlGPIcZ6EEbEl6QZzP # looksky1
      - Bearer fa-KAudGSK3aKZk-XbEAPjaEzaJo6wump9AlIdqV # looksky-cw1
      - Bearer fa-7FM5HcCLkRuM-gtwdcx9HOaawFFYjKcBX1nCA # looksky-gg1
  meiTu:
    apiKey: d38147d0ce024b159f2fef5b0973e975
    secretId: 8ebb36d5a4664b8a95cea9e467e9e928
    appId: 357473

trace:
  enabled: true
  serviceName: ${spring.application.name}
  buffer:
    local:
      ringBufferSize: 2048 # Disruptor RingBuffer 大小 (2的幂次方)
      batchSize: 50 # 本地缓冲批处理大小 (发送到Redis的批次)
  mongodb:
    methodInvocationsCollection: "methodInvocations"
    batchWriteSize: 50 # MongoDB批量写入大小emo使用JSON，易于调试
  aspect:
    defaultCaptureParams: true
    defaultCaptureReturnValue: true
