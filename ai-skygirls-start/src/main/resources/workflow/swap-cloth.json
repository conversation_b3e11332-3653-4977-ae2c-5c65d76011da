{"10": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "11": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}, "97": {"inputs": {"provider": "CUDA"}, "class_type": "PulidFluxInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID Flux)"}}, "99": {"inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "class_type": "PulidFluxModelLoader", "_meta": {"title": "Load PuLID Flux Model"}}, "100": {"inputs": {"weight": 0.8500000000000002, "start_at": 0, "end_at": 1, "model": ["167", 0], "pulid_flux": ["99", 0], "eva_clip": ["101", 0], "face_analysis": ["97", 0], "image": ["152", 0]}, "class_type": "ApplyPulidFlux", "_meta": {"title": "Apply PuLID Flux"}}, "101": {"inputs": {}, "class_type": "PulidFluxEvaClipLoader", "_meta": {"title": "Load <PERSON> (PuLID Flux)"}}, "102": {"inputs": {"width": ["117", 1], "height": ["117", 2], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空Latent图像"}}, "112": {"inputs": {"unet_name": "flux1-dev-fp8.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "UNet加载器"}}, "117": {"inputs": {"image": ["131", 0]}, "class_type": "GetImageSizeAndCount", "_meta": {"title": "Get Image Size & Count"}}, "122": {"inputs": {"detect_hand": "enable", "detect_body": "enable", "detect_face": "disable", "resolution": 512, "scale_stick_for_xinsr_cn": "disable", "image": ["131", 0]}, "class_type": "OpenposePreprocessor", "_meta": {"title": "OpenPose Pose"}}, "125": {"inputs": {"strength": 0.85, "start_percent": 0, "end_percent": 0.8000000000000002, "positive": ["134", 0], "negative": ["128", 0], "control_net": ["126", 0], "image": ["122", 0], "vae": ["10", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "应用ControlNet（旧版高级）"}}, "126": {"inputs": {"control_net_name": "FLUX.1-dev-Controlnet-Union-InstantX.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "加载ControlNet模型"}}, "128": {"inputs": {"text": ["140", 0], "clip": ["167", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "129": {"inputs": {"seed": 185649871345961, "steps": 35, "cfg": 2, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["100", 0], "positive": ["125", 0], "negative": ["125", 1], "latent_image": ["102", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "130": {"inputs": {"samples": ["129", 0], "vae": ["10", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "131": {"inputs": {"max_width": 1024, "max_height": 1024, "min_width": 0, "min_height": 0, "crop_if_required": "no", "images": ["151", 0]}, "class_type": "ConstrainImage|pysssss", "_meta": {"title": "Constrain Image 🐍"}}, "134": {"inputs": {"clip_l": "", "t5xxl": ["159", 0], "guidance": 5, "clip": ["167", 1]}, "class_type": "CLIPTextEncodeFlux", "_meta": {"title": "CLIP文本编码Flux"}}, "140": {"inputs": {"string": "blurry, oversaturated colors, modern buildings,  watermarks, distorted proportions, unrealistic lighting ,Imperfect,ugly, deformed, non-standard, poor quality"}, "class_type": "StringConstant", "_meta": {"title": "String Constant"}}, "149": {"inputs": {"image": ["130", 0]}, "class_type": "UploadImage", "_meta": {"title": "UploadImage to looksky"}}, "151": {"inputs": {"url": "${modelImage}"}, "class_type": "DownloadImageFromURL", "_meta": {"title": "clothimage"}}, "152": {"inputs": {"url": "${faceImage}"}, "class_type": "DownloadImageFromURL", "_meta": {"title": "useimage"}}, "158": {"inputs": {"text": ["149", 0], "text2": "https://cdn.lookskyai.com/upload/agent/92247286c0454e5770431761a8fb78d4.jpeg"}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "159": {"inputs": {"delimiter": " ", "clean_whitespace": "false", "text_a": ["160", 0], "text_b": ["161", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "160": {"inputs": {"text": "((Masterpiece, Photo, Best Quality)), Award-winning, 4k, 8k,sharp and detailed,high resolution, Person sheet, slightly upward angle, Visible face, Portrait, This is a masterpiece, Photography.The artwork maintains anatomical accuracy with properly proportioned eyes, faces, and bodies, avoiding deformities, extra limbs, or close-up distortions. It eliminates technical flaws like watermarks, text errors, missing digits, cropping artifacts, and low-resolution issues. The composition features correctly rendered hands and neck anatomy without mutations, additional heads, or out-of-frame elements. Executed in high-quality detail, the piece demonstrates professional craftsmanship free of blurriness or disfigurements."}, "class_type": "Text Multiline", "_meta": {"title": "Text Multiline"}}, "161": {"inputs": {"text": "${imagePrompt}"}, "class_type": "Text Multiline", "_meta": {"title": "Text Multiline"}}, "167": {"inputs": {"lora_name": "Handv2.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["112", 0], "clip": ["11", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "加载LoRA"}}}