<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <!-- 定义变量 ， 文件输出格式  -->
    <springProperty scope="context" name="app.name" source="spring.application.name" defaultValue="app-service"/>
    <!-- 定义变量 ，日志文件输出路径 -->
<!--    <property name="logs.path" valcdue="logs"/>-->
    <springProperty scope="context" name="logs.path" source="logging.file.path" defaultValue="logs"/>

    <conversionRule conversionWord="tid" converterClass="org.apache.skywalking.apm.toolkit.log.logback.v1.x.LogbackPatternConverter"/>

    <property name="CONSOLE_LOG_PATTERN" value="%clr([%tid]){magenta} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(---){faint} %clr([%30.30t]){faint} %clr(%-40.40logger{39}){cyan} %clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
    <property name="FILE_LOG_PATTERN" value="[%tid] ${LOG_LEVEL_PATTERN:-%5p} --- [%t] %-40.40logger{39} %d{yyyy-MM-dd HH:mm:ss.SSS} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

    <!-- 输出到控制台 -->
    <appender name="DEV_CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--错误日志单独输出-->
    <appender name="FILE-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logs.path}/${app.name}-error.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${logs.path}/${app.name}-error.%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>10MB</maxFileSize>
        </triggeringPolicy>
        <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 按照每天和固定大小(5MB)生成日志文件 -->
    <appender name="FILE-INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logs.path}/${app.name}.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logs.path}/${app.name}_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--日志文件保留天数-->
            <MaxHistory>7</MaxHistory>
            <!--日志文件最大的大小-->
            <MaxFileSize>20MB</MaxFileSize>
        </rollingPolicy>
        <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- gRPC 日志采集 -->
    <logger name="io.grpc" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE-INFO"/>
    </logger>

    <!-- 生产环境和测试环境-->
    <springProfile name="prod,test">
        <root level="info">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE-ERROR"/>
            <appender-ref ref="FILE-INFO"/>
        </root>
        <logger name="com.looksky" level="DEBUG"/>
    </springProfile>

    <!-- 开发环境 -->
    <springProfile name="dev">
        <root level="info">
            <appender-ref ref="DEV_CONSOLE"/>
            <appender-ref ref="FILE-ERROR"/>
            <appender-ref ref="FILE-INFO"/>
        </root>
        <logger name="com.looksky" level="DEBUG"/>
    </springProfile>
</configuration>