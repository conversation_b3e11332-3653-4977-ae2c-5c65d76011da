spring:
  ai:
    openai:
      api-key: ********************************************************************************************************************************************************************
      chat:
        api-key: ********************************************************************************************************************************************************************

  data:
    redis:
      host: 'master.looksky-business-redis.zszi3w.use1.cache.amazonaws.com'
      port: 6379
      password: 'YfHl8FcsePY4LdFB'
      database: 6
      lettuce:
        pool:
          max-active: 8  # 降低最大连接数
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
      ssl:
        enabled: true

    mongodb:
      host: 'a172f759ba2834f2bb1fa60af8e2bb9a-727069250.us-east-1.elb.amazonaws.com'
      port: 27017
      username: 'admin'
      password: 'Lj5RjnryZsIZbjTX'
      database: 'looksky-agents'

  datasource:
    url: '***************************************************************************************************************************************************************************'
    username: 'looksky-skygirls-agents'
    password: 'yuFhwXVTYV5yASKL'
    hikari:
      maximum-pool-size: 10      # 添加连接池大小配置
      minimum-idle: 5            # 添加最小空闲连接数
      max-lifetime: 60000        # 保持现有配置
      connection-timeout: 30000  # 添加连接超时配置


  freemarker:
    cache: true
    settings:
      classic_compatible: true
      template_exception_handler: ignore

http:
  client:
    log-requests: true
    log-responses: true

  third-party:
    base-urls:
      looksky-api: 'http://graecove-business-rest.graecove.svc.cluster.local:11180'
      skygirls-api: 'http://looksky-skygirls.graecove.svc.cluster.local:13181'
      open-perplex: 'https://44c57909-d9e2-41cb-9244-9cd4a443cb41.app.bhs.ai.cloud.ovh.net'
      other-service: 'http://other-service.example.com'
      py-agent-api: 'http://looksky-skygirls-fashion-agent-rest.graecove.svc.cluster.local:8501'
      data-hub-api: 'http://looksky-datahub.graecove.svc.cluster.local:11280'
      localhost: 'http://*************:8112'  # 辉哥本地地址
      data-collect-api: 'http://looksky-data-rest.graecove.svc.cluster.local:80'
    #      localhost: 'http://**************:8112'  # GPU 服务器地址
    headers:
      open-perplex:
        api-key: '3ScBYqCEUtsuwDHJmCoerS2i7WxsN49cHq26KiSZbU8'
      feiShu:
        secret: 'b2f14623-a082-41a3-96a3-0d841657b8b5'

logging:
  level:
    root: info
    com.looksky: debug
    org.springframework: info


opensearch:
  aws:
    accessKeyId: ********************
    secretAccessKey: cGermX8c6OAvC373S1rR+hxPuGkRu+eK5vyq1S9w
    endpoint: search-looksky-agent-s4cersomv5pa6fvstzcb5u5fsq.us-east-1.es.amazonaws.com
    signingServiceName: es
    region: us-east-1
    connectionTimeout: 6000
    socketTimeout: 6000
    maxConnections: 100


try-on:
  fal:
    apiKeyList:
#      - 26bd4a4d-e3a8-43f8-bb5e-421380a6c180:0ecb95ee615922f1f6a49a2242044169 # skygrils_online
      - ee50aaec-1050-4ece-9856-30341c6bfe1b:0f23201ab11d27ca8f055d1f76ab6105 # looksky1
      - 398dd79e-4ef1-4573-a1e8-9849697950d6:528cc2f2ec395f181ac42cb2d2dd9ff0 # looksky-cw-github
  fashn:
    apiKeyList:
      - Bearer fa-MjnZQdX64WSe-ZD1K9HmlGPIcZ6EEbEl6QZzP # looksky1
      - Bearer fa-KAudGSK3aKZk-XbEAPjaEzaJo6wump9AlIdqV # looksky-cw1
      - Bearer fa-7FM5HcCLkRuM-gtwdcx9HOaawFFYjKcBX1nCA # looksky-gg1

trace:
  enabled: true