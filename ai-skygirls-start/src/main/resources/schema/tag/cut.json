{
  "title": "CutPreference",
  "description": "User's preferences for cuts",
  "type": "object",
  "properties": {
    "cut": {
      "title": "Cut Preferences",
      "description": "User's preferences for cut types",
      "type": "object",
      "properties": {
        "like": {
          "description": "cut_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/CutEnum"
          }
        },
        "dislike": {
          "description": "cut_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/CutEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "CutEnum": {
      "title": "CutEnum",
      "description": "Available cut types",
      "enum": ${tagService.getEnumValues(current.category, "cut")}
    }
  },
  "additionalProperties": false,
  "required": ["cut"]
}