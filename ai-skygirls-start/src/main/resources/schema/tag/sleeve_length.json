{
    "title": "SleeveLengthPreference",
    "type": "object",
    "properties": {
        "like": {
            "description": "sleeve_length_like",
            "type": "array",
            "items": {
                "$ref": "#/definitions/SleeveLengthEnum"
            }
        },
        "dislike": {
            "description": "sleeve_length_dislike",
            "type": "array",
            "items": {
                "$ref": "#/definitions/SleeveLengthEnum"
            }
        }
    },
    "definitions": {
        "SleeveLengthEnum": {
            "title": "SleeveLengthEnum",
            "description": "An enumeration.",
            "enum": ${tagService.getEnumValues(current.category, "sleeve_length")}
        }
    },
    "additionalProperties": false,
    "required": [
        "like",
        "dislike"
    ]
}