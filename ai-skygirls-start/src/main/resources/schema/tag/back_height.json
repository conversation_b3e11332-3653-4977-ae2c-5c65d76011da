{
  "title": "BackPreference",
  "description": "User's preferences for back features",
  "type": "object",
  "properties": {
    "back_height": {
      "title": "Back Height Preferences",
      "description": "User's preferences for back height",
      "type": "object",
      "properties": {
        "like": {
          "title": "Like",
          "description": "Like back heights",
          "type": "array",
          "items": {
            "$ref": "#/definitions/BackHeightEnum"
          }
        },
        "dislike": {
          "title": "Dislike",
          "description": "Disliked back heights",
          "type": "array",
          "items": {
            "$ref": "#/definitions/BackHeightEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "BackHeightEnum": {
      "title": "Back Height Types",
      "description": "Available types of back heights",
      "enum": ${tagService.getEnumValues(current.category, "back_height")}
    }
  },
  "additionalProperties": false,
  "required": ["back_height"]
}