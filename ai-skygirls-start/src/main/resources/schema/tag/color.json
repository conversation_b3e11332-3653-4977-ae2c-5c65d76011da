{
  "title": "ColorPreference",
  "description": "User's preferences for colors",
  "type": "object",
  "properties": {
    "color": {
      "title": "Color Preferences",
      "description": "User's preferences for color types",
      "type": "object",
      "properties": {
        "like": {
          "description": "color_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/ColorEnum"
          }
        },
        "dislike": {
          "description": "color_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/ColorEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "ColorEnum": {
      "title": "ColorEnum",
      "description": "Available color types",
      "enum": ${tagService.getEnumValues(current.category, "color")}
    }
  },
  "additionalProperties": false,
  "required": ["color"]
}