{
  "title": "WaistlinePreference",
  "type": "object",
  "properties": {
    "like": {
      "description": "waistline_like",
      "type": "array",
      "items": {
        "$ref": "#/definitions/WaistlineEnum"
      }
    },
    "dislike": {
      "description": "waistline_dislike",
      "type": "array",
      "items": {
        "$ref": "#/definitions/WaistlineEnum"
      }
    }
  },
  "definitions": {
    "WaistlineEnum": {
      "title": "WaistlineEnum",
      "description": "An enumeration.",
      "enum": ${tagService.getEnumValues(current.category, "waistline")}
    }
  },
  "additionalProperties": false,
  "required": [
    "like",
    "dislike"
  ]
}