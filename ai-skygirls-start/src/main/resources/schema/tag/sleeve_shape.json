{
  "title": "SleeveShapePreference",
  "description": "User's preferences for sleeve shapes",
  "type": "object",
  "properties": {
    "sleeve_shape": {
      "title": "Sleeve Shape Preferences",
      "description": "User's preferences for different sleeve shapes",
      "type": "object",
      "properties": {
        "like": {
          "description": "sleeve_shape_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/SleeveShapeEnum"
          }
        },
        "dislike": {
          "description": "sleeve_shape_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/SleeveShapeEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "SleeveShapeEnum": {
      "title": "SleeveShapeEnum",
      "description": "Available sleeve shape options",
      "enum": ${tagService.getEnumValues(current.category, "sleeve_shape")}
    }
  },
  "additionalProperties": false,
  "required": ["sleeve_shape"]
}