{
    "title": "FunctionPreference",
    "type": "object",
    "properties": {
        "like": {
            "description": "function_like",
            "type": "array",
            "items": {
                "$ref": "#/definitions/FunctionEnum"
            }
        },
        "dislike": {
            "description": "function_dislike",
            "type": "array",
            "items": {
                "$ref": "#/definitions/FunctionEnum"
            }
        }
    },
    "definitions": {
        "FunctionEnum": {
            "title": "FunctionEnum",
            "description": "An enumeration.",
            "enum": ${tagService.getEnumValues(current.category, "function")}
        }
    },
    "additionalProperties": false,
    "required": [
        "like",
        "dislike"
    ]
}