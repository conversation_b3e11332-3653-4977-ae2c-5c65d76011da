{
  "title": "CuffPreference",
  "description": "User's preferences for cuffs",
  "type": "object",
  "properties": {
    "cuff": {
      "title": "Cuff Preferences",
      "description": "User's preferences for cuff types",
      "type": "object",
      "properties": {
        "like": {
          "description": "cuff_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/cuff"
          }
        },
        "dislike": {
          "description": "cuff_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/cuff"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "cuff": {
      "title": "cuff",
      "description": "Available cuff types",
      "enum": ${tagService.getEnumValues(current.category, "cuff")}
  },
  "additionalProperties": false,
  "required": ["cuff"]
}