{
  "title": "PocketPreference",
  "description": "User's preferences for pockets",
  "type": "object",
  "properties": {
    "pocket": {
      "title": "Pocket Preferences",
      "description": "User's preferences for pocket types and details",
      "type": "object",
      "properties": {
        "like": {
          "title": "Like",
          "description": "pocket_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/Pocket"
          }
        },
        "dislike": {
          "title": "Dislike",
          "description": "pocket_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/Pocket"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "PocketTypeEnum": {
      "title": "PocketTypeEnum",
      "description": "Available pocket types",
      "enum": ${tagService.getEnumValues(current.category, "pocket_type")}
    },
    "PocketShapeEnum": {
      "title": "PocketShapeEnum",
      "description": "Available pocket shapes",
      "enum": ${tagService.getEnumValues(current.category, "pocket_shape")}
    },
    "PocketPositionEnum": {
      "title": "PocketPositionEnum",
      "description": "Available pocket positions",
      "enum": ${tagService.getEnumValues(current.category, "pocket_position")}
    },
    "Pocket": {
      "title": "Pocket",
      "type": "object",
      "properties": {
        "pocket_type": {
          "$ref": "#/definitions/PocketTypeEnum"
        },
        "pocket_shape": {
          "$ref": "#/definitions/PocketShapeEnum"
        },
        "pocket_position": {
          "$ref": "#/definitions/PocketPositionEnum"
        }
      },
      "additionalProperties": false,
      "required": [
        "pocket_type",
        "pocket_shape",
        "pocket_position"
      ]
    }
  },
  "additionalProperties": false,
  "required": ["pocket"]
}