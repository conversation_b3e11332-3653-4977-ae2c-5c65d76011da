{
  "title": "WaistFitPreference",
  "type": "object",
  "properties": {
    "like": {
      "description": "waist_fit_like",
      "type": "array",
      "items": {
        "$ref": "#/definitions/WaistFitEnum"
      }
    },
    "dislike": {
      "description": "waist_fit_dislike",
      "type": "array",
      "items": {
        "$ref": "#/definitions/WaistFitEnum"
      }
    }
  },
  "definitions": {
    "WaistFitEnum": {
      "title": "WaistFitEnum",
      "description": "An enumeration.",
      "enum": ${tagService.getEnumValues(current.category, "waist_fit")}
    }
  },
  "additionalProperties": false,
  "required": [
    "like",
    "dislike"
  ]
}