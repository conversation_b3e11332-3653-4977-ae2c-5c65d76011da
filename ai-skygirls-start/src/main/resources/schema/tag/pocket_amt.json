{
  "title": "PocketAmountPreference",
  "description": "User's preferences for pocket amounts",
  "type": "object",
  "properties": {
    "pocket_amt": {
      "title": "Pocket Amount Preferences",
      "description": "User's preferences for number of pockets",
      "type": "object",
      "properties": {
        "like": {
          "description": "pocket_amount_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/PocketAmtEnum"
          }
        },
        "dislike": {
          "description": "pocket_amount_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/PocketAmtEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "PocketAmtEnum": {
      "title": "PocketAmtEnum",
      "description": "Available pocket amount options",
      "enum": ${tagService.getEnumValues(current.category, "pocket_amt")}
    }
  },
  "additionalProperties": false,
  "required": ["pocket_amt"]
}