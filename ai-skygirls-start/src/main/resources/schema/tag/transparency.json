{
  "title": "TransparencyPreference",
  "description": "User's preferences for garment transparency",
  "type": "object",
  "properties": {
    "transparency": {
      "title": "Transparency Preferences",
      "description": "User's preferences for clothing transparency levels",
      "type": "object",
      "properties": {
        "like": {
          "description": "transparency_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/TransparencyEnum"
          }
        },
        "dislike": {
          "description": "transparency_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/TransparencyEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "TransparencyEnum": {
      "title": "TransparencyEnum",
      "description": "Available transparency levels",
      "enum": ${tagService.getEnumValues(current.category, "transparency")}
    }
  },
  "additionalProperties": false,
  "required": ["transparency"]
}