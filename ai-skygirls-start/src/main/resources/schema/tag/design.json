{
  "title": "DesighDetailPreference",
  "description": "User's preferences for design details",
  "type": "object",
  "properties": {
    "design": {
      "title": "Design Detail Preferences",
      "description": "User's preferences for design details and positions",
      "type": "object",
      "properties": {
        "like": {
          "title": "Like",
          "description": "design_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/DesignDetail"
          }
        },
        "dislike": {
          "title": "Dislike",
          "description": "design_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/DesignDetail"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "DesignDetailTypeEnum": {
      "title": "DesignDetailTypeEnum",
      "description": "Available design detail types",
      "enum": ${tagService.getEnumValues(current.category, "design_detail_type")}
    },
    "DesignPositionEnum": {
      "title": "DesignPositionEnum",
      "description": "Available design positions",
      "enum": ${tagService.getEnumValues(current.category, "design_detail_position")}
    },
    "DesignDetail": {
      "title": "DesignDetail",
      "type": "object",
      "properties": {
        "design_detail_type": {
          "$ref": "#/definitions/DesignDetailTypeEnum"
        },
        "design_detail_position": {
          "$ref": "#/definitions/DesignPositionEnum"
        }
      },
      "additionalProperties": false,
      "required": [
        "design_detail_type",
        "design_detail_position"
      ]
    }
  },
  "additionalProperties": false,
  "required": ["design"]
}