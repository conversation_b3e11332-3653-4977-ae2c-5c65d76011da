{
    "title": "LengthPreference",
    "type": "object",
    "properties": {
        "like": {
            "description": "length_like",
            "type": "array",
            "items": {
                "$ref": "#/definitions/LengthEnum"
            }
        },
        "dislike": {
            "description": "length_dislike",
            "type": "array",
            "items": {
                "$ref": "#/definitions/LengthEnum"
            }
        }
    },
    "definitions": {
        "LengthEnum": {
            "title": "LengthEnum",
            "description": "An enumeration.",
            "enum": ${tagService.getEnumValues(current.category, "length")}
        }
    },
    "additionalProperties": false,
    "required": [
        "like",
        "dislike"
    ]
}