{
  "title": "ClosurePreference",
  "description": "User's preferences for closures",
  "type": "object",
  "properties": {
    "closure": {
      "title": "Closure Preferences",
      "description": "User's preferences for closure types and positions",
      "type": "object",
      "properties": {
        "like": {
          "title": "Like",
          "description": "closure_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/Closure"
          }
        },
        "dislike": {
          "title": "Dislike",
          "description": "closure_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/Closure"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "ClosureTypeEnum": {
      "title": "ClosureTypeEnum",
      "description": "Available closure types",
      "enum": ${tagService.getEnumValues(current.category, "closure_type")}
    },
    "ClosurePositionEnum": {
      "title": "ClosurePositionEnum",
      "description": "Available closure positions",
      "enum": ${tagService.getEnumValues(current.category, "closure_position")}
    },
    "Closure": {
      "title": "Closure",
      "type": "object",
      "properties": {
        "closure_type": {
          "$ref": "#/definitions/ClosureTypeEnum"
        },
        "closure_position": {
          "$ref": "#/definitions/ClosurePositionEnum"
        }
      },
      "additionalProperties": false,
      "required": [
        "closure_type",
        "closure_position"
      ]
    }
  },
  "additionalProperties": false,
  "required": ["closure"]
}