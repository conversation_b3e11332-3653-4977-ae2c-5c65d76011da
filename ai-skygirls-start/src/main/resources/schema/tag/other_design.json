{
  "title": "OtherDesignPreference",
  "description": "User's preferences for other design features",
  "type": "object",
  "properties": {
    "other_design": {
      "title": "Other Design Preferences",
      "description": "User's preferences for additional design features",
      "type": "object",
      "properties": {
        "like": {
          "description": "other_design_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/OtherDesignEnum"
          }
        },
        "dislike": {
          "description": "other_design_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/OtherDesignEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "OtherDesignEnum": {
      "title": "OtherDesignEnum",
      "description": "Available other design features",
      "enum": ${tagService.getEnumValues(current.category, "other_design")}
    }
  },
  "additionalProperties": false,
  "required": ["other_design"]
}