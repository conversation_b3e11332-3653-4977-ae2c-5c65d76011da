{
    "title": "CarePreference",
    "type": "object",
    "properties": {
        "like": {
            "description": "care_instruction_like",
            "type": "array",
            "items": {
                "$ref": "#/definitions/CareEnum"
            }
        },
        "dislike": {
            "description": "care_instruction_dislike",
            "type": "array",
            "items": {
                "$ref": "#/definitions/CareEnum"
            }
        }
    },
    "definitions": {
        "CareEnum": {
            "title": "CareEnum",
            "description": "An enumeration.",
            "enum": ${tagService.getEnumValues(current.category, "care_instruction")}
        }
    },
    "additionalProperties": false,
    "required": [
        "like",
        "dislike"
    ]
}