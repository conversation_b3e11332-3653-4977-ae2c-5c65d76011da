{
    "title": "FabriclPreference",
    "type": "object",
    "properties": {
        "like": {
            "description": "fabric_like",
            "type": "array",
            "items": {
                "$ref": "#/definitions/FabricEnum"
            }
        },
        "dislike": {
            "description": "fabric_dislike",
            "type": "array",
            "items": {
                "$ref": "#/definitions/FabricEnum"
            }
        }
    },
    "definitions": {
        "FabricEnum": {
            "title": "FabricEnum",
            "description": "An enumeration.",
            "enum": ${tagService.getFabricEnum()}
        }
    },
    "additionalProperties": false,
    "required": [
        "like",
        "dislike"
    ]
}