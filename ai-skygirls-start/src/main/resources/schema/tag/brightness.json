{
    "title": "BrightnessPreference",
    "type": "object",
    "properties": {
        "like": {
            "description": "brightness_like",
            "type": "array",
            "items": {
                "$ref": "#/definitions/BrightnessEnum"
            }
        },
        "dislike": {
            "description": "brightness_dislike",
            "type": "array",
            "items": {
                "$ref": "#/definitions/BrightnessEnum"
            }
        }
    },
    "definitions": {
        "BrightnessEnum": {
            "title": "BrightnessEnum",
            "description": "An enumeration.",
            "enum": ${tagService.getEnumValues(current.category, "brightness")}
        }
    },
    "additionalProperties": false,
    "required": [
        "like",
        "dislike"
    ]
}