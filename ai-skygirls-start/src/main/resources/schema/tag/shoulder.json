{
  "title": "ShoulderPreference",
  "description": "User's preferences for shoulder styles",
  "type": "object",
  "properties": {
    "shoulder": {
      "title": "Shoulder Preferences",
      "description": "User's preferences for shoulder designs",
      "type": "object",
      "properties": {
        "like": {
          "description": "shoulder_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/ShoulderEnum"
          }
        },
        "dislike": {
          "description": "shoulder_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/ShoulderEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "ShoulderEnum": {
      "title": "ShoulderEnum",
      "description": "Available shoulder styles",
      "enum": ${tagService.getEnumValues(current.category, "shoulder")}
    }
  },
  "additionalProperties": false,
  "required": ["shoulder"]
}