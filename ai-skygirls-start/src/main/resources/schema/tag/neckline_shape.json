{
  "title": "NecklineShapePreference",
  "description": "User's preferences for neckline shapes",
  "type": "object",
  "properties": {
    "like": {
      "title": "Like",
      "description": "neckline_shape_like",
      "type": "array",
      "items": {
        "$ref": "#/definitions/NecklineShapeEnum"
      }
    },
    "dislike": {
      "title": "Dislike",
      "description": "neckline_shape_dislike",
      "type": "array",
      "items": {
        "$ref": "#/definitions/NecklineShapeEnum"
      }
    }
  },
  "definitions": {
    "NecklineShapeEnum": {
      "title": "NecklineShapeEnum",
      "description": "Available neckline shape types",
      "enum": ${tagService.getEnumValues(current.category, "neckline_shape")}
    }
  },
  "additionalProperties": false,
  "required": ["like", "dislike"]
}