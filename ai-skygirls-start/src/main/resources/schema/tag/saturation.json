{
    "title": "SaturationPreference",
    "type": "object",
    "properties": {
        "like": {
            "description": "saturation_like",
            "type": "array",
            "items": {
                "$ref": "#/definitions/SaturationEnum"
            }
        },
        "dislike": {
            "description": "saturation_dislike",
            "type": "array",
            "items": {
                "$ref": "#/definitions/SaturationEnum"
            }
        }
    },
    "definitions": {
        "SaturationEnum": {
            "title": "SaturationEnum",
            "description": "An enumeration.",
            "enum": ${tagService.getEnumValues(current.category, "saturation")}
        }
    },
    "additionalProperties": false,
    "required": [
        "like",
        "dislike"
    ]
}