{
  "title": "ElasticityPreference",
  "description": "User's preferences for elasticity",
  "type": "object",
  "properties": {
    "elasticity": {
      "title": "Elasticity Preferences",
      "description": "User's preferences for elasticity levels",
      "type": "object",
      "properties": {
        "like": {
          "description": "elasticity_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/ElasticityEnum"
          }
        },
        "dislike": {
          "description": "elasticity_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/ElasticityEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "ElasticityEnum": {
      "title": "ElasticityEnum",
      "description": "Available elasticity levels",
      "enum": ${tagService.getEnumValues(current.category, "elasticity")}
    }
  },
  "additionalProperties": false,
  "required": ["elasticity"]
}