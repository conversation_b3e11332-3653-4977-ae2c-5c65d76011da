{"description": "User's price preferences, including maximum, minimum, and preferred price points.", "additionalProperties": false, "title": "pricePreferences", "type": "object", "properties": {"high": {"title": "High", "description": "The highest price the user is willing to pay.", "type": "number"}, "low": {"title": "Low", "description": "The lowest price the user is willing to consider.", "type": "number"}, "norm": {"title": "Norm", "description": "The typical or desired price, falling between 'low' and 'high'.", "type": "number"}}, "required": ["high", "low", "norm"]}