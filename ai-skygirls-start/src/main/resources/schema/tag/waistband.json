{
  "title": "WaistBandPreference",
  "description": "User's preferences for waistband styles",
  "type": "object",
  "properties": {
    "waistband": {
      "title": "Waistband Preferences",
      "description": "User's preferences for different waistband types",
      "type": "object",
      "properties": {
        "like": {
          "description": "waistband_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/WaistBandEnum"
          }
        },
        "dislike": {
          "description": "waistband_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/WaistBandEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "WaistBandEnum": {
      "title": "WaistBandEnum",
      "description": "Available waistband types",
      "enum": ${tagService.getEnumValues(tagInfo.current, "waistband")}
    }
  },
  "additionalProperties": false,
  "required": ["waistband"]
}