{
  "title": "BackPreference",
  "description": "User's preferences for back features",
  "type": "object",
  "properties": {
    "back_detail": {
      "title": "Back Detail Preferences",
      "description": "User's preferences for back details",
      "type": "object",
      "properties": {
        "like": {
          "title": "Like",
          "description": "Like back details",
          "type": "array",
          "items": {
            "$ref": "#/definitions/BackDetailEnum"
          }
        },
        "dislike": {
          "title": "Dislike",
          "description": "Disliked back details",
          "type": "array",
          "items": {
            "$ref": "#/definitions/BackDetailEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "BackDetailEnum": {
      "title": "Back Detail Types",
      "description": "Available types of back details",
      "enum": ${tagService.getEnumValues(current.category, "back_detail")}
    }
  },
  "additionalProperties": false,
  "required": ["back_detail"]
}