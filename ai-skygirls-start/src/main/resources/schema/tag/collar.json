{
  "title": "CollarPreference",
  "description": "User's preferences for collars",
  "type": "object",
  "properties": {
    "collar": {
      "title": "Collar Preferences",
      "description": "User's preferences for collar types",
      "type": "object",
      "properties": {
        "like": {
          "description": "collar_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/CollarEnum"
          }
        },
        "dislike": {
          "description": "collar_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/CollarEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "CollarEnum": {
      "title": "CollarEnum",
      "description": "Available collar types",
      "enum": ${tagService.getEnumValues(current.category, "collar")}
    }
  },
  "additionalProperties": false,
  "required": ["collar"]
}