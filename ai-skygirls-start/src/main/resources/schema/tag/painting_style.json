{
  "title": "PaintingStylePreference",
  "description": "User's preferences for painting styles",
  "type": "object",
  "properties": {
    "painting_style": {
      "title": "Painting Style Preferences",
      "description": "User's preferences for painting styles",
      "type": "object",
      "properties": {
        "like": {
          "description": "painting_style_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/PaintingStyleEnum"
          }
        },
        "dislike": {
          "description": "painting_style_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/PaintingStyleEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "PaintingStyleEnum": {
      "title": "PaintingStyleEnum",
      "description": "Available painting styles",
      "enum":  ${tagService.getEnumValues(current.category, "painting_style")}
    }
  },
  "additionalProperties": false,
  "required": ["painting_style"]
}