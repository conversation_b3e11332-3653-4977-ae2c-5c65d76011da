{
  "title": "MaterialPreference",
  "description": "User's preferences for clothing materials",
  "type": "object",
  "properties": {
    "material": {
      "title": "Material Preferences",
      "description": "User's preferences for different fabric materials",
      "type": "object",
      "properties": {
        "like": {
          "description": "material_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/MaterialEnum"
          }
        },
        "dislike": {
          "description": "material_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/MaterialEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "MaterialEnum": {
      "title": "MaterialEnum",
      "description": "Available material types",
      "enum": ${tagService.getEnumValues(current.category, "material")}
    }
  },
  "additionalProperties": false,
  "required": ["material"]
}