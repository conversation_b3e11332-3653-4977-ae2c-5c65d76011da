{"title": "SizePreference", "description": "User's preferences for clothing sizes", "type": "object", "properties": {"size": {"title": "Size Preferences", "description": "User's size preferences including type, alphabet and number codes", "type": "object", "properties": {"size_type": {"description": "size_type", "type": "array", "items": {"$ref": "#/definitions/SizeTypeEnum"}}, "alphabet_code": {"description": "alphabet_code", "type": "array", "items": {"$ref": "#/definitions/NormalSizeType"}}, "number_code": {"title": "Number Code", "description": "number_code", "type": "array", "items": {"type": "integer"}}}, "additionalProperties": false, "required": ["size_type", "alphabet_code", "number_code"]}}, "definitions": {"SizeTypeEnum": {"title": "SizeTypeEnum", "description": "Available size types", "enum": ["plus_size", "petite_size", "standard_size"]}, "NormalSizeType": {"title": "NormalSizeType", "description": "Standard alphabet size codes", "enum": ["XXS", "XS", "S", "M", "L", "XL", "2XL", "3XL", "4XL"]}}, "additionalProperties": false, "required": ["size"]}