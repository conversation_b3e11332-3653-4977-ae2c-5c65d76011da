{
    "title": "ProcessPreference",
    "type": "object",
    "properties": {
        "like": {
            "description": "process_like",
            "type": "array",
            "items": {
                "$ref": "#/definitions/ProcessEnum"
            }
        },
        "dislike": {
            "description": "process_dislike",
            "type": "array",
            "items": {
                "$ref": "#/definitions/ProcessEnum"
            }
        }
    },
    "definitions": {
        "ProcessEnum": {
            "title": "ProcessEnum",
            "description": "An enumeration.",
            "enum": ${tagService.getEnumValues(current.category, "process")}
        }
    },
    "additionalProperties": false,
    "required": [
        "like",
        "dislike"
    ]
}