{
    "title": "RisePreference",
    "type": "object",
    "properties": {
        "like": {
            "description": "rise_like",
            "type": "array",
            "items": {
                "$ref": "#/definitions/RiseEnum"
            }
        },
        "dislike": {
            "description": "rise_dislike",
            "type": "array",
            "items": {
                "$ref": "#/definitions/RiseEnum"
            }
        }
    },
    "definitions": {
        "RiseEnum": {
            "title": "RiseEnum",
            "description": "An enumeration.",
            "enum": ${tagService.getEnumValues(current.category, "rise")}
        }
    },
    "additionalProperties": false,
    "required": [
        "like",
        "dislike"
    ]
}