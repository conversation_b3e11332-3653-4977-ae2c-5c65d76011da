{
  "title": "BackPreference",
  "description": "User's preferences for back features",
  "type": "object",
  "properties": {
    "back_shape": {
      "title": "Back Shape Preferences",
      "description": "User's preferences for back shape",
      "type": "object",
      "properties": {
        "like": {
          "title": "Like",
          "description": "Like back shapes",
          "type": "array",
          "items": {
            "$ref": "#/definitions/BackShapeEnum"
          }
        },
        "dislike": {
          "title": "Dislike",
          "description": "Disliked back shapes",
          "type": "array",
          "items": {
            "$ref": "#/definitions/BackShapeEnum"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "BackShapeEnum": {
      "title": "Back Shape Types",
      "description": "Available types of back shapes",
      "enum": ${tagService.getEnumValues(current.category, "back_shape")}
    }
  },
  "additionalProperties": false,
  "required": ["back_shape"]
}