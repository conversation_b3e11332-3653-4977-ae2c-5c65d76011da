{
  "title": "NecklineDetailPreference",
  "description": "User's preferences for neckline details",
  "type": "object",
  "properties": {
    "like": {
      "title": "Like",
      "description": "neckline_detail_like",
      "type": "array",
      "items": {
        "$ref": "#/definitions/NecklineDetailEnum"
      }
    },
    "dislike": {
      "title": "Dislike",
      "description": "neckline_detail_dislike",
      "type": "array",
      "items": {
        "$ref": "#/definitions/NecklineDetailEnum"
      }
    }
  },
  "definitions": {
    "NecklineDetailEnum": {
      "title": "NecklineDetailEnum",
      "description": "Available neckline detail types",
      "enum": ${tagService.getEnumValues(current.category, "neckline_detail")}
    }
  },
  "additionalProperties": false,
  "required": ["like", "dislike"]
}