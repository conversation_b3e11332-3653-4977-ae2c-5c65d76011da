{
  "title": "TrimPreference",
  "description": "User's preferences for garment trims",
  "type": "object",
  "properties": {
    "trim": {
      "title": "Trim Preferences",
      "description": "User's preferences for clothing trims and their positions",
      "type": "object",
      "properties": {
        "like": {
          "title": "Like",
          "description": "trims_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/Trims"
          }
        },
        "dislike": {
          "title": "Dislike",
          "description": "trims_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/Trims"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "TrimTypeEnum": {
      "title": "TrimTypeEnum",
      "description": "Available trim types",
      "enum": ${tagService.getEnumValues(current.category, "trims_type")}
    },
    "TrimPositionEnum": {
      "title": "TrimPositionEnum",
      "description": "Available trim positions",
      "enum": ${tagService.getEnumValues(current.category, "trims_position")}
    },
    "Trims": {
      "title": "Trims",
      "type": "object",
      "properties": {
        "trims_type": {
          "$ref": "#/definitions/TrimTypeEnum"
        },
        "trims_position": {
          "$ref": "#/definitions/TrimPositionEnum"
        }
      },
      "additionalProperties": false,
      "required": [
        "trims_type",
        "trims_position"
      ]
    }
  },
  "additionalProperties": false,
  "required": ["trim"]
}