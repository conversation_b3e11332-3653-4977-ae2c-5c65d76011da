{
    "title": "FitPreference",
    "type": "object",
    "properties": {
        "like": {
            "description": "fit_like",
            "type": "array",
            "items": {
                "$ref": "#/definitions/FitEnum"
            }
        },
        "dislike": {
            "description": "fit_dislike",
            "type": "array",
            "items": {
                "$ref": "#/definitions/FitEnum"
            }
        }
    },
    "definitions": {
        "FitEnum": {
            "title": "FitEnum",
            "description": "An enumeration.",
            "enum": ${tagService.getEnumValues(current.category, "fit")}
        }
    },
    "additionalProperties": false,
    "required": [
        "like",
        "dislike"
    ]
}