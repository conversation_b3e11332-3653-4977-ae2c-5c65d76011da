{
  "title": "PatternPreference",
  "description": "User's preferences for patterns",
  "type": "object",
  "properties": {
    "pattern": {
      "title": "Pattern Preferences",
      "description": "User's preferences for pattern types and details",
      "type": "object",
      "properties": {
        "like": {
          "title": "Like",
          "description": "pattern_like",
          "type": "array",
          "items": {
            "$ref": "#/definitions/Pattern"
          }
        },
        "dislike": {
          "title": "Dislike",
          "description": "pattern_dislike",
          "type": "array",
          "items": {
            "$ref": "#/definitions/Pattern"
          }
        }
      },
      "required": ["like", "dislike"],
      "additionalProperties": false
    }
  },
  "definitions": {
    "PatternTypeEnum": {
      "title": "PatternTypeEnum",
      "description": "Available pattern types",
      "enum": ${tagService.getEnumValues(current.category, "pattern_type")}
    },
    "PatternSpecificTypeEnum": {
      "title": "PatternSpecificTypeEnum",
      "description": "Specific pattern types",
      "enum": ${tagService.getEnumValues(current.category, "specific_pattern_type")}
    },
    "PatternScaleEnum": {
      "title": "PatternScaleEnum",
      "description": "Pattern scale options",
      "enum": ${tagService.getEnumValues(current.category, "specific_pattern_scale")}
    },
    "PatternArrangmentEnum": {
      "title": "PatternArrangmentEnum",
      "description": "Pattern arrangement options",
      "enum": ${tagService.getEnumValues(current.category, "specific_pattern_arrangement")}
    },
    "Pattern": {
      "title": "Pattern",
      "type": "object",
      "properties": {
        "pattern_type": {
          "$ref": "#/definitions/PatternTypeEnum"
        },
        "specific_pattern_type": {
          "$ref": "#/definitions/PatternSpecificTypeEnum"
        },
        "specific_pattern_scale": {
          "$ref": "#/definitions/PatternScaleEnum"
        },
        "specific_pattern_arrangement": {
          "$ref": "#/definitions/PatternArrangmentEnum"
        }
      },
      "additionalProperties": false,
      "required": [
        "pattern_type",
        "specific_pattern_type",
        "specific_pattern_scale",
        "specific_pattern_arrangement"
      ]
    }
  },
  "additionalProperties": false,
  "required": ["pattern"]
}