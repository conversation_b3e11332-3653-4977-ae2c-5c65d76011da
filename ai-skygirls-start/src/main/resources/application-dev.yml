spring:
  ai:
    openai:
      api-key: ********************************************************
#      api-key: sk-tasbqqxsgdoqmlgewtjiiycughvvhrkchfffgqzcbmcyjbxo
#      base-url: https://api.siliconflow.cn
#    azure:
#      openai:
#        api-key: Dogxi9bo2EPU3PK4JrqMiuRrrXHIWcKxMtacN336AkJKjTyIpLA0JQQJ99BAACfhMk5XJ3w3AAAAACOGQ64G
#        endpoint: https://admin-m5rs622a-swedencentral.cognitiveservices.azure.com
#        chat:
#          options:
#            deployment-name: gpt-4o
#        endpoint: https://DeepSeek-R1-zuvkx.eastus.models.ai.azure.com

#            deployment-name: gpt-4o-2024-08-06

#        custom-headers:
#          Authorization: twNwgcxSITjsLdnHc6eta7DLXFObvHHt
  #      chat:
#        options:
#          top-p: 1
#          presence-penalty: 0
#          frequency-penalty: 0
#          max-tokens: 128000
#          max-completion-tokens: 128000
#    chat:
#      client:
#        observations:
#          include-input: true
#        enabled: true
#      observations:
#        include-prompt: true
#        include-completion: true
#        include-error-logging: true
  data:
    redis:
      host: 'a7006e0784cd34716a82b957c8a2a0f6-84714602.us-east-1.elb.amazonaws.com'
#      host: '**************'
      port: 6379
      password: 'LsyNmEhfwYFW0xaL'
      database: 6
      lettuce:
        pool:
          max-active: 1  # 降低最大连接数
          max-idle: 1
          min-idle: 0
          max-wait: -1ms
    mongodb:
      host: 'a112cf0732d7f4b29bad82420feaba1f-17464087.us-east-1.elb.amazonaws.com'
      port: 27017
      username: 'root'
      password: 'Lj5RjnryZsIZbjTX'
      database: 'looksky-agents'
      authentication-database: admin  # 添加认证数据库配置

  datasource:
    url: '*****************************************************************************************************************************************************'
    username: 'looksky-skygirls-agents'
    password: 'KsjGsRGCrd53xI8i'
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 5      # 添加连接池大小配置
      minimum-idle: 2            # 添加最小空闲连接数
      max-lifetime: 60000        # 保持现有配置
      connection-timeout: 30000  # 添加连接超时配置

  freemarker:
    charset: UTF-8
    cache: false
    settings:
      classic_compatible: false
      template_exception_handler: rethrow
#      template_exception_handler: ignore
#      template_exception_handler: debug
grpc:
  client:
    girlsAgentSearchRecommendService:
      address: aa4aa94b705574148ab39c9518dcf3e0-807415741.us-east-1.elb.amazonaws.com:9090
      negotiationType: PLAINTEXT
    girlsDailyRecommendService:
      address: aa4aa94b705574148ab39c9518dcf3e0-807415741.us-east-1.elb.amazonaws.com:9090
      negotiationType: PLAINTEXT
    girlsAgentSimilarRecommendService:
      address: aa4aa94b705574148ab39c9518dcf3e0-807415741.us-east-1.elb.amazonaws.com:9090
      negotiationType: PLAINTEXT
    girlsColorSeasonRecommendService:
      address: aa4aa94b705574148ab39c9518dcf3e0-807415741.us-east-1.elb.amazonaws.com:9090
#      address: static://**************:9090
      negotiationType: PLAINTEXT
    girlsPartitionDailyRecommendService:
      address: aa4aa94b705574148ab39c9518dcf3e0-807415741.us-east-1.elb.amazonaws.com:9090
#      address: static://**************:9090
      negotiationType: PLAINTEXT
http:
  client:
    connect-timeout: '5s'
    read-timeout: '60s'
    log-requests: true
    log-responses: true

  third-party:
    base-urls:
      py-agent-api: 'http://localhost:8500'

looksky:
  retry:
    daily100-expected-duration: 10000
logging:
#  config: 'classpath:logback-spring-dev.xml'
  level:
    root: info
    com.looksky: debug
    org.springframework: info
    com.azure.core.http.policy.HttpLoggingPolicy: DEBUG
  file:
    path: '${user.home}/logs/skygirls'


#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
