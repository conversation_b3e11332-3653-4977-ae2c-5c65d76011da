spring:
  ai:
    openai:
      api-key: ********************************************************************************************************************************************************************

  data:
    redis:
      host: 'a7006e0784cd34716a82b957c8a2a0f6-84714602.us-east-1.elb.amazonaws.com'
#      host: '**************'
      port: 6379
      password: 'LsyNmEhfwYFW0xaL'
      database: 6
      lettuce:
        pool:
          max-active: 8  # 降低最大连接数
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
    mongodb:
      host: 'a112cf0732d7f4b29bad82420feaba1f-17464087.us-east-1.elb.amazonaws.com'
      port: 27017
      username: 'root'
      password: 'Lj5RjnryZsIZbjTX'
      database: 'looksky-agents'
      authentication-database: admin  # 添加认证数据库配置

  datasource:
    url: 'jdbc:mysql://************:3306/looksky_ai_agents?useSSL=false&useUnicode=true&characterEncoding=utf-8&autoReconnect=true'
    username: 'looksky-skygirls-agents'
    password: 'KsjGsRGCrd53xI8i'
    hikari:
      maximum-pool-size: 10      # 添加连接池大小配置
      minimum-idle: 5            # 添加最小空闲连接数
      max-lifetime: 60000        # 保持现有配置
      connection-timeout: 30000  # 添加连接超时配置

  freemarker:
    charset: UTF-8
    cache: true
    settings:
      classic_compatible: false
#      template_exception_handler: ignore
      template_exception_handler: rethrow
#      template_exception_handler: debug
opensearch:
  aws:
    socketTimeout: 60000
looksky:
  retry:
    daily100-expected-duration: 10000
logging:
  level:
    root: info
    com.looksky: debug
    org.springframework: info
http:
  client:
    log-requests: true
    log-responses: true
  third-party:
    base-urls:
      looksky-api: 'https://test-api.looksky.ai'
      skygirls-api: 'https://test-api.skygirls.ai'
      open-perplex: 'https://44c57909-d9e2-41cb-9244-9cd4a443cb41.app.bhs.ai.cloud.ovh.net'
      py-agent-api: 'http://looksky-skygirls-fashion-agent-rest:8501'
      localhost: 'https://test-api.looksky.ai'  # 辉哥本地地址
    #      localhost: 'http://**************:8112'  # GPU 服务器地址
    headers:
      open-perplex:
        api-key: '*******************************************'
      feiShu:
        secret: '77dfb377-74fa-4987-90d7-47561af3e6d0'
