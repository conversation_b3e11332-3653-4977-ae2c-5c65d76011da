package com.looksky.agents.start;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

// , "com.graecove.*"
@EnableRetry
@EnableScheduling
@ComponentScan(basePackages = {"com.looksky.*"})
@MapperScan("com.looksky.agents.data.mysql.**.mapper")
@SpringBootApplication
@EnableAspectJAutoProxy
@EnableConfigurationProperties
public class LookSkyApplication {
    public static void main(String[] args) {
//        SpringApplication.run(LookSkyApplication.class, args);
        ApplicationContext ctx = SpringApplication.run(LookSkyApplication.class, args);
//        System.out.println("所有bean列表:");
//        for (String name : ctx.getBeanDefinitionNames()) {
//            System.out.println(name);
//        }
    }

}