package com.looksky.agents.recomReason;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.recomReason.ForYouRecomReasonService;
import com.looksky.agents.sdk.recommend.reason.dto.RecommendReasonParam;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = LookSkyApplication.class)
class TestForYouReason {
    @Resource
    private ForYouRecomReasonService forYouRecomReasonService;

    @Test
    void test1() {
        String param = "{\"userId\":\"1869951191400550400\",\"season\":\"winter\",\"city\":\"Ashburn\",\"itemIds\":[\"1768532684894760961\"],\"batchSize\":1,\"scene\":\"girlsPartitionBasic\"}";
        RecommendReasonParam req = JSONUtil.toBean(param, RecommendReasonParam.class);
        forYouRecomReasonService.buildRecomReasonByForYou(req.getUserId(), req.getSeason(), req.getCity(), req.getItemIds(), req.getBatchSize(), req.getScene());
    }
}
