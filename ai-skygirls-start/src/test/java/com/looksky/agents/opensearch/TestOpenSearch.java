package com.looksky.agents.opensearch;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.template.FreemarkerService;
import com.looksky.agents.data.client.service.GirlsDataService;
import com.looksky.agents.data.opensearch.SkcSearchService;
import com.looksky.agents.sdk.product.ProductInfo;
import com.looksky.agents.start.LookSkyApplication;
import freemarker.template.TemplateException;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class TestOpenSearch {

    @Resource
    private SkcSearchService skcSearchService;

    @Resource
    private FreemarkerService freemarkerService;

    @Resource
    private GirlsDataService girlsDataService;

    @Test
    void test() throws IOException {
        List<ProductInfo> osSkcModels = skcSearchService.searchSkc(List.of("1723950332180787202"));
        System.out.println(JSONUtil.toJsonStr(osSkcModels));
    }

    @Test
    void test2() {
        List<String> allBrand = skcSearchService.findAllBrand();
        log.info("所有品牌: {}", JSONUtil.toJsonStr(allBrand));
    }

    @Test
    void test3() throws TemplateException, IOException {
        String prompt = """
            <@productTitlesAndDescriptions/>
            
            ===============================
            
            <@productsNumber/>
            
            ===============================
            
            <@productImages/>
            
            ===============================
            
            <@productTagValues "color"/>
            """;

        ProductInfo productInfo = girlsDataService.getProductInfo("1723950332180787202");

        String result = freemarkerService.parseTemplate(prompt, MapUtil.empty());

        log.info(result);

    }

}
