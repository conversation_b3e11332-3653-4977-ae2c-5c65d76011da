package com.looksky.agents.common;

import cn.hutool.json.JSONUtil;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class WeightConversionTest {

    @Test
    public void testWeightConversion() {
        List<String> weights = Arrays.asList(
                "63 kg","121 lbs, 55 kg","50 kg or 110 lbs","123.5 lbs, 56 kg","121 lbs, 55 kg","121 lbs (Approx.), 55 kg","128 lbs, 58 kg","117 lbs, 53 kg","51 kg or 112.5 lbs","55 kg or 121 lbs","137 lbs, 62 kg","56 kg or 123.5 lbs","128 lbs, 58 kg","50 kg","121 lbs, 55 kg","117 lbs, 53 kg","130 lbs, 59 kg","115 lbs, 52 kg","119 lbs, 54 kg","125.6 lbs, 57 kg","115 lbs, 52 kg","150 lbs, 68 kg","58 kg (128 lbs)","128 lbs, 58 kg","60 kg","52 kg (114 lbs)","115 lbs, 52 kg","108 lbs, 49 kg","121 lbs, 55 kg","54 kg","115 lbs, 50 kg","130 lbs, 59 kg","119 lbs, 54 kg","115 lbs, 52 kg","123.5 lbs, 56 kg","45 kg","45 kg","48 kg","48 kg or 106 lbs","47 kg","62 kg or 136.5 lbs","55 kg","50","58 kg","57 kg","51 kg","54 kg","55 kg or 121 lbs","54 kg or 119 lbs","64 kg","56 kg or 123 lbs","55 kg","66 kg or 145.5 lbs","60 kg","58 kg","54 kg","55 kg or 121 lbs","52 kg or 114.5 lbs","53 kg","67 kg","51 kg or 112.5 lbs","52 kg","56 kg or 123 lbs","50 kg","50 kg","50 kg","49 kg","58 kg or 128 lbs","47 kg","59 kg","49 kg or 108 lbs","53 kg","53 kg or 117 lbs","50 kg or 110 lbs","52 kg or 114.5 lbs","52 kg or 114.5 lbs","56 kg or 123.5 lbs","56 kg","58 kg","60 kg","34 kg or 75 lbs","53 kg or 117 lbs","64 kg or 141 lbs","57 kg","54 kg","58 kg","59 kg","58 kg","64 kg or 141 lbs","66 kg or 145.5 lbs","49 kg or 108 lbs","128 lbs, 58 kg","128 lbs, 58 kg","56 kg or 123.5 lbs","56 kg or 123.5 lbs","56 kg","115 lbs, 50 kg","106 lbs, 48 kg","117 lbs, 53 kg","132 lbs, 60 kg","119 lbs, 54 kg","55 kg","125.6 lbs, 57 kg","106 lbs, 48 kg","128 lbs, 58 kg","125.6 lbs, 57 kg","141 lbs, 64 kg","54 kg","50 kg","121 lbs, 55 kg","50 kg","50 kg","115 lbs, 52 kg","50 kg","54 kg","58 kg","58 kg","54 kg","48 kg","58 kg","85 kg","48 kg","52 kg","54 kg","48 kg","62 kg","54 kg","75 kg","117 lbs, 53 kg","66 kg","106 lbs, 48 kg","50 kg","58 kg","58 kg","57 kg","58 kg","64 kg","52 kg","57 kg","54 kg","50 kg","55 kg","56 kg","53 kg","121 lbs, 55 kg","123.5 lbs, 56 kg","56 kg","59 kg","56 kg","60 kg","59 kg","49 kg","61 kg","121 lbs, 55 kg","46 kg","52 kg","119 lbs, 54 kg","117 lbs, 53 kg","105 lbs, 48 kg","55 kg","123.5 lbs, 56 kg","65 kg","51 kg","112½ lbs, 51 kg","50 kg","58 kg","60 kg","112 lbs, 51 kg","117 lbs, 53 kg","58 kg","55 kg","68 kg","61 kg","121 lbs, 55 kg","75 kg","57 kg","115 lbs, 52 kg","66 kg","51 kg","55 kg","45 kg","130 lbs, 59 kg","123.5 lbs, 56 kg","50 kg or 110 lbs","117 lbs, 53 kg","121 lbs, 55 kg","115 lbs, 52 kg","143 lbs, 65 kg","125.6 lbs, 57 kg","47 kg","58 kg","121 lbs, 55 kg","117 lbs, 53 kg","110 lbs, 50 kg","119 lbs, 54 kg","121 lbs, 55 kg","121 lbs, 55 kg","145 lbs, 66 kg","123.5 lbs, 56 kg","130 lbs, 59 kg","55 kg","66 kg","121 lbs, 55 kg","140 lbs, 64 kg","53 kg","121 lbs, 55 kg","128 lbs, 58 kg","141 lbs, 64 kg","115 lbs, 52 kg","55 kg","125.6 lbs, 57 kg","105 lbs, 48 kg","141 lbs, 64 kg","50 kg","141 lbs, 64 kg","130 lbs, 59 kg","52 kg","57 kg","123.5 lbs, 56 kg","61 kg or 134.5 lbs","121 lbs, 55 kg","130 lbs, 59 kg","58 kg or 128 lbs","123 lbs, 56 kg","110 lbs, 50 kg","121 lbs, 55 kg","128 lbs, 58 kg","128 lbs, 58 kg","51 kg or 112.5 lbs","110 lbs, 50 kg","115 lbs, 52 kg","52 kg or 114.5 lbs","50 kg or 110 lbs","57 kg or 125.5 lbs","119 lbs, 54 kg","123.5 lbs, 56 kg","139 lbs, 63 kg","54 kg or 119 lbs","112 lbs, 51 kg","130 lbs, 59 kg","121 lbs, 55 kg","57 kg or 125.5 lbs","62 kg or 136.5 lbs","119 lbs, 54 kg","58 kg or 128 lbs","117 lbs, 53 kg","128 lbs, 58 kg","51 kg or 112.5 lbs","57 kg or 125.5 lbs","55 kg or 121 lbs","119 lbs, 54 kg","121 lbs, 55 kg","123.5 lbs, 56 kg","53 kg or 117 lbs"
        );

        for (String weight : weights) {
            double kilograms = parseWeightToKilograms(weight);
            System.out.printf("%s => %.2f kg%n", weight, kilograms);
        }
    }

    private double parseWeightToKilograms(String weight) {
        // 如果直接包含千克值，优先使用
        Pattern kgPattern = Pattern.compile("(\\d+(?:\\.\\d+)?)\\s*kg");
        Matcher kgMatcher = kgPattern.matcher(weight);
        if (kgMatcher.find()) {
            return Math.round(Double.parseDouble(kgMatcher.group(1)) * 100) / 100.0;
        }

        // 处理纯数字（假设为kg）
        Pattern pureNumberPattern = Pattern.compile("^(\\d+(?:\\.\\d+)?)$");
        Matcher pureNumberMatcher = pureNumberPattern.matcher(weight.trim());
        if (pureNumberMatcher.find()) {
            return Math.round(Double.parseDouble(pureNumberMatcher.group(1)) * 100) / 100.0;
        }

        // 处理磅值
        Pattern lbsPattern = Pattern.compile("(\\d+(?:\\.\\d+)?)\\s*(?:lbs|lb|pounds)");
        Matcher lbsMatcher = lbsPattern.matcher(weight);
        if (lbsMatcher.find()) {
            double pounds = Double.parseDouble(lbsMatcher.group(1));
            return Math.round(pounds * 0.45359237 * 100) / 100.0; // 转换为千克并保留两位小数
        }

        throw new IllegalArgumentException("无法解析的体重格式: " + weight);
    }

    @Test
    public void testSingleValue() {
        String[] testCases = {
            "63 kg",
            "121 lbs, 55 kg",
            "50 kg or 110 lbs",
            "123.5 lbs, 56 kg",
            "121 lbs (Approx.), 55 kg",
            "50",
            "112½ lbs, 51 kg"
        };

        for (String testCase : testCases) {
            double kilograms = parseWeightToKilograms(testCase);
            System.out.printf("原始数据: %-25s => %.2f 千克%n", testCase, kilograms);
        }
    }

    @Test
    public void testSpecialCases() {
        // 测试一些特殊情况
        String[] specialCases = {
            "112½ lbs, 51 kg",  // 带分数的磅值
            "50",               // 纯数字
            "121 lbs (Approx.)", // 带括号的近似值
            "50 kg or 110 lbs"   // 带or的多个值
        };

        for (String specialCase : specialCases) {
            try {
                double kilograms = parseWeightToKilograms(specialCase);
                System.out.printf("特殊情况: %-25s => %.2f kg%n", specialCase, kilograms);
            } catch (Exception e) {
                System.out.printf("处理失败: %s, 错误: %s%n", specialCase, e.getMessage());
            }
        }
    }

    @Test
    public void test() {
        System.out.println(JSONUtil.toJsonStr("你好"));
    }
} 