package com.looksky.agents.common;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.common.utils.EnvUtil;
import com.looksky.agents.start.LookSkyApplication;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 1.1.14
 **/
@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class TestEnvUtil {
    @Test
    void test1() {
        Map<String, String> allEnv = EnvUtil.getAllEnv();
        log.info("所有的环境变量: {}", JSONUtil.toJsonStr(allEnv));
    }
}
