package com.looksky.agents.common;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class HeightConversionTest {

    @Test
    public void testHeightConversion() {
        List<String> heights = Arrays.asList(
                "5 ft 10 in","5′ 9″, 1.75 m","5 ft 5 in or 165 cm","5′ 6½”, 1.69 m","5′ 5″, 1.65 m","5′ 7″, 1.70 m","5′ 9″, 1.75 m","5′ 4″, 1.63 m","5 ft 3½ in or 161 cm","5 ft 7 in or 170 cm","5′ 8″, 1.73 m","5 ft 5 in or 165 cm","5′ 4″, 1.63 m","5 ft 3 in","5′ 5½”, 1.66 m","5′ 9½”, 1.77 m","5′ 8″, 1.73 m","5′ 2″, 1.57 m","5′ 5″, 1.65 m","5′ 6″, 1.68 m","5′ 3½”, 1.61 m","5′ 3″, 1.60 m","5 ft 3¾ in (1.62 m)","5′ 8″, 1.73 m","5 ft 4 in","5 ft 7 in (1.70 m)","5′ 1″, 1.55 m","5′ 7″, 1.70 m","5′ 5″, 1.65 m","5 ft 6 in","5′ 5″, 1.65 m","5′ 7″, 1.70 m","5′ 7″, 1.70 m","5′ 5″, 1.65 m","5′ 9″, 1.75 m","5 ft 2 in","5 ft 1 in","5 ft 1 in","5 ft 5½ in or 166.5 cm","5 ft 6 in","5 ft 5 in or 165 cm","5 ft 4 in","4 ft 0 in","5 ft 7 in","5 ft 8 in","5 ft 4 in","5 ft 6 in","5 ft 6½ in or 169 cm","5 ft 6 in or 167.5 cm","6 ft 1 in","5 ft 7 in or 170 cm","5 ft 8 in","5 ft 11 in or 180.5 cm","5 ft 9 in","5 ft 10 in","5 ft 5¼ in","5 ft 3 in or 160 cm","5 ft 5 in or 165 cm","5 ft 3 in","6 ft 1 in","5 ft 5 in or 165 cm","5 ft 9 in","5 ft 6 in or 168 cm","5 ft 3 in","5 ft 5 in","5 ft 5 in","5 ft 3 in","5 ft 6 in or 167.5 cm","5 ft 3 in","5 ft 7 in","5 ft 7 in or 170 cm","5 ft 4 in","5 ft 5 in or 165 cm","5 ft 6¼ in or 168 cm","5 ft 6 in or 167.5 cm","4 ft 11¾ in or 151.5 cm","5 ft 5 in or 165 cm","5 ft 3 in","5 ft 6 in","5 ft 6 in","4 ft 7 in or 139.5 cm","5 ft 2 in or 157.5 cm","5 ft 7 in or 170 cm","5 ft 8 in","5 ft 6 in","5 ft 7 in","5 ft 7 in","5 ft 8 in","6 ft 0 in or 183 cm","5 ft 6 in or 167.5 cm","5 ft 3¾ in or 162 cm","5′ 10″, 1.78 m","5′ 5″, 1.65 m","5 ft 7 in or 170 cm","5 ft 4 in or 162.5 cm","5 ft 2 in","5′ 2″, 1.57 m","5′ 1″, 1.55 m","5′ 4″, 1.63 m","5′ 6½”, 1.69 m","5′ 3″, 1.60 m","5 ft 3 in","5′ 3¾”, 1.62 m","5′ 4″, 1.63 m","5′ 7¾”, 1.72 m","5′ 9″, 1.75 m","5′ 9½”, 1.77 m","5 ft 5 in","5 ft 2 in","5′ 6″, 1.68 m","5 ft 5 in","5 ft 7 in","5′ 3″, 1.60 m","5 ft 6 in","5 ft 7 in","5 ft 5 in","5 ft 7 in","5 ft 7 in","5 ft 5 in","5 ft 7 in","5 ft 6 in","5 ft 4 in","5 ft 1 in","5 ft 5 in","5 ft 2 in","5 ft 6 in","5 ft 6 in","5 ft 6 in","5′ 7″, 1.70 m","5 ft 4 in","5′ 1¾”, 1.57 m","5 ft 2.5 in","5 ft 8 in","5 ft 8 in","5 ft 6 in","5 ft 7 in","5 ft 7 in","5 ft 7 in","5 ft 6 in","5 ft 5 in","5 ft 1 in","5 ft 7 in","5 ft 4 in","5 ft 7¾ in","5′ 5″, 1.65 m","5′ 7″, 1.70 m","5 ft 4 in","5 ft 8 in","5 ft 7 in","5 ft 5 in","5 ft 8.5 in","5 ft 2 in","5 ft 6 in","5′ 8″, 1.73 m","4 ft 11 in","5 ft 0 in","5′ 5″, 1.65 m","5′ 2″, 1.57 m","5′ 3½”, 1.61 m","5 ft 3 in","5′ 7″, 1.70 m","5 ft 4 in","4 ft 11 in","5′ 4″, 1.63 m","5 ft 5 in","5 ft 6 in","5 ft 5 in","5′ 5″, 1.65 m","5′ 4¼”, 1.63 m","5 ft 9 in","5 ft 7 in","5 ft 8 in","5 ft 8 in","5′ 5¾”, 1.67 m","5 ft 9 in","5 ft 3 in","5′ 1″, 1.55 m","6 ft 0 in","4 ft 11 in","5 ft 4 in","4 ft 10 in","5′ 8″, 1.73 m","5′ 9″, 1.75 m","5 ft 4 in or 162.5 cm","5′ 5″, 1.65 m","5′ 5″, 1.65 m","5 ft 4 in, 1.63 m","5 ft 10 in, 1.77 m","5′ 8″, 1.73 m","5 ft 2¼ in","5 ft 8 in","5′ 10″, 1.78 m","5′ 5¾”, 1.67 m","5′ 5″, 1.65 m","5′ 3″, 1.60 m","5′ 7¾”, 1.72 m","5′ 5″, 1.65 m","5′ 7″, 1.70 m","5′ 7″, 1.70 m","5′ 8″, 1.73 m","5 ft 5 in","5 ft 9 in","5′ 2″, 1.57 m","5′ 9″, 1.75 m","5 ft 5 in","5′ 8½”, 1.74 m","5′ 8″, 1.73 m","6 ft 0 in, 1.82 m","5′ 3″, 1.60 m","5 ft 5 in","5′ 5½”, 1.66 m","5′ 7″, 1.70 m","5′ 10″, 1.78 m","5 ft 3 in","6′ 0″, 1.83 m","5′ 6½”, 1.69 m","5 ft 4 in","5 ft 6 in","5′ 7¾”, 1.72 m","5 ft 6 in or 167.5 cm","5′ 6″, 1.68 m","5′ 5″, 1.65 m","5 ft 5 in or 165 cm","5’4″, 1.63 m","5′ 5″, 1.65 m","5′ 3½”, 1.61 m","5′ 7″, 1.70 m","5′ 5¾”, 1.67 m","5 ft 3 in or 160 cm","5′ 3″, 1.60 m","5′ 3″, 1.60 m","5 ft 5 in or 165 cm","5 ft 6 in or 167.5 cm","5 ft 8½ in or 174 cm","5′ 8½”, 1.74 m","5′ 8″, 1.73 m","5′ 9″, 1.75 m","5 ft 2 in or 157.5 cm","5′ 5″, 1.65 m","5′ 10½”, 1.79 m","5′ 4″, 1.63 m","5 ft 9 in or 175 cm","5 ft 4 in or 162.5 cm","5′ 7″, 1.70 m","5 ft 3 in or 160 cm","5′ 4¼”, 1.63 m","5′ 10″, 1.78 m","5 ft 3½ in or 161 cm","5 ft 10 in or 178 cm","5 ft 2 in or 157.5 cm","5′ 5″, 1.65 m","5′ 8″, 1.73 m","5′ 8½”, 1.74 m","5 ft 9¾ in or 177 cm"
            // ... 其他数据
        );

        for (String height : heights) {
            double centimeters = parseHeightToCentimeters(height);
            System.out.printf("%s => %.2f cm%n", height, centimeters);
        }
    }

    private double parseHeightToCentimeters(String height) {
        // 如果直接包含米值，转换为厘米
        Pattern meterPattern = Pattern.compile("(\\d+\\.\\d+)\\s*m");
        Matcher meterMatcher = meterPattern.matcher(height);
        if (meterMatcher.find()) {
            return Math.round(Double.parseDouble(meterMatcher.group(1)) * 100.0 * 100) / 100.0;
        }

        // 如果包含厘米值，直接使用
        Pattern cmPattern = Pattern.compile("(\\d+(?:\\.\\d+)?)\\s*cm");
        Matcher cmMatcher = cmPattern.matcher(height);
        if (cmMatcher.find()) {
            return Math.round(Double.parseDouble(cmMatcher.group(1)) * 100) / 100.0;
        }

        // 处理英尺和英寸格式
        Pattern ftInPattern = Pattern.compile(
            "(\\d+)\\s*(?:ft|′|'|feet)\\s*(\\d+(?:\\.\\d+)?)?\\s*(?:in|″|\"|inches)?");
        Matcher ftInMatcher = ftInPattern.matcher(height);
        if (ftInMatcher.find()) {
            int feet = Integer.parseInt(ftInMatcher.group(1));
            double inches = ftInMatcher.group(2) != null ? 
                Double.parseDouble(ftInMatcher.group(2)) : 0;
            return Math.round((feet * 12 + inches) * 2.54 * 100) / 100.0; // 转换为厘米并保留两位小数
        }

        throw new IllegalArgumentException("无法解析的身高格式: " + height);
    }

    @Test
    public void testSingleValue() {
        String[] testCases = {
            "5 ft 10 in",
            "5′ 9″, 1.75 m",
            "5 ft 5 in or 165 cm",
            "5′ 6½\", 1.69 m",
            "4 ft 11¾ in or 151.5 cm"
        };

        for (String testCase : testCases) {
            double centimeters = parseHeightToCentimeters(testCase);
            System.out.printf("原始数据: %-25s => %.2f 厘米%n", testCase, centimeters);
        }
    }
} 