package com.looksky.agents.swapCloth;

import com.looksky.agents.application.tryon.colorseason.impl.TryOnColorSeasonServiceV1;
import com.looksky.agents.application.tryon.swapcloth.SwapClothServiceV2;
import com.looksky.agents.application.tryon.swapcloth.SwapClothServiceV3;
import com.looksky.agents.application.tryon.swapcloth.SwapClothServiceV4;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.sdk.tryon.colorseason.dto.TryOnColorSeasonDescriptionsDTO;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothParam;
import com.looksky.agents.start.LookSkyApplication;
import com.skygirls.biz.user.model.enums.MeasureTypeEnum;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class SwapClothServiceTest {


    @Resource
    private TryOnColorSeasonServiceV1 tryOnColorSeasonServiceV1;

    @Resource
    private RedissonClient redissonClient;

    //@Resource
    private SwapClothServiceV2 swapClothServiceV2;

    @Resource
    private SwapClothServiceV3 swapClothServiceV3;


    @Resource
    private SwapClothServiceV4 swapClothServiceV4;


    @Test
    void test5() throws InterruptedException {

        String modelImage = "https://cdn.lookskyai.com/upload/agent/59735d901b2f5ddf82aac8dbedbeed6f.png";
        String fullBodyImage = "https://cdn.lookskyai.com/upload/agent/86adba30ed2159e73c842a2ea81d832a.jpeg";
        String faceImage = "https://cdn.lookskyai.com/upload/agent/c986b91de4c7f84b112896cdbd66f862.jpeg";
        SwapClothParam swapClothParam = new SwapClothParam();
        swapClothParam.setModelImage(modelImage);
        swapClothParam.setFullBodyImage(fullBodyImage);
        swapClothParam.setFaceImage(faceImage);
        swapClothParam.setUserId("111");
        swapClothParam.setSkcId("1866078006418251777");
        swapClothParam.setId("111111");
        swapClothParam.setHeight("178 cm");
        swapClothParam.setWeight("60 kg");
        swapClothParam.setMeasureType(MeasureTypeEnum.cmkg);

        swapClothServiceV4.asyncSwapCloth(swapClothParam);


        TimeUnit.MINUTES.sleep(5);
    }


    @Test
    void test4() throws InterruptedException {

        String modelImage = "https://cdn.lookskyai.com/upload/agent/59735d901b2f5ddf82aac8dbedbeed6f.png";
        String fullBodyImage = "https://cdn.lookskyai.com/upload/agent/86adba30ed2159e73c842a2ea81d832a.jpeg";
        String faceImage = "https://cdn.lookskyai.com/upload/agent/c986b91de4c7f84b112896cdbd66f862.jpeg";
        SwapClothParam swapClothParam = new SwapClothParam();
        swapClothParam.setModelImage(modelImage);
        swapClothParam.setFullBodyImage(fullBodyImage);
        swapClothParam.setFaceImage(faceImage);
        swapClothParam.setUserId("111");
        swapClothParam.setSkcId("1866078006418251777");
        swapClothParam.setId("111111");
        swapClothParam.setHeight("178 cm");
        swapClothParam.setWeight("60 kg");
        swapClothParam.setMeasureType(MeasureTypeEnum.cmkg);

        swapClothServiceV3.asyncSwapCloth(swapClothParam);


        TimeUnit.MINUTES.sleep(5);
    }


    @Test
    void test() throws InterruptedException {

        String modelImage = "https://cdn.lookskyai.com/upload/agent/59735d901b2f5ddf82aac8dbedbeed6f.png";
        String fullBodyImage = "https://cdn.lookskyai.com/upload/agent/86adba30ed2159e73c842a2ea81d832a.jpeg";
        String faceImage = "https://cdn.lookskyai.com/upload/agent/c986b91de4c7f84b112896cdbd66f862.jpeg";
        SwapClothParam swapClothParam = new SwapClothParam();
        swapClothParam.setModelImage(modelImage);
        swapClothParam.setFullBodyImage(fullBodyImage);
        swapClothParam.setFaceImage(faceImage);
        swapClothParam.setUserId("111");
        swapClothParam.setId("111111");

        swapClothServiceV2.create(swapClothParam);


        TimeUnit.MINUTES.sleep(5);
    }


    @Test
    void test1() {
        String modelImage = "https://cdn.lookskyai.com/upload/agent/59735d901b2f5ddf82aac8dbedbeed6f.png";
        String fullBodyImage = "https://cdn.lookskyai.com/upload/agent/86adba30ed2159e73c842a2ea81d832a.jpeg";
        String faceImage = "https://cdn.lookskyai.com/upload/agent/c986b91de4c7f84b112896cdbd66f862.jpeg";
        SwapClothParam swapClothParam = new SwapClothParam();
        swapClothParam.setModelImage(modelImage);
        swapClothParam.setFullBodyImage(fullBodyImage);
        swapClothParam.setFaceImage(faceImage);

        //swapClothService.swapCloth(swapClothRequest);

    }


    @Test
    void test2() {
        String tempImage = "https://cdn.lookskyai.com/upload/agent/4d0ebdebfbbc3b4fd53ef5f02a1af73a.jpeg";
        String modelImage = "https://cdn.lookskyai.com/upload/agent/59735d901b2f5ddf82aac8dbedbeed6f.png";
        //String result = swapClothService.fashnSwapFace(tempImage, modelImage);
        //log.info("result: {}", result);
    }

    @Test
    void test3() {
        initRedisData();
    }


    public void initRedisData() {
        try {
            TryOnColorSeasonDescriptionsDTO descriptions = new TryOnColorSeasonDescriptionsDTO();

            // 初始化领型说明映射
            Map<String, String> necklinesMap = new HashMap<>() {{
                put("heart crew neck", "ribbed short-sleeve top featuring a heart-shaped cutout on the chest ,(narrow mock neckline), fitted silhouette");
                put("sweetheart neck", " top,sweetheart neckline,puff sleeves,elastic ruffled cuffs, vintage-inspired design ,");
                put("square neck", "top,square neckline,cap short sleeves,stretchy, form-fitting fabric, modern silhouette,");
                put("scoop neck", "top,deep scoop neck wild, very sexy,form-fitting cut,stretchy jersey material,seamless design,sleeveless,");
                put("crew neck", "t-shirt ,classic ribbed crew neck, drop shoulder design,elbow-length sleeves,loose-fitting silhouette,jersey material,");
                put("deep v-neck", "knit cardigan , deep v-neck,oversized, relaxed fit,medium-weight knit construction");
                put("off shoulder", "top,off-shoulder neckline ,Double-folded edge treatment,");
                put("v-neck", "top,polo collar,v-neck,short sleeves,even, consistent ribbing pattern,three-button placket,");
                put("turtleneck", "turtleneck");
                put("cowl neck", "top,elegant silk blouse,(asymmetrical  cowl neckline),soft draping at neckline,loose, flowing fit,sleeveless design with dropped shoulders,satin material,");
                put("halter neck", "top,high halter neck,sleeveless");
            }};
            descriptions.setNecklinesDescriptions(necklinesMap);

            // 初始化推荐或避免说明映射
            Map<String, String> recommendAvoidMap = new HashMap<>() {{
                put("positive", "(glorious,happy smile,pupil highlights)");
                put("negative", "(dull skin tone, dull eyes)");
            }};
            descriptions.setPositiveAndNegativeDescriptions(recommendAvoidMap);

            // 初始化头发颜色说明映射
            Map<String, String> hairColorMap = new HashMap<>() {{
                put("black", "black ");
                put("brown", "brown ");
                put("blonde", "blonde ");
                put("red", "red ");
            }};
            descriptions.setHairColorDescriptions(hairColorMap);

            // 初始化体重类型说明映射
            Map<String, String> weightTypeMap = new HashMap<>() {{
                put("plus", "plus ");
                put("mid", "mid ");
                put("average", "average ");
                put("slim", "slim ");
            }};
            descriptions.setWeightTypeDescriptions(weightTypeMap);

            // 将整个对象保存到Redis
            saveToRedis(descriptions);

            log.info("颜色季节描述数据已成功初始化到Redis");
        } catch (Exception e) {
            log.error("初始化颜色季节描述数据到Redis失败", e);
        }
    }

    void saveToRedis(TryOnColorSeasonDescriptionsDTO colorSeasonDescriptions) {
        redissonClient.getBucket(RedisKeyConstants.TRY_ON_COLOR_SEASON_DESCRIPTION, new TypedJsonJacksonCodec(TryOnColorSeasonDescriptionsDTO.class)).set(colorSeasonDescriptions);
    }
}
