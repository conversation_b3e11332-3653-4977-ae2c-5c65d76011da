package com.looksky.agents.data.client.business;

import cn.hutool.json.JSONUtil;
import com.graecove.common.ApiResp;
import com.looksky.agents.start.LookSkyApplication;
import com.skygirls.biz.agent.dto.BodyMeasurementResp;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.DigestUtils;

@SpringBootTest(classes = LookSkyApplication.class)
public class LookSkyClientTest {

    @Resource
    private LookSkyClient lookSkyClient;

    @Test
    void testBodyMeasurementResp() {
        String userId = "1805556828908879873";


        String sign = "225a44b332f0492bbbc04b4ab5378f42";
        // 应用名称
        // 混淆加签，期望输出：7c3d4159e147bfdb2f3abe98693ea212
        String ps = DigestUtils.md5DigestAsHex((userId + sign).getBytes());
        ApiResp<BodyMeasurementResp> bodyMeasurement = lookSkyClient.getBodyMeasurement(userId, ps, userId);

        BodyMeasurementResp data = bodyMeasurement.getData();
        System.out.println(JSONUtil.toJsonPrettyStr(data));

    }

}