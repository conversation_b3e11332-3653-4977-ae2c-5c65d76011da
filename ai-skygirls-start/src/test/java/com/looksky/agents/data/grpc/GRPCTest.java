package com.looksky.agents.data.grpc;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * @ClassName GRPCTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 下午2:40
 * @Version 1.0
 **/
@SpringBootTest(classes = LookSkyApplication.class)
public class GRPCTest {
    @Resource
    private SearchGrpcClient girlsAgentSearchGrpcClient;

    @Test
    public void testSearch() {

        List<SearchRequestDTO.VectorQuery> vectorQueryList = List.of(
                SearchRequestDTO.VectorQuery.builder().text("Elegant sleeveless dress with a soft cream base and intricate paisley pattern").weight(1).build(),
                SearchRequestDTO.VectorQuery.builder().text("featuring a gathered neckline").weight(1).build(),
                SearchRequestDTO.VectorQuery.builder().text("soft cream base").weight(-1).build());

        SearchRequestDTO.SearchTerm searchTerm = SearchRequestDTO.SearchTerm.builder()
                .searchStrategy("Duis")
                .categories(List.of("short_dresses"))
                .vectorQueries(vectorQueryList).build();

        var request =  SearchRequestDTO.builder()
                .userId("1864276787028647936")
                .season("winter")
                //.searchTerms(List.of(searchTerm))
                .build();

        var response = girlsAgentSearchGrpcClient.search(request);
        System.out.println(JSONUtil.toJsonPrettyStr(response));
    }
}
