package com.looksky.agents.data.client.service;

import static org.junit.jupiter.api.Assertions.assertTrue;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @ClassName TagSystemTableServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/13 下午7:19
 * @Version 1.0
 **/
@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
public class TagSystemTableServiceTest {

    @Resource
    private TagSystemTableService tagSystemTableService;



//    @Resource
//    private TagSystemTableParser tagSystemTableParser;

    @Resource
    private TagSystemTableParser tagSystemTableParser;

    private static final HashSet<String> SWIM = new HashSet<>(Set.of("swimwear", "cover-ups", "swim_bottoms", "swim_one-piece", "swim_sets", "swim_tops"));

    @DisplayName("测试获取风格标签值")
    @Test
    void test11() {
        String style = tagSystemTableService.getEnumValues(null, "clothing_styles");
        log.info("风格标签值: {}", style);
    }



    @Test
    @DisplayName("测试品类中是否有泳装")
    void test10() {
        List<String> categoryEnum = tagSystemTableService.getCategoryEnum();
        System.out.println(JSONUtil.toJsonStr(categoryEnum));
        assertTrue(categoryEnum.containsAll(SWIM), "泳装 tag 有缺失");
    }

    @Test
    void test9() {
        // 获取所有品类
        ArrayList<String> category = new ArrayList<>(tagSystemTableService.getCategoryEnum());

        // 获取 Set 的二级品类
        List<String> setCategory = new ArrayList<>(tagSystemTableService.getSubCategoryByFirstCategory(CategoryEnum.SET.getName()));
        setCategory.add(CategoryEnum.SET.getName());

        category.removeAll(setCategory);
        System.out.println(JSONUtil.toJsonStr(category));
    }


    @Test
    void test8() {
        String specialFabric = tagSystemTableService.getEnumValues("t-shirt", "special_fabric");
        String fabric = tagSystemTableService.getEnumValues("t-shirt", "fabric_type");
        System.out.println(fabric);

        System.out.println(specialFabric);


    }


    @Test
    void test7() {
        tagSystemTableService.getInnerParentTag("design_detail");
    }


    @Test
    void test1() {
        tagSystemTableService.getFirstCategories().forEach(System.out::println);
    }

    @Test
    void test2() {
//        tagSystemTableService.getSubCategoryPreferenceTags("short_dresses");
    }

    @Test
    void test3() {

//        ApiResp<JsonNode> tagSystemTable = tagSystemTableClient.getTagSystemTable();
//        JsonNode data = tagSystemTable.getData();
//        List<Item> tag_list = tagSystemTableService.getTagPart("tag_list");
////        List<Item> tag_tree = tagSystemTableService.getTagPart("tag_tree");
//        tagSystemTableParser.removeCategoryTagsValue(tag_list);  // 初始化tagMap和subCategoryList
//        tagSystemTableParser.saveLabelSystemTable(data.get("tag_tree"), tag_list);
    }


    @Test
    void test4() {
//        List<Item> tag_list = tagSystemTableService.getTagList("tag_list");
//        List<Item> tag_tree = tagSystemTableService.getTagTree("tag_tree");
//        Map<String, List<String>> tag_parent = tagSystemTableService.getTagParent();
//        wgiao.removeCategoryTagsValue(tag_list);
//        tagSystemTableParser.init(tag_tree, tag_list);
//        Map<String, String> tagToParentTagMap = tagSystemTableParser.getTagToParentTagMap(tag_parent);
//
//        System.out.println(JSONUtil.toJsonStr(tagToParentTagMap));
//
//        HashSet<String> strings = new HashSet<>();
//        tagToParentTagMap.forEach((key, value) -> strings.add(value));
//
//        System.out.println(strings);
    }


    @Test
    void test5() {
        String specialFabric = tagSystemTableService.getEnumValues("t-shirt", "special_fabric");
        System.out.println(specialFabric);
    }

    @Test
    void test6() {
        Map<String, List<String>> carCoat = tagSystemTableService.getTagAndTagValueByCategory("car_coat");
        System.out.println(JSONUtil.toJsonStr(carCoat));
    }


}
