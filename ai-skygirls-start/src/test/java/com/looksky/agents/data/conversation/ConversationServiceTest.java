//package com.looksky.agents.data.conversation;
//
//import cn.hutool.json.JSONUtil;
//import com.looksky.agents.common.model.preference.CategoryPreference;
//import com.looksky.agents.common.model.UserPreferenceTypeList;
//import com.looksky.agent.search.bo.EcommercePreference;
//import com.looksky.agent.preference.ExtractedEntityObject;
//import com.looksky.agents.common.model.dto.prompt.UserSubjectivePreference;
//import com.looksky.agents.start.LookSkyApplication;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.List;
//
//@SpringBootTest(classes = LookSkyApplication.class)
//class ConversationServiceTest {
//
//    @Autowired
//    private ConversationService conversationService;
//
//    private final String userId = "testUser";
//    private final String conversationId = "testConversation";
//
//    @Test
//    void getUserPreferenceInConversation_Success() {
//        // 准备测试数据
//        ExtractedEntityObject mockPreference = createMockPreference();
//        conversationService.setUserPreferenceInConversation(userId, conversationId, mockPreference);
//
//        ExtractedEntityObject userPreferenceInConversation = conversationService.getUserPreferenceInConversation(userId, conversationId);
//        System.out.println(JSONUtil.toJsonPrettyStr(userPreferenceInConversation));
//
//    }
//
//    private ExtractedEntityObject createMockPreference() {
//        ExtractedEntityObject preference = new ExtractedEntityObject();
//
//        // 设置 UserPreferenceTypeList
//        UserPreferenceTypeList userPreferenceType = new UserPreferenceTypeList();
//        userPreferenceType.setClothIndex(1);
//        UserPreferenceTypeList.SingleUserPreference singleUserPreference = new UserPreferenceTypeList.SingleUserPreference();
//        singleUserPreference.setCategory("category");
//        userPreferenceType.setUserPreferenceList(Collections.singletonList(singleUserPreference));
//        // 根据实际 UserPreferenceTypeList 结构设置属性
//        preference.setUserPreferenceType(userPreferenceType);
//
//        // 设置 UserSubjectivePreference
//        UserSubjectivePreference userSubjectivePreference = new UserSubjectivePreference();
//        userSubjectivePreference.setOccasion("occasion");
//        userSubjectivePreference.setStyle("style");
//        // 根据实际 UserSubjectivePreference 结构设置属性
//        preference.setUserSubjectivePreference(userSubjectivePreference);
//
//        // 设置 EcommercePreference
//        EcommercePreference ecommercePreference = new EcommercePreference();
//        ecommercePreference.setCanReturn(true);
//        // 根据实际 EcommercePreference 结构设置属性
//        preference.setEcommercePreference(ecommercePreference);
//
//        // 设置 CategoryPreference 列表
//        List<CategoryPreference> categoryPreferences = new ArrayList<>();
//        CategoryPreference preference1 = new CategoryPreference();
//        preference1.setCategory("c1");
//        // 设置 preference1 的属性
//        CategoryPreference preference2 = new CategoryPreference();
//        preference2.setCategory("c2");
//        // 设置 preference2 的属性
//        categoryPreferences.addAll(Arrays.asList(preference1, preference2));
//        preference.setCategoryPreferences(categoryPreferences);
//
//        return preference;
//    }
//}