package com.looksky.agents.data.client.business;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.sdk.perplex.dto.request.PerplexSearchRequest;
import com.looksky.agents.sdk.perplex.dto.response.PerplexSearchResponse;
import com.looksky.agents.data.client.service.OpenPerplexService;
import com.looksky.agents.start.LookSkyApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @ClassName OpenPerplexClientTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/2 下午9:16
 * @Version 1.0
 **/
@SpringBootTest(classes = LookSkyApplication.class)
public class OpenPerplexClientTest {

    @Autowired
    private OpenPerplexService openPerplexService;


    @Test
    public void testSearchService() {
        PerplexSearchRequest searchRequest = new PerplexSearchRequest();
        searchRequest.setQuery("What is OpenPerplex?");
        PerplexSearchResponse search = openPerplexService.search(searchRequest);
        System.out.println(JSONUtil.toJsonStr(search));
    }



}
