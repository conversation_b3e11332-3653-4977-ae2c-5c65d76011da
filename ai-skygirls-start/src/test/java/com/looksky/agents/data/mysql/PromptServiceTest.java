package com.looksky.agents.data.mysql;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.data.mysql.service.IPromptService;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Prompt服务测试类
 */
@ComponentScan(basePackages = {"com.looksky.agents"})
@MapperScan("com.looksky.agents.data.mysql.**.mapper")
@SpringBootTest(classes = LookSkyApplication.class)
public class PromptServiceTest {
    @Resource
    private IPromptService promptService;

    @Test
    public void testCreate() {
        PromptModel prompt = new PromptModel();
        prompt.setName("测试名称");
        
        // 测试保存
        boolean success = promptService.save(prompt);
        Assertions.assertTrue(success);
        
        // 验证ID自动生成
        Assertions.assertNotNull(prompt.getId());
        
        // 验证创建时间和更新时间自动填充
        Assertions.assertNotNull(prompt.getCreatedTime());
        Assertions.assertNotNull(prompt.getUpdatedTime());
        
        // 验证逻辑删除默认值
        Assertions.assertEquals(false, prompt.getDeleted());
    }

    @Test
    public void testUpdate() {
        // 先创建一条记录
        PromptModel prompt = new PromptModel();
        prompt.setName("测试名称");
        promptService.save(prompt);
        
        // 记录原始更新时间
        LocalDateTime originalUpdateTime = prompt.getUpdatedTime();
        
        // 等待1秒确保更新时间有变化
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        
        // 更新记录
        prompt.setName("更新后的名称");
        boolean success = promptService.updateById(prompt);
        Assertions.assertTrue(success);
        
        // 重新查询验证更新
        PromptModel updated = promptService.getById(prompt.getId());
        Assertions.assertEquals("更新后的标题", updated.getName());
        Assertions.assertTrue(updated.getUpdatedTime().isAfter(originalUpdateTime));
    }

    @Test
    public void testQuery() {
        // 创建测试数据
        for (int i = 0; i < 3; i++) {
            PromptModel prompt = new PromptModel();
            prompt.setName("测试名称" + i);
            promptService.save(prompt);
        }
        
        // 测试条件查询
        LambdaQueryWrapper<PromptModel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(PromptModel::getName, "测试标题");
        List<PromptModel> prompts = promptService.list(queryWrapper);
        Assertions.assertEquals(3, prompts.size());
        
        // 测试精确查询
        queryWrapper.clear();
        queryWrapper.eq(PromptModel::getName, "测试标题1");
        PromptModel prompt = promptService.getOne(queryWrapper);
        Assertions.assertNotNull(prompt);
    }

    @Test
    public void testDelete() {
        // 创建测试数据
        PromptModel prompt = new PromptModel();
        prompt.setName("测试名称");
        promptService.save(prompt);
        
        // 测试逻辑删除
        boolean success = promptService.removeById(prompt.getId());
        Assertions.assertTrue(success);
        
        // 验证无法查询到已删除数据
        PromptModel deleted = promptService.getById(prompt.getId());
        Assertions.assertNull(deleted);
        
        // 使用自定义SQL验证数据仍然存在但已标记删除
        LambdaQueryWrapper<PromptModel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PromptModel::getId, prompt.getId());
        // 注意：需要手动设置查询已删除数据，具体方法取决于你的MyBatis-Plus配置
        // 这里假设你有一个自定义方法可以查询已删除数据
        // Prompt logicalDeleted = promptService.getByIdWithDeleted(prompt.getId());
        // Assertions.assertNotNull(logicalDeleted);
        // Assertions.assertEquals(1, logicalDeleted.getIsDeleted());
    }
}
