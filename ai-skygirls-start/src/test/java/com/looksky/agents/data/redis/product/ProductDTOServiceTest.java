package com.looksky.agents.data.redis.product;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @ClassName ProductServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/8 下午2:18
 * @Version 1.0
 **/
@SpringBootTest(classes = LookSkyApplication.class)
public class ProductDTOServiceTest {

    @Resource
    private ProductClient productClient;

    @Resource
    private RedissonClient redissonClient;

    @Test
    public void getProductByProductId() throws JsonProcessingException {
        Object productIndexValue = productClient.getProductByProductId("1722174128645857281");
        ObjectMapper objectMapper = new ObjectMapper();
        System.out.println("处理后的对象:");
        System.out.println(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(productIndexValue));
    }
}
