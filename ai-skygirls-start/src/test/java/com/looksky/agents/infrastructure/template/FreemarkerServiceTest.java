package com.looksky.agents.infrastructure.template;

import static org.assertj.core.api.Assertions.assertThat;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.template.FreemarkerService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.start.LookSkyApplication;
import freemarker.template.TemplateException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class FreemarkerServiceTest {

    @Autowired
    private FreemarkerService freemarkerService;

    @SneakyThrows
    @Test
    void testKOC() {

        String data = """
            {
              "kocInfo": {
                "name": "ebonyshield",
                "age": "30",
                "height": "5'3\\"",
                "weight": "115",
                "heightType": "tall",
                "weightType": "slim",
                "upperToLowerBodyRatio": "balanced",
                "bodyShape": "rectangle",
                "focusArea": "focusArea",
                "styleVibe": "style1, style2",
                "outfitStyle": "chic, classic, polished"
              },
              "products": [
                {
                  "name": "Product name",
                  "desc": "Product Description",
                  "price": "price",
                  "originPrice": "originPrice",
                  "brand": "brand"
                },
                {
                  "name": "Product name",
                  "desc": "Product Description",
                  "price": "price",
                  "originPrice": "originPrice",
                  "brand": "brand"
                }
              ],
              "mainImage": "https://xxxx.xx"
            }
            """;

        String prompt = """
            #### KOC Info:
            <@displayField label="KOC Name" path="name" data=kocInfo/>
            <@displayField label="KOC Age" path="age" data=kocInfo/>
            <@displayField label="KOC Height" path="height" data=kocInfo/>
            <@displayField label="KOC Weight" path="weight" data=kocInfo/>
            <@displayField label="KOC Height Type" path="heightType" data=kocInfo/>
            <@displayField label="KOC Weight Type" path="weightType" data=kocInfo/>
            <@displayField label="KOC Upper To Lower Body Ratio" path="upperToLowerBodyRatio" data=kocInfo/>
            <@displayField label="KOC Body Shape" path="bodyShape" data=kocInfo/>
            <@displayField label="KOC Focus Area" path="focusArea" data=kocInfo/>
            <@displayField label="KOC Style Vibe" path="styleVibe" data=kocInfo/>
            <@displayField label="KOC Outfit Style" path="outfitStyle" data=kocInfo/>
            
            #### Post Cover Image
            <img src="${mainImage}"/>
            
            #### Product Info
            <#if products?? && products?has_content>
                <#list products as product>
            <@displayField label="Product Name" path="name" data=product/>
            <@displayField label="Product Description" path="desc" data=product/>
            <@displayField label="Product Price" path="price" data=product/>
            <@displayField label="Product Origin Price" path="originPrice" data=product/>
            <@displayField label="Product Brand Name" path="brand" data=product/>
                </#list>
            </#if>
            """;



        Map<String, Object> map = JSONUtil.toBean(data, HashMap.class);


        String result = freemarkerService.parseTemplate(prompt, map);

        log.info("渲染结果: {}", result);
    }


    @BeforeEach
    void setUp() {

        // 清理 MessageContext
        Context.clear();
    }


    @Test
    void test() throws TemplateException, IOException {
        String template = """
            <@userInfoData/>
            """;

        String result = freemarkerService.parseTemplate(template, new HashMap<>());

        System.out.println(result);
    }


    @Test
    void shouldProcessTemplateWithSingleMacro () throws TemplateException, IOException {
        // Given
        Map<String, Object> userInfo = new HashMap<>();
        Map<String, Object> user = new HashMap<>();
        user.put("username", "张三");
        user.put("sex", "男");
        userInfo.put("user", user);

        Context.put("userInfo", userInfo);

        String template = """
            # 用户信息
            <@userInfo/>
            """;

        // When
        String result = freemarkerService.parseTemplate(template, new HashMap<>());

        // Then
        assertThat(result)
                .contains("# 用户信息")
                .contains("name:张三")
                .contains("sex:男");
    }

    @Test
    void shouldProcessTemplateWithMultipleMacros() throws TemplateException, IOException {
        // Given
        // 设置用户信息
        Map<String, Object> userInfo = new HashMap<>();
        Map<String, Object> user = new HashMap<>();
        user.put("username", "张三");
        userInfo.put("user", user);
        Context.put("user", userInfo);

        // 设置对话历史
        Context.put("conversation_history", "用户: 你好\nAI: 您好！");

        String template = """
            # 完整信息
            <@userInfo/>
            
            <@conversationHistory/>
            
            <@questionTypes/>
            """;

        // When
        String result = freemarkerService.parseTemplate(template, new HashMap<>());

        // Then
        assertThat(result)
                .contains("# 完整信息")
                .contains("name:张三")
                .contains("用户: 你好")
                .contains("AI: 您好！")
                .contains("Question type description")
                .contains("kibbe");
    }

    @Test
    void shouldHandleNonExistentMacro() throws TemplateException, IOException {
        // Given
        String template = """
            <@nonExistentMacro/>
            """;

        // When
        String result = freemarkerService.parseTemplate(template, new HashMap<>());

        // Then
        assertThat(result).doesNotContain("nonExistentMacro");
    }

    @Test
    void shouldHandleEmptyContext() throws TemplateException, IOException {
        // Given
        String template = """
            <@userInfo/>
            """;

        // When
        String result = freemarkerService.parseTemplate(template, new HashMap<>());

        // Then
        assertThat(result).doesNotContain("name:")
                         .doesNotContain("sex:");
    }

    @Test
    void shouldHandleComplexNestedData() throws TemplateException, IOException {
        // Given
        Map<String, Object> userInfo = new HashMap<>();
        Map<String, Object> user = new HashMap<>();
        user.put("username", "张三");
        user.put("sex", "男");
        userInfo.put("user", user);
        userInfo.put("kibbeStyle", "Dramatic");
        userInfo.put("bodyShape", "X型");
        userInfo.put("age", 25);
        userInfo.put("height", 170);
        userInfo.put("weight", 60);
        userInfo.put("season", "春季");
        
        Context.put("user", userInfo);

        String template = """
            # 详细用户信息
            <@userInfo/>
            """;

        // When
        String result = freemarkerService.parseTemplate(template, new HashMap<>());

        // Then
        assertThat(result)
                .contains("name:张三")
                .contains("sex:男")
                .contains("kibbeStyle:Dramatic")
                .contains("bodyShape:X型")
                .contains("age:25")
                .contains("height:170")
                .contains("weight:60")
                .contains("season:春季");
    }
} 