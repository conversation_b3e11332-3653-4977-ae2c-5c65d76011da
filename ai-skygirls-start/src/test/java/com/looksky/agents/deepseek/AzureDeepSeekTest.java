//package com.looksky.agents.deepseek;
//
//import com.azure.ai.inference.ChatCompletionsAsyncClient;
//import com.azure.ai.inference.ChatCompletionsClient;
//import com.azure.ai.inference.ChatCompletionsClientBuilder;
//import com.azure.ai.inference.models.ChatCompletions;
//import com.azure.ai.inference.models.ChatCompletionsOptions;
//import com.azure.ai.inference.models.ChatRequestAssistantMessage;
//import com.azure.ai.inference.models.ChatRequestMessage;
//import com.azure.ai.inference.models.ChatRequestSystemMessage;
//import com.azure.ai.inference.models.ChatRequestUserMessage;
//import com.azure.ai.inference.models.ChatResponseMessage;
//import com.azure.ai.inference.models.CompletionsUsage;
//import com.azure.ai.inference.models.StreamingChatCompletionsUpdate;
//import com.azure.ai.inference.models.StreamingChatResponseMessageUpdate;
//import com.azure.ai.openai.OpenAIClientBuilder;
//import com.azure.ai.openai.OpenAIServiceVersion;
//import com.azure.core.credential.AzureKeyCredential;
//import com.azure.core.util.CoreUtils;
//import com.azure.core.util.IterableStream;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Test;
//import org.springframework.ai.azure.openai.AzureOpenAiChatModel;
//import org.springframework.ai.chat.messages.UserMessage;
//import org.springframework.ai.chat.model.ChatResponse;
//import org.springframework.ai.chat.prompt.Prompt;
//import org.springframework.ai.openai.OpenAiChatOptions;
//
////@SpringBootTest(classes = LookSkyApplication.class)
//@Slf4j
//class AzureDeepSeekTest {
//
//    String endpoint = "https://ai-huida3161ai104986368156.services.ai.azure.com/models";
//    String apiKey = "DSa8YPpNXizH65yRzb7i42twvhw3YfCofhfFAKz6P9cFIY7PRXxnJQQJ99BCAC4f1cMXJ3w3AAAAACOGnrKh";
//    String deployment = "DeepSeek-R1";
//    ChatCompletionsClient client = new ChatCompletionsClientBuilder()
//        .credential(new AzureKeyCredential(apiKey))
//        .endpoint(endpoint)
//        .buildClient();
//
//
//    @Test
//    @DisplayName("测试非流")
//    void test1() throws InterruptedException {
//        //AzureOpenAiChatOptions build = AzureOpenAiChatOptions.builder().deploymentName().build();
//        //OpenAIAsyncClient openAIAsyncClient = new OpenAIClientBuilder().endpoint(endpoint)
//        //    .credential(new AzureKeyCredential(apiKey)).buildAsyncClient();
//        //
//        //AzureOpenAiChatModel model = new AzureOpenAiChatModel(openAIAsyncClient, deployment);
//
//        //ChatCompletionsAsyncClient client = new ChatCompletionsClientBuilder()
//        //    .credential(new AzureKeyCredential(apiKey))
//        //    .endpoint(endpoint)
//        //    .buildAsyncClient();
//        //
//        //  client.complete("Tell me about Euler's Identity").subscribe(
//        //    chatCompletions -> {
//        //        System.out.printf("Model ID=%s.%n", chatCompletions.getId());
//        //        ChatResponseMessage message = chatCompletions.getChoice().getMessage();
//        //        System.out.printf("Chat Role: %s.%n", message.getRole());
//        //        System.out.println("Message:");
//        //        System.out.println(message.getContent());
//        //
//        //        System.out.println();
//        //        CompletionsUsage usage = chatCompletions.getUsage();
//        //        System.out.printf("Usage: number of prompt token is %d, "
//        //                + "number of completion token is %d, and number of total tokens in request and response is %d.%n",
//        //            usage.getPromptTokens(), usage.getCompletionTokens(), usage.getTotalTokens());
//        //    },
//        //    error -> System.err.println("There was an error getting chat completions." + error),
//        //    () -> System.out.println("\nCompleted calling complete."));
//        //
//        //// The .subscribe() creation and assignment is not a blocking call. For the purpose of this example, we sleep
//        //// the thread so the program does not end before the send operation is complete. Using .block() instead of
//        //// .subscribe() will turn this into a synchronous call.
//        //TimeUnit.SECONDS.sleep(10);
//        //String endpoint = "https://ai-huida3161ai104986368156.services.ai.azure.com/models";
//        //String model = "DeepSeek-R1";
//
//
//        List<ChatRequestMessage> chatMessages = Arrays.asList(
//            new ChatRequestSystemMessage("You are a helpful assistant."),
//            new ChatRequestUserMessage("I am going to Paris, what should I see?")
//        );
//        ChatCompletionsOptions chatCompletionsOptions = new ChatCompletionsOptions(chatMessages);
//        chatCompletionsOptions.setMaxTokens(2048);
//        chatCompletionsOptions.setModel(deployment);
//
//
//        ChatCompletions completions = client.complete(chatCompletionsOptions);
//
//        System.out.printf("%s.%n", completions.getChoice().getMessage().getContent());
//    }
//
//    @Test
//    @DisplayName("测试流式输出")
//    void test2() {
//        List<ChatRequestMessage> chatMessages = new ArrayList<>();
//        chatMessages.add(new ChatRequestSystemMessage("You are a helpful assistant."));
//        chatMessages.add(new ChatRequestUserMessage("I am going to Paris, what should I see?"));
//        chatMessages.add(new ChatRequestAssistantMessage(
//            "Paris, the capital of France, is known for its stunning architecture, art museums, historical landmarks, and romantic atmosphere. Here are some of the top attractions to see in Paris:\n \n 1. The Eiffel Tower: The iconic Eiffel Tower is one of the most recognizable landmarks in the world and offers breathtaking views of the city.\n 2. The Louvre Museum: The Louvre is one of the world's largest and most famous museums, housing an impressive collection of art and artifacts, including the Mona Lisa.\n 3. Notre-Dame Cathedral: This beautiful cathedral is one of the most famous landmarks in Paris and is known for its Gothic architecture and stunning stained glass windows.\n \n These are just a few of the many attractions that Paris has to offer. With so much to see and do, it's no wonder that Paris is one of the most popular tourist destinations in the world."));
//        chatMessages.add(new ChatRequestUserMessage("What is so great about #1?"));
//
//        ChatCompletionsOptions chatCompletionsOptions = new ChatCompletionsOptions(chatMessages);
//        chatCompletionsOptions.setMaxTokens(2048);
//        chatCompletionsOptions.setModel(deployment);
//
//
//        IterableStream<StreamingChatCompletionsUpdate> chatCompletionsStream = client.completeStream(
//            chatCompletionsOptions);
//
//        // The delta is the message content for a streaming response.
//        // Subsequence of streaming delta will be like:
//        // "delta": {
//        //     "role": "assistant"
//        // },
//        // "delta": {
//        //     "content": "Why"
//        //  },
//        //  "delta": {
//        //     "content": " don"
//        //  },
//        //  "delta": {
//        //     "content": "'t"
//        //  }
//        chatCompletionsStream
//            .stream()
//            .forEach(chatCompletions -> {
//                System.out.println(chatCompletions);
//                //if (CoreUtils.isNullOrEmpty(chatCompletions.getChoices())) {
//                //    return;
//                //}
//                //
//                //StreamingChatResponseMessageUpdate delta = chatCompletions.getChoice().getDelta();
//                //
//                //if (delta.getRole() != null) {
//                //    System.out.println("Role = " + delta.getRole());
//                //}
//                //
//                //if (delta.getContent() != null) {
//                //    String content = delta.getContent();
//                //    System.out.print(content);
//                //}
//            });
//
//    }
//
//    @Test
//    @DisplayName("测试其他流式方法")
//    void test3() throws InterruptedException {
//        ChatCompletionsAsyncClient client = new ChatCompletionsClientBuilder()
//            .credential(new AzureKeyCredential(apiKey))
//            .endpoint("https://ai-huida3161ai104986368156.services.ai.azure.com/models/chat/completions?api-version=2024-05-01-preview")
//            .buildAsyncClient();
//
//
//        client.complete("Tell me about Euler's Identity").subscribe(
//            chatCompletions -> {
//                System.out.printf("Model ID=%s.%n", chatCompletions.getId());
//                ChatResponseMessage message = chatCompletions.getChoice().getMessage();
//                System.out.printf("Chat Role: %s.%n", message.getRole());
//                System.out.println("Message:");
//                System.out.println(message.getContent());
//
//                System.out.println();
//                CompletionsUsage usage = chatCompletions.getUsage();
//                System.out.printf("Usage: number of prompt token is %d, "
//                        + "number of completion token is %d, and number of total tokens in request and response is %d.%n",
//                    usage.getPromptTokens(), usage.getCompletionTokens(), usage.getTotalTokens());
//            },
//            error -> System.err.println("There was an error getting chat completions." + error),
//            () -> System.out.println("\nCompleted calling complete."));
//
//        // The .subscribe() creation and assignment is not a blocking call. For the purpose of this example, we sleep
//        // the thread so the program does not end before the send operation is complete. Using .block() instead of
//        // .subscribe() will turn this into a synchronous call.
//        TimeUnit.SECONDS.sleep(10);
//
//    }
//
//
//    @Test
//    @DisplayName("测试使用 openai")
//    void test4() {
//        OpenAIClientBuilder openAIClientBuilder = new OpenAIClientBuilder();
//        openAIClientBuilder.endpoint(endpoint);
//        openAIClientBuilder.credential(new AzureKeyCredential(apiKey));
//        openAIClientBuilder.serviceVersion(OpenAIServiceVersion.V2024_05_01_PREVIEW);
//
//
//        OpenAiChatOptions build = OpenAiChatOptions.builder().model(deployment).build();
//        UserMessage message = new UserMessage("你好呀");
//        Prompt prompt = new Prompt(List.of(message), build);
//
//        ChatResponse call = new AzureOpenAiChatModel(openAIClientBuilder).call(prompt);
//        System.out.println(call);
//    }
//
//
//    @Test
//    @DisplayName("测试使用异步 client")
//    void test5() throws InterruptedException {
//        ChatCompletionsAsyncClient client = new ChatCompletionsClientBuilder()
//            .credential(new AzureKeyCredential(apiKey))
//            .endpoint(endpoint)
//            .buildAsyncClient();
//
//        List<ChatRequestMessage> chatMessages = new ArrayList<>();
//        chatMessages.add(new ChatRequestSystemMessage("You are a helpful assistant."));
//        chatMessages.add(new ChatRequestUserMessage("I am going to Paris, what should I see?"));
//        chatMessages.add(new ChatRequestAssistantMessage(
//            "Paris, the capital of France, is known for its stunning architecture, art museums, historical landmarks, and romantic atmosphere. Here are some of the top attractions to see in Paris:\n \n 1. The Eiffel Tower: The iconic Eiffel Tower is one of the most recognizable landmarks in the world and offers breathtaking views of the city.\n 2. The Louvre Museum: The Louvre is one of the world's largest and most famous museums, housing an impressive collection of art and artifacts, including the Mona Lisa.\n 3. Notre-Dame Cathedral: This beautiful cathedral is one of the most famous landmarks in Paris and is known for its Gothic architecture and stunning stained glass windows.\n \n These are just a few of the many attractions that Paris has to offer. With so much to see and do, it's no wonder that Paris is one of the most popular tourist destinations in the world."));
//        chatMessages.add(new ChatRequestUserMessage("What is so great about #1?"));
//
//        ChatCompletionsOptions chatCompletionsOptions = new ChatCompletionsOptions(chatMessages);
//        chatCompletionsOptions.setMaxTokens(2048);
//        chatCompletionsOptions.setModel(deployment);
//
//        StringBuilder sb = new StringBuilder();
//
//        client.completeStream(chatCompletionsOptions).subscribe(
//            chatCompletions -> {
//
//                if (CoreUtils.isNullOrEmpty(chatCompletions.getChoices())) {
//                    System.out.println("choices 为空");
//                    return;
//                }
//
//                StreamingChatResponseMessageUpdate delta = chatCompletions.getChoice().getDelta();
//                if (delta.getRole() != null) {
//                    sb.append("Role = ").append(delta.getRole());
//                    //System.out.println("Role = " + delta.getRole());
//                }
//
//                if (delta.getContent() != null) {
//                    sb.append(delta.getContent());
//                    //System.out.print(delta.getContent());
//                }
//
//            },
//            error -> {
//                System.err.println("Streaming error: " + error.getMessage());
//            },
//            () -> {
//                System.out.println("Streaming complete");
//            }
//        );
//
//
//
//        TimeUnit.SECONDS.sleep(20);
//        System.out.println("========================");
//        System.out.println(sb.toString());
//
//    }
//}
//
