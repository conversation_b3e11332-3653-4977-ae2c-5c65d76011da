package com.looksky.agents.deepseek;


import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.ai.bedrock.converse.BedrockProxyChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.boot.test.context.SpringBootTest;
import reactor.core.publisher.Flux;


@SpringBootTest(classes = LookSkyApplication.class)
class AwsDeepSeekTest {


    @Resource
    private BedrockProxyChatModel bedrockProxyChatModel;


    @Test
    void test() {
        ChatOptions build = ChatOptions.builder()
            .model("us.deepseek.r1-v1:0")
            .maxTokens(32768)
            .build();
        Prompt prompt = new Prompt("1 加 1 等于几", build);

        //ChatResponse call = bedrockProxyChatModel.call(prompt);
        //call.getResults().forEach(System.out::println);
        //System.out.println(call.getResult().getOutput());

        //System.out.println(call.getResult().getOutput().getText());
        //


        Flux<ChatResponse> stream = bedrockProxyChatModel.stream(prompt);
        stream.subscribe(t -> {
            if (t.getResult() != null) {
                System.out.print(t.getResult().getOutput().getText());
            } else {
                System.out.println(t);
            }
        });
    }


    //@Test
    //void test() {
    //
    //    // Create a Bedrock Runtime client in the AWS Region you want to use.
    //    // Replace the DefaultCredentialsProvider with your preferred credentials provider.
    //    var client = BedrockRuntimeAsyncClient.builder()
    //            .credentialsProvider(DefaultCredentialsProvider.create())
    //            .region(Region.US_EAST_1)
    //            .build();
    //
    //
    //    var authSchemeOptions =
    //        DefaultBedrockRuntimeAuthSchemeProvider.create().resolveAuthScheme(BedrockRuntimeAuthSchemeParams.builder().build());
    //
    //
    //    BedrockRuntimeAsyncClientBuilder builder = BedrockRuntimeAsyncClient.builder().authSchemeProvider(authSchemeOptions)
    //    ).endpointProvider(BedrockRuntimeEndpointProvider.defaultProvider());
    //
    //
    //    // Set the model ID, e.g., Llama 3 8b Instruct.
    //    var modelId = "meta.llama3-8b-instruct-v1:0";
    //
    //    // Create the input text and embed it in a message object with the user role.
    //    var inputText = "Describe the purpose of a 'hello world' program in one line.";
    //    var message = Message.builder()
    //            .content(ContentBlock.fromText(inputText))
    //            .role(ConversationRole.USER)
    //            .build();
    //
    //    // Send the message with a basic inference configuration.
    //    var request = client.converse(params -> params
    //            .modelId(modelId)
    //            .messages(message)
    //            .inferenceConfig(config -> config
    //                    .maxTokens(512)
    //                    .temperature(0.5F)
    //                    .topP(0.9F))
    //    );
    //
    //    // Prepare a future object to handle the asynchronous response.
    //    CompletableFuture<String> future = new CompletableFuture<>();
    //
    //    // Handle the response or error using the future object.
    //    request.whenComplete((response, error) -> {
    //        if (error == null) {
    //            // Extract the generated text from Bedrock's response object.
    //            String responseText = response.output().message().content().get(0).text();
    //            future.complete(responseText);
    //        } else {
    //            future.completeExceptionally(error);
    //        }
    //    });
    //
    //    try {
    //        // Wait for the future object to complete and retrieve the generated text.
    //        String responseText = future.get();
    //        System.out.println(responseText);
    //
    //        return responseText;
    //
    //    } catch (ExecutionException | InterruptedException e) {
    //        System.err.printf("Can't invoke '%s': %s", modelId, e.getMessage());
    //        throw new RuntimeException(e);
    //    }
    //}

}
