package com.looksky.agents.feishu;

import com.looksky.agents.infrastructure.exception.FeiShuNotifyHelper;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 1.1.8
 **/
@SpringBootTest(classes = LookSkyApplication.class)
class FeiShuNotifyTest {
    @Resource
    private FeiShuNotifyHelper feiShuNotifyHelper;

    @Test
    void test1() throws InterruptedException {
        feiShuNotifyHelper.notify("hello");
        TimeUnit.SECONDS.sleep(10);
    }
}
