package com.looksky.agents.context.user.userName;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.util.HashSet;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 1.1.14
 **/
@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class TestUserNameGenerateService {
    @Resource
    private CommonRequestService commonRequestService;

    @Test
    void generateUserName() {
        String prompt = "generate_user_name";
        String result = commonRequestService.commonExecuteStrategy(prompt);
        log.info("结果: {}", result);
        HashSet<String> names = new HashSet<>(Set.of(result.split("\\R")));
        log.info("分隔后的结果: {}", JSONUtil.toJsonStr(names));
    }
}
