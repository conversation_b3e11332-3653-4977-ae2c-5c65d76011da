//package com.looksky.agents.application.intentionRecognition;
//
//import cn.hutool.json.JSONUtil;
//import com.looksky.agents.application.intentionRecognition.service.IntentRecognitionService;
//import com.looksky.agents.start.LookSkyApplication;
//import jakarta.annotation.Resource;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//
///**
// * @ClassName IntentRecognitionServiceTest
// * @Description TODO
// * <AUTHOR>
// * @Date 2024/11/6 上午10:53
// * @Version 1.0
// **/
//@SpringBootTest(classes = LookSkyApplication.class)
//public class IntentRecognitionServiceTest {
//
//    @Resource
//    private IntentRecognitionService intentRecognitionService;
//
//    @Test
//    public void execPrompt() {
//        String s = intentRecognitionService.execPrompt();
//        System.out.println(JSONUtil.toJsonPrettyStr(s));
//    }
//
////    @Test
////    public void convertIntentRecognitionDTO() {
////        String s = intentRecognitionService.execPrompt();
////        try {
////            IntentRecognitionDTO intentRecognitionDTO = intentRecognitionService.convertIntentRecognitionDTO(s);
////            System.out.println(intentRecognitionDTO);
////            System.out.println(JSONUtil.toJsonPrettyStr(intentRecognitionDTO));
////        } catch (JsonProcessingException e) {
////            throw new RuntimeException(e);
////        }
////    }
//}
