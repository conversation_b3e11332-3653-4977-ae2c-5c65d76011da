package com.looksky.agents.application.common;

import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class CommonRequestServiceTest {
    @Resource
    private CommonRequestService commonRequestService;

    @Test
    void test1() {
        String result = commonRequestService.commonExecuteStrategy("extract_user_tag_preference", Map.of("input", "{\"plans\":[{\"title\":\"夏日泳装\",\"products\":[{\"id\":\"12345\",\"name\":\"泳衣\"}]}]}"));
        log.info("Result: {}", result);

    }
}