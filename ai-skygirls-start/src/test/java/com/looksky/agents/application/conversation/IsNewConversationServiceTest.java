//package com.looksky.agents.application.conversation;
//
//import cn.hutool.json.JSONUtil;
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.looksky.agents.common.model.IsNewConversationDTO;
//import com.looksky.agents.application.conversation.service.IsNewConversationService;
//import com.looksky.agents.start.LookSkyApplication;
//import jakarta.annotation.Resource;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//
///**
// * @ClassName IsNewConversationServiceTest
// * @Description TODO
// * <AUTHOR>
// * @Date 2024/11/6 上午11:31
// * @Version 1.0
// **/
//@SpringBootTest(classes = LookSkyApplication.class)
//public class IsNewConversationServiceTest {
//    @Resource
//    private IsNewConversationService isNewConversationService;
//
//    @Test
//    public void test() {
//        String s = isNewConversationService.execPrompt("测试");
//        try {
//            s = "{\"isNewConversation\":true}";
//            IsNewConversationDTO isNewConversationDTO = isNewConversationService.convertDTO(s);
//            System.out.println(isNewConversationDTO);
//            System.out.println(JSONUtil.toJsonPrettyStr(isNewConversationDTO));
//        } catch (JsonProcessingException e) {
//            throw new RuntimeException(e);
//        }
//    }
//}
