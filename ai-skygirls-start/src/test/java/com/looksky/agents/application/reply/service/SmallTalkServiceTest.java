//package com.looksky.agents.application.reply.service;
//
//
//import com.looksky.agents.data.template.TemplateNamespace;
//import com.looksky.agents.infrastructure.content.MessageContext;
//import com.looksky.agents.infrastructure.entity.dto.ChatRequestDTO;
//import com.looksky.agents.start.LookSkyApplication;
//import jakarta.annotation.Resource;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import java.util.Map;
//
///**
// * @ClassName SmallTalkServiceTest
// * @Description TODO
// * <AUTHOR>
// * @Date 2024/11/7 下午7:35
// * @Version 1.0
// **/
//@SpringBootTest(classes = LookSkyApplication.class)
//public class SmallTalkServiceTest {
//
//    @Resource
//    private SmallTalkService service;
//
//    @Resource
//    private MessageContext messageContext;
//
//    @Test
//    public void test() {
//        ChatRequestDTO chatRequestDTO = new ChatRequestDTO();
//        chatRequestDTO.setUserId("1805556828908879873");
//        messageContext.initContext(Map.of(TemplateNamespace.CHAT_REQUEST_DTO.getNameSpace(), chatRequestDTO));
//        System.out.println(service.execPrompt());
//    }
//}
