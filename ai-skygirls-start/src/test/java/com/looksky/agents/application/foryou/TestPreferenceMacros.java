package com.looksky.agents.application.foryou;

import cn.hutool.core.map.MapUtil;
import com.looksky.agents.application.template.FreemarkerService;
import com.looksky.agents.data.client.service.GirlsDataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.start.LookSkyApplication;
import com.skygirls.biz.report.IosUserInfoDto;
import freemarker.template.TemplateException;
import jakarta.annotation.Resource;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 1.1.12
 **/
@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class TestPreferenceMacros {

    @Resource
    private GirlsDataService girlsDataService;

    @Resource
    private FreemarkerService freemarkerService;

    @Test
    void test1() throws TemplateException, IOException {
        String prompt = """
            <@outfitsPreferences/>
            
            ---------------------
            <@colorPreferences/>
            
            ---------------------
            <@dressingGoals/>
            
            ---------------------
            <@neverWears/>
            
            ---------------------
            <@merchantPreferences/>
            
            ---------------------
            <@vibeOutfits/>
            
            ---------------------
            <@designTowards/>
            
            ---------------------
            <@dressingGoalsExcludeSize/>
            """;

        IosUserInfoDto userInfo = girlsDataService.getUserInfo("1889516464100294656");
        Context.put(Context.Name.USER_INFO.getName(), userInfo);

        String template = freemarkerService.parseTemplate(prompt, MapUtil.empty());
        log.info("模版的结果:\n{}", template);
    }

}
