package com.looksky.agents.application;

import com.looksky.agents.application.chat.ChatBaseService;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.ext.RequestVo;
import com.looksky.agents.data.client.service.LookSkyDataService;
import com.looksky.agents.start.LookSkyApplication;
import com.skygirls.biz.im.dto.MessageRestDTO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

/**
 * @ClassName ChatBaseServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/13 下午3:28
 * @Version 1.0
 **/
@SpringBootTest(
    classes = LookSkyApplication.class
)
public class ChatBaseServiceTest {

    @Resource
    private ChatBaseService chatBaseService;

    @Resource
    private LookSkyDataService lookSkyDataService;

    RequestVo messageRestDTO = new RequestVo();

    @BeforeEach
    void setUp() {

        // {"enter_point": "search", "message_id": "18392660663573913297", "conversation_id": "c7da34cb-fadd-44ad-8b70-8b3aa43d8925", "connection_id": "epinieFyIAMCKYg=", "request_id": "24A30919-4910-4DA5-B9B7-3AC269570516", "user_id": "1848203411187720192", "time": "2024-09-25 06:45:39+00:00", "zone": "Asia/Shanghai", "event": "user_speak", "event_dict": {"text": "I don't like dresses"}, "page": "chat_box"}
        messageRestDTO.setEnterPoint("search");
        messageRestDTO.setMessageId("1861970542526476288");
        messageRestDTO.setConversationId("9cf23e68-2803-4bbf-857f-2d9c7272a39b");
        messageRestDTO.setConnectionId("B7_AIeedIAMCLmQ=");
        messageRestDTO.setRequestId("e090e484-32fc-41f2-9659-16a7c11c88d7");
        messageRestDTO.setUserId("1848203411187720192");
        messageRestDTO.setTime(LocalDateTime.now().toString());
        messageRestDTO.setZone("Asia/Shanghai");
        messageRestDTO.setEvent("user_speak");
        MessageRestDTO.EventDict eventDict = new MessageRestDTO.EventDict();
        eventDict.setText("I don't like dresses");
        messageRestDTO.setEventDict(eventDict);
        messageRestDTO.setPage("chat_box");
    }

    @Test
    void test1() {
        RequestInputDTO requestInput = chatBaseService.buildData(messageRestDTO);
        chatBaseService.asStep(requestInput);
    }


    @Test
    void test2() {


        AgentMessageResp agentSendMessageVO = new AgentMessageResp();
        agentSendMessageVO.setConnectionId(messageRestDTO.getConnectionId());
        agentSendMessageVO.setMessageId(messageRestDTO.getMessageId());
        agentSendMessageVO.setRequestId(messageRestDTO.getRequestId());
        agentSendMessageVO.setEventName("event name");
        agentSendMessageVO.setUserId(messageRestDTO.getUserId());
//        agentSendMessageVO.setShowType(strategy.getShowType());
//        agentSendMessageVO.setSubMessageIdList(subMessageIdList);
        agentSendMessageVO.setZone(messageRestDTO.getZone());
//
//        ZonedDateTime zdt = ZonedDateTime.now();
//        Date date = Date.from(zdt.toInstant());
//        agentSendMessageVO.setTime(new Date());

        agentSendMessageVO.setConnectionId(messageRestDTO.getConnectionId());
        agentSendMessageVO.setPage(messageRestDTO.getPage());
//        agentSendMessageVO.setSkcId(messageRestDTO.getEventDict().getProductId());
//        agentSendMessageVO.setProductId(messageRestDTO.getProduct().getSpuId());
        agentSendMessageVO.setStrategy("test");
//        agentSendMessageVO.setQuestionType(strategy.getQuestionType());
        agentSendMessageVO.setCode(1);
        agentSendMessageVO.setErrorMessage("no error");
        lookSkyDataService.sendAgentMessage(agentSendMessageVO);
    }
}
