package com.looksky.agents.application.chat.recommend;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.sdk.agent.search.bo.CategoriesPositiveAndNegativeQueries;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 1.1.14
 **/
@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class TestSearchRecommendService {
    @Resource
    private SearchRecommendService searchRecommendService;

    CategoriesPositiveAndNegativeQueries test1() {
        String temp = """
            {
                "categories": [
                    {
                        "category": "tops",
                        "basic_needs": {
                                "positive_preferences": "sparkly,sleeveless,sexy",
                                "negative_preferences": ""
                        },
                        "personalized_refinement": [
                            {
                                "title": "Coquette Lace-Tri<PERSON>",
                                "positive_preferences": "sparkly,sleeveless,satin,lace,sequin",
                                "negative_preferences": ""
                            },
                            {
                                "title": "Y2K-Inspired Glitter Mesh Tank",
                                "positive_preferences": "sparkly,sleeveless,cropped,mesh,glitter",
                                "negative_preferences": ""
                            },
                            {
                                "title": "Minimalist Sequin Shell Top",
                                "positive_preferences": "sparkly,sleeveless,shell,sequin,V-neck",
                                "negative_preferences": ""
                            }
                        ]
                    }
                ]
            }
            """;

        CategoriesPositiveAndNegativeQueries categoriesPositiveAndNegativeQueries = JSONUtil.toBean(temp, CategoriesPositiveAndNegativeQueries.class);
        log.info("结果: {}", JSONUtil.toJsonStr(categoriesPositiveAndNegativeQueries));
        return categoriesPositiveAndNegativeQueries;
    }

    @Test
    void test2() {
        searchRecommendService.recommendProduct(test1());
    }
}
