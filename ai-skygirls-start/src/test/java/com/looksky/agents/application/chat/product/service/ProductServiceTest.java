package com.looksky.agents.application.chat.product.service;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.data.redis.product.ProductClient;
import com.looksky.agents.sdk.product.ProductDTO;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @ClassName ProductServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/11 上午10:19
 * @Version 1.0
 **/
@SpringBootTest(classes = LookSkyApplication.class)
public class ProductServiceTest {

    @Resource
    private ProductService productService;

    @Resource
    private ProductClient productClient;

    @Test
    public void test1() {
        ProductDTO productByProductId = productClient.getProductByProductId("1722161716316200962");
        System.out.println(JSONUtil.toJsonPrettyStr(productByProductId));
    }

//    @Test
//    public void testGetProductInfo() {
//        Product productInfo = productService.getProductInfo("1722161716316200962", "1722161716886626305");
//        System.out.println(JSONUtil.toJsonPrettyStr(productInfo));
//    }
}
