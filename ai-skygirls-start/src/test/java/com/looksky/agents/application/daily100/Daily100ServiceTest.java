//package com.looksky.agents.application.daily100;
//
//import com.looksky.agents.start.LookSkyApplication;
//import com.skygirls.biz.product.dto.Daily100SearchDto;
//import jakarta.annotation.Resource;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//
///**
// * @ClassName Daily100ServiceTest
// * @Description TODO
// * <AUTHOR>
// * @Date 2024/12/9 下午10:55
// * @Version 1.0
// **/
//@SpringBootTest(classes = LookSkyApplication.class)
//public class Daily100ServiceTest {
//
//    @Resource
//    private Daily100Service daily100Service;
//
//
//    @Test
//    public void test1() {
//        Daily100SearchDto daily100SearchDto = new Daily100SearchDto();
//        daily100SearchDto.setCount(5);
//        daily100SearchDto.setOffset(0);
//        daily100SearchDto.setVersionCode("123");
//
//        daily100Service.daily100("1862103144024449024", daily100SearchDto);
//    }
//}
