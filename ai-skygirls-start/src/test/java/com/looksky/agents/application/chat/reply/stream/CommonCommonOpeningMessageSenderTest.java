package com.looksky.agents.application.chat.reply.stream;

import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = LookSkyApplication.class)
class CommonCommonOpeningMessageSenderTest {
    @Resource
    private RedissonClient redissonClient;

    @Test
    void test1() {
        String result =
            "Hey Grace, Welcome to SkyGirls! \"I'm Skylar-your go-to <PERSON> Stylist. @@ This is major! You helped your friend unlock Skygirls Pro for free (saved $49.99!), and you scored a free Color Season analysis!";

        redissonClient.getBucket("opening:home:home").set(result);
    }

    @Test
    void test2() {

        String result = """
            Upload your photo, and <PERSON><PERSON> will do a Vibe Scan 🔍 to analyze your outfit, makeup, and hairstyle—delivering expert tips to elevate your style every time! ✨
            """;

        redissonClient.getBucket("opening:search:vibe_scan").set(result);


    }

    @Test
    void test3() {

        String result = """
            We’re launching the Closed Refresh feature! 🚀 Just upload one photo of a clothing item you’re wearing, and we’ll create matching outfits and recommend perfect pairings. Stay tuned! 👀✨
            """;

        redissonClient.getBucket("opening:search:closet").set(result);


    }

}
