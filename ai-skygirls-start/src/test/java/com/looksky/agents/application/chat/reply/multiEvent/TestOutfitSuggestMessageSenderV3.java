package com.looksky.agents.application.chat.reply.multiEvent;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.sdk.agent.search.bo.OutfitSuggestionList;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 1.1.14
 **/
@SpringBootTest(classes = LookSkyApplication.class)
public class TestOutfitSuggestMessageSenderV3 {
    @Resource
    private OutfitSuggestMessageSenderV3 outfitSuggestMessageSenderV3;

    @Resource
    private ObjectMapper objectMapper;

    @Test
    void testTagsToRecommend() throws Exception {
        String result = """
            {
                "outfit_suggestions": [
                    {
                        "title": "Romantic Midi Dress Moment",
                        "category": "dresses",
                        "positive_preferences": "flowy, empire waistlines, flutter sleeves, subtle shirring at hips",
                        "negative_preferences": "",
                        "positive_style_material_fit_length_labels": "flowy, empire waistlines, midi",
                        "negative_style_material_fit_length_labels": ""
                    },
                    {
                        "title": "Cottagecore Blouse Love",
                        "category": "blouse",
                        "positive_preferences": "peasant-style, pintuck details, smocked shoulders, mini puff sleeves",
                        "negative_preferences": "",
                        "positive_style_material_fit_length_labels": "peasant-style, smocked shoulders",
                        "negative_style_material_fit_length_labels": ""
                    },
                    {
                        "title": "Playful Girly Skirt Energy",
                        "category": "skirt",
                        "positive_preferences": "tiered midi, diagonal seam lines, side slit, elasticated waistband",
                        "negative_preferences": "",
                        "positive_style_material_fit_length_labels": "tiered midi, elasticated waistband",
                        "negative_style_material_fit_length_labels": ""
                    },
                    {
                        "title": "Cool-Girl Wide-Leg Pants",
                        "category": "pants",
                        "positive_preferences": "tailored linen-blend, ultra-high waist, cropped ankle length, slanted pockets",
                        "negative_preferences": "",
                        "positive_style_material_fit_length_labels": "tailored linen-blend, ultra-high waist, cropped",
                        "negative_style_material_fit_length_labels": ""
                    },
                    {
                        "title": "Soft Knit Cardigan Layer",
                        "category": "sweater",
                        "positive_preferences": "draped open-front, bell sleeves, waist ties, heathered textures",
                        "negative_preferences": "",
                        "positive_style_material_fit_length_labels": "draped open-front, heathered textures",
                        "negative_style_material_fit_length_labels": ""
                    }
                ]
            }
            """;

        OutfitSuggestionList outfitSuggestionList = objectMapper.readValue(result, OutfitSuggestionList.class);
        //OutfitSuggestionList bean = JSONUtil.toBean(result, OutfitSuggestionList.class);
        //List<RecommendOutfitBO> recommendOutfitBOS = outfitSuggestMessageSenderV3.recommendOutfitProduct(outfitSuggestionList.getOutfitSuggestions());

    }


}
