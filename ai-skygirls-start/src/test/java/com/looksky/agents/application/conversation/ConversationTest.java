//package com.looksky.agents.application.conversation;
//
//import com.looksky.agents.data.redis.conversation.HistoryDataService;
//import com.looksky.agent.conversation.ConversationStatus;
//import com.looksky.agents.start.LookSkyApplication;
//import jakarta.annotation.Resource;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//
///**
// * @ClassName ConversationTest
// * @Description TODO
// * <AUTHOR>
// * @Date 2024/11/19 下午3:31
// * @Version 1.0
// **/
//@SpringBootTest(classes = LookSkyApplication.class)
//public class ConversationTest {
//    @Resource
//    private HistoryDataService historyDataService;
//
//    @Test
//    public void test1() {
//        historyDataService.updateStatus("1848203411187720192", "c7da34cb-fadd-44ad-8b70-8b3aa43d892225", new ConversationStatus());
//    }
//}
//
//
