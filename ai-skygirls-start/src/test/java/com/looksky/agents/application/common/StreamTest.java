package com.looksky.agents.application.common;

import com.looksky.agents.models.schema.JsonSchemaRequestBuilder;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.boot.test.context.SpringBootTest;
import reactor.core.publisher.Flux;

/**
 * @ClassName StreamTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/27 下午9:16
 * @Version 1.0
 **/
@SpringBootTest(classes = LookSkyApplication.class)
public class StreamTest {

    @Resource
    private OpenAiChatModel chatModel;

    @Test
    void test1() throws InterruptedException {
        Flux<String> responseFlux = new JsonSchemaRequestBuilder()
                .withUserMessage("给我讲个笑话")
//                .withStreamUsage(true)
                .withChatModel(chatModel)
                .executeAsync();

        responseFlux.subscribe(System.out::print);


//        Flux<String> result = chatModel.stream("给我讲个笑话");
//        result.subscribe(System.out::println);


        Thread.sleep(100000);
    }
}
