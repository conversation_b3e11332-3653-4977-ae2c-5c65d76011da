//package com.looksky.agents.application.chat.utils;
//
//import cn.hutool.json.JSONUtil;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.looksky.agents.common.model.preference.CategoryPreference;
//import com.looksky.agents.common.model.preference.PreferenceValue;
//import com.looksky.agent.preference.ExtractedEntityObject;
//import org.bson.json.JsonObject;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Nested;
//import org.junit.jupiter.api.Test;
//
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Set;
//
//import static org.junit.jupiter.api.Assertions.*;
//
///**
// * PreferenceMergeUtils的测试类
// */
//@DisplayName("偏好合并工具类测试")
//class PreferenceMergeUtilsTest {
//
//    /**
//     * 创建PreferenceValue的辅助方法
//     */
//    private PreferenceValue pv(String value) {
//        PreferenceValue pv = new PreferenceValue();
//        pv.setTextValue(value);
//        return pv;
//    }
//
//    /**
//     * 标签偏好合并相关测试
//     */
//    @Nested
//    @DisplayName("标签偏好合并测试")
//    class MergeTagPreferenceTests {
//
//        @Test
//        @DisplayName("处理当前标签为空的情况")
//        void shouldHandleNullCurrentTags() {
//            // 当前标签为null
//            Map<String, CategoryPreference.LikePreference> currTags = null;
//            Map<String, CategoryPreference.LikePreference> prevTags = new HashMap<>();
//
//            // 设置前一轮的颜色偏好
//            CategoryPreference.LikePreference prevPref = new CategoryPreference.LikePreference();
//            prevPref.setLike(Set.of(pv("红色")));
//            prevTags.put("color", prevPref);
//
//            PreferenceMergeUtils.mergeTagPreference(currTags, prevTags);
//
//
//
//            System.out.println(JSONUtil.toJsonPrettyStr(currTags));
//
//            // 验证结果
//            assertNotNull(currTags, "合并后的标签不应为空");
//            assertTrue(currTags.containsKey("color"), "应包含颜色标签");
//            assertEquals(1, currTags.get("color").getLike().size(), "应有一个喜欢的颜色");
//        }
//
//        @Test
//        @DisplayName("特殊标签合并测试")
//        void shouldMergeSpecialTagsCorrectly() {
//            Map<String, CategoryPreference.LikePreference> currTags = new HashMap<>();
//            Map<String, CategoryPreference.LikePreference> prevTags = new HashMap<>();
//
//            // 设置当前的不喜欢品类
//            CategoryPreference.LikePreference currPref = new CategoryPreference.LikePreference();
//            currPref.setLike(Set.of(pv("裙子")));
//            currTags.put("dislike_categories", currPref);
//
//            // 设置前一轮的不喜欢品类
//            CategoryPreference.LikePreference prevPref = new CategoryPreference.LikePreference();
//            prevPref.setLike(Set.of(pv("连衣裙")));
//            prevTags.put("dislike_categories", prevPref);
//
//            PreferenceMergeUtils.mergeTagPreference(currTags, prevTags);
//
//
//
//
//            // 验证特殊标签合并结果
//            CategoryPreference.LikePreference merged = currTags.get("dislike_categories");
//            assertNotNull(merged, "合并后的特殊标签不应为空");
//            assertEquals(2, merged.getLike().size(), "应包含两个不喜欢的品类");
//            assertTrue(merged.getLike().contains(pv("裙子")), "应包含当前不喜欢的品类");
//            assertTrue(merged.getLike().contains(pv("连衣裙")), "应包含历史不喜欢的品类");
//        }
//
//        @Test
//        @DisplayName("喜欢/不喜欢冲突处理测试")
//        void shouldHandleLikeDislikeConflicts() {
//            Map<String, CategoryPreference.LikePreference> currTags = new HashMap<>();
//            Map<String, CategoryPreference.LikePreference> prevTags = new HashMap<>();
//
//            // 当前轮次：喜欢绿色，不喜欢红色
//            CategoryPreference.LikePreference currPref = new CategoryPreference.LikePreference();
//            currPref.setLike(Set.of(pv("绿色")));
//            currPref.setDislike(Set.of(pv("红色")));
//            currTags.put("color", currPref);
//
//            // 前一轮：喜欢红色，不喜欢黑色
//            CategoryPreference.LikePreference prevPref = new CategoryPreference.LikePreference();
//            prevPref.setLike(Set.of(pv("红色")));
//            prevPref.setDislike(Set.of(pv("黑色")));
//            prevTags.put("color", prevPref);
//
//            PreferenceMergeUtils.mergeTagPreference(currTags, prevTags);
//
//            // 验证冲突处理结果
//            CategoryPreference.LikePreference merged = currTags.get("color");
//            assertNotNull(merged, "合并后的颜色偏好不应为空");
//            assertEquals(1, merged.getLike().size(), "应只有一个喜欢的颜色");
//            assertTrue(merged.getLike().contains(pv("绿色")), "应保留当前喜欢的颜色");
//            assertEquals(2, merged.getDislike().size(), "应有两个不喜欢的颜色");
//            assertTrue(merged.getDislike().contains(pv("红色")), "应包含当前不喜欢的颜色");
//            assertTrue(merged.getDislike().contains(pv("黑色")), "应继承历史不喜欢的颜色");
//        }
//    }
//
//    /**
//     * 偏好合并相关测试
//     */
//    @Nested
//    @DisplayName("整体偏好合并测试")
//    class MergePreferenceTests {
//
//        @Test
//        @DisplayName("通用标签合并测试")
//        void shouldMergePreferencesWithCommonTags() {
//            // 创建当前偏好和历史偏好
//            ExtractedEntityObject currentPreference = createPreferenceWithCommonTags();
//            ExtractedEntityObject previousPreference = createPreviousPreference();
//
//            // 执行合并
//            ExtractedEntityObject merged = PreferenceMergeUtils.mergePreference(currentPreference, previousPreference);
//
//            // 验证合并结果
//            CategoryPreference mergedCategoryPrefs = merged.getCategoryPreferences();
//            assertNotNull(mergedCategoryPrefs, "合并后的品类偏好不应为空");
//
//            // 验证裙子类别是否正确继承了通用标签
//            CategoryPreference.TagPreferences skirtPrefs = mergedCategoryPrefs.getCategoryPreferences().get("skirt");
//            assertNotNull(skirtPrefs, "裙子的偏好不应为空");
//            assertTrue(skirtPrefs.getTagsPreferences().get("style").getLike().contains(pv("休闲")),
//                "裙子应继承通用的风格标签");
//        }
//
//        // ... 其他测试方法的实现 ...
//
//        /**
//         * 创建带有通用标签的偏好对象
//         */
////        private ExtractedEntityObject createPreferenceWithCommonTags() {
////            CategoryPreference categoryPreference = new CategoryPreference();
////            Map<String, CategoryPreference.TagPreferences> categoryPreferences = new HashMap<>();
////
////            // 设置cloth的通用标签
////            CategoryPreference.TagPreferences clothPreferences = new CategoryPreference.TagPreferences();
////            Map<String, CategoryPreference.LikePreference> clothTags = new HashMap<>();
////
////            CategoryPreference.LikePreference stylePref = new CategoryPreference.LikePreference();
////            stylePref.setLike(Set.of(pv("休闲")));
////            clothTags.put("style", stylePref);
////
////            clothPreferences.setTagsPreferences(clothTags);
////            categoryPreferences.put("cloth", clothPreferences);
////
////            categoryPreference.setCategoryPreferences(categoryPreferences);
////
////            return new ExtractedEntityObject(
////                UserPreferenceType.CATEGORY_TAG,
////                new UserSubjectivePreference(),
////                new EcommercePreference(),
////                categoryPreference
////            );
////        }
//
//        // ... 其他辅助方法的实现 ...
//    }
//
//    private ExtractedEntityObject createPreviousPreference() {
//        String str = "{\"categoryPreferences\":{\"skirt\":{\"color\":{\"like\":[\"red\"],\"dislike\":[\"black\"]},\"fabric\":{\"like\":[\"lace\"]}},\"cloth\":{\"sleeve_length\":{\"like\":[\"long\"]},\"neckline\":{\"dislike\":[{\"neckline_detail\":\"collar\"}]},\"collar\":{\"dislike\":[\"none\"]}}},\"userSubjectivePreference\":{},\"ecommercePreference\":{}}";
//        ObjectMapper mapper = new ObjectMapper();
//        JsonObject jsonObject = new JsonObject(str);
//        return mapper.convertValue(jsonObject, ExtractedEntityObject.class);
//    }
//
//    private ExtractedEntityObject createPreferenceWithCommonTags() {
//        String str = "{\"categoryPreferences\":{\"skirt\":{\"color\":{\"like\":[\"green\"],\"dislike\":[\"red\"]},\"fabric\":{\"like\":[\"lace\"]},\"sleeve_length\":{\"like\":[\"long\"]},\"neckline\":{\"dislike\":[{\"neckline_detail\":\"collar\"},{\"neckline_detail\":\"collar\"}]},\"collar\":{\"dislike\":[\"none\",\"none\"]}}},\"userSubjectivePreference\":{},\"ecommercePreference\":{}}";
//        ObjectMapper mapper = new ObjectMapper();
//        JsonObject jsonObject = new JsonObject(str);
//        return mapper.convertValue(jsonObject, ExtractedEntityObject.class);
//
//    }
//}
