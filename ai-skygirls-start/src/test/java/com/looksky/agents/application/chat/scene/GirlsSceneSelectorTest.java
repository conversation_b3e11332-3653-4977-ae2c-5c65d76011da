//package com.looksky.agents.application.chat.scene;
//
//import cn.hutool.json.JSONUtil;
//import com.looksky.agent.conversation.Event;
//import com.looksky.agents.common.model.vo.ApiResp;
//import com.looksky.agents.common.model.vo.resp.UserPersonalCenterData;
//import com.looksky.agents.data.client.business.UserClient;
//import com.looksky.agents.infrastructure.context.Context;
//import com.looksky.agents.start.LookSkyApplication;
//import com.skygirls.biz.user.dto.MainPageDTO;
//import jakarta.annotation.Resource;
//import org.junit.jupiter.api.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.boot.test.context.SpringBootTest;
//
///**
// * @ClassName GirlsSceneSelectorTest
// * @Description TODO
// * <AUTHOR>
// * @Date 2024/12/3 上午11:54
// * @Version 1.0
// **/
//@SpringBootTest(classes = LookSkyApplication.class)
//public class GirlsSceneSelectorTest {
//
//    private static final Logger log = LoggerFactory.getLogger(GirlsSceneSelectorTest.class);
//    @Resource
//    private GirlsSceneSelector girlsSceneSelector;
//
//    @Resource
//    private Context context;
//
//    @Resource
//    private UserClient userClient;
//
//
//
//    @Test
//    public void test1() {
//        context.initContext();
//        Event event = new Event();
//        event.setContent("我想要一个圣诞节穿的晚礼服");
//        context.put(Context.Name.EVENT.getName(), event);
//
//        ApiResp<UserPersonalCenterData> response = userClient.getUserPersonalCenterData(new MainPageDTO().setUserId("1805556828908879873"));
//        UserPersonalCenterData userInfo = response.getData();
//        context.put(Context.Name.USER_PERSONAL_CENTER_DATA.getName(),userInfo);
//
//        String s = girlsSceneSelector.buildSearchProcess();
//        GirlsSceneSelector.SummaryQuery summaryQuery = girlsSceneSelector.buildPositiveNegativeQuery(s);
//        log.info("summaryQuery: {}", JSONUtil.toJsonStr(summaryQuery));
//
//    }
//
//
//}
