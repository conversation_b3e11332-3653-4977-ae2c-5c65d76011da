package com.looksky.agents.model.tool;

import com.looksky.agents.infrastructure.context.Context;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MockWeatherService implements Function<MockWeatherService.Request, MockWeatherService.Response> {

    public enum Unit { C, F }

    public record Request(@Schema(description = "这是位置") String location, @Schema(description = "这是单位") Unit unit) {
    }

    public record Response(double temp, Unit unit) {
    }

    public Response apply(Request request) {
        String message = Context.get("currentWeather");
        log.info("当前调用的函数: {}", message);
        log.info("当前位置: {}, 单位: {}", request.location, request.unit);
        return new Response(30.0, Unit.C);
    }
}