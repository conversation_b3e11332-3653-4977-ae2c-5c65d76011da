package com.looksky.agents.model.memory;

import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.models.memory.RedisChatMemoryRepository;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 1.1.12
 **/
@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class TestRedisChatMemoryRepository {
    @Resource
    private OpenAiChatModel chatModel;

    @Resource
    private RedisChatMemoryRepository redisChatMemoryRepository;

    @Test
    @DisplayName("测试 memory")
    void test3() {
        MessageWindowChatMemory inMemoryChatMemory = MessageWindowChatMemory.builder().chatMemoryRepository(redisChatMemoryRepository).maxMessages(10).build();

        var messageChatMemoryAdvisor = MessageChatMemoryAdvisor.builder(inMemoryChatMemory).conversationId("1").build();

        ChatClient chatClient = ChatClient.builder(chatModel)
            .defaultAdvisors(messageChatMemoryAdvisor)
            .defaultTools("currentWeather")
            .build();

        Context.put("currentWeather", "getCurrentWeather");

        Prompt prompt = new Prompt("What is the temperature in Shanghai now?");
        String content1 = chatClient.prompt(prompt)
            //.advisors(a -> a.param(CHAT_MEMORY_CONVERSATION_ID_KEY, 1).param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, 100))  memory 的其他实现方式, 待测试
            .call().content();
        log.info("content1:{}", content1);

        String content2 = chatClient.prompt("What is the temperature in Beijing now?").call().content();
        log.info("content2:{}", content2);


        var messageChatMemoryAdvisor1 = MessageChatMemoryAdvisor.builder(inMemoryChatMemory).conversationId("2").build();
        ChatClient chatClient1 = ChatClient.builder(chatModel)
            .defaultAdvisors(messageChatMemoryAdvisor1)
            .defaultTools("currentWeather")
            .build();

        String content3 = chatClient1.prompt("你好呀").call().content();
        log.info("content3:{}", content3);



        String content4 = chatClient.prompt(new Prompt("我问了哪几个地方的温度?"))
            //.advisors(a -> a.param(CHAT_MEMORY_CONVERSATION_ID_KEY, 1).param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, 100))  memory 的其他实现方式, 待测试
            .call().content();
        log.info("content1:{}", content4);

    }
}
