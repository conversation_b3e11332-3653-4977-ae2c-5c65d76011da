package com.looksky.agents.model.model;

import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.ai.azure.openai.AzureOpenAiChatModel;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 1.1.14
 **/
@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class TestAzureOpenAI {
    @Resource
    private AzureOpenAiChatModel azureOpenAiChatModel;

    @Test
    void test1() {
        String result = azureOpenAiChatModel.call("你好呀");
        log.info("响应结果: {}", result);
    }
}
