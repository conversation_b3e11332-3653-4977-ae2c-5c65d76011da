package com.looksky.agents.model.model;

import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.openai.api.ResponseFormat;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 1.1.14
 **/
@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class TestOpenAi {

    @Resource
    private OpenAiChatModel openAiChatModel;

    @Test
    @DisplayName("测试 Search Model")
    void testSearch() {
        OpenAiChatModel chatModel = OpenAiChatModel.builder().openAiApi(OpenAiApi.builder().apiKey("********************************************************").build()).defaultOptions(OpenAiChatOptions.builder().model("gpt-4o-search-preview").build()).build();
        String chatResponse = chatModel.call("ai 最新的新闻");
        log.info("接口返回结果: {}", chatResponse);
    }

    @Test
    @DisplayName("测试输出非严格 JSON 对象")
    void testOutputJsonObject() {
        ChatOptions chatOptions = OpenAiChatOptions.builder()
            .responseFormat(ResponseFormat.builder().type(ResponseFormat.Type.JSON_SCHEMA).jsonSchema(ResponseFormat.JsonSchema.builder()
                    //.name("Jock")
                    .schema("""
                        {
                          "type": "object",
                          "properties": {
                              "setup": {
                                  "description": "笑话的开场白",
                                  "type": "string"
                              },
                              "punchline": {
                                  "description": "笑话的笑点",
                                  "type": "string"
                              },
                              "rating": {
                                  "description": "笑话的有趣程度, 从 1 到 10",
                                  "type": "integer"
                              }
                          },
                          "required": [
                              "setup",
                              "punchline"
                          ]
                        }
                        """)
                    .strict(false)
                .build()).build())
            .build();
        Prompt prompt = new Prompt("给我一个关于猫的笑话", chatOptions);
        String text = openAiChatModel.call(prompt).getResult().getOutput().getText();
        log.info("GPT 输出的 JSON 对象: {}", text);
    }

    record Jock(String setup, String punchline, Integer rating) {}
}
