package com.looksky.agents.model;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.enums.RoleTypeEnum;
import com.looksky.agents.sdk.agent.conversation.Event;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.function.Function;
import org.junit.jupiter.api.Test;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @ClassName FunctionCallTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/6 下午7:15
 * @Version 1.0
 **/
@SpringBootTest(classes = LookSkyApplication.class)
public class FunctionCallTest {

    @Resource
    private CommonRequestService commonRequestService;

    @Resource
    private OpenAiChatModel openAiChatModel;

    @Resource
    private ObjectMapper objectMapper;


    @Test
    void test2() throws JsonProcessingException {
        Object currentWeather = OpenAiApi.ChatCompletionRequest.ToolChoiceBuilder.FUNCTION("CurrentWeather");
//        OpenAiChatOptions chatOptions = OpenAiChatOptions.builder()
//                .toolCallbacks(
//                        List.of(FunctionCallback.builder()
//                                .function("CurrentWeather", new MockWeatherService())
//                                .description("Get the weather in location") // (2) function description
//                                .inputType(MockWeatherService.Request.class) // (3) function input type
//                                .build()))
//
//                .toolChoice(OpenAiApi.ChatCompletionRequest.ToolChoiceBuilder.FUNCTION("CurrentWeather"))
////                .withToolChoice(objectMapper.writeValueAsString(toolChoice))
////                .withToolChoice(OpenAiApi.ChatCompletionRequest.ToolChoiceBuilder.NONE)
////                .withToolChoice(objectMapper.writeValueAsString(currentWeather))
//
//                .build();
//
//        //chatOptions.setProxyToolCalls(true);
//        ChatResponse chatResponse = openAiChatModel.call(new Prompt("What is the temperature in Shanghai now?", chatOptions));
//        System.out.println(chatResponse.getResult().getOutput());

    }
//    @Configuration
//    class Config {
//
//        @Bean
//        public static FunctionCallback weatherFunctionInfo() {
//
//            return FunctionCallback.builder()
//                    .description("Get the weather in location") // (2) function description
//                    .function("CurrentWeather", new MockWeatherService()) // (1) function name and instance
//                    .inputType(MockWeatherService.Request.class) // (3) function input type
//                    .build();
//        }
//
//    }
    public static class MockWeatherService implements Function<MockWeatherService.Request, MockWeatherService.Response> {

        public enum Unit { C, F }
        public record Request(String location, Unit unit) {}
        public record Response(double temp, Unit unit) {}

        public Response apply(Request request) {
            return new Response(30.0, Unit.C);
        }
    }



    @Test
    void test1() {
        HashMap<String, Object> map = new HashMap<>();
        map.put(Context.Name.EVENT.getName(), Event.builder().role(RoleTypeEnum.USER).content("give me a red dresses").build());
//        map.put(Context.Name.EVENT_LIST.getName(), Collections.singletonList(new Event()));
        map.put(Context.Name.USER_PERSONAL_CENTER_DATA.getName(), null);

        String result = commonRequestService.commonExecuteStrategy("new_query_and_is_search", map);
        System.out.println(result);

    }


}
