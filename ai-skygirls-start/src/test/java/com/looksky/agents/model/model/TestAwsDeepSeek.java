package com.looksky.agents.model.model;

import com.looksky.agents.models.schema.AwsDeepSeekRequestBuilder;
import com.looksky.agents.sdk.enums.OutputModelEnum;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.ai.bedrock.converse.BedrockProxyChatModel;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 1.1.14
 **/
@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class TestAwsDeepSeek {
    @Resource
    private BedrockProxyChatModel bedrockProxyChatModel;

    @Test
    void test1() {

        String prompt = """
            按年代帮我阐述一下中国的发展进程, 并返回一个 json, 示例如下:
            {
            "data": [
                {
                    "year": "xxx",  // 年份
                    "content": "xxx"  // 具体的大事件
                },
                {
                    "year": " xxx",
                    "content": "xxx"
                }
            ]
            """;

        String result = bedrockProxyChatModel.call(prompt);
        log.info("响应: {}", result);

    }


    @Test
    void test2() throws InterruptedException {
        Prompt prompt = new Prompt("""
            按年代帮我阐述一下中国的发展进程, 并返回一个 json, 示例如下:
            Return a single JSON object (no Markdown fencing) exactly in this structure:
            {
            "data": [
                {
                    "year": "xxx",  // 年份
                    "content": "xxx"  // 具体的大事件
                },System.out.print(t.getResult().get0utput().getText());
                {
                    "year": " xxx",
                    "content": "xxx"
                }
            ]
            }""", ChatOptions.builder().maxTokens(32000).build());
        bedrockProxyChatModel.stream(prompt).map(response -> (response.getResult() == null || response.getResult().getOutput() == null
            || response.getResult().getOutput().getText() == null) ? ""
            : response.getResult().getOutput().getText()).doOnNext(System.out::print).subscribe();

        //String content = ChatClient.builder(bedrockProxyChatModel).defaultOptions(ChatOptions.builder().maxTokens(32000).build()).build().prompt(prompt).call().content();
        //log.info("ChatClient call 响应: {}", content);
        TimeUnit.SECONDS.sleep(10);
    }

    @Test
    void test3() throws InterruptedException {
        new AwsDeepSeekRequestBuilder()
            .withChatModel(bedrockProxyChatModel)
            .withMaxTokens(31768)
            .withOutputType(OutputModelEnum.TEXT)
            .withUserMessage("中国的国运如何")
            .executeAsync().doOnNext(s -> {
                log.info("AwsDeepSeekRequestBuilder响应: {}", s);
            }).subscribe();
        TimeUnit.SECONDS.sleep(10);
    }
}
