package com.looksky.agents.model;

import static org.springframework.test.util.AssertionErrors.assertNotNull;

import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.image.ImagePrompt;
import org.springframework.ai.image.ImageResponse;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.OpenAiImageModel;
import org.springframework.ai.openai.OpenAiImageOptions;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 1.1.8
 **/
@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class ToolTest {

    @Resource
    private OpenAiImageModel openaiImageModel;

    @Resource
    private OpenAiChatModel chatModel;


    @Test
    @DisplayName("文生图")
    void test1() {
        ImageResponse response = openaiImageModel.call(
            new ImagePrompt("A light cream colored mini golden doodle",
                OpenAiImageOptions.builder()
                    .quality("hd")
                    .N(1)
                    .height(1024)
                    .width(1024).build()));

        assertNotNull("response is null", response);

        System.out.println(response.getResult().getOutput().getUrl());

    }


    @Test
    @DisplayName("测试 单个tool")
    void test2() {
        //var toolCallback = FunctionToolCallback.builder("getCurrentWeather", new MockWeatherService())
        //    .description("Get the weather in location")
        //    .inputType(MockWeatherService.Request.class)
        //    .build();

        Context.put("currentWeather", "getCurrentWeather");

        OpenAiChatOptions chatOptions = OpenAiChatOptions.builder()
            //.toolCallbacks(List.of(toolCallback))
            .toolNames(Set.of("currentWeather"))
            .build();
        Prompt prompt = new Prompt("What is the temperature in Shanghai now?", chatOptions);
        ChatResponse call = chatModel.call(prompt);
        System.out.println(call.getResult().getOutput());
    }


}
