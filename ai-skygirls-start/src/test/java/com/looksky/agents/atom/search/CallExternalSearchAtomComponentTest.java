package com.looksky.agents.atom.search;

import com.looksky.agents.application.evaluate.search.CallExternalSearchAtomComponent;
import com.looksky.agents.sdk.optimize.dto.OptimizePromptResultDTO;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class CallExternalSearchAtomComponentTest {
    @Resource
    private CallExternalSearchAtomComponent callExternalSearchAtomComponent;

    @Test
    void test1() {
        DatasetRecordDTO datasetRecord = new DatasetRecordDTO();
        HashMap<String, Object> map = new HashMap<>();
        map.put("season", "winter");
        map.put("need_search", false);
        map.put("query", "red maxi dresses full length sleeveless");
        map.put("user_id", "8");

        datasetRecord.setData(map);

        OptimizePromptResultDTO run = callExternalSearchAtomComponent.run(datasetRecord);

        log.info("结果: {}", run);
    }
}
