package com.looksky.agents.atom.search;

import com.looksky.agents.application.evaluate.search.ExtractSingleTagValueAtomComponent;
import com.looksky.agents.sdk.optimize.dto.OptimizePromptResultDTO;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class ExtractSingleTagValueAtomComponentTest {

    @Resource
    private ExtractSingleTagValueAtomComponent extractSingleTagValueAtomComponent;

    @Test
    public void test1() {
        DatasetRecordDTO datasetRecord = new DatasetRecordDTO();
        HashMap<String, Object> map = new HashMap<>();
        map.put("tagName", "fabric");
        map.put("query", "I want a pair of jeans");
        map.put("current", Map.of("tagName", "fabric", "category", "pants"));

        datasetRecord.setData(map);

        OptimizePromptResultDTO run = extractSingleTagValueAtomComponent.run(datasetRecord);

        log.info("结果: {}", run);
    }

}