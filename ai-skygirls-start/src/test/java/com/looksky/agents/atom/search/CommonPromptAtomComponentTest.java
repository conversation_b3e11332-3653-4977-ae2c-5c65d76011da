package com.looksky.agents.atom.search;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.evaluate.search.CommonPromptAtomComponent;
import com.looksky.agents.sdk.optimize.dto.OptimizePromptResultDTO;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class CommonPromptAtomComponentTest {
    @Resource
    private CommonPromptAtomComponent commonPromptAtomComponent;

    @Test
    void test1() {

        String promptName = "for_you_prefect_match_panel";

        DatasetRecordDTO datasetRecord = new DatasetRecordDTO();
        HashMap<String, Object> map = new HashMap<>();
        map.put("season", "winter");
        //map.put("current", Map.of("tagName", "fabric", "category", "pants"));
        map.put("query", "red maxi dresses full length sleeveless");
        map.put("date", "2026-01-01");
        map.put("user_id", "8");

        datasetRecord.setData(map);

        OptimizePromptResultDTO run = commonPromptAtomComponent.run(promptName, datasetRecord);

        log.info("结果: {}", JSONUtil.toJsonStr(run));
    }
}
