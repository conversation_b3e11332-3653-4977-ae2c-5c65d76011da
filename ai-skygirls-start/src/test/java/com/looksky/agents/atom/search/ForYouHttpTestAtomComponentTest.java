package com.looksky.agents.atom.search;

import com.looksky.agents.application.evaluate.recommend.ForYouHttpTestAtomComponent;
import com.looksky.agents.sdk.recommend.foryou.enums.RecommendScenEnum;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class ForYouHttpTestAtomComponentTest {
    @Resource
    private ForYouHttpTestAtomComponent forYouHttpTestAtomComponent;

    @Test
    void test1() {
        DatasetRecordDTO datasetRecord = new DatasetRecordDTO();

        datasetRecord.setData(Map.of(
            "user_id", "1869951191400550400",
            "input", "{\"boldTheme\":{\"date\":\"2023-11-02\",\"theme\":\"Ethereal Elegance\",\"recommendedDescription\":\"Embrace the ethereal charm of a white dress, a timeless wardrobe staple. Its purity and versatility allow for various styling options, from casual to formal. Pair with neutral accessories for a minimalist look, or add bold colors for a striking contrast. Ideal for showcasing your unique style, this piece complements every occasion with grace.\"},\"categoryQueries\":[{\"category\":\"White Dresses\",\"positiveQuery\":\"Minimalist, flowing fabric, versatile design.\",\"negativeQuery\":\"Avoid overly intricate patterns, heavy embellishments.\"}]}",
            "scene", RecommendScenEnum.GIRLS_PARTITION_BASIC.getName()
        ));

        //var result = forYouHttpTestAtomComponent.run(datasetRecord);
        //log.info("结果: {}", JSONUtil.toJsonStr(result));

    }

}
