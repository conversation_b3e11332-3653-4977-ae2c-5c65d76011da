package com.looksky.agents.config;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.common.utils.FeiShuSheetUtil;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.product.enums.SearchQuestionType;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class OpeningAdviceQuestionConfigLoading {

    @Resource
    private RedissonClient redissonClient;

    @Test
    void test1() {
        List<Map<String, Object>> listMaps = FeiShuSheetUtil.queryFeishuExcelDataAsMap("RG6ZslEjShNwSptrrjOcts7vnSb", "sXUVHk");
        List<String> seasonList = new ArrayList<>();
        List<String> occassionList = new ArrayList<>();
        List<String> homeList = new ArrayList<>();

        listMaps.forEach(map -> {
            String keys = (String) map.get("标签分类（最后14个是gpt创造，加了不喜欢什么款式）");
            Object value = map.get("英文原句（绿色的需要写变量）");
            String[] split = keys.split(",");
            for (String key : split) {
                if ("场合页".equals(key)) {
                    occassionList.add((String) value);
                } else if ("季节页".equals(key)) {
                    seasonList.add((String) value);
                } else if ("放首页".equals(key)) {
                    homeList.add((String) value);
                }
            }
        });

        redissonClient.getSet(EventTypeEnum.ADVICE_QUESTIONS.getType() + ":search:dressing:home").addAll(homeList);
        redissonClient.getSet(EventTypeEnum.ADVICE_QUESTIONS.getType() + ":search:dressing:season_style").addAll(seasonList);
        redissonClient.getSet(EventTypeEnum.ADVICE_QUESTIONS.getType() + ":search:dressing:occasion_outfit").addAll(occassionList);

        System.out.println(JSONUtil.toJsonStr(seasonList));
        System.out.println(JSONUtil.toJsonStr(occassionList));
        System.out.println(JSONUtil.toJsonStr(homeList));

        System.out.println(JSONUtil.toJsonStr(listMaps));
    }


    @Test
    void test2() {
        List<Map<String, Object>> listMaps = FeiShuSheetUtil.queryFeishuExcelDataAsMap("RG6ZslEjShNwSptrrjOcts7vnSb", "sXUVHk");

        //log.info("结果: {}", JSONUtil.toJsonStr(listMaps));

        Map<String, String> result = new HashMap<>();


        listMaps.forEach(map -> {
            String key = (String) map.get("英文原句（绿色的需要写变量）");
            // 计算 key 的 md5
            String md5Key = DigestUtil.md5Hex(key);

            String temp = (String) map.get("推荐的策略");
            String value;

            if ("推荐单品".equals(temp)) {
                value = SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
            } else {
                value = SearchQuestionType.SUBJECTIVE_WITHOUT_ASK_SOLUTION.getValue();

            }

            redissonClient.getBucket(RedisKeyConstants.adviceQuestionNextStep(md5Key)).set(value);

            result.put(md5Key, value);
        });

        log.info("结果: {}", JSONUtil.toJsonStr(result));


    }

}
