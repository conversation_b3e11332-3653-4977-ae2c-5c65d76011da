package com.looksky.agents.tryon;

import com.looksky.agents.application.tryon.colorseason.impl.TryOnColorSeasonServiceV4;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonParamV1;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 1.1.12
 **/
@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
public class TestTryOnColorSeasonServiceV3 {

    @Resource
    private TryOnColorSeasonServiceV4 tryOnColorSeasonServiceV4;

    @Test
    public void test1() {
        TryOnColorSeasonParamV1 paramV1 = new TryOnColorSeasonParamV1();
        paramV1.setUserId("1");
        //paramV1.setUserImage("https://d28ypdag318gi4.cloudfront.net/looksky/product/imgs/14fa66b163e2d96ddd966ee47bb0003f.jpg");
        paramV1.setUserImage("https://cdn.lookskyai.com/upload/agent/5d4cce85d3429879363d419956c348ba.jpeg");
        Object o = tryOnColorSeasonServiceV4.tryOnWhiteT(paramV1);
        log.info("换白 T 结果: {}", o);
    }
}
