package com.looksky.agents.tryon;

import com.looksky.agents.data.client.business.PyAgentClient;
import com.looksky.agents.data.redis.product.ProductClient;
import com.looksky.agents.sdk.agent.common.dto.PyAgentRedisDataDTO;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = LookSkyApplication.class)
class TestAgentRedisData {

    @Resource
    private PyAgentClient client;

    @Resource
    private ProductClient productClient;


    @Test
    void getBrandName() {
        String brandName = productClient.getBrandName("1867438810139119617");
        System.out.println(brandName);

        String shippingInfo = productClient.getShippingInfoBySkcId("1867438810139119617");
        System.out.println(shippingInfo);

        String shippingInfoByBrandName = productClient.getShippingInfoByBrandName("1822denim");
        System.out.println(shippingInfoByBrandName);
    }


    @Test
    void test1() {
        PyAgentRedisDataDTO pyAgentRedisDataDTO = client.redisData("all:br:thehalara");
        System.out.println(pyAgentRedisDataDTO);
    }

}
