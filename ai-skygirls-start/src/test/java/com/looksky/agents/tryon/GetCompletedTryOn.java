package com.looksky.agents.tryon;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.looksky.agents.data.redis.config.RedisCodecFactory;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothParam;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothStatus;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = LookSkyApplication.class)
class GetCompletedTryOn {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedisCodecFactory factory;

    @Test
    void test1() throws IOException {
        // 获取完成的试穿队列
        RBlockingQueue<SwapClothStatus> blockingQueue = redissonClient.getBlockingQueue("swap_cloth_queue:COMPLETED", factory.createCodec(SwapClothStatus.class));
        
        // 读取队列中的所有元素（不移除）
        List<SwapClothStatus> allElements = new ArrayList<>(blockingQueue);
        
        System.out.println("队列中共有 " + allElements.size() + " 个元素");
        
        // 转换为Excel导出对象
        List<SwapClothExcelData> excelDataList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        for (SwapClothStatus status : allElements) {
            SwapClothExcelData data = new SwapClothExcelData();
            data.setTraceId(status.getTraceId());
            data.setStatus(status.getStatus() != null ? status.getStatus().toString() : "");
            data.setResultUrl(status.getResultUrl());
            data.setErrorMessage(status.getErrorMessage());
            data.setCreateTime(status.getCreateTime() != null ? status.getCreateTime().format(formatter) : "");
            data.setUpdateTime(status.getUpdateTime() != null ? status.getUpdateTime().format(formatter) : "");
            data.setWorkflowResultUrl(status.getWorkflowResultUrl());
            data.setResultImage(status.getResultImage());
            data.setPromptContent(status.getPromptContent());

            SwapClothParam swapClothParam = status.getSwapClothParam();
            data.setModelImage(swapClothParam.getModelImage());
            data.setFullBodyImage(swapClothParam.getFullBodyImage());
            data.setFaceImage(swapClothParam.getFaceImage());

            
            excelDataList.add(data);
        }
        
        // 保存Excel文件
        String filePath = "tryOnCompletedData.xlsx";
        EasyExcel.write(filePath, SwapClothExcelData.class)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 自动列宽
                .sheet("试穿完成数据")
                .doWrite(excelDataList);
        
        System.out.println("Excel文件已保存至: " + System.getProperty("user.dir") + "/" + filePath);
    }
    
    /**
     * Excel导出数据模型
     */
    @Data
    public static class SwapClothExcelData {
        // 使用EasyExcel注解标注列名
        @com.alibaba.excel.annotation.ExcelProperty("追踪ID")
        private String traceId;
        
        @com.alibaba.excel.annotation.ExcelProperty("状态")
        private String status;
        
        @com.alibaba.excel.annotation.ExcelProperty("结果URL")
        private String resultUrl;
        
        @com.alibaba.excel.annotation.ExcelProperty("错误信息")
        private String errorMessage;
        
        @com.alibaba.excel.annotation.ExcelProperty("创建时间")
        private String createTime;
        
        @com.alibaba.excel.annotation.ExcelProperty("更新时间")
        private String updateTime;
        
        @com.alibaba.excel.annotation.ExcelProperty("工作流结果URL")
        private String workflowResultUrl;

        @ExcelProperty("模特图")
        private String modelImage;

        @ExcelProperty("全身照")
        private String fullBodyImage;

        @ExcelProperty("脸部图")
        private String faceImage;
        
        @com.alibaba.excel.annotation.ExcelProperty("结果图片")
        private String resultImage;
        
        @com.alibaba.excel.annotation.ExcelProperty("提示内容")
        private String promptContent;
    }
}
