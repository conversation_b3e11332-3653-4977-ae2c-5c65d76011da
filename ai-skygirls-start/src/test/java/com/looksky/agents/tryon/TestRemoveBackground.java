package com.looksky.agents.tryon;

import com.looksky.agents.application.tryon.swapcloth.background.remove.impl.RemoveBackgroundServiceV1;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class TestRemoveBackground {
    @Resource
    private RemoveBackgroundServiceV1 removeBackgroundServiceV1;

    @Test
    void test() throws InterruptedException {
        String s = removeBackgroundServiceV1.removeBackground("https://cdn.lookskyai.com/upload/agent/86adba30ed2159e73c842a2ea81d832a.jpeg");

        log.info(s);

        TimeUnit.MINUTES.sleep(5);
    }
}
