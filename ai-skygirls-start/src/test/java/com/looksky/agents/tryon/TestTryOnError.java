package com.looksky.agents.tryon;

import com.looksky.agents.application.tryon.swapcloth.FaceSwapTask;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = LookSkyApplication.class)
public class TestTryOnError {
    @Resource
    private FaceSwapTask faceSwapTask;

    @Test
    public void test1() throws InterruptedException {

        String result = faceSwapTask.fashnSwapFace("https://cdn.lookskyai.com/upload/agent/a50f0f47613c3174de9df7b0c88de9b3.jpeg",
            "https://d28ypdag318gi4.cloudfront.net/looksky/product/imgs/9df5381edba4129a761b4486bb576436.jpg");

        TimeUnit.MINUTES.sleep(1);
    }
}
