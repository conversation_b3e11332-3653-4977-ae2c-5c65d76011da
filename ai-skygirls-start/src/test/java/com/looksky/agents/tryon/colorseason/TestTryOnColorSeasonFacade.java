package com.looksky.agents.tryon.colorseason;

import com.looksky.agents.infrastructure.exception.BusinessException;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 1.1.12
 **/
@Slf4j
class TestTryOnColorSeasonFacade {

    @Test
    void test1() throws InterruptedException {
        VirtualCompletableFuture<String> future = VirtualCompletableFuture.supplyAsync(() -> {
            try {
                log.info("start");
                TimeUnit.SECONDS.sleep(10);
                log.info("end");
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            return "success";
        }).orTimeout(5, TimeUnit.SECONDS).exceptionally(throwable -> {
            Throwable cause = throwable instanceof ExecutionException ? throwable.getCause() : throwable;

            if (cause instanceof TimeoutException) {
                log.error("超时了");
                throw new BusinessException(cause.getMessage());
            } else {
                log.error("异常", cause);
                throw new BusinessException(cause.getMessage());
            }
        });
        log.info(future.join());

        TimeUnit.SECONDS.sleep(10);

        log.info("close");

    }
}
