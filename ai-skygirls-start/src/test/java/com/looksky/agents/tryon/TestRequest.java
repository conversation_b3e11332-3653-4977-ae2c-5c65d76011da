package com.looksky.agents.tryon;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.data.client.business.TryOnClient;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.SwapClothStatusRequest;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.SwapClothWorkflowRequest;
import com.looksky.agents.sdk.tryon.swapcloth.enums.SwapClothStatusEnum;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class TestRequest {


    @Resource
    private TryOnClient   tryOnClient;

    @Test
    void test() {
        String temp = "{\"new_face_image\":\"https://cdn.lookskyai.com/upload/agent/c986b91de4c7f84b112896cdbd66f862.jpeg\",\"new_cloth_image\":\"https://cdn.lookskyai.com/upload/agent/59735d901b2f5ddf82aac8dbedbeed6f.png\",\"new_prompt_text\":\"1The individual is dressed in a comfortable beige sweatshirt that features a large graphic and text. The cuffs and neck of the sweatshirt are in a contrasting dark color. They have paired it with fitted black capri pants that complement their figure. They are carrying a black handbag adorned with decorative keychains. Their footwear consists of black flat shoes with strap details. The person stands in a relaxed posture, with one hand casually tucked into a pocket. The setting features a neutral gray background with soft, even lighting that gently highlights the scene without creating harsh shadows.\"}";


        //String body = HttpRequest.post("https://ranran3623--tryon-dev.modal.run").body(temp).execute().body();
        //System.out.println(body);

        SwapClothWorkflowRequest bean = JSONUtil.toBean(temp, SwapClothWorkflowRequest.class);
        //String resutl = tryOnClient.swapClothWorkflow(bean);
        //String resutl = client.swapClothWorkflow(bean);
        //System.out.println(resutl);

    }

        @Test
    void test1() {
        String temp = "{\"new_face_image\":\"https://cdn.lookskyai.com/upload/agent/c986b91de4c7f84b112896cdbd66f862.jpeg\",\"new_cloth_image\":\"https://cdn.lookskyai.com/upload/agent/59735d901b2f5ddf82aac8dbedbeed6f.png\",\"new_prompt_text\":\"The individual is dressed in a comfortable beige sweatshirt that features a large graphic and text. The cuffs and neck of the sweatshirt are in a contrasting dark color. They have paired it with fitted black capri pants that complement their figure. They are carrying a black handbag adorned with decorative keychains. Their footwear consists of black flat shoes with strap details. The person stands in a relaxed posture, with one hand casually tucked into a pocket. The setting features a neutral gray background with soft, even lighting that gently highlights the scene without creating harsh shadows.\"}";


        //String body = HttpRequest.post("https://ranran3623--tryon-dev.modal.run").body(temp).execute().body();
        //System.out.println(body);

        SwapClothWorkflowRequest bean = JSONUtil.toBean(temp, SwapClothWorkflowRequest.class);
        SwapClothStatusRequest resutl = tryOnClient.swapClothWorkflowAsync(bean);
        //String resutl = client.swapClothWorkflow(bean);
        System.out.println(resutl);
        System.out.println(JSONUtil.toJsonStr(resutl));

    }

       @Test
    void test2() {
        String temp = "{\"promptId\":\"a265920d-0c82-42c3-88cd-61ea8bcd926c\",\"clientId\":\"80b1427f181a4217ad4b607caac249ba\"}";


        //String body = HttpRequest.post("https://ranran3623--tryon-dev.modal.run").body(temp).execute().body();
        //System.out.println(body);

        SwapClothStatusRequest bean = JSONUtil.toBean(temp, SwapClothStatusRequest.class);
        Object resutl = tryOnClient.swapClothStatus(bean.getClientId(), bean.getPromptId());
        //String resutl = client.swapClothWorkflow(bean);
        System.out.println(resutl);
        System.out.println(JSONUtil.toJsonStr(resutl));

    }

    public static void main(String[] args) {
        boolean queueState = SwapClothStatusEnum.QUEUED_FOR_GENERATION.isQueueState();
        System.out.println(queueState);
    }
}
