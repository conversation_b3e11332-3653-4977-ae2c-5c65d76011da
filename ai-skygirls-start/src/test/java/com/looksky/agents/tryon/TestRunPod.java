package com.looksky.agents.tryon;

import com.looksky.agents.application.tryon.swapcloth.generation.RunPodWorkflowService;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = LookSkyApplication.class)
class TestRunPod {
    @Resource
    private RunPodWorkflowService runPodWorkflowService;

    @Test
    void test1() {
        //String run = runPodWorkflowService.run(
        //    "She had Medium Golden Honey skin, and Square face, no glasses face ,with Shoulder-length, wavy, dark brown, natural  ((normal build 1.3)),((Body shape: Rectangle 1.2)) ,  with straight shoulders, lean waist, balanced proportions The person is wearing a fitted, strapless, blue dress with a straight neckline and a side slit extending above the knee. The dress features no embellishments or patterns and has a smooth texture. They are accessorized with simple hoop earrings. The person's pose is standard, standing with arms relaxed at the sides. The background is a solid color e-commerce backdrop, featuring no additional elements, while lighting is even, highlighting the garment's color and fit.",
        //    "https://cdn.lookskyai.com/upload/agent/05fefa4d99bd820841508dfeeec0da63.jpeg", "https://d28ypdag318gi4.cloudfront.net/looksky/product/imgs/9df5381edba4129a761b4486bb576436.jpg");
        //
        //System.out.println(run);
    }
}
