package com.looksky.agents.tryon;

import com.looksky.agents.application.tryon.swapcloth.changecolor.MeiTuChangeClotheColorService;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 1.1.8
 **/
@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
class MeiTuTest {
    @Resource
    private MeiTuChangeClotheColorService changeClotheColorService;

    @Test
    void test1() {
        //String result = changeClotheColorService.changeClothColor("https://d28ypdag318gi4.cloudfront.net/upload/agent/4f9c9b24d5d754152309393f9519c312.jpeg", "#ADD8E6");
        //log.info("换色结果: {}", result);
    }
}
