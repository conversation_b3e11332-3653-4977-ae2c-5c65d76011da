package temp;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.sdk.recommend.common.dto.UserPageKibbeCacheDataDTO;
import com.looksky.agents.data.client.service.GirlsDataService;
import com.looksky.agents.data.redis.daily100.Daily100DataService;
import com.looksky.agents.start.LookSkyApplication;
import com.skygirls.biz.report.IosUserInfoDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @ClassName KbCacheDataTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/24 下午3:46
 * @Version 1.0
 **/
@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
public class KbCacheDataTest {

    @Resource
    private GirlsDataService girlsDataService;

    @Resource
    private Daily100DataService daily100DataService;

    @Test
    void test1() {
        IosUserInfoDto userInfo = girlsDataService.getUserInfo("1864276787028647936");
        UserPageKibbeCacheDataDTO kbType = daily100DataService.getKbType(userInfo.getAppUser().getKibbeType().getCode());
        log.info("kbType: {}", JSONUtil.toJsonStr(kbType));
    }
}
