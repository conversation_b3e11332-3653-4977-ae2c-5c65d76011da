package temp;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.sdk.common.dto.CommonModelRequestDTO;
import com.looksky.agents.start.LookSkyApplication;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.ai.azure.openai.AzureOpenAiChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = LookSkyApplication.class)
public class Test7 {

    @Resource
    private AzureOpenAiChatModel chatModel;


    @Resource
    private CommonRequestService commonRequestService;

    @Test
    public void test1() {
        System.out.println("开始咯");
        Prompt prompt = new Prompt("您好呀");
        ChatResponse call = chatModel.call(prompt);

        System.out.println(call.getResult().getOutput().getText());
    }


    @Test
    public void test2() {

        String json = """
            {
                "strategyName": "girls_scen",
                "variable": {
                    "userRegistrationFillsOutData": {
                        "overallFacialFeaturesSummary": "SWEET_AND_APPROACHABLE",
                        "colorSeason": "LIGHT_SPRING",
                        "seasonalColorOutfitAdvice": "As a Light Spring, your ideal color palette is filled with soft, warm, and light hues that harmonize with your natural coloring. Consider embracing pastel shades like peach, soft coral, and light turquoise to enhance your natural glow. These colors can bring a fresh and youthful vibrance to your look. Avoid overly dark or muted colors, as they might overshadow your light and airy essence, making your features appear less lively.",
                        "kibbeStyle": "SOFT_GAMINE",
                        "age": "20",
                        "weight": "125 lbs",
                        "height": "5' 2\\"",
                        "faceShape": "ROUND",
                        "skinTone": "white",
                        "skinUndertone": "WARM",
                        "skinDepth": "LIGHT",
                        "hairColor": "BLONDE",
                        "pupilColor": "BLUE",
                        "girlsEyeShape": "Almond_Eyes",
                        "girlsNose": "Greek_Nose",
                        "girlsMouth": "bow_shaped_lips",
                        "eyebrowDistribution": "MEDIUM_DENSITY",
                        "eyebrow": "Arched_Brows"
                    }
                },
                "images": [
                    "https://cdn.lookskyai.com/upload/agent/0e56af4d0fd57c40e76e5ab813f90e41.jpeg",
                    "https://cdn.lookskyai.com/upload/agent/9cee4981c4c4c858f73674935f20cba5.jpeg",
                    "https://cdn.lookskyai.com/upload/agent/5b41ef01086dd7bdfda30f60c53bfeb4.jpeg"
                ]
            }
            """;
        CommonModelRequestDTO bean = JSONUtil.toBean(json, CommonModelRequestDTO.class);
        String s = commonRequestService.commonRequestModelWithText(bean);
        System.out.println("================结果================");
        System.out.println(s);
    }

}
