package temp;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ooxml.POIXMLDocumentPart;
import org.apache.poi.ss.usermodel.PictureData;
import org.apache.poi.xssf.usermodel.*;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.FileInputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @ClassName Test4
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/25 下午3:39
 * @Version 1.0
 **/
@Slf4j
public class Test4 {
    private static final String API_URL = "https://test-api.looksky.ai/ai-agents/common/strategy";
    private static final String UPLOAD_URL = "https://test-cms.westyle.ai/backend/file/upload";
    private final RestTemplate restTemplate = new RestTemplate();
    final int TARGET_COLUMN = 13; // 目标列号

    @Test
    public void test1() throws Exception {
        // 指定Excel文件路径
        String fileName = "/Users/<USER>/Desktop/star_1.xlsx";

        FileInputStream fis = new FileInputStream(fileName);
//        byte[] bytes = IoUtil.readBytes(fis);
//        InputStream workbookStream = new ByteArrayInputStream(bytes);

//        Map<String, PictureData> picMap = ExcelPicUtil.getPicMap(
//                WorkbookFactory.create(workbookStream),
//                0
//        );

        XSSFWorkbook workbook = new XSSFWorkbook(fis);
        XSSFSheet sheet = workbook.getSheetAt(0);

        // 存储图片映射
        Map<String, PictureData> picMap = new HashMap<>();

        // 获取所有图片
        List<XSSFPictureData> allPictures = workbook.getAllPictures();
        if (!allPictures.isEmpty()) {
            for (POIXMLDocumentPart.RelationPart relationPart : sheet.getRelationParts()) {
                POIXMLDocumentPart documentPart = relationPart.getDocumentPart();
                if (documentPart instanceof XSSFDrawing) {
                    XSSFDrawing drawing = (XSSFDrawing) documentPart;
                    List<XSSFShape> shapes = drawing.getShapes();
                    for (XSSFShape shape : shapes) {
                        if (shape instanceof XSSFPicture) {


                            XSSFPicture picture = (XSSFPicture) shape;
                            XSSFClientAnchor anchor = (XSSFClientAnchor) picture.getAnchor();

                            // 只处理第15列的图片
                            if (anchor.getCol1() == TARGET_COLUMN) {
                                String key = anchor.getRow1() + "_" + anchor.getCol1();
                                picMap.put(key, picture.getPictureData());
                                log.info("找到第 {} 行的图片", anchor.getRow1());
                            }

//                            // 使用行号和列号作为key
//                            String key = anchor.getRow1() + "_" + anchor.getCol1();
//                            picMap.put(key, picture.getPictureData());
                        }
                    }
                }
            }
        }


        // 读取Excel文件
//        List<ExcelData> list = EasyExcel.read(fileName)
//                .head(ExcelData.class)
//                .sheet()
//                .doReadSync();


        List<ExcelData> successList = new ArrayList<>();
        List<ExcelData> failList = new ArrayList<>();

        EasyExcel.read(fileName, ExcelData.class, new AnalysisEventListener<ExcelData>() {
            @Override
            public void invoke(ExcelData data, AnalysisContext context) {
                try {
                    // 获取当前行号
                    int rowIndex = context.readRowHolder().getRowIndex();
                    log.info("处理第 {} 行数据", rowIndex);

                    // 3.1 处理图片上传
                    PictureData pictureData = picMap.get(rowIndex + "_" + TARGET_COLUMN);
                    if (pictureData != null) {
                        String imageUrl = uploadImage(pictureData);
                        data.setUrl(imageUrl);


                        CompletableFuture<JSONObject> facialFeaturesFuture = CompletableFuture.supplyAsync(
                                () -> getFacialFeatures(imageUrl)
                        );

                        CompletableFuture<JSONObject> faceShapeFuture = CompletableFuture.supplyAsync(
                                () -> getFaceShape(imageUrl)
                        );

                        try {
                            JSONObject features = facialFeaturesFuture.get();
                            if (features != null) {
                                fillFacialFeatures(data, features);
                            }

                            JSONObject faceShapeFeatures = faceShapeFuture.get();
                            if (faceShapeFeatures != null) {
                                fillFaceShape(data, faceShapeFeatures);
                            }
                        } catch (Exception e) {
                            log.error("并行处理请求失败", e);
                            throw e;
                        }
//
//                        // 3.2 获取面部特征
//                        JSONObject features = getFacialFeatures(imageUrl);
//                        if (features != null) {
//                            fillFacialFeatures(data, features);
//                        }
//
//                        // 3.3 获取面部形状
//                        JSONObject faceShapeFeatures = getFaceShape(imageUrl);
//                        if (faceShapeFeatures != null) {
//                            fillFaceShape(data, faceShapeFeatures);
//                        }

                        // 3.4 获取Kibbe报告
                        JSONObject kibbeReport = getKibbeReport(data);
                        if (kibbeReport != null) {
                            fillKibbeReport(data, kibbeReport);
                        }

                        successList.add(data);
                    } else {
                        log.warn("行 {} 没有找到对应的图片", rowIndex);
                        failList.add(data);
                    }

                    Thread.sleep(1000); // 请求间隔
                } catch (Exception e) {
                    log.error("处理行 {} 失败: {}", context.readRowHolder().getRowIndex(), data.getFullName(), e);
                    failList.add(data);
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("Excel处理完成，成功：{}，失败：{}", successList.size(), failList.size());

                // 4. 写入处理结果
                String outputFileName = "/Users/<USER>/Desktop/" + "star_output1.xlsx";
                EasyExcel.write(outputFileName, ExcelData.class)
                        .sheet("处理结果")
                        .doWrite(() -> {
                            List<ExcelData> allData = new ArrayList<>();
                            allData.addAll(successList);
                            allData.addAll(failList);
                            return allData;
                        });
            }
        }).sheet().doRead();
    }


    private String uploadImage(PictureData pictureData) {
        String md5 = SecureUtil.md5(new String(pictureData.getData()));
        String fileName = "star/" + md5 + ".jpg";
        String expectedUrl = "https://cdn.lookskyai.com/" + fileName;
        try {

            try {
                restTemplate.headForHeaders(expectedUrl);
                // 如果没有抛出异常，说明图片存在
                log.info("图片已存在: {}", expectedUrl);
                return expectedUrl;
            } catch (Exception e) {
                log.info("图片不存在，准备上传: {}", fileName);
            }

            // 创建文件资源
            ByteArrayResource fileResource = new ByteArrayResource(pictureData.getData()) {
                @Override
                public String getFilename() {
                    return fileName;
                }
            };

            // 构建表单数据
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", fileResource);
            body.add("fileName", fileName);
            body.add("wrapper", false);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 创建请求实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送请求
            String response = restTemplate.postForObject(UPLOAD_URL, requestEntity, String.class);
            JSONObject jsonResponse = JSON.parseObject(response);
            return jsonResponse.getString("data");
        } catch (Exception e) {
            log.error("图片上传失败: {}", fileName, e);
            throw new RuntimeException("图片上传失败", e);
        }
    }


    private JSONObject getFacialFeatures(String imageUrl) {
        return sendRequest(imageUrl, "Facial_features");
    }

    private JSONObject getFaceShape(String imageUrl) {
        return sendRequest(imageUrl, "Facial_shape");
    }

    private JSONObject getKibbeReport(ExcelData data) {
        log.info("请求Kibbe报告API");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        Map<String, Object> userRegistrationData = new HashMap<>();
        userRegistrationData.put("name", data.getFullName());
        userRegistrationData.put("age", data.getAge());
        userRegistrationData.put("weight", data.getWeight() + " kg");
        userRegistrationData.put("height", data.getHeight() + " cm");
        userRegistrationData.put("weightType", data.getWeightType());
        userRegistrationData.put("heightType", data.getHeightType());
        userRegistrationData.put("bodyShape", data.getBodyShape());
        userRegistrationData.put("jawlineShape", data.getJawlineShape());
        userRegistrationData.put("faceShape", data.getFaceShape());
        userRegistrationData.put("cheekboneType", data.getCheekboneType());
        userRegistrationData.put("skinTone", data.getSkinTone());
        userRegistrationData.put("skinUndertone", data.getSkinUndertone());
        userRegistrationData.put("skinDepth", data.getSkinDepth());
        userRegistrationData.put("hairTone", data.getHairTone());
        userRegistrationData.put("hairColor", data.getHairColor());
        userRegistrationData.put("pupilColor", data.getPupilColor());
        userRegistrationData.put("pupilTone", data.getPupilTone());
        userRegistrationData.put("eyeShape", data.getEyeShape());

        Map<String, Object> variable = new HashMap<>();
        variable.put("userRegistrationFillsOutData", userRegistrationData);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("strategyName", "Kibbe_Report");
        requestBody.put("variable", variable);

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

        try {
            String response = restTemplate.postForObject(API_URL, request, String.class);
            return JSON.parseObject(response).getJSONObject("data");
        } catch (Exception e) {
            System.err.println("Kibbe报告API请求失败: " + e.getMessage());
            return null;
        }
    }

    private JSONObject sendRequest(String imageUrl, String strategyName) {
        log.info("请求API: {}", strategyName);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        Map<String, Object> requestBody = Map.of(
                "images", Collections.singletonList(imageUrl),
                "strategyName", strategyName
        );

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

        try {
            String response = restTemplate.postForObject(API_URL, request, String.class);
            JSONObject jsonResponse = JSON.parseObject(response);
            return jsonResponse.getJSONObject("data").getJSONObject("features");
        } catch (Exception e) {
            System.err.println("API请求失败 [" + strategyName + "]: " + e.getMessage());
            return null;
        }
    }

    private void fillFacialFeatures(ExcelData data, JSONObject features) {
        data.setJawlineShape(features.getString("jawlineShape"));
        data.setCheekboneType(features.getString("cheekboneType"));
        data.setSkinTone(features.getString("skinTone"));
        data.setSkinUndertone(features.getString("skinUndertone"));
        data.setSkinDepth(features.getString("skinDepth"));
        data.setHairTone(features.getString("hairTone"));
        data.setHairColor(features.getString("hairColor"));
        data.setPupilColor(features.getString("pupilColor"));
        data.setPupilTone(features.getString("pupilTone"));
        data.setEyeShape(features.getString("eyeShape"));
        data.setNoseType(features.getString("noseType"));
        data.setMouthType(features.getString("mouthType"));
    }

    private void fillFaceShape(ExcelData data, JSONObject features) {
        data.setFaceShape(features.getString("faceShape"));
    }

    private void fillKibbeReport(ExcelData data, JSONObject report) {
        // 填充colorSeason和kibbeStyle
        data.setColorSeason(report.getString("colorSeason"));
        data.setKibbleStyle(report.getString("kibbeStyle"));
    }
}
