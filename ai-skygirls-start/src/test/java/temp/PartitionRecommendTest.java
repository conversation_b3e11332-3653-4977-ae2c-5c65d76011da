//package temp;
//
//import cn.hutool.json.JSONUtil;
//import com.looksky.recommend.foryou.dto.ForYouParam;
//import com.looksky.recommend.foryou.dto.PartitionDailyResponseDTO;
//import com.looksky.agents.data.grpc.PartitionRecommendGrpcClient;
//import com.looksky.agents.start.LookSkyApplication;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//
//@Slf4j
//@SpringBootTest(classes = LookSkyApplication.class)
//class PartitionRecommendTest {
//    @Resource
//    private PartitionRecommendGrpcClient partitionRecommendGrpcClient;
//
//    @Test
//    void test1() {
//        String request = "{\"userId\":\"1862103144024449024\",\"clientDayTime\":\"2025-02-27\",\"partitionRecomModels\":[{\"partitionRecomScenes\":\"girlsPartitionMatch\",\"recallVectors\":[{\"vectorQueries\":[{\"text\":\"coral pink A-line midi dress, ruffle details, short sleeves, V-neckline\",\"weight\":1},{\"text\":\"stripes, sheer, snakeskin print, plaid\",\"weight\":-1}],\"recallStrategy\":\"midi dress\",\"mustSubCategory\":[\"midi dress\"]},{\"vectorQueries\":[{\"text\":\"sky blue fitted T-shirt, sweetheart neckline, short sleeves, waist-length\",\"weight\":1},{\"text\":\"sheer, loose fit, cropped, high neck\",\"weight\":-1}],\"recallStrategy\":\"t shirt\",\"mustSubCategory\":[\"t shirt\"]},{\"vectorQueries\":[{\"text\":\"olive green blouse, draped silhouette, boat neckline, three-quarter sleeves\",\"weight\":1},{\"text\":\"exaggerated sleeves, snakeskin print, cropped, high neck\",\"weight\":-1}],\"recallStrategy\":\"blouse\",\"mustSubCategory\":[\"blouse\"]},{\"vectorQueries\":[{\"text\":\"light taupe straight-leg pants, mid-rise, ankle-length\",\"weight\":1},{\"text\":\"snakeskin print, plaid\",\"weight\":-1}],\"recallStrategy\":\"pants\",\"mustSubCategory\":[\"pants\"]}]},{\"partitionRecomScenes\":\"girlsPartitionTry\",\"recallVectors\":[{\"vectorQueries\":[{\"text\":\"coral pink A-line midi dress, ruffle details, short sleeves, V-neckline\",\"weight\":1},{\"text\":\"stripes, sheer, snakeskin print, plaid\",\"weight\":-1}],\"recallStrategy\":\"midi dress\",\"mustSubCategory\":[\"midi dress\"]},{\"vectorQueries\":[{\"text\":\"sky blue fitted T-shirt, sweetheart neckline, short sleeves, waist-length\",\"weight\":1},{\"text\":\"sheer, loose fit, cropped, high neck\",\"weight\":-1}],\"recallStrategy\":\"t shirt\",\"mustSubCategory\":[\"t shirt\"]},{\"vectorQueries\":[{\"text\":\"olive green blouse, draped silhouette, boat neckline, three-quarter sleeves\",\"weight\":1},{\"text\":\"exaggerated sleeves, snakeskin print, cropped, high neck\",\"weight\":-1}],\"recallStrategy\":\"blouse\",\"mustSubCategory\":[\"blouse\"]},{\"vectorQueries\":[{\"text\":\"light taupe straight-leg pants, mid-rise, ankle-length\",\"weight\":1},{\"text\":\"snakeskin print, plaid\",\"weight\":-1}],\"recallStrategy\":\"pants\",\"mustSubCategory\":[\"pants\"]}]},{\"partitionRecomScenes\":\"girlsPartitionBrand\",\"recallVectors\":[{\"vectorQueries\":[{\"text\":\"coral pink A-line midi dress, ruffle details, short sleeves, V-neckline\",\"weight\":1},{\"text\":\"stripes, sheer, snakeskin print, plaid\",\"weight\":-1}],\"recallStrategy\":\"midi dress\",\"mustSubCategory\":[\"midi dress\"]},{\"vectorQueries\":[{\"text\":\"sky blue fitted T-shirt, sweetheart neckline, short sleeves, waist-length\",\"weight\":1},{\"text\":\"sheer, loose fit, cropped, high neck\",\"weight\":-1}],\"recallStrategy\":\"t shirt\",\"mustSubCategory\":[\"t shirt\"]},{\"vectorQueries\":[{\"text\":\"olive green blouse, draped silhouette, boat neckline, three-quarter sleeves\",\"weight\":1},{\"text\":\"exaggerated sleeves, snakeskin print, cropped, high neck\",\"weight\":-1}],\"recallStrategy\":\"blouse\",\"mustSubCategory\":[\"blouse\"]},{\"vectorQueries\":[{\"text\":\"light taupe straight-leg pants, mid-rise, ankle-length\",\"weight\":1},{\"text\":\"snakeskin print, plaid\",\"weight\":-1}],\"recallStrategy\":\"pants\",\"mustSubCategory\":[\"pants\"]}]},{\"partitionRecomScenes\":\"girlsPartitionFestival\",\"recallVectors\":[{\"vectorQueries\":[{\"text\":\"coral pink A-line midi dress, ruffle details, short sleeves, V-neckline\",\"weight\":1},{\"text\":\"stripes, sheer, snakeskin print, plaid\",\"weight\":-1}],\"recallStrategy\":\"midi dress\",\"mustSubCategory\":[\"midi dress\"]},{\"vectorQueries\":[{\"text\":\"sky blue fitted T-shirt, sweetheart neckline, short sleeves, waist-length\",\"weight\":1},{\"text\":\"sheer, loose fit, cropped, high neck\",\"weight\":-1}],\"recallStrategy\":\"t shirt\",\"mustSubCategory\":[\"t shirt\"]},{\"vectorQueries\":[{\"text\":\"olive green blouse, draped silhouette, boat neckline, three-quarter sleeves\",\"weight\":1},{\"text\":\"exaggerated sleeves, snakeskin print, cropped, high neck\",\"weight\":-1}],\"recallStrategy\":\"blouse\",\"mustSubCategory\":[\"blouse\"]},{\"vectorQueries\":[{\"text\":\"light taupe straight-leg pants, mid-rise, ankle-length\",\"weight\":1},{\"text\":\"snakeskin print, plaid\",\"weight\":-1}],\"recallStrategy\":\"pants\",\"mustSubCategory\":[\"pants\"]}]},{\"partitionRecomScenes\":\"girlsPartitionBasic\",\"recallVectors\":[{\"vectorQueries\":[{\"text\":\"coral pink A-line midi dress, ruffle details, short sleeves, V-neckline\",\"weight\":1},{\"text\":\"stripes, sheer, snakeskin print, plaid\",\"weight\":-1}],\"recallStrategy\":\"midi dress\",\"mustSubCategory\":[\"midi dress\"]},{\"vectorQueries\":[{\"text\":\"sky blue fitted T-shirt, sweetheart neckline, short sleeves, waist-length\",\"weight\":1},{\"text\":\"sheer, loose fit, cropped, high neck\",\"weight\":-1}],\"recallStrategy\":\"t shirt\",\"mustSubCategory\":[\"t shirt\"]},{\"vectorQueries\":[{\"text\":\"olive green blouse, draped silhouette, boat neckline, three-quarter sleeves\",\"weight\":1},{\"text\":\"exaggerated sleeves, snakeskin print, cropped, high neck\",\"weight\":-1}],\"recallStrategy\":\"blouse\",\"mustSubCategory\":[\"blouse\"]},{\"vectorQueries\":[{\"text\":\"light taupe straight-leg pants, mid-rise, ankle-length\",\"weight\":1},{\"text\":\"snakeskin print, plaid\",\"weight\":-1}],\"recallStrategy\":\"pants\",\"mustSubCategory\":[\"pants\"]}]},{\"partitionRecomScenes\":\"girlsPartitionTrend\",\"recallVectors\":[{\"vectorQueries\":[{\"text\":\"coral pink A-line midi dress, ruffle details, short sleeves, V-neckline\",\"weight\":1},{\"text\":\"stripes, sheer, snakeskin print, plaid\",\"weight\":-1}],\"recallStrategy\":\"midi dress\",\"mustSubCategory\":[\"midi dress\"]},{\"vectorQueries\":[{\"text\":\"sky blue fitted T-shirt, sweetheart neckline, short sleeves, waist-length\",\"weight\":1},{\"text\":\"sheer, loose fit, cropped, high neck\",\"weight\":-1}],\"recallStrategy\":\"t shirt\",\"mustSubCategory\":[\"t shirt\"]},{\"vectorQueries\":[{\"text\":\"olive green blouse, draped silhouette, boat neckline, three-quarter sleeves\",\"weight\":1},{\"text\":\"exaggerated sleeves, snakeskin print, cropped, high neck\",\"weight\":-1}],\"recallStrategy\":\"blouse\",\"mustSubCategory\":[\"blouse\"]},{\"vectorQueries\":[{\"text\":\"light taupe straight-leg pants, mid-rise, ankle-length\",\"weight\":1},{\"text\":\"snakeskin print, plaid\",\"weight\":-1}],\"recallStrategy\":\"pants\",\"mustSubCategory\":[\"pants\"]}]},{\"partitionRecomScenes\":\"girlsPartitionDiscount\",\"recallVectors\":[{\"vectorQueries\":[{\"text\":\"coral pink A-line midi dress, ruffle details, short sleeves, V-neckline\",\"weight\":1},{\"text\":\"stripes, sheer, snakeskin print, plaid\",\"weight\":-1}],\"recallStrategy\":\"midi dress\",\"mustSubCategory\":[\"midi dress\"]},{\"vectorQueries\":[{\"text\":\"sky blue fitted T-shirt, sweetheart neckline, short sleeves, waist-length\",\"weight\":1},{\"text\":\"sheer, loose fit, cropped, high neck\",\"weight\":-1}],\"recallStrategy\":\"t shirt\",\"mustSubCategory\":[\"t shirt\"]},{\"vectorQueries\":[{\"text\":\"olive green blouse, draped silhouette, boat neckline, three-quarter sleeves\",\"weight\":1},{\"text\":\"exaggerated sleeves, snakeskin print, cropped, high neck\",\"weight\":-1}],\"recallStrategy\":\"blouse\",\"mustSubCategory\":[\"blouse\"]},{\"vectorQueries\":[{\"text\":\"light taupe straight-leg pants, mid-rise, ankle-length\",\"weight\":1},{\"text\":\"snakeskin print, plaid\",\"weight\":-1}],\"recallStrategy\":\"pants\",\"mustSubCategory\":[\"pants\"]}]}]}";
//
//        ForYouParam bean = JSONUtil.toBean(request, ForYouParam.class);
//        PartitionDailyResponseDTO partitionDailyResponseDTO = partitionRecommendGrpcClient.partitionRecommend(bean);
//        log.info("girlsPartitionDailyResponseDTO: {}", JSONUtil.toJsonStr(partitionDailyResponseDTO));
//    }
//}
