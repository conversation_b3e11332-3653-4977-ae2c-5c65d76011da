package temp;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;
import java.util.Map;

/**
 * @ClassName Test5
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/28 下午6:06
 * @Version 1.0
 **/
public class Test5 {
    public static void main(String[] args) throws Exception {
        String json = "{\"text\":\"Based on your preferences, we've selected the best tags for you. We hope you like them!\",\"sleeve_shape\":[\"bishop_sleeve\",\"puff_sleeve\",\"bell_sleeve\",\"kimono_sleeve\",\"regular\"],\"cut\":[\"mermaid\",\"sheath\",\"a-line\",\"trapeze\",\"shift\"],\"trims\":[\"lace\",\"rhinestones\",\"3d_floral\",\"pearl\",\"sequin\"]}";

        ObjectMapper objectMapper = new ObjectMapper();
        TagSelectionResponse response = objectMapper.readValue(json, TagSelectionResponse.class);

        System.out.println(response.getText());
        System.out.println(response.getTags());
    }

    public static class TagSelectionResponse {
        private String text;
        private Map<String, List<String>> tags;

        // Getters and Setters
        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public Map<String, List<String>> getTags() {
            return tags;
        }

        public void setTags(Map<String, List<String>> tags) {
            this.tags = tags;
        }

        @JsonAnySetter
        public void setTag(String key, List<String> value) {
            this.tags.put(key, value);
        }
    }
}
