//package temp;
//
//import com.looksky.agents.application.facialFeatureExtraction.*;
//import com.looksky.agents.start.LookSkyApplication;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.mock.web.MockHttpServletRequest;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//
//@SpringBootTest(classes = LookSkyApplication.class)
//public class Test2 {
//
//    private static final Logger log = LoggerFactory.getLogger(Test2.class);
//
//
//
//    @Autowired
//    private FacialFeaturesExtractionService facialFeaturesExtractionService;
//
//    @Autowired
//    private FaceShapeExtractionService faceShapeExtractionService;
//
//    @Autowired
//    private PhotoComplianceService photoComplianceService;
//
//    @BeforeEach
//    void setUp() {
//        MockHttpServletRequest request = new MockHttpServletRequest();
//        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
//    }
//
//
//
//    @Test
//    public void test3() {
//        facialFeaturesExtractionService.processExcelFile("/Users/<USER>/Desktop/input.xlsx", "./test_001.xlsx");
//    }
//
//    @Test
//    public void test4() throws InterruptedException {
////        for (double i = 0.5; i <= 1.0; i+=0.1) {
//            faceShapeExtractionService.processExcelFile("/Users/<USER>/Desktop/input.xlsx", "./test_002.xlsx", 0.0);
////        }
////
////        Thread.sleep(1000 * 60 * 2);
////
////        for (double i = 0.5; i <= 1.0; i+=0.1) {
////            faceShapeExtractionService.processExcelFile("/Users/<USER>/Desktop/batch/gg.xlsx", "./result4_temperature_" + i + ".xlsx", i);
////        }
//    }
//
//    @Test
//    public void test6() {
//        photoComplianceService.processExcelFile("/Users/<USER>/Desktop/batch/face_check.xlsx", "./gg_result_10.xlsx");
//    }
//
//}
