//package temp;
//
//import cn.hutool.json.JSONUtil;
//import com.looksky.agents.application.facialFeatureExtraction.FacialFeatureExtractionService;
//import com.looksky.agents.start.LookSkyApplication;
//import jakarta.annotation.Resource;
//import org.junit.jupiter.api.Test;
//import org.springframework.ai.openai.OpenAiChatModel;
//import org.springframework.boot.test.context.SpringBootTest;
//
//@SpringBootTest(classes = LookSkyApplication.class)
//public class Test1 {
//
//    @Resource
//    private OpenAiChatModel chatModel;
//
//    @Resource
//    private FacialFeatureExtractionService facialFeatureExtractionService;
//
//    private static final String TEST_IMAGE_URL = "https://codetheory-file.oss-cn-shenzhen.aliyuncs.com/img/2024-10-31%2012%3A02%3A17%207cdvmfsalq1730347336744.png";
//
//    private static final String STEP1 = "Facial_compliance";
//    private static final String STEP2 = "Face_shape_and_cheekbones";
//    private static final String STEP3 = "Faces_Eyes_Type";
//    private static final String STEP4 = "Face_Skin_Hair";
//
//
//    @Test
//    public void test1() {
//        String facialCompliance = facialFeatureExtractionService.commonRequestModel(TEST_IMAGE_URL, STEP1);
//        System.out.println(JSONUtil.toJsonPrettyStr(facialCompliance));
//        /**
//         * {
//         *     "photoCompliance": true,
//         *     "nonComplianceReasons": {
//         *         "faceOrientation": "",
//         *         "accessoryExclusion": "",
//         *         "singleSubjectVerification": "",
//         *         "clarityResolution": ""
//         *     }
//         * }
//         */
//    }
//
//    @Test
//    public void test2() {
//        String facialCompliance = facialFeatureExtractionService.commonRequestModel(TEST_IMAGE_URL, STEP2);
//        System.out.println(JSONUtil.toJsonPrettyStr(facialCompliance));
//        /**
//         * {
//         *     "features": {
//         *         "faceShape": "oval",
//         *         "jawline": "rounded",
//         *         "cheekboneShape": "moderate"
//         *     },
//         *     "descriptions": {
//         *         "faceShape": "The face has a length that is approximately 1.5 times its width, with a gently rounded jawline, indicating an oval shape.",
//         *         "jawline": "The jawline is soft and round without sharp angles, adding a gentle contour.",
//         *         "cheekboneShape": "The cheekbones have a balanced placement with neutral contours, not strongly prominent."
//         *     }
//         * }
//         */
//    }
//
//    @Test
//    public void test3() {
//        String facialCompliance = facialFeatureExtractionService.commonRequestModel(TEST_IMAGE_URL, STEP3);
//        System.out.println(JSONUtil.toJsonPrettyStr(facialCompliance));
//        /**
//         * {
//         *     "features": {
//         *         "eyeColor": "dark_brown",
//         *         "eyeColorType": "warm",
//         *         "eyeType": "moderate"
//         *     },
//         *     "reasoning": {
//         *         "eyeColor": "The pupil appears to have a deep brown hue, indicating 'Dark Brown.'",
//         *         "eyeColorType": "The brown color has a warm undertone, suggesting a 'Warm' tone.",
//         *         "eyeType": "The eyes have a balanced shape without extreme roundness or narrowness, fitting the 'Moderate' category."
//         *     }
//         * }
//         */
//    }
//
//    @Test
//    public void test4() {
//        String facialCompliance = facialFeatureExtractionService.commonRequestModel(TEST_IMAGE_URL, STEP4);
//        System.out.println(JSONUtil.toJsonPrettyStr(facialCompliance));
//        /**
//         * {
//         *     "features": {
//         *         "skinUndertone": "neutral",
//         *         "skinColorType": "light",
//         *         "hairColorType": "soft",
//         *         "hairColor": "brown"
//         *     },
//         *     "descriptions": {
//         *         "skinUndertone": "The skin appears balanced without strong yellow or blue undertones, indicating a neutral tone.",
//         *         "skinColorType": "The skin is fair and bright, resembling light beige, which classifies it as light.",
//         *         "hairColorType": "The hair has a muted appearance with a soft undertone, lacking high contrast, indicating a soft tone.",
//         *         "hairColor": "The hair is brown, ranging from light to medium brown tones."
//         *     }
//         * }
//         */
//    }
//
//    @Test
//    public void test5() {
//        String facialCompliance = facialFeatureExtractionService.commonRequestModel("https://celebritytall.com/wp-content/uploads/2019/10/Jessica-Simpson.jpg", "Facial_features");
//        System.out.println(JSONUtil.toJsonPrettyStr(facialCompliance));
//
//    }
//}
