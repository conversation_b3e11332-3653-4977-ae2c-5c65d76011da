package temp;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ExcelData {
    @ExcelProperty("full_name")
    private String fullName;
    private String url;
    @ExcelProperty("weight_type")
    private String weightType;
    @ExcelProperty("kibble_style")
    private String kibbleStyle;
    @ExcelProperty("body_shape")
    private String bodyShape;
    @ExcelProperty("skin_tone")
    private String skinTone;
    @ExcelProperty("face_shape")
    private String faceShape;
    @ExcelProperty("skin_undertone")
    private String skinUndertone;

    @ExcelProperty("color_season")
    private String colorSeason;
    @ExcelProperty("height_type")
    private String heightType;
    private Integer age;
    @ExcelProperty("Height(cm)")
    private String height;
    @ExcelProperty("Weight(kg)")
    private String weight;


    private String jawlineShape;
    private String cheekboneType;
    private String skinDepth;
    private String hairTone;
    private String hairColor;
    private String pupilColor;
    private String pupilTone;
    private String eyeShape;
    private String noseType;
    private String mouthType;
} 