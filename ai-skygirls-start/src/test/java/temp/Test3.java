package temp;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * @ClassName Test3
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/12 上午10:45
 * @Version 1.0
 **/
public class Test3 {

    @Test
    public void test1() throws JsonProcessingException {
        String prompt = """
                Extract the product info from the page.
                #here is the output example:
                {'title': "Women's Cozy Ribbed Wide Leg Pants - Auden™", 'description': 'Wide-leg ribbed pants in a solid hue, made of soft-brushed 2x2 rib fabric with spandex. Features a mid-rise design, full elastic waistband with drawstring, and flared legs. STANDARD 100 by OEKO-TEX certified.', 'price': '$25.00', 'color': 'Oatmeal', 'all_colors': 'Oatmeal, Dark Gray, Green, Pink, Red, Turquoise Blue', 'size': 'M', 'all_sizes': ['XS', 'S', 'M', 'L', 'XL', 'XXL', '1X', '2X', '3X', '4X'], 'imgs': ['https://xxx.com/xxx', 'https://target.scene7.com/xxx'], 'merchant_name': 'Auden', 'tags': ['Women', 'Leg Pants'], 'image': 'https://target.scene7.com/xxx/', 'shipping_details': 'Estimated ship dimensions: 1 inches length x 7.5 inches width x 9 inches height
                Estimated ship weight: 0.5 pounds', 'return_details': 'This item can be returned to any Target store or Target.com. This item must be returned within 365 days of the date it was purchased in store, shipped, delivered by a Shipt shopper, or made ready for pickup.See the return policy for complete information.'}
                please extract the product info, make sure include title, description, and price. Also try to get these fields: size(means currentCategoryTag clothes size), all_sizes, imgs(all image urls), merchant_name(brand name of the product), tags(tags or keywords), image(the image url of currentCategoryTag product), etc. Note that do not use the example output as default value.
                """;

        String jsonSchema = "{\"name\":\"custom_schema\",\"schema\":{\"title\":\"ProductItem\",\"description\":\"Product detailed information\",\"type\":\"object\",\"properties\":{\"title\":{\"title\":\"Title\",\"description\":\"Product title\",\"type\":\"string\"},\"link\":{\"title\":\"Link\",\"description\":\"Product detail page URL\",\"type\":\"string\"},\"desc\":{\"title\":\"Desc\",\"description\":\"Product description\",\"type\":\"string\"},\"imgs\":{\"title\":\"Imgs\",\"description\":\"Main product images (single image required)\",\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"defaultColor\":{\"title\":\"Defaultcolor\",\"description\":\"Default color\",\"type\":\"string\"},\"trueBrandName\":{\"title\":\"Truebrandname\",\"description\":\"Actual brand name\",\"type\":\"string\"},\"skcs\":{\"title\":\"Skcs\",\"description\":\"SKC information list\",\"type\":\"array\",\"items\":{\"$ref\":\"#/definitions/SkcItem\"}},\"skus\":{\"title\":\"Skus\",\"description\":\"SKU information list\",\"type\":\"array\",\"items\":{\"$ref\":\"#/definitions/SkuItem\"}},\"spu\":{\"title\":\"Spu\",\"description\":\"SPU code\",\"type\":\"string\"},\"price\":{\"title\":\"Price\",\"description\":\"Selling price\",\"type\":\"number\"},\"originalPrice\":{\"title\":\"Originalprice\",\"description\":\"Original price\",\"type\":\"number\"},\"currency\":{\"title\":\"Currency\",\"description\":\"Currency code (e.g., USD, AUD)\",\"type\":\"string\"},\"category\":{\"title\":\"Category\",\"description\":\"Category name\",\"type\":\"string\"},\"brand\":{\"title\":\"Brand\",\"description\":\"Brand name\",\"type\":\"string\"},\"status\":{\"title\":\"Status\",\"description\":\"Status: 1-Offline, 2-Online, 3-Pre-sale\",\"type\":\"integer\"},\"attributes\":{\"title\":\"Attributes\",\"description\":\"Product attributes\",\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"publishTime\":{\"title\":\"Publishtime\",\"description\":\"Publication timestamp\",\"type\":\"string\"},\"updateTime\":{\"title\":\"Updatetime\",\"description\":\"Last update timestamp\",\"type\":\"string\"},\"features\":{\"title\":\"Features\",\"description\":\"Product features\",\"type\":\"object\",\"properties\":{\"fit\":{\"title\":\"Fit\",\"description\":\"Fit type\",\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"washCare\":{\"title\":\"Washcare\",\"description\":\"Washing care instructions\",\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"sustainability\":{\"title\":\"Sustainability\",\"description\":\"Sustainability information\",\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"materials\":{\"title\":\"Materials\",\"description\":\"Materials/Fabric/Components\",\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"flexibility\":{\"title\":\"Flexibility\",\"description\":\"Flexibility level\",\"type\":\"array\",\"items\":{\"type\":\"string\"}}},\"additionalProperties\":false,\"required\":[\"fit\",\"washCare\",\"sustainability\",\"materials\",\"flexibility\"]},\"modelInfo\":{\"title\":\"Modelinfo\",\"description\":\"Model information\",\"type\":\"string\"},\"videos\":{\"title\":\"Videos\",\"description\":\"Video collection\",\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"soldBy\":{\"title\":\"Soldby\",\"description\":\"Seller information\",\"type\":\"string\"},\"shipsFrom\":{\"title\":\"Shipsfrom\",\"description\":\"Shipping origin\",\"type\":\"string\"}},\"definitions\":{\"SkcItem\":{\"title\":\"SkcItem\",\"description\":\"SKC item information\",\"type\":\"object\",\"properties\":{\"name\":{\"title\":\"Name\",\"description\":\"SKC name\",\"type\":\"string\"},\"imgs\":{\"title\":\"Imgs\",\"description\":\"SKC image collection\",\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"link\":{\"title\":\"Link\",\"description\":\"SKC redirect URL\",\"type\":\"string\"}},\"additionalProperties\":false,\"required\":[\"name\",\"imgs\",\"link\"]},\"SkuItem\":{\"title\":\"SkuItem\",\"description\":\"SKU item information\",\"type\":\"object\",\"properties\":{\"color\":{\"title\":\"Color\",\"description\":\"SKU color name\",\"type\":\"string\"},\"colorValue\":{\"title\":\"Colorvalue\",\"description\":\"SKU color value or image URL\",\"type\":\"string\"},\"size\":{\"title\":\"Size\",\"description\":\"SKU size\",\"type\":\"string\"},\"stock\":{\"title\":\"Stock\",\"description\":\"SKU inventory quantity\",\"type\":\"integer\"},\"price\":{\"title\":\"Price\",\"description\":\"SKU selling price\",\"type\":\"number\"},\"originalPrice\":{\"title\":\"Originalprice\",\"description\":\"SKU original price\",\"type\":\"number\"}},\"additionalProperties\":false,\"required\":[\"color\",\"colorValue\",\"size\",\"stock\",\"price\",\"originalPrice\"]},\"FeaturesObj\":{\"title\":\"FeaturesObj\",\"description\":\"Product feature information\",\"type\":\"object\",\"properties\":{\"fit\":{\"title\":\"Fit\",\"description\":\"Fit type\",\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"washCare\":{\"title\":\"Washcare\",\"description\":\"Washing care instructions\",\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"sustainability\":{\"title\":\"Sustainability\",\"description\":\"Sustainability information\",\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"materials\":{\"title\":\"Materials\",\"description\":\"Materials/Fabric/Components\",\"type\":\"array\",\"items\":{\"type\":\"string\"}},\"flexibility\":{\"title\":\"Flexibility\",\"description\":\"Flexibility level\",\"type\":\"array\",\"items\":{\"type\":\"string\"}}},\"additionalProperties\":false,\"required\":[\"fit\",\"washCare\",\"sustainability\",\"materials\",\"flexibility\"]}},\"additionalProperties\":false,\"required\":[\"title\",\"link\",\"desc\",\"imgs\",\"defaultColor\",\"trueBrandName\",\"skcs\",\"skus\",\"spu\",\"price\",\"originalPrice\",\"currency\",\"category\",\"brand\",\"status\",\"attributes\",\"publishTime\",\"updateTime\",\"features\",\"modelInfo\",\"videos\",\"soldBy\",\"shipsFrom\"]},\"strict\":true}";



        // 帮我加载 html.txt 文件
        String html;
        try {
            html = new String(Files.readAllBytes(Paths.get("src/test/resources/html1.txt")));
        } catch (IOException e) {
            throw new RuntimeException("无法读取 html.txt 文件", e);
        }

 // 创建请求JSON对象
    ObjectMapper mapper = new ObjectMapper();
    ObjectNode requestJson = mapper.createObjectNode();
    
    requestJson.put("model", "gpt-4o");
    requestJson.put("stream", false);
    requestJson.put("temperature", 0.0);
    
    // 创建messages数组
    ArrayNode messages = requestJson.putArray("messages");
    
    // 添加system message
    ObjectNode systemMessage = messages.addObject();
    systemMessage.put("role", "system");
    systemMessage.put("content", prompt);
    
    // 添加user message
    ObjectNode userMessage = messages.addObject();
    userMessage.put("role", "user");
    userMessage.put("content", html);
    
    // 添加response_format
    ObjectNode responseFormat = requestJson.putObject("response_format");
    responseFormat.put("type", "json_schema");
    
    // 将jsonSchema字符串解析为JSON对象，然后设置到response_format中
    JsonNode schemaNode = mapper.readTree(jsonSchema);
    responseFormat.set("json_schema", schemaNode);

        



        System.out.println(JSONUtil.toJsonPrettyStr(mapper.writeValueAsString(requestJson)));


        HttpResponse response = HttpRequest.post("https://test-api.looksky.ai/agent-service/common/request")
            .header("Content-Type", "application/json")
            .body(mapper.writeValueAsString(requestJson))
            .execute();

        if (response.getStatus() != 200) {
            System.out.println("错误状态码: " + response.getStatus());
            System.out.println("错误响应: " + response.body());
            return;
        }

        JsonNode responseJson = mapper.readTree(response.body());
        System.out.println(mapper.writerWithDefaultPrettyPrinter().writeValueAsString(responseJson));

    }
}
