apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.serviceNameRest }}
  namespace: {{ .Release.Namespace }}
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9100"
  labels:
    name: {{ .Values.serviceNameRest }}
spec:
  ports:
    - name: http
      port: {{ .Values.ports.restServicePort }}
      targetPort: {{ .Values.ports.restServicePort }}
    - name: grpc
      port: {{ .Values.ports.grpcServicePort }}
      targetPort: {{ .Values.ports.grpcServicePort }}
  selector:
    name: {{ .Values.serviceNameRest }}