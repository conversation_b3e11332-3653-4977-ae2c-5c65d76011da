apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.serviceNameRest }}
  namespace: {{ .Release.Namespace }}
spec:
  replicas: {{ .Values.controllerReplicaCount }}
  selector:
    matchLabels:
      name: {{ .Values.serviceNameRest }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: {{ .Values.rollingUpdate.maxSurge }}
      maxUnavailable: {{ .Values.rollingUpdate.maxUnavailable }}
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9100"
      labels:
        name: {{ .Values.serviceNameRest }}
    spec:
      # dockerhub私有仓库拉取镜像的secret，详情参考
      imagePullSecrets:
        - name: {{ .Values.image.pullSecret }}
      containers:
        - name: {{ .Values.serviceNameRest }}
          image: {{ .Values.image.repository }}:{{ .Values.image.tag }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: prod
          #command: ['sh','-c','java -Xms256m -Xmx800m -XX:PermSize=64M -XX:MaxNewSize=256m -XX:MaxPermSize=128m -jar /looksky-agent/app.jar']
          resources:
            requests:
              cpu: {{ .Values.resourcesClient.requests.cpu }}
            limits:
              cpu: {{ .Values.resourcesClient.limits.cpu }}