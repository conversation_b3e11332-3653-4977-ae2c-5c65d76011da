# Default values for collector.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

controllerReplicaCount: 1

# 配置文件路径
configFilePath: /etc/datacenter

# 日志文件路径，与seelog.xml中的路径保持一致
logFilePath: /var/log/looksky-skygirls-agents

# 服务标识,grpc必须以-grpc结尾，rest必须以-rest结尾
serviceNameRest: looksky-skygirls-agents-rest

# 节点选择器，决定服务POD被调度到哪个节点运行
nodeSelector:
  operator: In
  nodeLabelKey: host-type
  nodeLabelValue: app

image:
  repository: lookskysvc/micro
  pullPolicy: Always
  pullSecret: graecove-repo
  # Overrides the image tag whose default is the chart appVersion.
  tag: looksky-skygirls-agents-prod-v1.0.4

ports:
  restServicePort: 11462
resourcesClient:
  limits:
    cpu: 1000m
    memory: 2Gi
  requests:
    cpu: 500m
    memory: 512Mi


rollingUpdate:
  # 滚动更新时最大可以多一个POD
  maxSurge: 1
  # 滚动更新时最多容忍一半的节点不可用
  maxUnavailable: 0

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

