package com.looksky.agents.infrastructure.httpapi;

import org.springframework.core.annotation.AliasFor;
import org.springframework.web.service.annotation.HttpExchange;

import java.lang.annotation.*;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@HttpExchange
public @interface HttpApi {
    @AliasFor("service")
    ApiServiceEnum value() default ApiServiceEnum.LOOKSKY_API;
    
    @AliasFor("value")
    ApiServiceEnum service() default ApiServiceEnum.LOOKSKY_API;
    
    @AliasFor(annotation = HttpExchange.class, attribute = "value")
    String path() default "";
} 