package com.looksky.agents.infrastructure.interceptor;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import io.grpc.CallOptions;
import io.grpc.Channel;
import io.grpc.ClientCall;
import io.grpc.ClientInterceptor;
import io.grpc.ForwardingClientCall;
import io.grpc.ForwardingClientCallListener;
import io.grpc.Metadata;
import io.grpc.MethodDescriptor;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GrpcResponseHeaderInterceptor implements ClientInterceptor {

    public static final String KEY_NAME = "abFlag";

    static final Metadata.Key<String> ABFlag = Metadata.Key.of(KEY_NAME, Metadata.ASCII_STRING_MARSHALLER);
    
    // 使用ThreadLocal存储header信息
    private static final TransmittableThreadLocal<Map<String, Object>> headerContext = new TransmittableThreadLocal<>();

    public static Object getAbTestFlag() {
        Object obj = getHeaders().getOrDefault(KEY_NAME, null);
        clearHeaders();
        return obj;
    }


    // 获取当前线程的header信息
    public static Map<String, Object> getHeaders() {
        Map<String, Object> headers = headerContext.get();
        return headers != null ? headers : new HashMap<>();
    }
    
    // 清理header信息
    public static void clearHeaders() {
        //log.debug("清理ThreadLocal中的header信息");
        headerContext.remove();
    }

    @Override
    public <Q, P> ClientCall<Q, P> interceptCall(
            MethodDescriptor<Q, P> method,
            CallOptions callOptions,
            Channel next) {
        
        return new ForwardingClientCall.SimpleForwardingClientCall<>(
                next.newCall(method, callOptions)) {
            
            @Override
            public void start(Listener<P> responseListener, Metadata headers) {
                super.start(new ForwardingClientCallListener.SimpleForwardingClientCallListener<>(responseListener) {
                    @Override
                    public void onHeaders(Metadata headers) {
                        log.debug("接收到的 grpc 的 header: {}", headers);
                        
                        // 创建新的header Map
                        Map<String, Object> headerMap = new HashMap<>();
                        
                        // 从header中获取所有需要的参数
                        String abFlag = headers.get(ABFlag);
                        if (CharSequenceUtil.isNotBlank(abFlag)) {
                            headerMap.put(KEY_NAME, abFlag);
                            // 存储到ThreadLocal中
                            headerContext.set(headerMap);
                        }

                        log.debug("已保存的HTTP请求头: {}", headerMap);

                        super.onHeaders(headers);
                    }
                }, headers);
            }
        };
    }
}