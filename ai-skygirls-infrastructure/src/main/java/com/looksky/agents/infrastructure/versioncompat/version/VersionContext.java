
package com.looksky.agents.infrastructure.versioncompat.version;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import lombok.NoArgsConstructor;

/**
 * 版本上下文，用于存储当前请求的版本信息
 *
 * <AUTHOR>
 * @since  1.1.8
 **/
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class VersionContext {
    private static final TransmittableThreadLocal<String> currentVersion = new TransmittableThreadLocal<>();
    private static final TransmittableThreadLocal<ApiVersion.PlatformType> currentPlatform = new TransmittableThreadLocal<>();
    public static final String DEFAULT_VERSION = "9.9.9";
    public static final ApiVersion.PlatformType DEFAULT_PLATFORM = ApiVersion.PlatformType.ALL;

    public static void setVersion(String version) {
        currentVersion.set(version);
    }

    public static String getVersion() {
        String version = currentVersion.get();
        if (CharSequenceUtil.isBlank(version)) {
            return DEFAULT_VERSION;
        }
        return version;
    }
    
    public static void setPlatform(ApiVersion.PlatformType platform) {
        currentPlatform.set(platform);
    }
    
    public static ApiVersion.PlatformType getPlatform() {
        ApiVersion.PlatformType platformType = currentPlatform.get();
        if (platformType == null) {
            return DEFAULT_PLATFORM;
        }
        return platformType;
    }

    public static void clear() {
        currentVersion.remove();
        currentPlatform.remove();
    }
}