package com.looksky.agents.infrastructure.versioncompat.client;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.graecove.common.ApiResp;
import com.looksky.agents.sdk.datahub.version.response.UserClientUaResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UaClient {
    @Value("${http.third-party.base-urls.data-hub-api}")
    private String url;

    private final ObjectMapper mapper = new ObjectMapper();

    /**
     * 获取用户 ua 信息
     *
     * @param userId 用户 id
     * @return ua 信息
     */
    public String getUserUa(String userId) {
        try (HttpResponse response = HttpRequest.get(url + "/datahub/data/clientUa").form("userId", userId).execute()) {
            if (response.isOk()) {
                String body = response.body();
                log.info("获取 {} 的 ua 信息: {}", userId, body);

                ApiResp<UserClientUaResponse> uaObject = mapper.readValue(body, new TypeReference<>() {
                });

                return uaObject.getData().getClientUa();
            }
        } catch (Exception e) {
            log.info("获取用户 ua 信息失败", e);
        }
        return null;
    }

}
