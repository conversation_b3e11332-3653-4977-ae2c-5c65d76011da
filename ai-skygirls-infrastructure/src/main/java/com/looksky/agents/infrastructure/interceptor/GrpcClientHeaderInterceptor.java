package com.looksky.agents.infrastructure.interceptor;

import cn.hutool.json.JSONUtil;
import com.google.common.annotations.VisibleForTesting;
import com.looksky.agents.infrastructure.logs.LogConstants;
import com.yomahub.tlog.context.TLogContext;
import io.grpc.*;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


@Slf4j
public class GrpcClientHeaderInterceptor implements ClientInterceptor {

    @VisibleForTesting
    static final Metadata.Key<String> HOST = Metadata.Key.of("host", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> USER_AGENT = Metadata.Key.of("user-agent", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> VIEWER_TIME_ZONE = Metadata.Key.of("cloudfront-viewer-time-zone", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> VIEWER_ADDRESS = Metadata.Key.of("cloudfront-viewer-address", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> VIEWER_COUNTRY = Metadata.Key.of("cloudfront-viewer-country", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> IS_IOS_VIEWER = Metadata.Key.of("cloudfront-is-ios-viewer", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> IS_TABLET_VIEWER = Metadata.Key.of("cloudfront-is-tablet-viewer", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> FORWARDED_PROTO = Metadata.Key.of("cloudfront-forwarded-proto", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> VIEWER_COUNTRY_NAME = Metadata.Key.of("cloudfront-viewer-country-name", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> IS_MOBILE_VIEWER = Metadata.Key.of("cloudfront-is-mobile-viewer", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> IS_SMARTTV_VIEWER = Metadata.Key.of("cloudfront-is-smarttv-viewer", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> VIEWER_COUNTRY_REGION = Metadata.Key.of("cloudfront-viewer-country-region", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> IS_ANDROID_VIEWER = Metadata.Key.of("cloudfront-is-android-viewer", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> VIEWER_COUNTRY_REGION_NAME = Metadata.Key.of("cloudfront-viewer-country-region-name", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> VIEWER_CITY = Metadata.Key.of("cloudfront-viewer-city", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> VIEWER_LATITUDE = Metadata.Key.of("cloudfront-viewer-latitude", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> VIEWER_LONGITUDE = Metadata.Key.of("cloudfront-viewer-longitude", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> VIEWER_HTTP_VERSION = Metadata.Key.of("cloudfront-viewer-http-version", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> VIEWER_POSTAL_CODE = Metadata.Key.of("cloudfront-viewer-postal-code", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> VIEWER_ASN = Metadata.Key.of("cloudfront-viewer-asn", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> IS_DESKTOP_VIEWER = Metadata.Key.of("cloudfront-is-desktop-viewer", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> VIEWER_METRO_CODE = Metadata.Key.of("cloudfront-viewer-metro-code", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> VIEWER_TLS = Metadata.Key.of("cloudfront-viewer-tls", Metadata.ASCII_STRING_MARSHALLER);
    @VisibleForTesting
    static final Metadata.Key<String> SW8 = Metadata.Key.of(LogConstants.SW8, Metadata.ASCII_STRING_MARSHALLER);

    @Override
    public <Q, P> ClientCall<Q, P> interceptCall(MethodDescriptor<Q, P> method, CallOptions callOptions, Channel next) {
        return new ForwardingClientCall.SimpleForwardingClientCall<>(next.newCall(method, callOptions)) {
            @Override
            public void start(Listener<P> responseListener, Metadata headers) {
                ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                if (requestAttributes != null) {
                    HttpServletRequest request = requestAttributes.getRequest();
                    addHeaderIfPresent(headers, HOST, request);
                    addHeaderIfPresent(headers, USER_AGENT, request);
                    addHeaderIfPresent(headers, VIEWER_TIME_ZONE, request);
                    addHeaderIfPresent(headers, VIEWER_ADDRESS, request);
                    addHeaderIfPresent(headers, VIEWER_COUNTRY, request);
                    addHeaderIfPresent(headers, IS_IOS_VIEWER, request);
                    addHeaderIfPresent(headers, IS_TABLET_VIEWER, request);
                    addHeaderIfPresent(headers, FORWARDED_PROTO, request);
                    addHeaderIfPresent(headers, VIEWER_COUNTRY_NAME, request);
                    addHeaderIfPresent(headers, IS_MOBILE_VIEWER, request);
                    addHeaderIfPresent(headers, IS_SMARTTV_VIEWER, request);
                    addHeaderIfPresent(headers, VIEWER_COUNTRY_REGION, request);
                    addHeaderIfPresent(headers, IS_ANDROID_VIEWER, request);
                    addHeaderIfPresent(headers, VIEWER_COUNTRY_REGION_NAME, request);
                    addHeaderIfPresent(headers, VIEWER_CITY, request);
                    addHeaderIfPresent(headers, VIEWER_LATITUDE, request);
                    addHeaderIfPresent(headers, VIEWER_LONGITUDE, request);
                    addHeaderIfPresent(headers, VIEWER_HTTP_VERSION, request);
                    addHeaderIfPresent(headers, VIEWER_POSTAL_CODE, request);
                    addHeaderIfPresent(headers, VIEWER_ASN, request);
                    addHeaderIfPresent(headers, IS_DESKTOP_VIEWER, request);
                    addHeaderIfPresent(headers, VIEWER_METRO_CODE, request);
                    addHeaderIfPresent(headers, VIEWER_TLS, request);
                }
                Optional.ofNullable(TLogContext.getTraceId()).ifPresent(tid -> headers.put(SW8, tid));
                log.debug("请求头数据: {}", headers);
                log.debug("请求头JSON数据: {}", JSONUtil.toJsonStr(headers));
                super.start(responseListener, headers);
            }
        };
    }

    private void addHeaderIfPresent(Metadata headers, Metadata.Key<String> key, HttpServletRequest request) {
        String value = request.getHeader(key.name());
        if (value != null) {
            headers.put(key, value);
        }
    }
}