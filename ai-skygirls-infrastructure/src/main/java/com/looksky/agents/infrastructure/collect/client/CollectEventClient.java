package com.looksky.agents.infrastructure.collect.client;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.collect.BatchEventCollectionRequest;
import com.looksky.agents.sdk.collect.EventDto;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 埋点数据上报 Client
 *
 * <AUTHOR>
 * @since 1.1.11
 **/
@Slf4j
@Component
public class CollectEventClient {

    @Value("${http.third-party.base-urls.data-collect-api}")
    private String url;


    private final ObjectMapper mapper = new ObjectMapper();

    /**
     * 批量收集事件数据
     *
     * @param events 事件列表
     */
    public void batchCollectEvents(List<EventDto> events) {
        VirtualCompletableFuture.runAsync(() -> {
            BatchEventCollectionRequest request = new BatchEventCollectionRequest();
            request.setEvent(events);
            try (HttpResponse response = HttpRequest.post(url + "/collector/BatchCollect").body(mapper.writeValueAsString(request)).execute()) {
                //log.debug("批量收集事件\n请求体:{}\n数据结果: {}", mapper.writeValueAsString(request), response.body());
                if (!response.isOk()) {
                    log.error("批量收集事件数据失败, 事件对象: {}", JSONUtil.toJsonStr(request));
                }
            } catch (Exception e) {
                log.error("批量收集事件数据发生异常, 事件对象: {}, 异常信息:{}", JSONUtil.toJsonStr(request), e.getMessage(), e);
            }
        });
    }
}
