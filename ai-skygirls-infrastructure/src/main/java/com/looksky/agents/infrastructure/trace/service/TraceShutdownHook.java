package com.looksky.agents.infrastructure.trace.service;

import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;


/**
 * 应用关闭时的钩子，确保所有追踪数据被刷新到MongoDB
 *
 * @since  1.2.0
 * <AUTHOR>
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class TraceShutdownHook implements ApplicationListener<ContextClosedEvent> {

    private final DisruptorService disruptorService;

    @Override
    public void onApplicationEvent(@NotNull ContextClosedEvent event) {
        log.info("应用关闭中，确保所有追踪数据被刷新到MongoDB");
        try {
            disruptorService.flushTracesToMongoDB();
            // 给异步操作一些时间完成
            TimeUnit.SECONDS.sleep(5);
            log.info("追踪数据刷新完成");
        } catch (InterruptedException e) {
            log.error("关闭时刷新追踪数据失败: {}", e.getMessage(), e);
            Thread.currentThread().interrupt();
        }
    }
} 