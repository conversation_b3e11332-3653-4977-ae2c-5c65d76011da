package com.looksky.agents.infrastructure.openapi;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger2配置
 *
 * <AUTHOR>
 * @date 2024-05-30 13:51:35
 */
@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("系统API文档")
                        .description("API 接口文档")
                        .version("v1.0")
                        .contact(new Contact()
                                .name("开发团队")
                                .email("<EMAIL>")));
    }



}