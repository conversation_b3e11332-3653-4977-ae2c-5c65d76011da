package com.looksky.agents.infrastructure.mybatis;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import org.springframework.stereotype.Component;

/**
 * @ClassName IdGenerator
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/20 下午8:45
 * @Version 1.0
 **/
@Component
public class IdGenerator implements IdentifierGenerator{

    @Override
    public Number nextId(Object entity) {
        return IdUtil.getSnowflakeNextId();
    }

    @Override
    public String nextUUID(Object entity) {
        return IdUtil.getSnowflakeNextIdStr();
    }
}
