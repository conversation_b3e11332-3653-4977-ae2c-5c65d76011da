package com.looksky.agents.infrastructure.trace.context;

import cn.hutool.core.util.IdUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import java.util.Deque;
import java.util.LinkedList;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SpanContext {
    // 使用 Deque 来管理 spanId 栈，便于获取 parentSpanId
    private static final TransmittableThreadLocal<Deque<String>> CURRENT_SPAN_ID_STACK = TransmittableThreadLocal.withInitial(LinkedList::new);


    public static String createNewSpanId() {
        String spanId = generateId();
        CURRENT_SPAN_ID_STACK.get().push(spanId);
        return spanId;
    }
    
    public static void setSpanStack(Deque<String> spanStack) {
        if (spanStack != null) {
            CURRENT_SPAN_ID_STACK.set(new LinkedList<>(spanStack)); // 复制一份，避免多线程问题
        } else {
            CURRENT_SPAN_ID_STACK.set(new LinkedList<>());
        }
    }

    public static Deque<String> getSpanStack() {
        return CURRENT_SPAN_ID_STACK.get();
    }


    public static String getCurrentSpanId() {
        Deque<String> stack = CURRENT_SPAN_ID_STACK.get();
        return stack.isEmpty() ? null : stack.peek();
    }

    public static String getParentSpanId() {
        Deque<String> stack = CURRENT_SPAN_ID_STACK.get();
        if (stack.size() > 1) {
            String current = stack.pop(); // 弹出当前
            String parent = stack.peek(); // 查看父
            stack.push(current); // 恢复当前
            return parent;
        }
        return null; // 根 Span 没有 Parent
    }

    public static void poplarSpanId() {
        Deque<String> stack = CURRENT_SPAN_ID_STACK.get();
        if (!stack.isEmpty()) {
            stack.pop();
        }
    }

    public static void clear() {
        CURRENT_SPAN_ID_STACK.remove();
    }

    private static String generateId() {
        return IdUtil.getSnowflakeNextIdStr();
    }
}