package com.looksky.agents.infrastructure.retry;

import com.looksky.agents.infrastructure.config.RetryConfig;
import com.looksky.agents.infrastructure.context.Context;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class DynamicRetryAspect {

    private final RetryConfig retryConfig;

    @Around("@annotation(dynamicRetry)")
    public Object around(ProceedingJoinPoint point, DynamicRetry dynamicRetry) throws Throwable {
        // 从上下文中获取开始时间
        Long startTime = null;
        Map<String, Object> currentContext = Context.get();
        if (currentContext != null) {
            Object startTimeObj = currentContext.get(dynamicRetry.startTimeKey());
            if (startTimeObj instanceof Long time) {
                startTime = time;
            }
        }

        // 使用配置中的值，如果注解中有值则优先使用注解中的值
        long expectedDuration = dynamicRetry.expectedDuration();
        if (dynamicRetry.expectedDuration() == 6000 && retryConfig.getDaily100ExpectedDuration() > 0) {
            expectedDuration = retryConfig.getDaily100ExpectedDuration();
        }


        RetryContext retryContext = new RetryContext(
            dynamicRetry.timeout(),
            expectedDuration,
            startTime,
            dynamicRetry.maxAttempts(),
            dynamicRetry.retryInterval()
        );
        
        while (true) {
            try {
                return point.proceed();
            } catch (Exception e) {
                retryContext.setLastException(e);
                retryContext.incrementRetryCount();
                
                if (!retryContext.canRetry()) {
                    log.error("动态重试失败，已重试{}次（最大重试次数{}），总耗时{}ms，剩余时间{}ms，最后一次异常", 
                            retryContext.getRetryCount(),
                            dynamicRetry.maxAttempts(),
                            System.currentTimeMillis() - retryContext.getStartTime(),
                            retryContext.getRemainingTime(),
                            e);
                    throw e;
                }
                
                log.warn("动态重试中，当前第{}次重试（最大重试次数{}），剩余时间{}ms，将等待{}ms后重试，异常信息: {}", 
                        retryContext.getRetryCount(),
                        dynamicRetry.maxAttempts(),
                        retryContext.getRemainingTime(),
                        dynamicRetry.retryInterval(),
                        e.getMessage());
                
                retryContext.waitForNextRetry();
            }
        }
    }
} 