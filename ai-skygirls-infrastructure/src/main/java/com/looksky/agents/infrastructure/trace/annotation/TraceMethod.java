package com.looksky.agents.infrastructure.trace.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 追踪方法注解
 *
 * @since  1.2.0
 * <AUTHOR>
 **/
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TraceMethod {
    String description() default "";
    TraceTag[] tags() default {};
    boolean captureParams() default true;
    boolean captureReturnValue() default true;
    long slowThresholdMillis() default -1; // -1 表示不特殊标记
}