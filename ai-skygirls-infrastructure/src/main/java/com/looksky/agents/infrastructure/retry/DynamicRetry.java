package com.looksky.agents.infrastructure.retry;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DynamicRetry {
    /**
     * 总超时时间（毫秒）
     */
    long timeout() default 30000;

    /**
     * 单次调用预期耗时（毫秒）
     */
    long expectedDuration() default 6000;

    /**
     * 开始时间在上下文中的key
     */
    String startTimeKey() default "requestStartTime";

    /**
     * 最大重试次数
     */
    int maxAttempts() default 2;

    /**
     * 重试间隔（毫秒）
     */
    long retryInterval() default 1000;
} 