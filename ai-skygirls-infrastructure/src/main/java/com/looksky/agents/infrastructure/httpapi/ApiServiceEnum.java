package com.looksky.agents.infrastructure.httpapi;

import lombok.Getter;

@Getter
public enum ApiServiceEnum {
    LOOKSKY_API("looksky-api"),
    SKYGIRLS_API("skygirls-api"),
    OPEN_PERPLEX("open-perplex"),
    OTHER_SERVICE("other-service"),
    PY_AGENT("py-agent-api"),
    LOCALHOST("localhost"),
    DATA_HUB("data-hub-api"),

    ;
    
    private final String value;
    
    ApiServiceEnum(String value) {
        this.value = value;
    }

} 