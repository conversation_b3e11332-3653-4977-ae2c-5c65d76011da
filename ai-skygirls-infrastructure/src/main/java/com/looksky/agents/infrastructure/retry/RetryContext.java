package com.looksky.agents.infrastructure.retry;

import lombok.Data;

@Data
public class RetryContext {
    private long startTime;
    private long timeout;
    private long expectedDuration;
    private int retryCount;
    private int maxAttempts;
    private long retryInterval;
    private Throwable lastException;

    public RetryContext(long timeout, long expectedDuration, Long externalStartTime, int maxAttempts, long retryInterval) {
        this.startTime = externalStartTime != null ? externalStartTime : System.currentTimeMillis();
        this.timeout = timeout;
        this.expectedDuration = expectedDuration;
        this.maxAttempts = maxAttempts;
        this.retryInterval = retryInterval;
        this.retryCount = 0;
    }

    public boolean canRetry() {
        if (retryCount >= maxAttempts) {
            return false;
        }
        long elapsedTime = System.currentTimeMillis() - startTime;
        long remainingTime = timeout - elapsedTime;
        return remainingTime >= expectedDuration;
    }

    public long getRemainingTime() {
        long elapsedTime = System.currentTimeMillis() - startTime;
        return Math.max(0, timeout - elapsedTime);
    }

    public void incrementRetryCount() {
        this.retryCount++;
    }

    public void waitForNextRetry() throws InterruptedException {
        Thread.sleep(retryInterval);
    }
} 