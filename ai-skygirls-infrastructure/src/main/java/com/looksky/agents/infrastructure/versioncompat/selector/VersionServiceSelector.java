package com.looksky.agents.infrastructure.versioncompat.selector;

import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import com.looksky.agents.infrastructure.versioncompat.annotation.VersionCompatible;
import com.looksky.agents.infrastructure.versioncompat.comparator.VersionComparator;
import com.looksky.agents.infrastructure.versioncompat.exception.VersionCompatException;
import com.looksky.agents.infrastructure.versioncompat.version.VersionContext;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 版本服务选择器 - 负责根据当前版本和平台选择合适的服务实现
 *
 * <AUTHOR>
 * @since 1.1.8
 **/
@Slf4j
@Component
public class VersionServiceSelector implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    // 服务缓存: <服务类型, <平台类型, <版本号,  实现实例列表>>>
    private final Map<Class<?>, Map<ApiVersion.PlatformType, TreeMap<String, List<Object>>>> serviceCache = new ConcurrentHashMap<>();

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    public void init() {
        // 在初始化时扫描所有带有@ApiVersion注解的Bean
        scanVersionedServices();
    }

    /**
     * 扫描所有版本化服务实现
     */
    private void scanVersionedServices() {
        // 查找所有带@ApiVersion注解的Bean
        Map<String, Object> versionedBeans = applicationContext.getBeansWithAnnotation(ApiVersion.class);

        for (Object bean : versionedBeans.values()) {
            Class<?> beanClass = AopUtils.getTargetClass(bean);

            // 处理接口
            for (Class<?> interfaceClass : beanClass.getInterfaces()) {
                if (interfaceClass.isAnnotationPresent(VersionCompatible.class)) {
                    registerService(interfaceClass, bean);
                }
            }

            // 处理抽象类
            Class<?> superClass = beanClass.getSuperclass();
            while (superClass != null && superClass != Object.class) {
                if (superClass.isAnnotationPresent(VersionCompatible.class)) {
                    registerService(superClass, bean);
                }
                superClass = superClass.getSuperclass();
            }
        }
    }

    /**
     * 注册服务实现
     */
    private void registerService(Class<?> serviceType, Object implementation) {
        // 获取原始类，而不是代理类
        Class<?> originalClass = AopUtils.getTargetClass(implementation);
        // 只使用getAnnotationsByType获取所有注解（包括单个注解的情况）
        ApiVersion[] apiVersions = originalClass.getAnnotationsByType(ApiVersion.class);
        for (ApiVersion av : apiVersions) {
            registerVersionedImplementation(serviceType, av.platform(), av.version(), implementation);
        }
    }

    /**
     * 注册特定版本和平台的服务实现
     */
    private void registerVersionedImplementation(Class<?> serviceType, ApiVersion.PlatformType platform, String version, Object implementation) {
        Map<ApiVersion.PlatformType, TreeMap<String, List<Object>>> platformMap = serviceCache.computeIfAbsent(
            serviceType, k -> new ConcurrentHashMap<>());

        TreeMap<String, List<Object>> versionMap = platformMap.computeIfAbsent(
            platform, k -> new TreeMap<>(new VersionComparator()));
        // 获取该版本的实现列表，如果不存在则创建新列表
        List<Object> implementationList = versionMap.computeIfAbsent(version, k -> new ArrayList<>());
        // 添加新的实现
        implementationList.add(implementation);
    }


    /**
     * 获取适合当前版本和平台的服务实现
     *
     * @param serviceType 服务类型（接口或抽象类）
     * @return 符合当前版本和平台的服务实现
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> getServiceList(Class<T> serviceType) {
        // 获取版本映射
        TreeMap<String, List<Object>> versionMap = getVersionMap(serviceType);
        String currentVersion = VersionContext.getVersion();
        ApiVersion.PlatformType currentPlatform = VersionContext.getPlatform();

        // 找出所有接近但不大于当前版本的实现（按版本从新到旧排序）
        List<T> implementationList = new ArrayList<>();

        // 获取所有小于等于当前版本的条目
        SortedMap<String, List<Object>> eligibleVersions = versionMap.headMap(currentVersion, true);
        if (eligibleVersions.isEmpty()) {
            // 如果没有找到合适版本，使用最低版本的实现
            Map.Entry<String, List<Object>> lowestEntry = versionMap.firstEntry();
            if (lowestEntry == null) {
                throw new VersionCompatException("No implementation found for " + serviceType.getName() + " with version " + currentVersion + " on platform " + currentPlatform);
            }
            // 添加最低版本的所有实现
            lowestEntry.getValue().forEach(impl -> implementationList.add((T) impl));
        } else {
            // 按版本从新到旧添加所有符合条件的实现
            eligibleVersions.reversed().forEach((version, impls) ->
                impls.forEach(impl -> implementationList.add((T) impl)));
        }

        return implementationList;
    }

    /**
     * 获取适合当前版本和平台的服务实现
     *
     * @param serviceType 服务类型（接口或抽象类）
     * @return 符合当前版本和平台的服务实现
     */
    @SuppressWarnings("unchecked")
    public <T> T getService(Class<T> serviceType) {
        // 获取版本映射
        TreeMap<String, List<Object>> versionMap = getVersionMap(serviceType);

        String currentVersion = VersionContext.getVersion();
        ApiVersion.PlatformType currentPlatform = VersionContext.getPlatform();

        // 找到最接近但不大于当前版本的实现
        Map.Entry<String, List<Object>> entry = versionMap.floorEntry(currentVersion);
        if (entry == null) {
            // 如果没有找到，使用最低版本的实现
            entry = versionMap.firstEntry();
        }

        if (entry == null || entry.getValue().isEmpty()) {
            throw new VersionCompatException("No implementation found for " + serviceType.getName() + " with version " + currentVersion + " on platform " + currentPlatform);
        }

        // 返回该版本的第一个实现
        return (T) entry.getValue().getFirst();
    }


    private TreeMap<String, List<Object>> getVersionMap(Class<?> serviceType) {
        ApiVersion.PlatformType currentPlatform = VersionContext.getPlatform();

        // 获取服务实现
        Map<ApiVersion.PlatformType, TreeMap<String, List<Object>>> platformMap = serviceCache.get(serviceType);
        if (platformMap == null) {
            throw new VersionCompatException("No implementation found for " + serviceType.getName());
        }

        TreeMap<String, List<Object>> versionMap = new TreeMap<>(new VersionComparator());

        switch (currentPlatform) {
            case ApiVersion.PlatformType.IOS -> {
                mergePlatformMap(platformMap, ApiVersion.PlatformType.IOS, versionMap);
                mergePlatformMap(platformMap, ApiVersion.PlatformType.ALL, versionMap);
            }
            case ApiVersion.PlatformType.ANDROID -> {
                mergePlatformMap(platformMap, ApiVersion.PlatformType.ANDROID, versionMap);
                mergePlatformMap(platformMap, ApiVersion.PlatformType.ALL, versionMap);
            }
            default -> mergePlatformMap(platformMap, ApiVersion.PlatformType.ALL, versionMap);
        }

        if (versionMap.isEmpty()) {
            throw new VersionCompatException("No implementation found for " + serviceType.getName() + " with platform " + currentPlatform);
        }

        return versionMap;
    }

    /**
     * 合并指定平台的实现映射到结果映射中
     *
     * @param platformMap 平台到版本映射的映射
     * @param platform    要合并的平台类型
     * @param resultMap   要合并到的结果映射
     */
    private void mergePlatformMap(Map<ApiVersion.PlatformType, TreeMap<String, List<Object>>> platformMap, ApiVersion.PlatformType platform, TreeMap<String, List<Object>> resultMap) {
        Optional.ofNullable(platformMap.get(platform)).ifPresent(map -> map.forEach((version, implList) -> {
            List<Object> merged = resultMap.computeIfAbsent(version, k -> new ArrayList<>());
            merged.addAll(implList);
        }));
    }
}