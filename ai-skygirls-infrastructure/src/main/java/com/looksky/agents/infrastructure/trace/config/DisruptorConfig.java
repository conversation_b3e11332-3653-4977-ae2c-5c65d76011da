package com.looksky.agents.infrastructure.trace.config;


import com.lmax.disruptor.EventFactory;
import com.lmax.disruptor.EventHandler;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.SleepingWaitStrategy;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import com.lmax.disruptor.util.DaemonThreadFactory;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.infrastructure.trace.model.MethodInvocationRecord;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.mongodb.core.MongoTemplate;

@Slf4j
@Configuration
public class DisruptorConfig {

    @Setter
    @Getter
    public static class TraceEvent {
        private MethodInvocationRecord methodInvocationRecord;

        public void clear() {
            this.methodInvocationRecord = null;
        }
    }

    public static class TraceEventFactory implements EventFactory<TraceEvent> {
        @Override
        public TraceEvent newInstance() {
            return new TraceEvent();
        }
    }

    @Getter
    public static class TraceEventHandler implements EventHandler<TraceEvent> {
        private final TracingProperties properties;
        private final List<MethodInvocationRecord> batch;
        private final MongoTemplate mongoTemplate;

        public TraceEventHandler(MongoTemplate mongoTemplate, TracingProperties properties) {
            this.properties = properties;
            this.mongoTemplate = mongoTemplate;
            this.batch = new ArrayList<>(properties.getBuffer().getLocal().getBatchSize());
        }

        @Override
        public void onEvent(TraceEvent event, long sequence, boolean endOfBatch) {
            if (event.getMethodInvocationRecord() != null) {
                batch.add(event.getMethodInvocationRecord());
            }
            // 清理事件以便复用, 即使record是null也调用clear
            event.clear();

            if (!batch.isEmpty() && (batch.size() >= properties.getBuffer().getLocal().getBatchSize() || endOfBatch)) {
                flushBatch();
            }
        }

        /**
         * 手动将当前批次的数据刷新到MongoDB
         * 可以在需要时调用，例如应用关闭前或通过API触发
         */
        public synchronized void flushBatch() {
            if (!batch.isEmpty()) {
                String collectionName = properties.getMongodb().getMethodInvocationsCollection();
                ArrayList<MethodInvocationRecord> copyList = new ArrayList<>(batch);
                VirtualCompletableFuture.runAsync(() -> mongoTemplate.insert(copyList, collectionName));
                batch.clear();
            }
        }
    }

    @Bean
    public TraceEventHandler traceEventHandler(MongoTemplate mongoTemplate, TracingProperties properties) {
        if (!properties.isEnabled()) {
            log.info("Tracing is disabled. TraceEventHandler will not be initialized.");
            return null;
        }
        return new TraceEventHandler(mongoTemplate, properties);
    }

    @Bean
    @DependsOn({"tracingProperties", "traceEventHandler"}) // 确保 TracingProperties 和 TraceEventHandler 先被加载
    public RingBuffer<TraceEvent> traceRingBuffer(TracingProperties properties, TraceEventHandler traceEventHandler) {
        if (!properties.isEnabled()) {
            log.info("Tracing is disabled. Disruptor will not be initialized.");
            return null; // 如果禁用追踪，则不创建RingBuffer
        }

        TraceEventFactory factory = new TraceEventFactory();
        int bufferSize = properties.getBuffer().getLocal().getRingBufferSize();

        Disruptor<TraceEvent> disruptor = new Disruptor<>(
            factory,
            bufferSize,
            DaemonThreadFactory.INSTANCE, // 使用守护线程
            ProducerType.MULTI, // 允许多个生产者 (aspect可能在多个线程中运行)
            new SleepingWaitStrategy() // 或其他等待策略
        );

        disruptor.handleEventsWith(traceEventHandler);
        disruptor.start();
        log.info("Disruptor started with RingBuffer size: {}", bufferSize);
        return disruptor.getRingBuffer();
    }
}