package com.looksky.agents.infrastructure.httpclient;

import com.looksky.agents.infrastructure.interceptor.HttpHeaderInterceptor;
import com.looksky.agents.infrastructure.thread.VirtualThreadExecutor;
import java.net.http.HttpClient;
import java.net.http.HttpClient.Version;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.client.RestClientCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.JdkClientHttpRequestFactory;

@Slf4j
@Configuration
public class RestClientConfig {

    @Bean
    RestClientCustomizer javaHttpClientCustomizer(HttpClientProperties properties) {
        return restClientBuilder -> {
            // 使用Java 21的HttpClient，不需要连接池管理
            HttpClient httpClient = HttpClient.newBuilder()
                .connectTimeout(properties.getConnectTimeout())
                .executor(VirtualThreadExecutor.get())
                // 显式指定HTTP版本为HTTP/2
                .version(Version.HTTP_2)
                .build();

            // 使用Java HttpClient创建请求工厂
            JdkClientHttpRequestFactory requestFactory = new JdkClientHttpRequestFactory(httpClient);
            requestFactory.setReadTimeout(properties.getReadTimeout());

            // 添加日志和请求头拦截器
            restClientBuilder.requestFactory(
                new BufferingClientHttpRequestFactory(requestFactory)
            ).requestInterceptors(interceptors -> {
                if (properties.isLogRequests() || properties.isLogResponses()) {
                    interceptors.add(new HttpLoggingInterceptor(properties.isLogRequests(), properties.isLogResponses()));
                }
                interceptors.add(new HttpHeaderInterceptor());
            });
        };
    }
}
