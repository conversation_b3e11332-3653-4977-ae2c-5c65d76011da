package com.looksky.agents.infrastructure.collect.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 收集事件的注解
 *
 * <AUTHOR>
 * @since 1.1.11
 **/
@Documented
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CollectEvent {
    /**
     * 事件ID
     */
    String eid() default "api";

    /**
     * 用户ID表达式，支持SpEL表达式
     * 例如: "#userId" 或 "@userService.getCurrentUserId()"
     */
    String userIdExpression() default "";

    String typeExpression() default "";

    String extExpression() default "";

    String error() default "";


}
