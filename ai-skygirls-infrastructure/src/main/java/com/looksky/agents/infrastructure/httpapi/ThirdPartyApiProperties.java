package com.looksky.agents.infrastructure.httpapi;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@Component
@ConfigurationProperties(prefix = "http.third-party")
public class ThirdPartyApiProperties {
    private Map<String, String> baseUrls;
    private Map<String, Map<String, String>> headers;
} 