package com.looksky.agents.infrastructure.thread;

import com.alibaba.ttl.TtlCallable;
import com.alibaba.ttl.TtlRunnable;
import com.alibaba.ttl.threadpool.TtlExecutors;
import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Supplier;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.CallableWrapper;
import org.apache.skywalking.apm.toolkit.trace.SupplierWrapper;

/**
 * 虚拟线程执行器单例
 */
@Slf4j
public class VirtualThreadExecutor {
    private static final Executor VIRTUAL_THREAD_EXECUTOR;
    
    static {
        Executor executor = Executors.newVirtualThreadPerTaskExecutor();
        VIRTUAL_THREAD_EXECUTOR = TtlExecutors.getTtlExecutor(executor);
    }
    
    /**
     * 获取执行器实例
     */
    public static Executor get() {
        return VIRTUAL_THREAD_EXECUTOR;
    }
    
    /**
     * 异步执行有返回值的任务
     */
    @Deprecated(since = "1.1.6", forRemoval = true)
    public static <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(BaseAwareSupplier.wrap(supplier), VIRTUAL_THREAD_EXECUTOR);
    }

    
    /**
     * 异步执行无返回值的任务
     */
    @Deprecated(since = "1.1.6", forRemoval = true)
    public static CompletableFuture<Void> runAsync(Runnable runnable) {
        return CompletableFuture.runAsync(BaseAwareRunnable.wrap(runnable), VIRTUAL_THREAD_EXECUTOR);
    }

    // 执行单个任务
    @Deprecated(since = "1.1.6", forRemoval = true)
    public static void execute(Runnable task) {
        Runnable ttlTask = TtlRunnable.get(task);
        VIRTUAL_THREAD_EXECUTOR.execute(BaseAwareRunnable.wrap(ttlTask));
    }

    /**
     * 执行多个任务并等待所有结果
     */
    @Deprecated(since = "1.1.6", forRemoval = true)
    public static <T> List<T> executeWithResults(Collection<Callable<T>> tasks) {
        List<CompletableFuture<T>> futures = tasks.stream()
                .map(task -> CompletableFuture.supplyAsync(() -> {
                    try {
                        return CallableWrapper.of(TtlCallable.get(task)).call();
                    } catch (Exception e) {
                        log.error("Task execution failed", e);
                        throw new CompletionException(e);
                    }
                }, VIRTUAL_THREAD_EXECUTOR))
                .toList();

        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            return futures.stream()
                    .map(CompletableFuture::join)
                    .toList();
        } catch (Exception e) {
            throw new CompletionException("Error executing tasks", e);
        }
    }

    /**
     * 执行多个任务并等待所有结果,带超时
     */
    @Deprecated(since = "1.1.6", forRemoval = true)
    public static <T> List<T> executeWithResults(Collection<Callable<T>> tasks, long timeout, TimeUnit unit) {
        List<CompletableFuture<T>> futures = tasks.stream()
                .map(task -> CompletableFuture.supplyAsync(() -> {
                    try {
                        return CallableWrapper.of(TtlCallable.get(task)).call();
                    } catch (Exception e) {
                        log.error("Task execution failed", e);
                        throw new CompletionException(e);
                    }
                }, VIRTUAL_THREAD_EXECUTOR))
                .toList();

        try {
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.get(timeout, unit);
            return futures.stream()
                    .map(CompletableFuture::join)
                    .toList();
        } catch (TimeoutException e) {
            throw new CompletionException("Tasks execution timed out", e);
        } catch (Exception e) {
            throw new CompletionException("Error executing tasks", e);
        }
    }

    /**
     * 执行单个任务,带超时
     */
    @Deprecated(since = "1.1.6", forRemoval = true)
    public static <T> T executeWithTimeout(Callable<T> task, long timeout, TimeUnit unit) {
        try {
            return CompletableFuture.supplyAsync(() -> {
                try {
                    return CallableWrapper.of(TtlCallable.get(task)).call();
                } catch (Exception e) {
                    throw new CompletionException(e);
                }
            }, VIRTUAL_THREAD_EXECUTOR).get(timeout, unit);
        } catch (TimeoutException e) {
            throw new CompletionException("Task execution timed out", e);
        } catch (Exception e) {
            throw new CompletionException("Error executing task", e);
        }
    }


    @Deprecated(since = "1.1.6", forRemoval = true)
    public static  <T> List<T> executeWithResults(List<Callable<T>> tasks, Duration timeout) {
        List<CompletableFuture<T>> futures = tasks.stream()
                .map(task -> CompletableFuture.supplyAsync(() -> {
                    try {
                        return CallableWrapper.of(TtlCallable.get(task)).call();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, VIRTUAL_THREAD_EXECUTOR))
                .toList();

        try {
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.get(timeout.toMillis(), TimeUnit.MILLISECONDS);

            return futures.stream()
                    .map(CompletableFuture::join)
                    .toList();
        } catch (TimeoutException e) {
            throw new RuntimeException("Tasks execution timed out after " + timeout.toSeconds() + " seconds", e);
        } catch (Exception e) {
            throw new RuntimeException("Error executing tasks", e);
        }
    }

    /**
     * 执行任务并处理异常
     */
    @Deprecated(since = "1.1.6", forRemoval = true)
    public static <T> CompletableFuture<T> supplyAsyncWithExceptionHandle(Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(SupplierWrapper.of(supplier), VIRTUAL_THREAD_EXECUTOR)
                .exceptionally(throwable -> {
                    log.error("Task execution failed", throwable);
                    throw new CompletionException(throwable);
                });
    }

    private VirtualThreadExecutor() {
        throw new UnsupportedOperationException("Utility class");
    }
}