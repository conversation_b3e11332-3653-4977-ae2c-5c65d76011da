package com.looksky.agents.infrastructure.trace.model;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "methodInvocations") // 会被 TracingProperties 覆盖
public class MethodInvocationRecord {
    @Id
    private String id; // spanId 通常用作文档ID
    private String description;
    private String traceId;
    private String spanId;
    private String parentSpanId;
    private String serviceName;
    private String className;
    private String methodName;
    private Instant startTime;
    private Instant endTime;
    private Long duration; // 毫秒
    private List<ParameterDetail> parameters;
    private ReturnDetail returnValue;
    private ExceptionDetail exception;
    private Map<String, String> tags; // 简化为 Map<String, String>
    private String version; // 数据模型版本
}