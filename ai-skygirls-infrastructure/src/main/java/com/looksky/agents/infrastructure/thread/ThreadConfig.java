package com.looksky.agents.infrastructure.thread;

import java.util.concurrent.Executor;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.TaskDecorator;
import org.springframework.core.task.TaskExecutor;
import org.springframework.core.task.support.TaskExecutorAdapter;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableAsync
@Configuration
public class ThreadConfig {

    @Bean
    public Executor virtualThreadExecutor() {
        Executor rawExecutor = VirtualThreadExecutor.get();
        return task -> {
            if (rawExecutor != null) {
                rawExecutor.execute(() -> BaseAwareRunnable.wrap(task).run());
            }
        };
    }

    @Bean
    public TaskExecutor taskExecutor() {
        return new TaskExecutorAdapter(virtualThreadExecutor());
    }

    /**
     * 配置全局异步任务使用虚拟线程，并用 TTL 包装
     */
    @Bean
    public AsyncTaskExecutor applicationTaskExecutor() {

        TaskExecutorAdapter executor = new TaskExecutorAdapter(VirtualThreadExecutor.get());

        // 配置异常处理和上下文清理
        TaskDecorator decorator = task -> () -> BaseAwareRunnable.wrap(task).run();

        executor.setTaskDecorator(decorator);
        return executor;
    }

    /**
     * 配置 Tomcat 使用虚拟线程处理请求，并用 TTL 包装
     */
    @Bean
    public TomcatConnectorCustomizer virtualThreadsCustomizer() {
        return connector -> {
            // 创建包装的执行器，确保请求处理完成后清理上下文
            Executor contextCleaningExecutor = new Executor() {
                private final Executor delegate = VirtualThreadExecutor.get();

                @Override
                public void execute(@NotNull Runnable command) {
                    // 使用与异步任务相同的包装方式
                    delegate.execute(() -> BaseAwareRunnable.wrap(command).run());
                }
            };

            connector.getProtocolHandler().setExecutor(contextCleaningExecutor);
        };
    }
} 