package com.looksky.agents.infrastructure.user.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.NoArgsConstructor;

/**
 * 用户上下文
 *
 * @since  1.1.11
 * <AUTHOR>
 **/
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class UserContext {
    private static final TransmittableThreadLocal<String> currentUser = new TransmittableThreadLocal<>();

    public static void setUserId(String userId) {
        currentUser.set(userId);
    }

    public static String getUserId() {
        return currentUser.get();
    }

    public static void clear() {
        currentUser.remove();
    }

}
