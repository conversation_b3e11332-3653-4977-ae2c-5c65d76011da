package com.looksky.agents.infrastructure.httpclient;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * HTTP客户端初始化配置
 * 用于设置Java HTTP客户端的系统属性
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HttpClientInitializer {

    private final HttpClientProperties properties;

    @PostConstruct
    public void init() {
        log.info("配置HTTP/2协议参数");
        
        // 设置HTTP/2的最大并发流数（默认是100）
        System.setProperty("jdk.httpclient.http2.maxStreams", String.valueOf(properties.getMaxConcurrentStreams()));
        
        // 设置初始窗口大小（默认是64K）
        System.setProperty("jdk.httpclient.http2.initialWindowSize", String.valueOf(properties.getInitialWindowSize()));
        
        // 设置头部表的大小（默认是16K）
        System.setProperty("jdk.httpclient.http2.headerTableSize", String.valueOf(properties.getHeaderTableSize()));
        
        // 开启HTTP/2的多路复用
        System.setProperty("jdk.httpclient.allowRestrictedHeaders", "true");
        
        // 启用HTTP/2连接共享
        System.setProperty("jdk.httpclient.enableAllMethodRetry", "true");
        
        // 启用HTTP/2连接重用
        System.setProperty("jdk.httpclient.disableRetryConnect", "false");
        
        log.info("HTTP/2协议参数配置完成: maxStreams={}, initialWindowSize={}, headerTableSize={}", 
                properties.getMaxConcurrentStreams(), 
                properties.getInitialWindowSize(),
                properties.getHeaderTableSize());
    }
} 