package com.looksky.agents.infrastructure.exception;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.skygirls.biz.payment.dto.FeishuDTO;
import com.yomahub.tlog.context.TLogContext;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 飞书通知工具
 *
 * <AUTHOR>
 * @since 1.1.8
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class FeiShuNotifyHelper {

    private static final String FEI_SHU_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/";

    @Value("${http.third-party.headers.feiShu.secret}")
    private String secret;

    private static final HttpClient CLIENT = HttpClient.newHttpClient();

    private final ObjectMapper objectMapper;


    public void notify(String message) {
        VirtualCompletableFuture.runAsync(() -> {
            try {
                String traceMessage = Optional.ofNullable(TLogContext.getTraceId()).map(t -> "TraceId: [" + t + "] \nMessage: " + message).orElse(message);
                FeishuDTO feishuDTO = new FeishuDTO();
                feishuDTO.setContent(new FeishuDTO.Content().setText(traceMessage));
                String requestBody = objectMapper.writeValueAsString(feishuDTO);
                log.debug("飞书通知请求体: {}", requestBody);
                HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(FEI_SHU_URL + secret))
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .build();
                HttpResponse<String> response = CLIENT.send(request, HttpResponse.BodyHandlers.ofString());
                String responseBody = response.body();
                log.debug("飞书通知结果: {}", responseBody);
            } catch (IOException | InterruptedException e) {
                log.error("飞书通知失败", e);
                Thread.currentThread().interrupt();
            }
        });
    }

}
