package com.looksky.agents.infrastructure.thread;

import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;

public class BaseAwareRunnable implements Runnable {
    private final Runnable delegate;

    public BaseAwareRunnable(Runnable delegate) {
        this.delegate = delegate;
    }
    
    @Override
    public void run() {
        delegate.run();
    }
    
    public static Runnable wrap(Runnable runnable) {
        return new BaseAwareRunnable(
            ContextAwareRunnable.wrap(  // 版本上下文
                RunnableWrapper.of(  // skywalking 上下文
                    runnable
                )
            )
        );
    }
}