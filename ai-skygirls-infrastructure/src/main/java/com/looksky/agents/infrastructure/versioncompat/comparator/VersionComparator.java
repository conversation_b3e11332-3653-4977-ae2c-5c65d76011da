package com.looksky.agents.infrastructure.versioncompat.comparator;

import java.util.Comparator;

/**
 * 版本号比较器
 */
public class VersionComparator implements Comparator<String> {
    @Override
    public int compare(String v1, String v2) {
        String[] parts1 = v1.split("\\.");
        String[] parts2 = v2.split("\\.");

        // 比较主版本号
        int majorCompare = Integer.compare(Integer.parseInt(parts1[0]), Integer.parseInt(parts2[0]));
        if (majorCompare != 0) {
            return majorCompare;
        }

        // 比较次版本号
        if (parts1.length > 1 && parts2.length > 1) {
            int minorCompare = Integer.compare(Integer.parseInt(parts1[1]), Integer.parseInt(parts2[1]));
            if (minorCompare != 0) {
                return minorCompare;
            }
        } else if (parts1.length > 1) {
            return 1;
        } else if (parts2.length > 1) {
            return -1;
        }

        // 比较修订版本号
        if (parts1.length > 2 && parts2.length > 2) {
            return Integer.compare(Integer.parseInt(parts1[2]), Integer.parseInt(parts2[2]));
        } else if (parts1.length > 2) {
            return 1;
        } else if (parts2.length > 2) {
            return -1;
        }

        return 0;
    }
}