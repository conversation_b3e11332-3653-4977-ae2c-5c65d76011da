package com.looksky.agents.infrastructure.trace.service;


import com.lmax.disruptor.RingBuffer;
import com.looksky.agents.infrastructure.trace.config.DisruptorConfig;
import com.looksky.agents.infrastructure.trace.config.TracingProperties;
import com.looksky.agents.infrastructure.trace.model.MethodInvocationRecord;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class DisruptorService {

    private final RingBuffer<DisruptorConfig.TraceEvent> ringBuffer;
    private final TracingProperties tracingProperties;
    private final DisruptorConfig.TraceEventHandler traceEventHandler;

    public void publish(MethodInvocationRecord methodInvocationRecord) {
        if (!tracingProperties.isEnabled() || ringBuffer == null) {
            return; // 如果禁用追踪或 RingBuffer 未初始化，则不发布
        }

        long sequence = ringBuffer.next();
        try {
            DisruptorConfig.TraceEvent event = ringBuffer.get(sequence);
            event.setMethodInvocationRecord(methodInvocationRecord);
        } finally {
            ringBuffer.publish(sequence);
        }
    }


    /**
     * 手动将当前批次的追踪数据刷新到MongoDB
     * 可以在请求完成后调用此方法，确保所有追踪数据都被保存
     */
    public void flushTracesToMongoDB() {
        if (traceEventHandler != null) {
            traceEventHandler.flushBatch();
        }
    }
}