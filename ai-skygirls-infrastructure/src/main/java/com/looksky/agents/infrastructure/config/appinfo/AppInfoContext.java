package com.looksky.agents.infrastructure.config.appinfo;

import cn.hutool.core.comparator.VersionComparator;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import java.util.Map;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;

/**
 * App 信息上下文
 *
 * <AUTHOR>
 * @since 1.1.14
 **/
@Slf4j
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class AppInfoContext {
    private static final TransmittableThreadLocal<AppInfo> currentAppInfo = new TransmittableThreadLocal<>();
    private static final String SEPARATED_VERSION = "1.2.0";
    private static final AppInfo LOOKSKY_INFO = AppInfo.builder().name("LookSky").role("Skylar").email("<EMAIL>").yearProPrice("$49.99/year").build();
    private static final AppInfo SKYGIRLS_INFO = AppInfo.builder().name("SkyGirls").role("Skylar").email("<EMAIL>").yearProPrice("$49.99/year").build();

    /**
     * 根据 平台 和 版本设置 appInfo
     *
     * @param platformType 平台
     * @param version      版本
     */
    public static void setAppInfo(@Nullable ApiVersion.PlatformType platformType, String version) {
        // LookSky
        if (VersionComparator.INSTANCE.compare(version, SEPARATED_VERSION) >= 0) {
            currentAppInfo.set(LOOKSKY_INFO);
            log.debug("当前 APP 是 LookSky");
        } else {
            currentAppInfo.set(SKYGIRLS_INFO);
            log.debug("当前 APP 是 Skygirls");
        }
    }

    /**
     * 只根据版本设置 appInfo
     *
     * @param version 版本
     */
    public static void setAppInfo(String version) {
        setAppInfo(ApiVersion.PlatformType.ALL, version);
    }

    /**
     * 获取 AppInfo 对象
     *
     * @return AppInfo
     */
    public static AppInfo getAppInfo() {
        if (currentAppInfo.get() == null) {
            return LOOKSKY_INFO;
        }

        return currentAppInfo.get();
    }


    /**
     * 获取 AppInfo Map
     *
     * @return map
     */
    public static Map<String, String> getAppInfoMap() {
        AppInfo appInfo = getAppInfo();
        return Map.of("name", appInfo.name, "role", appInfo.role, "email", appInfo.email, "yearProPrice", appInfo.yearProPrice);
    }

    /**
     * 获取 AppInfo 中的 name
     * @return appName
     */
    public static String getName() {
        return getAppInfo().name;
    }

    /**
     * 清除 Context 的信息
     */
    public static void clear() {
        currentAppInfo.remove();
    }


    /**
     * APP Info 对象
     */
    @Builder
    public static class AppInfo {
        private String name;
        private String role;
        private String email;
        private String yearProPrice;
    }

}
