package com.looksky.agents.infrastructure.versioncompat.annotation;

import com.looksky.agents.infrastructure.versioncompat.version.VersionContext;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Repeatable(ApiVersion.ApiVersions.class)
public @interface ApiVersion {
    /**
     * 平台类型
     */
    PlatformType platform() default PlatformType.ALL;

    /**
     * 版本号，格式如：1.0.0
     */
    String version() default VersionContext.DEFAULT_VERSION;

    /**
     * 平台类型枚举
     */
    enum PlatformType {
        IOS,
        ANDROID,
        WEB,
        ALL;  // 可用于所有平台

        public static PlatformType fromStr(String type) {
            if ("iOS".equals(type)) {
                return IOS;
            } else if ("Android".equals(type)) {
                return ANDROID;
            } else {
                return ALL;
            }
        }
    }

    /**
     * 容器注解，用于支持在同一个类上添加多个ApiVersion
     */
    @Target({ElementType.TYPE})
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    @interface ApiVersions {
        ApiVersion[] value();
    }
}