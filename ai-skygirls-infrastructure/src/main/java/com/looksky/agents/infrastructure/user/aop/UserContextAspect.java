package com.looksky.agents.infrastructure.user.aop;

import cn.hutool.core.text.CharSequenceUtil;
import com.looksky.agents.infrastructure.user.context.UserContext;
import jakarta.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 用户上下文切面，拦截所有Controller方法并提取用户ID
 *
 * @since  1.1.11
 * <AUTHOR>
 **/
@Slf4j
@Aspect
@Component
@Order(0) // 确保这个切面比其他切面先执行
public class UserContextAspect {

    /**
     * 拦截所有Controller方法
     */
    @Around("within(@org.springframework.web.bind.annotation.RestController *)" +
        "|| within(@org.springframework.stereotype.Controller *)" +
        "|| within(@com.looksky.agents.infrastructure.response.ResponseResultBody *)")
    public Object resolveUserId(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            // 提取并设置用户ID
            String userId = extractUserId(joinPoint);
            if (CharSequenceUtil.isNotBlank(userId)) {
                UserContext.setUserId(userId);
                log.debug("设置用户上下文，userId: {}", userId);
            }
            
            // 执行原始方法
            return joinPoint.proceed();
        } finally {
            // 清理上下文
            UserContext.clear();
        }
    }

    /**
     * 从请求中提取userId
     */
    private String extractUserId(ProceedingJoinPoint joinPoint) {
        // 尝试从方法参数中提取userId
        String userId = extractUserIdFromMethodArgs(joinPoint);
        if (CharSequenceUtil.isNotBlank(userId)) {
            return userId;
        }

        // 尝试从请求参数或路径中提取userId
        return extractUserIdFromRequest();
    }

    /**
     * 从方法参数中提取userId
     */
    private String extractUserIdFromMethodArgs(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        Object[] args = joinPoint.getArgs();

        // 遍历所有参数
        for (int i = 0; i < parameters.length; i++) {
            // 检查参数名称
            String paramName = parameters[i].getName();
            if (isUserIdParam(paramName)) {
                Object arg = args[i];
                if (arg != null) {
                    return String.valueOf(arg);
                }
            }

            // 检查参数注解
            Annotation[] annotations = parameters[i].getAnnotations();
            for (Annotation annotation : annotations) {
                if (annotation instanceof PathVariable) {
                    String value = ((PathVariable) annotation).value();
                    if (value.isEmpty()) {
                        value = ((PathVariable) annotation).name();
                    }
                    if (value.isEmpty()) {
                        value = parameters[i].getName();
                    }
                    if (isUserIdParam(value) && args[i] != null) {
                        return String.valueOf(args[i]);
                    }
                } else if (annotation instanceof RequestParam) {
                    String value = ((RequestParam) annotation).value();
                    if (value.isEmpty()) {
                        value = ((RequestParam) annotation).name();
                    }
                    if (value.isEmpty()) {
                        value = parameters[i].getName();
                    }
                    if (isUserIdParam(value) && args[i] != null) {
                        return String.valueOf(args[i]);
                    }
                }
            }

            // 如果参数是对象，尝试从对象中提取userId
            if (args[i] != null && !isPrimitiveOrWrapper(args[i].getClass())) {
                String userIdFromObject = extractUserIdFromObject(args[i]);
                if (CharSequenceUtil.isNotBlank(userIdFromObject)) {
                    return userIdFromObject;
                }
            }
        }

        return null;
    }

    /**
     * 从请求参数中提取userId
     */
    private String extractUserIdFromRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();

            // 尝试从查询参数中获取
            String userId = request.getParameter("userId");
            if (userId == null) {
                userId = request.getParameter("uid");
            }
            if (userId == null) {
                userId = request.getParameter("user_id");
            }

            if (userId != null) {
                try {
                    return userId;
                } catch (NumberFormatException e) {
                    // 忽略转换错误
                }
            }
        }

        return null;
    }

    /**
     * 从对象中提取userId字段
     */
    private String extractUserIdFromObject(Object obj) {
        if (obj == null) {
            return null;
        }

        Class<?> clazz = obj.getClass();
        while (clazz != null) { // 遍历当前类及其所有父类
            try {
                for (String fieldName : new String[] {"userId", "uid", "user_id", "id"}) {
                    try {
                        java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
                        field.setAccessible(true);
                        Object value = field.get(obj);
                        if (value != null) {
                            return String.valueOf(value);
                        }
                    } catch (NoSuchFieldException e) {
                        // 忽略不存在的字段，继续查找下一个字段
                    }
                }
            } catch (IllegalAccessException e) {
                // 忽略访问异常
            }
            clazz = clazz.getSuperclass(); // 继续查找父类
        }
        return null;
    }

    /**
     * 判断参数名是否是userId相关
     */
    private boolean isUserIdParam(String paramName) {
        return "userId".equalsIgnoreCase(paramName) ||
                "uid".equalsIgnoreCase(paramName) ||
                "user_id".equalsIgnoreCase(paramName);
    }

    /**
     * 判断是否为基本类型或包装类
     */
    private boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return clazz.isPrimitive() ||
                clazz == String.class ||
                clazz == Boolean.class ||
                clazz == Character.class ||
                clazz == Byte.class ||
                clazz == Short.class ||
                clazz == Integer.class ||
                clazz == Long.class ||
                clazz == Float.class ||
                clazz == Double.class;
    }
} 