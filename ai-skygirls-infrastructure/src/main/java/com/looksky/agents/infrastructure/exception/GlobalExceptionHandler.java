package com.looksky.agents.infrastructure.exception;

import cn.hutool.core.text.CharSequenceUtil;
import com.graecove.common.ApiResp;
import com.yomahub.tlog.context.TLogContext;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

/**
 * 全局异常处理器
 *
 * @since  1.1.0
 * <AUTHOR>
 **/
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {


    @Resource
    private FeiShuNotifyHelper feiShuNotifyHelper;


    /**
     * 全局异常处理
     * @param e 异常
     * @return ApiResp 包裹的异常, 携带 TraceId
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(Exception.class)
    public ApiResp<Object> exceptionHandler(Exception e){
   
        // 处理业务异常
        switch (e) {
            case BusinessException businessException -> {
                return buildWithMessageApiResp(ApiResp.FAIL_CODE, businessException.getMessage());
            }
            case MethodArgumentNotValidException methodArgumentNotValidException -> {
                // 参数检验异常
                Map<String, String> map = new HashMap<>();
                BindingResult result = methodArgumentNotValidException.getBindingResult();
                result.getFieldErrors().forEach(item -> {
                    String message = item.getDefaultMessage();
                    String field = item.getField();
                    map.put(field, message);
                });
                log.error("数据校验出现错误：", e);
                return buildWithMessageApiResp(ApiResp.FAIL_CODE, map.toString());
            }
            case HttpRequestMethodNotSupportedException httpRequestMethodNotSupportedException -> {
                log.error("请求方法错误：", e);
                return buildWithMessageApiResp(ApiResp.FAIL_CODE, "请求方法不正确: " + httpRequestMethodNotSupportedException.getMessage());
            }
            case MissingServletRequestParameterException ex -> {

                log.error("请求参数缺失：", e);
                return buildWithMessageApiResp(ApiResp.FAIL_CODE, "请求参数缺少" + ex.getParameterName());
            }
            case MethodArgumentTypeMismatchException ex -> {
                log.error("请求参数类型错误：", e);
                return buildWithMessageApiResp(ApiResp.FAIL_CODE, "请求参数类型不正确" + ex.getName());
            }
            case NoHandlerFoundException ex -> {

                log.error("请求地址不存在：", e);
                return buildWithMessageApiResp(ApiResp.FAIL_CODE, ex.getRequestURL());
            }
            case null, default -> {
                //系统的异常，比如空指针这些异常
                log.error("【系统异常】", e);
                return buildWithMessageApiResp(ApiResp.SYSTEM_ERROR_CODE, Optional.ofNullable(e).map(Throwable::getMessage).orElse("未知异常: " + e));
            }
        }
    }

    private ApiResp<Object> buildWithMessageApiResp(String code, String message) {
         feiShuNotifyHelper.notify(message);
        String traceId = TLogContext.getTraceId();
        if (CharSequenceUtil.isNotBlank(traceId)) {
            message = "TraceId: ["+ traceId + "] --- " + message;
        }
        ApiResp<Object> apiResp = ApiResp.error(code);
        apiResp.setMessage(message);
        return apiResp;
    }

}