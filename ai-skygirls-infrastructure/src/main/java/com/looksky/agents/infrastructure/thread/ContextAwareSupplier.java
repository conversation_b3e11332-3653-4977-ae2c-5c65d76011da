package com.looksky.agents.infrastructure.thread;

import com.looksky.agents.infrastructure.config.appinfo.AppInfoContext;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.user.context.UserContext;
import com.looksky.agents.infrastructure.versioncompat.version.VersionContext;
import java.util.function.Supplier;

public class ContextAwareSupplier<T> implements Supplier<T> {
    private final Supplier<T> delegate;

    public ContextAwareSupplier(Supplier<T> delegate) {
        this.delegate = delegate;
    }

    @Override
    public T get() {
        try {
            return delegate.get();
        } finally {
            // 任务完成后清理版本上下文
            VersionContext.clear();
            Context.clear();
            UserContext.clear();
            AppInfoContext.clear();
        }
    }

    public static <T> Supplier<T> wrap(Supplier<T> supplier) {
        return new ContextAwareSupplier<>(supplier);
    }
}