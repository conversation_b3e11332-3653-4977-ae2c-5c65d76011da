package com.looksky.agents.infrastructure.trace.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "trace")
public class TracingProperties {
    private boolean enabled = true;
    private String serviceName;
    private Buffer buffer = new Buffer();
    private Redis redis = new Redis();
    private Mongodb mongodb = new Mongodb();
    private Aspect aspect = new Aspect();

    @Getter @Setter
    public static class Buffer {
        private Local local = new Local();
        @Getter @Setter
        public static class Local {
            private int ringBufferSize = 1024;
            private int batchSize = 100;
        }
    }

    @Getter @Setter
    public static class Redis {
        private String queueNamePrefix = "trace:queue:"; // 向后兼容，但Demo中直接用queueName
        private String queueName = "method-invocations"; // 完整的队列名
    }

    @Getter @Setter
    public static class Mongodb {
        private String methodInvocationsCollection = "methodInvocations";
        private String invocationChainsCollection = "invocationChains";
        private int batchWriteSize = 500;
    }
    
    @Getter @Setter
    public static class Aspect {
        private boolean defaultCaptureParams = true;
        private boolean defaultCaptureReturnValue = true;
    }
}