package com.looksky.agents.infrastructure.versioncompat.aop;

import cn.hutool.core.text.CharSequenceUtil;
import com.looksky.agents.infrastructure.config.appinfo.AppInfoContext;
import com.looksky.agents.infrastructure.user.context.UserContext;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import com.looksky.agents.infrastructure.versioncompat.client.UaClient;
import com.looksky.agents.infrastructure.versioncompat.version.VersionContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
@Order(1) // 确保这个切面最先执行
public class VersionResolvingAspect {
    private final UaClient uaClient;

    @Autowired
    public VersionResolvingAspect(UaClient uaClient) {
        this.uaClient = uaClient;
    }

    /**
     * 拦截带有@VersionCompatController注解的控制器中的所有方法 <br/>
     * 或带有@VersionCompatMethod注解的单个方法
     */
    @Around("(@within(com.looksky.agents.infrastructure.versioncompat.annotation.VersionCompatController) || " +
        "@annotation(com.looksky.agents.infrastructure.versioncompat.annotation.VersionCompatMethod)) && " +
        "execution(* *(..))")
    public Object resolveVersionFromUserId(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            // 设置版本上下文
            setupVersionContext();
            // 执行原始方法
            return joinPoint.proceed();
        } finally {
            // 清理上下文
            VersionContext.clear();
        }
    }

    /**
     * 设置版本上下文
     */
    private void setupVersionContext() {
        String userId = UserContext.getUserId();
        String version = VersionContext.DEFAULT_VERSION;
        ApiVersion.PlatformType platform = VersionContext.DEFAULT_PLATFORM;

        // 如果找到了userId，从服务获取版本信息
        if (userId != null) {
            String ua = uaClient.getUserUa(userId);

            if (CharSequenceUtil.isNotBlank(ua)) {

                String[] split = ua.split("/");

                version = split[2];

                platform = ApiVersion.PlatformType.fromStr(split[0]);
            }

        }

        log.info("用户userId: {}, 用户版本: {}, 用户平台: {}", userId, version, platform);

        VersionContext.setVersion(version);
        VersionContext.setPlatform(platform);
        AppInfoContext.setAppInfo(version);
    }
}