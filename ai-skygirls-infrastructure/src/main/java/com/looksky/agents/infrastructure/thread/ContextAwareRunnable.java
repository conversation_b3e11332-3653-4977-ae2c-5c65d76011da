package com.looksky.agents.infrastructure.thread;

import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.user.context.UserContext;
import com.looksky.agents.infrastructure.versioncompat.version.VersionContext;

public class ContextAwareRunnable implements Runnable {
    private final Runnable delegate;

    public ContextAwareRunnable(Runnable delegate) {
        this.delegate = delegate;
    }

    @Override
    public void run() {
        try {
            delegate.run();
        } finally {
            // 任务完成后清理版本上下文
            VersionContext.clear();
            Context.clear();
            UserContext.clear();
        }
    }

    public static Runnable wrap(Runnable runnable) {
        return new ContextAwareRunnable(runnable);
    }
}