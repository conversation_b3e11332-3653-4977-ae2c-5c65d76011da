package com.looksky.agents.infrastructure.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class Context {
    private static final TransmittableThreadLocal<ConcurrentHashMap<String, Object>> contextMap = TransmittableThreadLocal.withInitial(ConcurrentHashMap::new);

    public static Map<String, Object> get() {
        return contextMap.get();
    }

    @SuppressWarnings("unchecked")
    public static <T> T get(String key) {
        return (T) get().get(key);
    }

    public static void put(String key, Object value) {
        get().put(key, value);
    }

    public static void putAll(Map<String, Object> map) {
        get().putAll(map);
    }

    public static void clear() {
        contextMap.remove();
    }


    @Getter
    @AllArgsConstructor
    public enum Name {
        REQUEST_INPUT("requestInput", "请求 chat 接口的对象"),
        CONTENT("content", "默认内容空间"),
        @Deprecated
        USER_PERSONAL_CENTER_DATA("personalCenterData", "用户个人中心的数据"),
        @Deprecated
        EXTRACTED_BODY_DATA("userBodyInfo", "用户在对话中保存的特征消息"),
        SYSTEM("system", "系统信息空间"),
        ORDER("order", " 测试"),
        COMMON_MODEL_REQUEST_DTO("commonModelRequestDTO", "外部调用 agent 的通用请求"),
        USER_REGISTRATION_FILLS_OUT_DATA("userRegistrationFillsOutData", "用户注册答题数据"),
        EVENT_LIST("eventList", "事件列表"),
        EVENT("event", "当前事件"),
        CURRENT("current", "当前的数据"),
        OUTPUT("output", "prompt 的输出结果"),
        STATUS("status", "会话状态"),
        PREFERENCE("preference", "用户偏好"),
        SEARCH_RESULT("searchResult", "搜索的结果"),
        EVENT_MODEL("eventModel", "主动触发的事件"),
        USER_INFO("userInfo", "用户信息, 包含个人信息, 以及报告信息"),
        @Deprecated
        CLOTH_INDEX("clothIndex", "衣服索引"),
        PRODUCT_INFO("productInfo", "商品信息"),
        USER_LOCATION("userLocation", "用户所在位置的信息"),
        USER_PREV_VISIT_DATE("userPrevVisitDate", "用户上一次访问的时间"),
        SEARCH_PROCESS("searchProcess", "搜索过程"),
        DATE_DATA("dateData", "用户所在的时区的时间数据"),
        NEW_CONVERSATION_STATUS("newConversationStatus", "新会话状态"),
        BRAND_INFO_DATA("brandInfoData", "品牌信息"),
        PRODUCT_INFO_DATA("productInfoData", "商品信息"),
        PRODUCT_INFO_ALL_DATA("productInfoAllData", "商品信息"),
        BRAND_INFO_ALL_DATA("brandInfoAllData", "品牌的所有信息"),
        BRAND_ALL_NAME_DATA("brandAllNameData", "品牌的所有名字信息"),
        ;

        private final String name;
        private final String description;

    }
}