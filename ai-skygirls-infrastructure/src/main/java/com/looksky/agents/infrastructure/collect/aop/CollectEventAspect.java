package com.looksky.agents.infrastructure.collect.aop;

import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.collect.client.CollectEventClient;
import com.looksky.agents.infrastructure.user.context.UserContext;
import com.looksky.agents.infrastructure.versioncompat.version.VersionContext;
import com.looksky.agents.sdk.collect.EventDto;
import com.yomahub.tlog.context.TLogContext;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 收集事件注解的AOP处理切面
 *
 * @since  1.1.11
 * <AUTHOR>
 **/
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class CollectEventAspect {

    @Value("${spring.profiles.active}")
    private String env;

    private final CollectEventClient collectEventClient;
    private final ExpressionParser expressionParser = new SpelExpressionParser();
    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();



    @Around("@annotation(com.looksky.agents.infrastructure.collect.annotation.CollectEvent)")
    public Object collectEventData(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法和注解
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        CollectEvent collectEvent = method.getAnnotation(CollectEvent.class);

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 执行原方法
        Object result;
        try {
            result = joinPoint.proceed();
        } catch (Throwable throwable) {
            // 即使方法执行失败也记录时间
            recordExecutionTime(collectEvent, startTime, joinPoint, false, throwable.getMessage());
            throw throwable;
        }

        // 记录结束时间并发送事件
        recordExecutionTime(collectEvent, startTime, joinPoint, true, null);

        return result;
    }

    private void recordExecutionTime(CollectEvent collectEvent, long startTime, ProceedingJoinPoint joinPoint, boolean isSuccess, String errorMessage) {
        try {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;

            // 创建事件DTO
            EventDto eventDto = createEventDto(collectEvent, joinPoint, executionTime, isSuccess, errorMessage);

            // 发送到收集服务
            collectEventClient.batchCollectEvents(Collections.singletonList(eventDto));
        } catch (Exception e) {
            // 日志记录异常，但不影响原方法执行
            log.error("记录执行时间错误: {}", e.getMessage());
        }
    }

    private EventDto createEventDto(CollectEvent collectEvent, ProceedingJoinPoint joinPoint, long executionTime, boolean isSuccess, String errorMessage) {
        EventDto eventDto = new EventDto();

        // 设置 AppName
        //eventDto.setAppid(AppInfoContext.getName());
        // 设置环境
        eventDto.setTest("prod".equals(env) ? 0 : 1);
        Optional.ofNullable(VersionContext.getVersion()).ifPresent(eventDto::setAppVer);
        // 设置基本事件信息
        eventDto.setEid(collectEvent.eid());
        eventDto.setTs(System.currentTimeMillis());
        // 设置请求相关信息
        eventDto.setExt1(getMethodPrefix(joinPoint));
        // 设置唯一请求 id
        eventDto.setExt2(TLogContext.getTraceId());

        // 设置执行状态
        eventDto.setExt4(isSuccess ? "1" : "2");
        // 设置响应代码
        eventDto.setExt5(isSuccess ? "200" : "400");
        // 设置响应消息
        eventDto.setExt6(isSuccess ? "Success." : errorMessage);
        // 设置执行时间
        eventDto.setExt7(String.valueOf(executionTime));

        // 解析 type
        Optional.ofNullable(getExpressionResult(collectEvent.typeExpression(), joinPoint)).ifPresent(eventDto::setExt8);
        Optional.ofNullable(getExpressionResult(collectEvent.extExpression(), joinPoint)).ifPresent(eventDto::setExt9);

        // 解析并设置用户ID
        Optional.ofNullable(resolveUserId(collectEvent.userIdExpression(), joinPoint)).ifPresent(eventDto::setUid);

        return eventDto;
    }

    /**
     * 获取方法的前缀（类名.方法名）
     */
    private String getMethodPrefix(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        // 获取类的全限定名
        String className = signature.getDeclaringType().getName();
        // 获取方法名
        String methodName = signature.getName();

        // 返回格式: 类名.方法名
        return className + "." + methodName;
    }

    private String resolveUserId(String uidExpression, ProceedingJoinPoint joinPoint) {
        if (StringUtils.hasText(UserContext.getUserId())) {
            return UserContext.getUserId();
        }

        return getExpressionResult(uidExpression, joinPoint);
    }



    private String getExpressionResult(String expression, ProceedingJoinPoint joinPoint) {
        if (!StringUtils.hasText(expression)) {
            return null;
        }

        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Object[] args = joinPoint.getArgs();

            // 创建表达式评估上下文
            Object target = joinPoint.getTarget();
            EvaluationContext context = new MethodBasedEvaluationContext(
                    target, method, args, parameterNameDiscoverer);

            // 解析表达式
            return expressionParser.parseExpression(expression).getValue(context, String.class);
        } catch (Exception e) {
            log.error("解析表达式错误: {}", expression, e);
            return null;
        }
    }
}