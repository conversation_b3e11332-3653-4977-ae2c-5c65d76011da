package com.looksky.agents.infrastructure.thread;

import java.util.function.Supplier;
import org.apache.skywalking.apm.toolkit.trace.SupplierWrapper;

public class BaseAwareSupplier<T> implements Supplier<T> {
    private final Supplier<T> delegate;

    public BaseAwareSupplier(Supplier<T> delegate) {
        this.delegate = delegate;
    }

    @Override
    public T get() {
        return delegate.get();
    }

    public static <T> Supplier<T> wrap(Supplier<T> supplier) {
        return new BaseAwareSupplier<>(
            ContextAwareSupplier.wrap(  // 版本上下文
                SupplierWrapper.of(  // skywalking 上下文
                    supplier
                )
            )
        );
    }
}