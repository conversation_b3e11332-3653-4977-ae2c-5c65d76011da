package com.looksky.agents.infrastructure.httpclient;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
public class HttpLoggingInterceptor implements ClientHttpRequestInterceptor {
    private final boolean logRequests;
    private final boolean logResponses;

    public HttpLoggingInterceptor(boolean logRequests, boolean logResponses) {
        this.logRequests = logRequests;
        this.logResponses = logResponses;
    }

    @NotNull
    @Override
    public ClientHttpResponse intercept(@NotNull HttpRequest request, byte @NotNull [] body, @NotNull ClientHttpRequestExecution execution) throws IOException {
        if (logRequests) {
            logRequest(request, body);
        }

        ClientHttpResponse response = execution.execute(request, body);

        if (logResponses) {
            logResponse(response);
        }

        return response;
    }

    private void logRequest(HttpRequest request, byte[] body) {
        log.info("Request: {} {} {}", request.getMethod(), request.getURI(), JSONUtil.toJsonStr(new String(body, StandardCharsets.UTF_8)));
    }

    private void logResponse(ClientHttpResponse response) throws IOException {
        String responseBody = new String(StreamUtils.copyToByteArray(response.getBody()), StandardCharsets.UTF_8);
        try {
            log.info("Response: {} {}", response.getStatusCode(), JSONUtil.isTypeJSON(responseBody) ? JSONUtil.toJsonStr(JSONUtil.parse(responseBody)) : responseBody);
        } catch (Exception e) {
            log.info("Response: {} Raw Content: {}", response.getStatusCode(), responseBody);
        }
    }

} 