package com.looksky.agents.infrastructure.interceptor;

import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;

/**
 * GRPC 请求头拦截器, 用于给 GRPC 接口注入 AB TEST FLAG
 *
 * @since  1.1.0
 * <AUTHOR>
 **/
@Slf4j
@Component
public class HttpHeaderInterceptor implements ClientHttpRequestInterceptor {

    @NotNull
    @Override
    public ClientHttpResponse intercept(@NotNull HttpRequest request, byte @NotNull [] body, ClientHttpRequestExecution execution) throws IOException {
            // 执行请求
            return execution.execute(request, body);
    }
} 