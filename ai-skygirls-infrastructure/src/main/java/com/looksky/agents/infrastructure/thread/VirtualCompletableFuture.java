package com.looksky.agents.infrastructure.thread;


import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import org.jetbrains.annotations.NotNull;

public class VirtualCompletableFuture<T> implements CompletionStage<T> {
    private static final Executor VIRTUAL_EXECUTOR = VirtualThreadExecutor.get();

    private final CompletableFuture<T> delegate;

    private VirtualCompletableFuture(CompletableFuture<T> delegate) {
        this.delegate = delegate;
    }

    public static <U> VirtualCompletableFuture<U> supplyAsync(Supplier<U> supplier) {
        return new VirtualCompletableFuture<>(CompletableFuture.supplyAsync(BaseAwareSupplier.wrap(supplier), VIRTUAL_EXECUTOR));
    }

    public static VirtualCompletableFuture<Void> runAsync(Runnable runnable) {
        return new VirtualCompletableFuture<>(CompletableFuture.runAsync(BaseAwareRunnable.wrap(runnable), VIRTUAL_EXECUTOR));
    }

    // todo 这里会出现线程上下文丢失的问题
    @NotNull
    @Override
    public <U> VirtualCompletableFuture<U> thenApplyAsync(Function<? super T, ? extends U> fn) {
        return new VirtualCompletableFuture<>(delegate.thenApplyAsync(fn, VIRTUAL_EXECUTOR));
    }

    // todo 这里会出现线程上下文丢失的问题
    @NotNull
    @Override
    public <U> VirtualCompletableFuture<U> thenApplyAsync(@NotNull Function<? super T, ? extends U> fn, Executor executor) {
        return null;
    }

    @NotNull
    @Override
    public <U>  VirtualCompletableFuture<U> thenApply(@NotNull Function<? super T, ? extends U> fn) {
        return new VirtualCompletableFuture<>(delegate.thenApply(fn));
    }

    @NotNull
    @Override
    public VirtualCompletableFuture<Void> thenAccept(@NotNull Consumer<? super T> action) {
        return new VirtualCompletableFuture<>(delegate.thenAccept(action));
    }

    public T get() throws InterruptedException, ExecutionException {
        return delegate.get();
    }

    public static <U> VirtualCompletableFuture<U> completedFuture(U value) {
        return new VirtualCompletableFuture<>(CompletableFuture.completedFuture(value));
    }

    public static VirtualCompletableFuture<Void> allOf(VirtualCompletableFuture<?>... cfs) {
        CompletableFuture<?>[] futures = new CompletableFuture[cfs.length];
        for (int i = 0; i < cfs.length; i++) {
            futures[i] = cfs[i].delegate;
        }
        return new VirtualCompletableFuture<>(CompletableFuture.allOf(futures));
    }

    public T join() {
        return delegate.join();
    }

    public T get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        return delegate.get(timeout, unit);
    }


    @NotNull
    @Override
    public CompletionStage<Void> thenAcceptAsync(@NotNull Consumer<? super T> action) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<Void> thenAcceptAsync(@NotNull Consumer<? super T> action, Executor executor) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<Void> thenRun(@NotNull Runnable action) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<Void> thenRunAsync(@NotNull Runnable action) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<Void> thenRunAsync(@NotNull Runnable action, Executor executor) {
        return null;
    }

    @NotNull
    @Override
    public <U, V> VirtualCompletableFuture<V> thenCombine(@NotNull CompletionStage<? extends U> other, @NotNull BiFunction<? super T, ? super U, ? extends V> fn) {
        return new VirtualCompletableFuture<>(delegate.thenCombine(other, fn));
    }

    @NotNull
    @Override
    public <U, V> CompletionStage<V> thenCombineAsync(@NotNull CompletionStage<? extends U> other, @NotNull BiFunction<? super T, ? super U, ? extends V> fn) {
        return null;
    }

    @NotNull
    @Override
    public <U, V> CompletionStage<V> thenCombineAsync(@NotNull CompletionStage<? extends U> other, @NotNull BiFunction<? super T, ? super U, ? extends V> fn, Executor executor) {
        return null;
    }

    @NotNull
    @Override
    public <U> CompletionStage<Void> thenAcceptBoth(@NotNull CompletionStage<? extends U> other, @NotNull BiConsumer<? super T, ? super U> action) {
        return null;
    }

    @NotNull
    @Override
    public <U> CompletionStage<Void> thenAcceptBothAsync(@NotNull CompletionStage<? extends U> other, @NotNull BiConsumer<? super T, ? super U> action) {
        return null;
    }

    @NotNull
    @Override
    public <U> CompletionStage<Void> thenAcceptBothAsync(@NotNull CompletionStage<? extends U> other, @NotNull BiConsumer<? super T, ? super U> action, Executor executor) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<Void> runAfterBoth(@NotNull CompletionStage<?> other, @NotNull Runnable action) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<Void> runAfterBothAsync(@NotNull CompletionStage<?> other, @NotNull Runnable action) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<Void> runAfterBothAsync(@NotNull CompletionStage<?> other, @NotNull Runnable action, Executor executor) {
        return null;
    }

    @NotNull
    @Override
    public <U> CompletionStage<U> applyToEither(@NotNull CompletionStage<? extends T> other, @NotNull Function<? super T, U> fn) {
        return null;
    }

    @NotNull
    @Override
    public <U> CompletionStage<U> applyToEitherAsync(@NotNull CompletionStage<? extends T> other, @NotNull Function<? super T, U> fn) {
        return null;
    }

    @NotNull
    @Override
    public <U> CompletionStage<U> applyToEitherAsync(@NotNull CompletionStage<? extends T> other, @NotNull Function<? super T, U> fn, Executor executor) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<Void> acceptEither(@NotNull CompletionStage<? extends T> other, @NotNull Consumer<? super T> action) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<Void> acceptEitherAsync(@NotNull CompletionStage<? extends T> other, @NotNull Consumer<? super T> action) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<Void> acceptEitherAsync(@NotNull CompletionStage<? extends T> other, @NotNull Consumer<? super T> action, Executor executor) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<Void> runAfterEither(@NotNull CompletionStage<?> other, @NotNull Runnable action) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<Void> runAfterEitherAsync(@NotNull CompletionStage<?> other, @NotNull Runnable action) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<Void> runAfterEitherAsync(@NotNull CompletionStage<?> other, @NotNull Runnable action, Executor executor) {
        return null;
    }

    @NotNull
    @Override
    public <U> CompletionStage<U> thenCompose(@NotNull Function<? super T, ? extends CompletionStage<U>> fn) {
        return null;
    }

    @NotNull
    @Override
    public <U> VirtualCompletableFuture<U> thenComposeAsync(@NotNull Function<? super T, ? extends CompletionStage<U>> fn) {
        return new VirtualCompletableFuture<>(delegate.thenComposeAsync(fn, VIRTUAL_EXECUTOR));
    }

    @NotNull
    @Override
    public <U> CompletionStage<U> thenComposeAsync(@NotNull Function<? super T, ? extends CompletionStage<U>> fn, Executor executor) {
        return null;
    }

    @NotNull
    @Override
    public <U> CompletionStage<U> handle(@NotNull BiFunction<? super T, Throwable, ? extends U> fn) {
        return null;
    }

    @NotNull
    @Override
    public <U> CompletionStage<U> handleAsync(@NotNull BiFunction<? super T, Throwable, ? extends U> fn) {
        return null;
    }

    @NotNull
    @Override
    public <U> CompletionStage<U> handleAsync(@NotNull BiFunction<? super T, Throwable, ? extends U> fn, Executor executor) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<T> whenComplete(@NotNull BiConsumer<? super T, ? super Throwable> action) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<T> whenCompleteAsync(@NotNull BiConsumer<? super T, ? super Throwable> action) {
        return null;
    }

    @NotNull
    @Override
    public CompletionStage<T> whenCompleteAsync(@NotNull BiConsumer<? super T, ? super Throwable> action,
                                                Executor executor) {
        return null;
    }


    public VirtualCompletableFuture<T> orTimeout(long timeout, TimeUnit unit) {
        delegate.orTimeout(timeout, unit);
        return this;
    }


    public VirtualCompletableFuture<T> exceptionally(Function<Throwable, ? extends T> fn) {
        return new VirtualCompletableFuture<>(delegate.exceptionally(fn));
    }

    @NotNull
    @Override
    public CompletionStage<T> exceptionallyAsync(@NotNull Function<Throwable, ? extends T> fn) {
        return CompletionStage.super.exceptionallyAsync(fn);
    }

    @NotNull
    @Override
    public CompletionStage<T> exceptionallyAsync(@NotNull Function<Throwable, ? extends T> fn, Executor executor) {
        return CompletionStage.super.exceptionallyAsync(fn, executor);
    }

    @NotNull
    @Override
    public CompletionStage<T> exceptionallyCompose(@NotNull Function<Throwable, ? extends CompletionStage<T>> fn) {
        return CompletionStage.super.exceptionallyCompose(fn);
    }

    @NotNull
    @Override
    public CompletionStage<T> exceptionallyComposeAsync(@NotNull Function<Throwable, ? extends CompletionStage<T>> fn) {
        return CompletionStage.super.exceptionallyComposeAsync(fn);
    }

    @NotNull
    @Override
    public CompletionStage<T> exceptionallyComposeAsync(@NotNull Function<Throwable, ? extends CompletionStage<T>> fn, Executor executor) {
        return CompletionStage.super.exceptionallyComposeAsync(fn, executor);
    }

    @NotNull
    @Override
    public CompletableFuture<T> toCompletableFuture() {
        return null;
    }
}
