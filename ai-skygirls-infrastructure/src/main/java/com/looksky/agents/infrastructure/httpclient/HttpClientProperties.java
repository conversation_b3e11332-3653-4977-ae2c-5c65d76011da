package com.looksky.agents.infrastructure.httpclient;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Data
@Component
@ConfigurationProperties(prefix = "http.client")
public class HttpClientProperties {
    private Duration connectTimeout;
    private Duration readTimeout;
    private String sslBundle;
    private boolean logRequests = false;
    private boolean logResponses = false;
    private int maxConcurrentStreams = 1000;
    private int initialWindowSize = 1024 * 1024;
    private int headerTableSize = 65536;
} 