package com.looksky.agents.infrastructure.trace.aop;


import com.looksky.agents.infrastructure.trace.annotation.TraceMethod;
import com.looksky.agents.infrastructure.trace.annotation.TraceTag;
import com.looksky.agents.infrastructure.trace.config.TracingProperties;
import com.looksky.agents.infrastructure.trace.context.SpanContext;
import com.looksky.agents.infrastructure.trace.model.ExceptionDetail;
import com.looksky.agents.infrastructure.trace.model.MethodInvocationRecord;
import com.looksky.agents.infrastructure.trace.model.ParameterDetail;
import com.looksky.agents.infrastructure.trace.model.ReturnDetail;
import com.looksky.agents.infrastructure.trace.service.DisruptorService;
import com.yomahub.tlog.context.TLogContext;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Method;
import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class TraceAspect {

    private final DisruptorService disruptorService;
    private final TracingProperties tracingProperties;

    @Value("${spring.application.name:unknown-service}")
    private String defaultServiceName;

    @Pointcut("@annotation(com.looksky.agents.infrastructure.trace.annotation.TraceMethod)")
    public void traceMethodPointcut() {
    }

    @Around("traceMethodPointcut() && @annotation(traceAnnotation)")
    public Object traceMethodExecution(ProceedingJoinPoint joinPoint, TraceMethod traceAnnotation) throws Throwable {
        if (!tracingProperties.isEnabled()) {
            return joinPoint.proceed();
        }


        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 1. 从TraceContextHolder获取或生成traceId, spanId, parentSpanId
        String traceId = TLogContext.getTraceId();
        String parentSpanId = SpanContext.getCurrentSpanId(); // 父级Span是当前栈顶的Span
        String spanId = SpanContext.createNewSpanId(); // 创建新Span并压栈

        Instant startTime = Instant.now();
        Object returnValue = null;
        Throwable exception = null;
        StopWatch stopWatch = new StopWatch();

        try {
            stopWatch.start();
            returnValue = joinPoint.proceed();
            return returnValue;
        } catch (Throwable t) {
            exception = t;
            throw t;
        } finally {
            stopWatch.stop();
            Instant endTime = Instant.now();
            long duration = stopWatch.getTotalTimeMillis();

            var recordBuilder = MethodInvocationRecord.builder().id(spanId).traceId(traceId).spanId(spanId).parentSpanId(parentSpanId)
                .serviceName(tracingProperties.getServiceName() != null ? tracingProperties.getServiceName() : defaultServiceName).className(signature.getDeclaringType().getSimpleName())
                .methodName(method.getName()).startTime(startTime).endTime(endTime).duration(duration).description(traceAnnotation.description());

            // 捕获参数
            boolean captureParams = tracingProperties.getAspect().isDefaultCaptureParams() && traceAnnotation.captureParams();
            if (captureParams) {
                recordBuilder.parameters(captureParameters(joinPoint, signature));
            }

            // 捕获返回值
            boolean captureReturnValue = tracingProperties.getAspect().isDefaultCaptureReturnValue() && traceAnnotation.captureReturnValue();
            if (captureReturnValue && exception == null) {
                recordBuilder.returnValue(captureReturnValue(returnValue, method.getReturnType()));
            }

            // 捕获异常
            if (exception != null) {
                recordBuilder.exception(captureException(exception));
            }

            // 自定义标签
            if (traceAnnotation.tags() != null && traceAnnotation.tags().length > 0) {
                Map<String, String> tagsMap = new HashMap<>();
                for (TraceTag tag : traceAnnotation.tags()) {
                    tagsMap.put(tag.key(), tag.value());
                }
                recordBuilder.tags(tagsMap);
            }

            // 发送到本地缓冲
            disruptorService.publish(recordBuilder.build());

            // 清理/恢复TraceContextHolder中的spanId
            SpanContext.poplarSpanId(); // 当前方法结束，弹出其 spanId
            if (parentSpanId == null) { // 如果是根Span，则请求结束，清理整个TraceContext
                disruptorService.flushTracesToMongoDB();
                SpanContext.clear();
            }

            if (traceAnnotation.slowThresholdMillis() > 0 && duration > traceAnnotation.slowThresholdMillis()) {
                log.warn("Slow method call: {}.{} took {}ms (threshold: {}ms), TraceID: {}, SpanID: {}", signature.getDeclaringTypeName(), method.getName(), duration,
                    traceAnnotation.slowThresholdMillis(), traceId, spanId);
            }
        }
    }

    private List<ParameterDetail> captureParameters(ProceedingJoinPoint joinPoint, MethodSignature signature) {
        Object[] args = joinPoint.getArgs();
        String[] paramNames = signature.getParameterNames();
        Class<?>[] paramTypes = signature.getParameterTypes();

        if (args == null || args.length == 0) {
            return Collections.emptyList();
        }

        return IntStream.range(0, args.length).mapToObj(i -> {
            String paramName = (paramNames != null && paramNames.length > i) ? paramNames[i] : "arg" + i;
            String paramType = (paramTypes != null && paramTypes.length > i) ? paramTypes[i].getName() : "Unknown";
            Object paramValue = "null";

            if (args[i] != null) {
                paramValue = args[i];
            }

            return ParameterDetail.builder().name(paramName).type(paramType).value(paramValue).build();
        }).toList();
    }


    private ReturnDetail captureReturnValue(Object returnValue, Class<?> returnType) {
        if (returnValue == null) {
            return ReturnDetail.builder().type(returnType != null ? returnType.getName() : "void").value("null").build();
        }
        return ReturnDetail.builder().type(returnType.getName()).value(returnValue).build();

    }

    private ExceptionDetail captureException(Throwable t) {
        ExceptionDetail detail = new ExceptionDetail();
        detail.setType(t.getClass().getName());
        detail.setMessage(t.getMessage());

        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        t.printStackTrace(pw);
        String stackTraceStr = sw.toString();
        detail.setStackTrace(stackTraceStr);
        return detail;
    }
}