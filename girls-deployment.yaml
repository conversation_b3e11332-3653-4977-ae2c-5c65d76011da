apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/instance: looksky-skygirls-agents
  name: looksky-skygirls-agents-rest
  namespace: graecove
spec:
  replicas: 1
  selector:
    matchLabels:
      name: looksky-skygirls-agents-rest
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        prometheus.io/port: "9100"
        prometheus.io/scrape: "true"
      labels:
        name: looksky-skygirls-agents-rest
    spec:
      containers:
      - env:
        - name: SPRING_PROFILES_ACTIVE
          value: girls
        image: lookskysvc/micro:looksky-skygirls-agents-test-3
        imagePullPolicy: Always
        name: looksky-skygirls-agents-rest
        resources:
          limits:
            cpu: "1"
          requests:
            cpu: 500m
      imagePullSecrets:
      - name: graecove-repo