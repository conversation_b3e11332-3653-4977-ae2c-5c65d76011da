<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.4</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.looksky</groupId>
    <artifactId>looksky-skygirls-agents</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>looksky-skygirls-agents</name>
    <description>looksky-skygirls-agents</description>

    <modules>
        <module>ai-skygirls-common</module>
        <module>ai-skygirls-infrastructure</module>
        <module>ai-skygirls-controller</module>
        <module>ai-skygirls-start</module>
        <module>ai-skygirls-data</module>
        <module>ai-skygirls-models</module>
        <module>ai-skygirls-application</module>
        <module>ai-skygirls-sdk</module>
    </modules>

    <properties>
        <!-- skygirls制品仓库 -->
        <ai-skygirls-sdk.version>0.0.1-SNAPSHOT</ai-skygirls-sdk.version>

        <!-- 三方库 -->
        <apm-toolkit-trace.version>9.3.0</apm-toolkit-trace.version>
        <apm-toolkit-logback-1.x.version>9.3.0</apm-toolkit-logback-1.x.version>
        <ai-agents.version>0.0.1-SNAPSHOT</ai-agents.version>
        <java.version>21</java.version>
        <maven.compile.source>21</maven.compile.source>
        <maven.compile.target>21</maven.compile.target>
        <spring.version>3.3.5-SNAPSHOT</spring.version>
        <spring-ai.version>1.0.0</spring-ai.version>
        <hutool-all.version>5.8.34</hutool-all.version>
        <lombok.version>1.18.34</lombok.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <protobuf.version>3.25.1</protobuf.version>
        <protobuf-plugin.version>0.6.1</protobuf-plugin.version>
        <grpc.version>1.70.0</grpc.version>
        <graecove-common.version>0.0.4-SNAPSHOT</graecove-common.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <skygirls-business-sdk.version>0.0.5-SNAPSHOT</skygirls-business-sdk.version>
        <graecove-business-sdk.version>0.0.2-SNAPSHOT</graecove-business-sdk.version>
        <springdoc.version>2.3.0</springdoc.version>
        <mybatis-plus.version>3.5.9</mybatis-plus.version>
        <aws.java.sdk.version>2.31.10</aws.java.sdk.version>
        <easyexcel.version>3.3.2</easyexcel.version>
    </properties>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>${aws.java.sdk.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.skygirls</groupId>
                <artifactId>skygirls-business-sdk</artifactId>
                <version>${skygirls-business-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.westyle</groupId>
                <artifactId>graecove-common</artifactId>
                <version>${graecove-common.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>
            <dependency>
                <groupId>com.westyle</groupId>
                <artifactId>graecove-business-sdk</artifactId>
                <version>${graecove-business-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.looksky</groupId>
                <artifactId>ai-skygirls-models</artifactId>
                <version>${ai-agents.version}</version>
            </dependency>
            <dependency>
                <groupId>com.looksky</groupId>
                <artifactId>ai-skygirls-sdk</artifactId>
                <version>${ai-skygirls-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.looksky</groupId>
                <artifactId>ai-skygirls-common</artifactId>
                <version>${ai-agents.version}</version>
            </dependency>
            <dependency>
                <groupId>com.looksky</groupId>
                <artifactId>ai-skygirls-infrastructure</artifactId>
                <version>${ai-agents.version}</version>
            </dependency>
            <dependency>
                <groupId>com.looksky</groupId>
                <artifactId>ai-skygirls-controller</artifactId>
                <version>${ai-agents.version}</version>
            </dependency>
            <dependency>
                <groupId>com.looksky</groupId>
                <artifactId>ai-skygirls-start</artifactId>
                <version>${ai-agents.version}</version>
            </dependency>
            <dependency>
                <groupId>com.looksky</groupId>
                <artifactId>ai-skygirls-data</artifactId>
                <version>${ai-agents.version}</version>
            </dependency>
            <dependency>
                <groupId>com.looksky</groupId>
                <artifactId>ai-skygirls-application</artifactId>
                <version>${ai-agents.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-netty-shaded</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-protobuf</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-stub</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <!--            &lt;!&ndash; 强制使用 jakarta.annotation-api &ndash;&gt;-->
            <!--            <dependency>-->
            <!--                <groupId>javax.annotation</groupId>-->
            <!--                <artifactId>javax.annotation-api</artifactId>-->
            <!--                <version>1.3.2</version>-->
            <!--                <scope>provided</scope>-->
            <!--            </dependency>-->
            <!--            <dependency>-->
            <!--                <groupId>jakarta.annotation</groupId>-->
            <!--                <artifactId>jakarta.annotation-api</artifactId>-->
            <!--                <version>2.1.1</version>-->
            <!--            </dependency>-->
        </dependencies>

    </dependencyManagement>


    <build>
        <finalName>${project.name}</finalName>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.7.1</version>
            </extension>
        </extensions>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

        </plugins>


        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                    </configuration>
                </plugin>
                <!-- Source -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

            </plugins>


        </pluginManagement>
    </build>

    <repositories>

        <repository>
            <id>2013133-release-FWFYro</id>
            <url>https://packages.aliyun.com/maven/repository/2013133-release-FWFYro</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>

        <repository>
            <id>2013133-snapshot-c0UdHx</id>
            <url>https://packages.aliyun.com/maven/repository/2013133-snapshot-c0UdHx</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>spring-snapshots</id>
            <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>spring-snapshots</id>
            <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url>
            <releases>
                <enabled>false</enabled>
            </releases>
        </pluginRepository>
    </pluginRepositories>
    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <spring.profiles.active>local</spring.profiles.active>
            </properties>
        </profile>

        <profile>
            <id>dev</id>
            <properties>
                <spring.profiles.active>dev</spring.profiles.active>
            </properties>
        </profile>

        <profile>
            <id>test</id>
            <properties>
                <spring.profiles.active>test</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
    </profiles>

    <distributionManagement>
        <repository>
            <id>2013133-release-FWFYro</id>
            <name>LookSky REALEASE repository</name>
            <url>https://packages.aliyun.com/maven/repository/2013133-release-FWFYro</url>
        </repository>
        <snapshotRepository>
            <id>2013133-snapshot-c0UdHx</id>
            <name>LookSky SNAPSHOT repository</name>
            <url>https://packages.aliyun.com/maven/repository/2013133-snapshot-c0UdHx</url>
        </snapshotRepository>
    </distributionManagement>

</project>
