<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.looksky</groupId>
        <artifactId>looksky-skygirls-agents</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>ai-skygirls-models</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.looksky</groupId>
            <artifactId>ai-skygirls-infrastructure</artifactId>
        </dependency>

        <!--openai-->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-openai</artifactId>
        </dependency>
        <!--azure openai-->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-azure-openai</artifactId>
        </dependency>
        <!--aws-->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-bedrock-converse</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-redis-store</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.38.1</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>
