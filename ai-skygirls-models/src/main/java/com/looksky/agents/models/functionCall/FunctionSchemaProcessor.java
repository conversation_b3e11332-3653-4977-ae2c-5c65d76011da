package com.looksky.agents.models.functionCall;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.lang.reflect.ParameterizedType;
import java.util.function.Function;
import org.springframework.ai.azure.openai.AzureOpenAiChatOptions;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.tool.function.FunctionToolCallback;

public class FunctionSchemaProcessor {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Schema配置信息
     */
    public record SchemaConfig(String functionName, String description, String simplifiedJson) {
    }

    /**
     * Spring AI 需要一个 Function<String, String> 类型的参数，这里使用一个默认的实现，返回空字符串
     */
    static class CommonFunctionParam implements Function<String, String> {
        public String apply(String inFunctionSchemaProcessorput) {
            return "";
        }
    }

    /**
     * 从jsonSchema中提取配置信息并简化json结构
     */
    public static SchemaConfig extractSchemaConfig(String jsonSchema) throws Exception {
        JsonNode rootNode = objectMapper.readTree(jsonSchema);

        // 提取functionName (title)
        String functionName = rootNode.has("title") ?
            rootNode.get("title").asText() :
            "defaultFunction";

        // 提取description
        String description = rootNode.has("description") ?
            rootNode.get("description").asText() :
            functionName.toLowerCase(); // 如果没有description，使用functionName的小写形式

        // 简化json结构
        String simplifiedJson = simplifySchema(rootNode);

        return new SchemaConfig(functionName, description, simplifiedJson);
    }

    private static String simplifySchema(JsonNode rootNode) {
        ObjectNode resultNode = objectMapper.createObjectNode();

        // 创建顶层结构
        resultNode.put("type", "object");

        // 复制 $defs 如果存在
        if (rootNode.has("$defs")) {
            resultNode.set("$defs", rootNode.get("$defs"));
        }

        // 复制 properties
        if (rootNode.has("properties")) {
            resultNode.set("properties", rootNode.get("properties"));
        }

        // 复制 required
        if (rootNode.has("required")) {
            resultNode.set("required", rootNode.get("required"));
        }

        // 复制 additionalProperties
        if (rootNode.has("additionalProperties")) {
            resultNode.set("additionalProperties", rootNode.get("additionalProperties"));
        }

        return resultNode.toString();
    }


    public static AzureOpenAiChatOptions buildChatOptions(AzureOpenAiChatOptions.Builder builder, String jsonSchema) throws Exception {
        SchemaConfig config = extractSchemaConfig(jsonSchema);

        var tool = FunctionToolCallback.builder(config.functionName(), new CommonFunctionParam()).description(config.description()).inputType(ParameterizedType.class).inputSchema(jsonSchema).build();

        AzureOpenAiChatOptions chatOptions = builder
            .toolCallbacks(tool)
            .toolNames(config.functionName())
            .toolChoice(OpenAiApi.ChatCompletionRequest.ToolChoiceBuilder.FUNCTION(config.functionName()))
            .build();
        chatOptions.setInternalToolExecutionEnabled(false);
        return chatOptions;
    }


    public static OpenAiChatOptions buildChatOptions(OpenAiChatOptions.Builder builder, String jsonSchema) throws Exception {
        SchemaConfig config = extractSchemaConfig(jsonSchema);

        var tool = FunctionToolCallback.builder(config.functionName(), new CommonFunctionParam()).description(config.description()).inputType(ParameterizedType.class).inputSchema(jsonSchema).build();

        OpenAiChatOptions chatOptions = builder
            .toolCallbacks(tool)
            .toolNames(config.functionName())
            .toolChoice(OpenAiApi.ChatCompletionRequest.ToolChoiceBuilder.FUNCTION(config.functionName()))
            .build();
        chatOptions.setInternalToolExecutionEnabled(false);
        return chatOptions;
    }
} 