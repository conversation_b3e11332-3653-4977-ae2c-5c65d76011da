package com.looksky.agents.models.schema;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.networknt.schema.JsonSchema;
import com.networknt.schema.JsonSchemaFactory;
import com.networknt.schema.SpecVersion;
import com.networknt.schema.ValidationMessage;
import java.util.Set;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Json 校验器
 *
 * @since  1.1.0
 * <AUTHOR>
 **/
@Slf4j
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class JsonSchemaValidate {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final JsonSchemaFactory factory = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V202012);

    /**
     * 校验 Json
     * @param json 需要校验的 json
     * @param schema json schema
     * @return 校验结果, true 表示校验通过, false 表示校验失败
     */
    public static boolean validate(String json, String schema) {
        try {
            JsonNode jsonNode = objectMapper.readTree(json);
            JsonNode schemaNode = objectMapper.readTree(schema);

            JsonSchema jsonSchema = factory.getSchema(schemaNode);
            Set<ValidationMessage> validate = jsonSchema.validate(jsonNode);

            if (validate.isEmpty()) {
                return true;
            } else {
                log.warn(validate.stream().map(ValidationMessage::getMessage) .reduce((a, b) -> a + ", " + b).orElse(""));
                return false;
            }
        } catch (Exception e) {
            log.warn("JSON Schema validation error", e);
            return false;
        }
    }
}