package com.looksky.agents.models.parser.jsonoutput;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * JSON输出解析工具类
 * 对应Python版本中的各种工具函数
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class JsonOutputParserUtils {
    private static final Pattern JSON_MARKDOWN_RE = Pattern.compile("```(json)?(.*)", Pattern.DOTALL);
    private static final String JSON_STRIP_CHARS = " \n\r\t`";

    private static String replaceNewLine(Matcher match) {
        String value = match.group(2);
        value = value.replace("\n", "\\\\n");
        value = value.replace("\r", "\\\\r");
        value = value.replace("\t", "\\\\t");
        // 只转义未转义的引号，对应Python的 re.sub(r'(?<!\\)"', r'\"', value)
        value = value.replaceAll("(?<!\\\\)\"", "\\\\\"");

        return match.group(1) + value + match.group(3);
    }

    /**
     * 对应Python的 _custom_parser 函数
     */
    public static String customParser(String multilineString) {
        if (multilineString == null) {
            return null;
        }

        Pattern actionInputPattern = Pattern.compile("(\"action_input\"\\s*:\\s*\")(.*?)(\")", Pattern.DOTALL);
        Matcher matcher = actionInputPattern.matcher(multilineString);

        StringBuilder sb = new StringBuilder();
        while (matcher.find()) {
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replaceNewLine(matcher)));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    private static JsonNode parseJson(String jsonStr, Function<String, JsonNode> parserFunc) {
        // 去除前后空白字符
        if (jsonStr == null) {
            throw new OutputParserException("Input JSON string is null");
        }

        jsonStr = jsonStr.trim();
        for (char c : JSON_STRIP_CHARS.toCharArray()) {
            jsonStr = jsonStr.replaceAll("^" + Pattern.quote(String.valueOf(c)) + "+|" + Pattern.quote(String.valueOf(c)) + "+$", "");
        }

        // 处理特殊字符
        jsonStr = customParser(jsonStr);

        // 解析JSON
        try {
            return parserFunc.apply(jsonStr);
        } catch (RuntimeException e) {
            if (e.getCause() instanceof JsonProcessingException) {
                throw new OutputParserException("Failed to parse JSON string: " + jsonStr, e.getCause());
            }
            throw e;
        }
    }


    public static JsonNode parseJsonMarkdown(String jsonString, Function<String, JsonNode> parserFunc) {
        try {
            // 首先尝试直接解析
            return parseJson(jsonString, parserFunc);
        } catch (OutputParserException e1) {
            // 如果直接解析失败，尝试从Markdown中提取JSON
            Matcher matcher = JSON_MARKDOWN_RE.matcher(jsonString);

            String jsonStrToParse;
            if (matcher.find()) {
                jsonStrToParse = matcher.group(2); // 获取backticks内的内容
            } else {
                // 如果没有找到Markdown格式，重新抛出原始异常
                throw e1;
            }

            // 尝试解析提取的内容
            try {
                return parseJson(jsonStrToParse, parserFunc);
            } catch (OutputParserException e2) {
                throw new OutputParserException("Failed to parse JSON from markdown content: " + jsonStrToParse, e2.getCause());
            }
        }
    }

    public static JsonNode parsePartialJson(String s, ObjectMapper mapper) {
        if (s == null) {
            return null;
        }

        // 首先尝试直接解析
        try {
            return mapper.readValue(s, JsonNode.class);
        } catch (JsonProcessingException e) {
            // 忽略异常，继续部分解析逻辑
        }

        List<Character> newChars = new ArrayList<>();
        List<Character> stack = new ArrayList<>();
        boolean isInsideString = false;
        boolean escaped = false;

        // 处理每个字符
        for (char c : s.toCharArray()) {
            if (isInsideString) {
                if (c == '"' && !escaped) {
                    isInsideString = false;
                } else if (c == '\n' && !escaped) {
                    newChars.add('\\');
                    newChars.add('n');
                    escaped = false;
                    continue;
                } else if (c == '\\') {
                    escaped = !escaped;
                } else {
                    escaped = false;
                }
            } else {
                if (c == '"') {
                    isInsideString = true;
                    escaped = false;
                } else if (c == '{') {
                    stack.addFirst('}'); // 在栈顶添加
                } else if (c == '[') {
                    stack.addFirst(']'); // 在栈顶添加
                } else if (c == '}' || c == ']') {
                    if (!stack.isEmpty() && stack.getFirst() == c) {
                        stack.removeFirst(); // 从栈顶移除
                    } else {
                        // 括号不匹配
                        return null;
                    }
                }
            }
            newChars.add(c);
        }

        // 如果字符串未关闭，添加关闭引号
        if (isInsideString) {
            newChars.add('"');
        }

        // 尝试解析，直到成功或用完字符
        while (!newChars.isEmpty()) {
            try {
                // 构建当前尝试的JSON字符串
                StringBuilder sb = new StringBuilder();
                for (char c : newChars) {
                    sb.append(c);
                }
                // 添加所有闭合字符
                for (char c : stack) {
                    sb.append(c);
                }

                // 尝试解析
                return mapper.readValue(sb.toString(), JsonNode.class);
            } catch (JsonProcessingException e) {
                // 如果解析失败，移除最后一个字符再试
                if (newChars.isEmpty()) {
                    break;
                }
                newChars.removeLast();
            }
        }

        // 如果所有尝试都失败，尝试解析原始字符串
        try {
            return mapper.readValue(s, JsonNode.class);
        } catch (JsonProcessingException e) {
            throw new OutputParserException("Failed to parse partial JSON after multiple attempts", e);
        }
    }
}