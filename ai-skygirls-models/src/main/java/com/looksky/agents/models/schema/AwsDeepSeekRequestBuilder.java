package com.looksky.agents.models.schema;

import cn.hutool.core.net.URLEncodeUtil;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.models.functionCall.FunctionSchemaProcessor;
import com.looksky.agents.models.model.ModelEnum;
import com.looksky.agents.models.utils.ParseImageUtils;
import com.looksky.agents.sdk.enums.OutputModelEnum;
import java.net.URI;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.bedrock.converse.BedrockProxyChatModel;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.content.Media;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.ResponseFormat;
import org.springframework.util.MimeTypeUtils;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;

@Slf4j
public class AwsDeepSeekRequestBuilder {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
        .configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS, true);

    private final String model = ModelEnum.AWS_DEEP_SEEK.getModelName();
    private BedrockProxyChatModel chatModel;
    private final List<Message> messageList = new ArrayList<>();
    private UserMessage userMessage;
    private SystemMessage systemMessage;
    private boolean debug = false;
    private Class<?> targetClass;
    private String customJsonSchema;
    private final Map<Class<?>, List<String>> enumReplacements = new HashMap<>();
    private String executedContent;
    private final OpenAiChatOptions.Builder optionsBuilder;
    @Getter
    private Prompt prompt;
    private OutputModelEnum outputType = OutputModelEnum.TEXT;
    private int size = 0;
    @Getter
    private ChatResponse chatResponse;

    public AwsDeepSeekRequestBuilder() {
        this.optionsBuilder = OpenAiChatOptions.builder().model(model);
    }

    public AwsDeepSeekRequestBuilder withChatModel(BedrockProxyChatModel chatModel) {
        this.chatModel = chatModel;
        return this;
    }


    public AwsDeepSeekRequestBuilder withSize(int size) {
        this.size = size;
        return this;
    }


    /**
     * 设置温度参数
     * @param temperature 取值范围 0-2，默认1
     */
    public AwsDeepSeekRequestBuilder withTemperature(Double temperature) {
        this.optionsBuilder.temperature(temperature);
        return this;
    }

    /**
     * 设置最大token数
     */
    public AwsDeepSeekRequestBuilder withMaxTokens(int maxTokens) {
        this.optionsBuilder.maxTokens(maxTokens);
        return this;
    }

    /**
     * 批量添加消息
     */
    public AwsDeepSeekRequestBuilder withMessages(List<Message> messages) {
        if (messages != null) {
            this.messageList.addAll(messages);
        }
        return this;
    }

    /**
     * 批量添加枚举替换规则
     */
    public AwsDeepSeekRequestBuilder withEnumReplacements(Map<Class<?>, List<String>> replacements) {
        if (replacements != null) {
            this.enumReplacements.putAll(replacements);
        }
        return this;
    }

    public AwsDeepSeekRequestBuilder withSystemMessage(String systemPrompt) {
        if (StringUtils.hasText(systemPrompt)) {
            this.systemMessage = new SystemMessage(systemPrompt);
        }
        return this;
    }

    public AwsDeepSeekRequestBuilder withUserMessage(String userPrompt) {
        if (StringUtils.hasText(userPrompt)) {
            this.userMessage = new UserMessage(userPrompt);
        }
        return this;
    }

    public AwsDeepSeekRequestBuilder withImagesUserPrompt(String userPrompt, List<String> imageUrls) {
        if (!StringUtils.hasText(userPrompt)) {
            return this;
        }
        if (imageUrls == null || imageUrls.isEmpty()) {
            this.userMessage = new UserMessage(userPrompt);
            return this;
        }
        try {

            List<Media> mediaList = imageUrls.stream()
                .map(url -> {
                    try {
                        return new Media(MimeTypeUtils.IMAGE_PNG, URI.create(URLEncodeUtil.encode(url)));
                    } catch (Exception e) {
                        log.error("Failed to create media for URL: {}", url, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .toList();

            if (!mediaList.isEmpty()) {
                this.userMessage = UserMessage.builder().text(userPrompt).media(mediaList).build();
            } else {
                this.userMessage = new UserMessage(userPrompt);
            }
        } catch (Exception e) {
            log.error("Failed to create user message with images", e);
            this.userMessage = new UserMessage(userPrompt);
        }
        return this;
    }

    public AwsDeepSeekRequestBuilder withModel(String model) {
        optionsBuilder.model(model);
        return this;
    }

    public AwsDeepSeekRequestBuilder withAdditionalMessage(Message message) {
        if (message != null) {
            this.messageList.add(message);
        }
        return this;
    }

    public AwsDeepSeekRequestBuilder withDebug(boolean debug) {
        this.debug = debug;
        return this;
    }



    public AwsDeepSeekRequestBuilder withEnumReplacement(Class<?> enumClass, List<String> replacementValues) {
        enumReplacements.put(enumClass, replacementValues);
        return this;
    }

    public AwsDeepSeekRequestBuilder withOutputType(OutputModelEnum outputType) {
        this.outputType = outputType;
        return this;
    }

    public AwsDeepSeekRequestBuilder withJsonSchema(String jsonSchema) {
        if (StringUtils.hasText(jsonSchema)) {
            this.customJsonSchema = jsonSchema;
            this.targetClass = null;
        }
        return this;
    }

    public AwsDeepSeekRequestBuilder withJsonSchema(Class<?> schemaClass) {
        this.targetClass = schemaClass;
        this.customJsonSchema = null;
        return this;
    }


    public void buildPrompt() {


        String jsonSchema;
        if (customJsonSchema != null) {
            jsonSchema = customJsonSchema;
        } else if (targetClass != null) {
            BeanOutputConverter<?> outputConverter = new BeanOutputConverter<>(targetClass, OBJECT_MAPPER);
            jsonSchema = outputConverter.getJsonSchema();
        } else {
            jsonSchema = null;
        }

        try {
            List<Message> messages = buildMessageList();
            if (messages.isEmpty()) {
                throw new IllegalStateException("No messages available for execution");
            }

            switch (outputType) {
                case TEXT:
                    prompt = new Prompt(messages, optionsBuilder.build());
                    break;
                case JSON_SCHEMA:
                    prompt = new Prompt(messages, optionsBuilder.responseFormat(
                        new ResponseFormat(ResponseFormat.Type.JSON_SCHEMA, jsonSchema)).build());
                    break;
                case FUNCTION_CALL:
                    prompt = new Prompt(messages, FunctionSchemaProcessor.buildChatOptions(optionsBuilder, jsonSchema));
                    break;
                default:
                    break;
            }

        } catch (Exception e) {
            log.error("构建 prompt 出错", e);
            throw new RuntimeException("构建 prompt 出错", e);
        }
    }


    public Flux<String> executeAsync() {
        try {
            buildPrompt();
            return chatModel.stream(prompt).map(response -> (response.getResult() == null || response.getResult().getOutput() == null
                || response.getResult().getOutput().getText() == null) ? ""
                : response.getResult().getOutput().getText());

        } catch (Exception e) {
            log.error("Failed to execute request", e);
            throw new RuntimeException("Failed to execute request", e);
        }
    }


    /**
     * 执行请求
     */
    public AwsDeepSeekRequestBuilder execute() {
        try {
            buildPrompt();

            chatResponse = chatModel.call(prompt);

            if (debug) {
                log.info("Chat Response: {}", chatResponse);
            }

            if (OutputModelEnum.FUNCTION_CALL == outputType) {
                executedContent = chatResponse.getResult().getOutput().getToolCalls().getFirst().arguments();
            } else {
                executedContent = chatResponse.getResult().getOutput().getText();
            }

            if (debug) {
                log.info("Response Content: {}", executedContent);
            }

            return this;
        } catch (Exception e) {
            log.error("Failed to execute request", e);
            throw new RuntimeException("Failed to execute request", e);
        }
    }

    private List<Message> buildMessageList() {
        List<Message> tempMessageList = new ArrayList<>();

        // 添加系统消息（如果存在）
        if (systemMessage != null) {
            tempMessageList.add(systemMessage);
        }

        // 添加用户消息（如果存在）
        if (userMessage != null) {
            tempMessageList.add(userMessage);
        }

        // 添加额外的消息
        tempMessageList.addAll(messageList);

        List<Message> finalMessageList = new ArrayList<>();

        tempMessageList.forEach(message -> {
            List<Message> messages = ParseImageUtils.processImageMessage(message, size);
            finalMessageList.addAll(messages);
        });


        return finalMessageList;
    }


    public String getContent() {
        if (executedContent == null) {
            throw new IllegalStateException("No content available. Please call execute() first.");
        }
        return executedContent;
    }

    /**
     * 将结果转换为指定类型的对象
     *
     * @param clazz 目标类型
     */
    public <R> R toObject(Class<R> clazz) {
        if (executedContent == null) {
            throw new IllegalStateException("No content available. Please call execute() first.");
        }

        try {
            BeanOutputConverter<R> outputConverter = new BeanOutputConverter<>(clazz, OBJECT_MAPPER);
            return outputConverter.convert(executedContent);
        } catch (Exception e) {
            log.error("Failed to convert response to object", e);
            throw new RuntimeException("Failed to convert response to object", e);
        }
    }

} 