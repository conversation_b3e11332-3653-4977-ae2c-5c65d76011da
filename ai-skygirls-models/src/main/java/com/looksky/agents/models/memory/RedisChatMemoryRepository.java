package com.looksky.agents.models.memory;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.Serial;
import java.io.Serializable;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RList;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.ToolResponseMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * 使用 redis 实现的 ChatMemory
 *
 * <AUTHOR>
 * @since 1.1.14
 **/
@Slf4j
@Component
public class RedisChatMemoryRepository implements ChatMemoryRepository {

    private static final String CONVERSATION_IDS_KEY = "ai:chat:memory:conversation_ids";
    private static final String CONVERSATION_PREFIX = "ai:chat:memory:conversation:";
    private static final int MESSAGE_EXPIRE_DATE = 1; // 默认过期时间24小时
    private static final String CONVERSATION_ID_ERROR_MESSAGE = "conversationId cannot be null or empty";
    private final RedissonClient redissonClient;
    private final TypedJsonJacksonCodec codec;


    public RedisChatMemoryRepository(RedissonClient redissonClient, ObjectMapper objectMapper) {
        this.redissonClient = redissonClient;
        this.codec= new TypedJsonJacksonCodec(MessageEntry.class, objectMapper);
    }


    /**
     * 根据业务侧的 userId 和 conversationId 生成存储的 会话 ID
     */
    public static String generateId(String userId, String conversationId) {
        return userId + ":" + conversationId;
    }

    @NotNull
    @Override
    public List<String> findConversationIds() {
        RSet<String> conversationIds = redissonClient.getSet(CONVERSATION_IDS_KEY);
        return new ArrayList<>(conversationIds);
    }

    @NotNull
    @Override
    public List<Message> findByConversationId(@NotNull String conversationId) {
        Assert.hasText(conversationId, CONVERSATION_ID_ERROR_MESSAGE);
        
        String key = getConversationKey(conversationId);
        RList<MessageEntry> messageJsonList = redissonClient.getList(key, codec);
        
        List<Message> messages = new ArrayList<>();
        messageJsonList.forEach(entry -> messages.add(createMessage(entry.getContent(), entry.getType())));

        return messages;
    }

    @Override
    public void saveAll(@NotNull String conversationId, @NotNull List<Message> messages) {
        Assert.hasText(conversationId, CONVERSATION_ID_ERROR_MESSAGE);
        Assert.notNull(messages, "messages cannot be null");
        Assert.noNullElements(messages, "messages cannot contain null elements");
        
        // 先删除原有的对话
        deleteByConversationId(conversationId);
        
        // 将新的对话ID添加到集合中
        RSet<String> conversationIds = redissonClient.getSet(CONVERSATION_IDS_KEY);
        conversationIds.add(conversationId);
        
        // 保存消息
        String key = getConversationKey(conversationId);
        RList<MessageEntry> messageList = redissonClient.getList(key, codec);
        
        List<MessageEntry> jsonMessages = messages.stream()
                .map(message -> MessageEntry.builder().content(message.getText()).type(message.getMessageType().getValue()).build())
                .filter(Objects::nonNull)
                .toList();
        
        messageList.addAll(jsonMessages);
        messageList.expire(Duration.ofDays(MESSAGE_EXPIRE_DATE));
    }

    @Override
    public void deleteByConversationId(@NotNull String conversationId) {
        Assert.hasText(conversationId, CONVERSATION_ID_ERROR_MESSAGE);
        
        String key = getConversationKey(conversationId);
        redissonClient.getList(key, codec).delete();
    }

    private String getConversationKey(String conversationId) {
        return CONVERSATION_PREFIX + conversationId;
    }

    private Message createMessage(String content, String type) {
        MessageType messageType = MessageType.fromValue(type);
        return switch (messageType) {
            case USER -> new UserMessage(content);
            case ASSISTANT -> new AssistantMessage(content);
            case SYSTEM -> new SystemMessage(content);
            case TOOL -> new ToolResponseMessage(List.of());
        };
    }

    /**
     * 用于 Redis 存储的消息对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    private static class MessageEntry implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private String content;
        private String type;
    }
}
