package com.looksky.agents.models.parseOutput;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public class ThinkTextBuffer {
    private final StringBuilder normalCompleteBuffer = new StringBuilder();
    private final StringBuilder thinkCompleteBuffer = new StringBuilder();
    private final StringBuilder chunkBuffer = new StringBuilder();
    private final StringBuilder thinkBuffer = new StringBuilder();
    private static final int THRESHOLD = 3;
    private boolean normalTextStarted = false;
    private boolean getThinkFinishMessage = true;

    public boolean getNormalTextStarted() {
        return normalTextStarted;
    }

    public void appendChunk(String chunk) {
        if (chunk != null) {

            String replace = chunk.replace("<think>", "");

            // 判断
            if (chunk.equals(replace)) {
                normalTextStarted = true;
                chunkBuffer.append(chunk);
            } else {
                thinkBuffer.append(replace);
            }

        }
    }

     public synchronized boolean hasEnoughNewWords() {
        if (normalTextStarted) {
            return getNewWords(chunkBuffer).length > THRESHOLD;
        }

        return getNewWords(thinkBuffer).length > THRESHOLD;
    }

    public String[] getNewWords(StringBuilder sb) {
        return sb.toString().trim().split("\\s+");
    }

     public synchronized void processAndUpdate() {

        if (normalTextStarted) {
            String newText = chunkBuffer.toString();
            int lastSpaceIndex = newText.lastIndexOf(" ");
            if (lastSpaceIndex > 0) {
                normalCompleteBuffer.append(newText, 0, lastSpaceIndex).append(" ");
                chunkBuffer.setLength(0);
                chunkBuffer.append(newText.substring(lastSpaceIndex + 1));
            }
        } else {
            String newText = thinkBuffer.toString();
            int lastSpaceIndex = newText.lastIndexOf(" ");
            if (lastSpaceIndex > 0) {
                thinkCompleteBuffer.append(newText, 0, lastSpaceIndex).append(" ");
                thinkBuffer.setLength(0);
                thinkBuffer.append(newText.substring(lastSpaceIndex + 1));
            }
        }


    }


    public String getThinkText() {
        return thinkCompleteBuffer.toString().trim();
    }


    public String getCurrentText() {
        return normalCompleteBuffer.toString().trim();
    }

    public String getFinalText() {
        String remaining = chunkBuffer.toString().trim();
        if (!remaining.isEmpty()) {
            normalCompleteBuffer.append(remaining);
        }
        return normalCompleteBuffer.toString().trim();
    }

    public synchronized String getThinkFinalText() {
        getThinkFinishMessage = false;
        String remaining = thinkBuffer.toString().trim();
        if (!remaining.isEmpty()) {
            thinkCompleteBuffer.append(remaining);
        }
        return thinkCompleteBuffer.toString().trim();


    }


    public static void main(String[] args) {
        String temp = "<think>你好";

        System.out.println(temp.replace("<think>", ""));
    }
}