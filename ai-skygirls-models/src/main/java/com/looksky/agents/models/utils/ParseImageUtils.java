package com.looksky.agents.models.utils;

import cn.hutool.core.net.URLEncodeUtil;
import java.net.MalformedURLException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.content.Media;
import org.springframework.util.MimeTypeUtils;

/**
 * @ClassName ParseImageUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/17 下午3:29
 * @Version 1.0
 **/
public class ParseImageUtils {


    private static final String PROMPT = """
            # Skygirls: Personalized Clothing Recommendation Prompt
            
            ## **Role**:
            
            You are a fashion-forward AI specializing in personalized clothing recommendations for young women (ages 14–22) in the U.S., helping them discover their best styles. Your expertise spans color analysis, body shape styling, and adapting trends to individual preferences. You also integrate region-specific recommendations and Z-generation fashion insights.
            
            <img src="https://codetheory-file.oss-cn-shenzhen.aliyuncs.com/img/2024-11-16%2012%3A07%3A52%20jo08lsd1au1731730071813.png"/>
            <img src="https://codetheory-file.oss-cn-shenzhen.aliyuncs.com/img/2024-11-14%2019%3A43%3A54%20d8ryndv30i1731584634388.png"/>
            
           
            
            ## **Task**:
           
            Using the provided user profile, style report, and operational business categories, generate:
            
            Operational Weighted Categories Queries: Provide positive and negative search queries for each operationally emphasized category.
            
            Non-Operational Categories Query: Produce a generalized query that captures versatile attributes and user alignment.
            
            <img src="https://codetheory-file.oss-cn-shenzhen.aliyuncs.com/img/2024-11-14%2019%3A43%3A44%20y5b7hhxuae1731584624734.png"/>
            """;


    private static final String test = """
        ##User Current Query
        USER:The third one looks good, but I want the black one. Please help me find it
        
        ## Current clothing info
        ### cloth description
        Radiate confidence with a striking red mini dress featuring a daring V front and delicate lace panels. Adjustable straps and a slight stretch ensure a perfect fit, making it a standout piece for any wardrobe.
        ### The image information of the clothing is as follows:
        <img src="https://d28ypdag318gi4.cloudfront.net/looksky/product/imgs/eb47bfda5d249b1db343aacf65df2278.jpg"/>
        
        Note:
        1 Ensure the accuracy of the information you provide. If you are unsure or doubtful, do not output that information.
        2 The generated content should be smooth and concise, not just a simple stacking of words.
        3 The description must include the color of the clothing, and this description should be detailed. Avoid simply using basic terms like red or green. Instead, describe aspects such as brightness and saturation, for example: \\"soft light pink.\\"
        4 the output the query preferably between 110 to 120 characters and do not omit any important elements on the clothing, such as distinctive patterns, unique designs, details, or embellishments.
        """;

    private static final Pattern FIT_IN_PATTERN = Pattern.compile("/fit-in/\\d+x\\d+");
    private static final Pattern PATH_PATTERN = Pattern.compile("/(upload|looksky)/");
    private static final String CDN_DOMAIN = "cdn.lookskyai.com";
    private static final String CLOUDFRONT_DOMAIN = "d28ypdag318gi4.cloudfront.net";

    /**
     * 调整图片URL的尺寸
     * @param imageUrl 原始图片URL
     * @param size size
     * @return 调整后的URL
     */
    public static String resizeImage(String imageUrl, int size) {
        if (imageUrl == null || imageUrl.isEmpty()) {
            return imageUrl;
        }
        
        if (size <= 0) {
            return imageUrl;
        }

        // 快速检查：如果已经包含fit-in参数，直接返回
        if (FIT_IN_PATTERN.matcher(imageUrl).find()) {
            return imageUrl;
        }

        // 检查域名并处理
        String processedUrl = imageUrl;
        if (imageUrl.contains(CDN_DOMAIN)) {
            processedUrl = imageUrl.replace(CDN_DOMAIN, CLOUDFRONT_DOMAIN);
        } else if (!imageUrl.contains(CLOUDFRONT_DOMAIN)) {
            return imageUrl;
        }

        // 使用正则表达式查找插入点
        Matcher pathMatcher = PATH_PATTERN.matcher(processedUrl);
        if (pathMatcher.find()) {
            int insertPoint = pathMatcher.start();
            return new StringBuilder(processedUrl)
                    .insert(insertPoint, "/fit-in/" + size + "x" + size)
                    .toString();
        }

        return processedUrl;
    }



    @SneakyThrows
    public static List<Message> processImageMessage(Message message, int size) {
        if (message == null) {
            return new ArrayList<>();
        }

        if (!message.getMessageType().equals(MessageType.USER)) {
            return new ArrayList<>(List.of(message));
        }

        UserMessage userMessage = (UserMessage) message;
        Collection<Media> originalMedia = userMessage.getMedia();

        // 解析文本内容中的图片和文本
        List<Message> parsedMessages = parseImageMessage(message.getText(), size);

        // 如果原始消息中有media，将其添加到最后一条消息中
        if (!originalMedia.isEmpty() && !parsedMessages.isEmpty()) {
            UserMessage lastMessage = (UserMessage) parsedMessages.getLast();
            List<Media> mediaList = new ArrayList<>(lastMessage.getMedia());

            // 对 originalMedia 进行处理
            originalMedia.stream().map(media -> new Media(media.getMimeType(), URI.create(URLEncodeUtil.encode(resizeImage(media.getData().toString(), size))))).forEach(mediaList::add);

            // 替换最后一条消息
            parsedMessages.set(parsedMessages.size() - 1,UserMessage.builder().text(lastMessage.getText()).media(mediaList).build());
        } else if (!originalMedia.isEmpty()) {
            // 如果没有解析出新消息但有原始media，创建一个新消息
            parsedMessages.add(UserMessage.builder().text(message.getText()).media(new ArrayList<>(originalMedia)).build());
        } else if (parsedMessages.isEmpty()) {
            // 如果既没有解析出消息也没有media，保留原始消息
            parsedMessages.add(message);
        }

        return parsedMessages;
    }



    public static List<Message> parseImageMessage(String message, int size) throws MalformedURLException {
        List<Message> messages = new ArrayList<>();

        // 使用正则表达式匹配完整的<img>标签
        Pattern imgPattern = Pattern.compile("<img[^>]+src=\"([^\"]+)\"[^>]*>");
        Matcher imgMatcher = imgPattern.matcher(message);

        // 记录上一次匹配结束的位置
        int lastEnd = 0;
        String currentText = "";
        List<String> currentImages = new ArrayList<>();

        while (imgMatcher.find()) {
            String imageUrl = imgMatcher.group(1);
            int start = imgMatcher.start();

            // 处理图片前的文本
            if (start > lastEnd) {
                String text = message.substring(lastEnd, start).trim();
                if (!text.isEmpty()) {
                    // 如果已经有之前的文本和图片，先创建一个message
                    if (!currentText.isEmpty()) {
                        createAndAddMessage(messages, currentText, currentImages, size);
                        currentImages = new ArrayList<>();
                    }
                    currentText = text;
                }
            }

            // 添加当前图片到列表
            currentImages.add(resizeImage(imageUrl, size));
            lastEnd = imgMatcher.end();
        }

        // 处理剩余的文本
        if (lastEnd < message.length()) {
            String remainingText = message.substring(lastEnd).trim();
            if (!remainingText.isEmpty()) {
                // 如果已经有之前的文本和图片，先创建一个message
                if (!currentText.isEmpty()) {
                    createAndAddMessage(messages, currentText, currentImages, size);
                    currentImages = new ArrayList<>();
                }
                currentText = remainingText;
            }
        }

        // 处理最后一组文本和图片
        if (!currentText.isEmpty() || !currentImages.isEmpty()) {
            createAndAddMessage(messages, currentText, currentImages, size);
        }

        return messages;
    }

    private static void createAndAddMessage(List<Message> messages, String text, List<String> imageUrls, int size) {
        List<Media> mediaList = imageUrls.stream()
                .map(url -> new Media(MimeTypeUtils.IMAGE_PNG, URI.create(URLEncodeUtil.encode(resizeImage(url, size)))))
                .collect(Collectors.toList());

        messages.add(UserMessage.builder().text(text).media(mediaList).build());
    }

    public static void main1(String[] args) {
        try {
            System.out.println("测试 PROMPT:");
            List<Message> messages = parseImageMessage(PROMPT, 1);
            System.out.println("消息数量: " + messages.size());
            for (int i = 0; i < messages.size(); i++) {
                UserMessage userMsg = (UserMessage) messages.get(i);
                System.out.println("消息 " + (i + 1) + ":");
                System.out.println("文本内容: " + userMsg.getText());
                System.out.println("图片数量: " + userMsg.getMedia().size());
                System.out.println("---");
            }
            
            System.out.println("\n测试中间多图片:");
            String multiImageTest = """
                第一段文本
                <img src=https://example.com/test1.jpg/>
                <img src=https://example.com/test2.jpg/>
                第二段文本
                <img src=https://example.com/test3.jpg/>
                <img src=https://example.com/test4.jpg/>""";
            List<Message> testMessages = parseImageMessage(test, 1);
            System.out.println("消息数量: " + testMessages.size());
            for (int i = 0; i < testMessages.size(); i++) {
                UserMessage userMsg = (UserMessage) testMessages.get(i);
                System.out.println("消息 " + (i + 1) + ":");
                System.out.println("文本内容: " + userMsg.getText());
                System.out.println("图片数量: " + userMsg.getMedia().size());
                System.out.println("---");
            }
            System.out.println("\n测试结尾图片:");
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws MalformedURLException {

        Media media = new Media(MimeTypeUtils.IMAGE_PNG, URI.create(URLEncodeUtil.encode("https://cdn.lookskyai.com/Olive Green Video 0521-ab.jpg", StandardCharsets.UTF_8)));
        System.out.println(media.getData().toString());

        //System.out.println(resizeImage(
        //    "https://cdn.lookskyai.com/upload/agent/0361c2736765b789eef7acfd0d3a916e.jpeg", 512));
    }
}
