package com.looksky.agents.models.parseOutput;

import lombok.Getter;

@Getter
public class StreamingTextBuffer {
    private final StringBuilder completeBuffer = new StringBuilder();
    private final StringBuilder chunkBuffer = new StringBuilder();
    private static final int THRESHOLD = 3;
    
    public void appendChunk(String chunk) {
        if (chunk != null) {
            chunkBuffer.append(chunk);
        }
    }
    
    public boolean hasEnoughNewWords() {
        return getNewWords().length > THRESHOLD;
    }
    
    public String[] getNewWords() {
        return chunkBuffer.toString().trim().split("\\s+");
    }
    
    public void processAndUpdate() {
        String newText = chunkBuffer.toString();
        int lastSpaceIndex = newText.lastIndexOf(" ");
        
        if (lastSpaceIndex > 0) {
            completeBuffer.append(newText, 0, lastSpaceIndex).append(" ");
            chunkBuffer.setLength(0);
            chunkBuffer.append(newText.substring(lastSpaceIndex + 1));
        }
    }
    
    public String getCurrentText() {
        return completeBuffer.toString().trim();
    }
    
    public String getFinalText() {
        String remaining = chunkBuffer.toString().trim();
        if (!remaining.isEmpty()) {
            completeBuffer.append(remaining);
        }
        return completeBuffer.toString().trim();
    }
}