package com.looksky.agents.models.model;

import lombok.Getter;

/**
 * 不使用 OpenAiApi.ChatModel, 使用精确的日期模型
 */
@Getter
public enum ModelEnum {

    GPT_4O_2024_0806("gpt-4o-2024-08-06"),
    GPT_4O_2024_1120("gpt-4o-2024-11-20"),
    /**
     * GTP_4O 现在默认为 {@link  ModelEnum#GPT_4O_2024_0806}
     */
    GPT_4O("gpt-4o"),
    AWS_DEEP_SEEK("us.deepseek.r1-v1:0"),
    GPT_4_1("gpt-4.1")

    ;

    private final String modelName;

    ModelEnum(String modelName) {
        this.modelName = modelName;
    }
}
