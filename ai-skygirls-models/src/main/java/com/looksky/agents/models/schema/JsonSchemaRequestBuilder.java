package com.looksky.agents.models.schema;

import cn.hutool.core.net.URLEncodeUtil;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.models.functionCall.FunctionSchemaProcessor;
import com.looksky.agents.models.model.ModelEnum;
import com.looksky.agents.models.utils.ParseImageUtils;
import com.looksky.agents.sdk.enums.OutputModelEnum;
import java.net.URI;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.content.Media;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.ResponseFormat;
import org.springframework.util.MimeTypeUtils;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;

@Slf4j
public class JsonSchemaRequestBuilder {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper().configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS, true);

    private final String model = ModelEnum.GPT_4O_2024_1120.getModelName();
    private OpenAiChatModel chatModel;
    private final List<Message> messageList = new ArrayList<>();
    private UserMessage userMessage;
    private SystemMessage systemMessage;
    private boolean debug = false;
    private Class<?> targetClass;
    private String customJsonSchema;
    private final Map<Class<?>, List<String>> enumReplacements = new HashMap<>();
    private String executedContent;
    private final OpenAiChatOptions.Builder optionsBuilder;
    @Getter
    private Prompt prompt;
    private OutputModelEnum outputType = OutputModelEnum.TEXT;
    private int size = 0;
    @Getter
    private ChatResponse chatResponse;

    public JsonSchemaRequestBuilder() {
        this.optionsBuilder = OpenAiChatOptions.builder().model(model);
    }

    public JsonSchemaRequestBuilder withChatModel(OpenAiChatModel chatModel) {
        this.chatModel = chatModel;
        return this;
    }


    public JsonSchemaRequestBuilder withSize(int size) {
        this.size = size;
        return this;
    }


    /**
     * 设置温度参数
     * @param temperature 取值范围 0-2，默认1
     */
    public JsonSchemaRequestBuilder withTemperature(Double temperature) {
        this.optionsBuilder.temperature(temperature);
        return this;
    }

    /**
     * 设置最大token数
     */
    public JsonSchemaRequestBuilder withMaxTokens(int maxTokens) {
        this.optionsBuilder.maxTokens(maxTokens);
        return this;
    }

    /**
     * 批量添加消息
     */
    public JsonSchemaRequestBuilder withMessages(List<Message> messages) {
        if (messages != null) {
            this.messageList.addAll(messages);
        }
        return this;
    }

    /**
     * 批量添加枚举替换规则
     */
    public JsonSchemaRequestBuilder withEnumReplacements(Map<Class<?>, List<String>> replacements) {
        if (replacements != null) {
            this.enumReplacements.putAll(replacements);
        }
        return this;
    }

    public JsonSchemaRequestBuilder withSystemMessage(String systemPrompt) {
        if (StringUtils.hasText(systemPrompt)) {
            this.systemMessage = new SystemMessage(systemPrompt);
        }
        return this;
    }

    public JsonSchemaRequestBuilder withUserMessage(String userPrompt) {
        if (StringUtils.hasText(userPrompt)) {
            this.userMessage = new UserMessage(userPrompt);
        }
        return this;
    }

    public JsonSchemaRequestBuilder withImagesUserPrompt(String userPrompt, List<String> imageUrls) {
        if (!StringUtils.hasText(userPrompt)) {
            return this;
        }
        if (imageUrls == null || imageUrls.isEmpty()) {
            this.userMessage = new UserMessage(userPrompt);
            return this;
        }
        try {

            List<Media> mediaList = imageUrls.stream()
                .map(url -> {
                    try {
                        return new Media(MimeTypeUtils.IMAGE_PNG, URI.create(URLEncodeUtil.encode(url)));
                    } catch (Exception e) {
                        log.error("Failed to create media for URL: {}", url, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .toList();

            if (!mediaList.isEmpty()) {
                this.userMessage = UserMessage.builder().text(userPrompt).media(mediaList).build();
            } else {
                this.userMessage = UserMessage.builder().text(userPrompt).build();
            }
        } catch (Exception e) {
            log.error("Failed to create user message with images", e);
            this.userMessage = UserMessage.builder().text(userPrompt).build();
        }
        return this;
    }

    public JsonSchemaRequestBuilder withModel(String model) {
        optionsBuilder.model(model);
        return this;
    }

    public JsonSchemaRequestBuilder withAdditionalMessage(Message message) {
        if (message != null) {
            this.messageList.add(message);
        }
        return this;
    }



    public JsonSchemaRequestBuilder withOutputType(OutputModelEnum outputType) {
        this.outputType = outputType;
        return this;
    }

    public JsonSchemaRequestBuilder withJsonSchema(String jsonSchema) {
        if (StringUtils.hasText(jsonSchema)) {
            this.customJsonSchema = jsonSchema;
            this.targetClass = null;
        }
        return this;
    }


    public void buildPrompt() {


        String jsonSchema;
        if (customJsonSchema != null) {
            jsonSchema = customJsonSchema;
        } else if (targetClass != null) {
            BeanOutputConverter<?> outputConverter = new BeanOutputConverter<>(targetClass, OBJECT_MAPPER);
            jsonSchema = outputConverter.getJsonSchema();
        } else {
            jsonSchema = null;
        }

        try {
            List<Message> messages = buildMessageList();
            if (messages.isEmpty()) {
                throw new IllegalStateException("No messages available for execution");
            }

            switch (outputType) {
                case TEXT:
                    prompt = new Prompt(messages, optionsBuilder.build());
                    break;
                case JSON_SCHEMA:
                    prompt = new Prompt(messages, optionsBuilder.responseFormat(
                        new ResponseFormat(ResponseFormat.Type.JSON_SCHEMA, jsonSchema)).build());
                    break;
                case NO_STRICT_JSON_SCHEMA:
                    ResponseFormat format = ResponseFormat.builder().jsonSchema(ResponseFormat.JsonSchema.builder().schema(jsonSchema).strict(false).build()).type(ResponseFormat.Type.JSON_SCHEMA).build();
                    prompt = new Prompt(messages, optionsBuilder.responseFormat(format).build());
                    break;
                case FUNCTION_CALL:
                    prompt = new Prompt(messages, FunctionSchemaProcessor.buildChatOptions(optionsBuilder, jsonSchema));
                    break;
                default:
                    break;
            }

        } catch (Exception e) {
            log.error("构建 prompt 出错", e);
            throw new RuntimeException("构建 prompt 出错", e);
        }
    }


    public Flux<String> executeAsync() {
        try {
            buildPrompt();
            return chatModel.stream(prompt).map(response -> (response.getResult() == null || response.getResult().getOutput() == null
                || response.getResult().getOutput().getText() == null) ? ""
                : response.getResult().getOutput().getText());

        } catch (Exception e) {
            log.error("Failed to execute request", e);
            throw new RuntimeException("Failed to execute request", e);
        }
    }


    /**
     * 执行请求
     */
    public JsonSchemaRequestBuilder execute() {
        try {
            buildPrompt();

            chatResponse = chatModel.call(prompt);

            if (debug) {
                log.info("Chat Response: {}", chatResponse);
            }

            if (OutputModelEnum.FUNCTION_CALL == outputType) {
                executedContent = chatResponse.getResult().getOutput().getToolCalls().getFirst().arguments();
            } else {
                executedContent = chatResponse.getResult().getOutput().getText();
            }

            if (debug) {
                log.info("Response Content: {}", executedContent);
            }

            return this;
        } catch (Exception e) {
            log.error("Failed to execute request", e);
            throw new RuntimeException("Failed to execute request", e);
        }
    }

    private List<Message> buildMessageList() {
        List<Message> tempMessageList = new ArrayList<>();

        // 添加系统消息（如果存在）
        if (systemMessage != null) {
            tempMessageList.add(systemMessage);
        }

        // 添加用户消息（如果存在）
        if (userMessage != null) {
            tempMessageList.add(userMessage);
        }

        // 添加额外的消息
        tempMessageList.addAll(messageList);

        List<Message> finalMessageList = new ArrayList<>();

        tempMessageList.forEach(message -> {
            List<Message> messages = ParseImageUtils.processImageMessage(message, size);
            finalMessageList.addAll(messages);
        });


        return finalMessageList;
    }


    public String getContent() {
        if (executedContent == null) {
            throw new IllegalStateException("No content available. Please call execute() first.");
        }
        return executedContent;
    }

} 