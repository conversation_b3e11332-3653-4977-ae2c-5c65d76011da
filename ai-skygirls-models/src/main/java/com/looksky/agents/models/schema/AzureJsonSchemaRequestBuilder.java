package com.looksky.agents.models.schema;

import cn.hutool.core.net.URLEncodeUtil;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.models.functionCall.FunctionSchemaProcessor;
import com.looksky.agents.models.model.ModelEnum;
import com.looksky.agents.models.utils.ParseImageUtils;
import com.looksky.agents.sdk.enums.OutputModelEnum;
import java.net.URI;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.azure.openai.AzureOpenAiChatModel;
import org.springframework.ai.azure.openai.AzureOpenAiChatOptions;
import org.springframework.ai.azure.openai.AzureOpenAiResponseFormat;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.content.Media;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.model.ModelOptionsUtils;
import org.springframework.util.MimeTypeUtils;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;

public class AzureJsonSchemaRequestBuilder {

	private static final Logger log = LoggerFactory.getLogger(AzureJsonSchemaRequestBuilder.class);


    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS, true);

    private final String model = ModelEnum.GPT_4_1.getModelName();
    private AzureOpenAiChatModel chatModel;
    private final List<Message> messageList = new ArrayList<>();
    private UserMessage userMessage;
    private SystemMessage systemMessage;
    private boolean debug = false;
    private Class<?> targetClass;
    private String customJsonSchema;
    private final Map<Class<?>, List<String>> enumReplacements = new HashMap<>();
    private String executedContent;
    private final AzureOpenAiChatOptions.Builder optionsBuilder;
    private Prompt prompt;
    private OutputModelEnum outputType = OutputModelEnum.TEXT;
    private int size = 0;
    @Getter
    private ChatResponse chatResponse;

    public AzureJsonSchemaRequestBuilder() {
        this.optionsBuilder = AzureOpenAiChatOptions.builder().deploymentName(model);
    }

    public AzureJsonSchemaRequestBuilder withChatModel(AzureOpenAiChatModel chatModel) {
        this.chatModel = chatModel;
        return this;
    }

    /**
     * 设置温度参数
     * @param temperature 取值范围 0-2，默认1
     */
    public AzureJsonSchemaRequestBuilder withTemperature(Double temperature) {
        this.optionsBuilder.temperature(temperature);
        return this;
    }

    public AzureJsonSchemaRequestBuilder withSize(int size) {
        this.size = size;
        return this;
    }

    /**
     * 设置最大token数
     */
    public AzureJsonSchemaRequestBuilder withMaxTokens(int maxTokens) {
        this.optionsBuilder.maxTokens(maxTokens);
        return this;
    }

    /**
     * 批量添加消息
     */
    public AzureJsonSchemaRequestBuilder withMessages(List<Message> messages) {
        if (messages != null) {
            this.messageList.addAll(messages);
        }
        return this;
    }

    /**
     * 批量添加枚举替换规则
     */
    public AzureJsonSchemaRequestBuilder withEnumReplacements(Map<Class<?>, List<String>> replacements) {
        if (replacements != null) {
            this.enumReplacements.putAll(replacements);
        }
        return this;
    }

    public AzureJsonSchemaRequestBuilder withSystemMessage(String systemPrompt) {
        if (StringUtils.hasText(systemPrompt)) {
            this.systemMessage = new SystemMessage(systemPrompt);
        }
        return this;
    }

    public AzureJsonSchemaRequestBuilder withUserMessage(String userPrompt) {
        if (StringUtils.hasText(userPrompt)) {
            this.userMessage = new UserMessage(userPrompt);
        }
        return this;
    }

    public AzureJsonSchemaRequestBuilder withImagesUserPrompt(String userPrompt, List<String> imageUrls) {
        if (!StringUtils.hasText(userPrompt)) {
            return this;
        }
        if (imageUrls == null || imageUrls.isEmpty()) {
            this.userMessage = new UserMessage(userPrompt);
            return this;
        }
        try {

            List<Media> mediaList = imageUrls.stream()
                    .map(url -> {
                        try {
                            return new Media(MimeTypeUtils.IMAGE_PNG,  URI.create(URLEncodeUtil.encode(url)));
                        } catch (Exception e) {
                            log.error("Failed to create media for URL: {}", url, e);
                            return null;
                        }
                    })
                    .filter(media -> media != null)
                    .toList();

            if (!mediaList.isEmpty()) {
                this.userMessage = UserMessage.builder().text(userPrompt).media(mediaList).build();
            } else {
                this.userMessage = new UserMessage(userPrompt);
            }
        } catch (Exception e) {
            log.error("Failed to create user message with images", e);
            this.userMessage = new UserMessage(userPrompt);
        }
        return this;
    }

    @Deprecated
    public AzureJsonSchemaRequestBuilder withModel(String model) {
        //optionsBuilder.deploymentName(model);
        return this;
    }

    public AzureJsonSchemaRequestBuilder withAdditionalMessage(Message message) {
        if (message != null) {
            this.messageList.add(message);
        }
        return this;
    }

    public AzureJsonSchemaRequestBuilder withDebug(boolean debug) {
        this.debug = debug;
        return this;
    }




    public AzureJsonSchemaRequestBuilder withEnumReplacement(Class<?> enumClass, List<String> replacementValues) {
        enumReplacements.put(enumClass, replacementValues);
        return this;
    }

    public AzureJsonSchemaRequestBuilder withOutputType(OutputModelEnum outputType) {
        this.outputType = outputType;
        return this;
    }

    public AzureJsonSchemaRequestBuilder withJsonSchema(String jsonSchema) {
        if (StringUtils.hasText(jsonSchema)) {
            this.customJsonSchema = jsonSchema;
            this.targetClass = null;
        }
        return this;
    }

    public AzureJsonSchemaRequestBuilder withJsonSchema(Class<?> schemaClass) {
        this.targetClass = schemaClass;
        this.customJsonSchema = null;
        return this;
    }



    public void buildPrompt() {


        String jsonSchema;
        if (customJsonSchema != null) {
            jsonSchema = customJsonSchema;
        } else if (targetClass != null) {
            BeanOutputConverter<?> outputConverter = new BeanOutputConverter<>(targetClass, OBJECT_MAPPER);
            jsonSchema = outputConverter.getJsonSchema();
        } else {
            jsonSchema = null;
        }

        try {
            List<Message> messages = buildMessageList();
            if (messages.isEmpty()) {
                throw new IllegalStateException("No messages available for execution");
            }

            switch (outputType) {
                case TEXT:
                    prompt = new Prompt(messages, optionsBuilder.build());
                    break;
                case JSON_SCHEMA:
                    prompt = new Prompt(messages, optionsBuilder.responseFormat(new AzureOpenAiResponseFormat(AzureOpenAiResponseFormat.Type.JSON_SCHEMA, jsonSchema)).build());
                    break;
                case FUNCTION_CALL:
                    prompt = new Prompt(messages, FunctionSchemaProcessor.buildChatOptions(optionsBuilder, jsonSchema));
                    break;
                case NO_STRICT_JSON_SCHEMA:
                    AzureOpenAiResponseFormat format = AzureOpenAiResponseFormat.builder().jsonSchema(AzureOpenAiResponseFormat.JsonSchema.builder().schema(ModelOptionsUtils.jsonToMap(jsonSchema)).strict(false).build()).type(AzureOpenAiResponseFormat.Type.JSON_SCHEMA).build();
                    prompt = new Prompt(messages, optionsBuilder.responseFormat(format).build());
                    break;
                default:
                    break;
            }

        } catch (Exception e) {
            log.error("构建 prompt 出错", e);
            throw new RuntimeException("构建 prompt 出错", e);
        }
    }


    public Flux<String> executeAsync() {
        try {
            buildPrompt();
            return chatModel.stream(prompt).map(response -> (response.getResult() == null || response.getResult().getOutput() == null
                    || response.getResult().getOutput().getText() == null) ? ""
                    : response.getResult().getOutput().getText());

        } catch (Exception e) {
            log.error("Failed to execute request", e);
            throw new RuntimeException("Failed to execute request", e);
        }
    }


    /**
     * 执行请求
     */
    public AzureJsonSchemaRequestBuilder execute() {
        try {
            buildPrompt();

            chatResponse = chatModel.call(prompt);

            if (debug) {
                log.info("Chat Response: {}", chatResponse);
            }

            if (OutputModelEnum.FUNCTION_CALL == outputType) {
                executedContent = chatResponse.getResult().getOutput().getToolCalls().getFirst().arguments();
            } else {
                executedContent = chatResponse.getResult().getOutput().getText();
            }

            if (debug) {
                log.info("Response Content: {}", executedContent);
            }

            return this;
        } catch (Exception e) {
            log.error("Failed to execute request", e);
            throw new RuntimeException("Failed to execute request", e);
        }
    }

    private List<Message> buildMessageList() {
        List<Message> tempMessageList = new ArrayList<>();
        
        // 添加系统消息（如果存在）
        if (systemMessage != null) {
            tempMessageList.add(systemMessage);
        }
        
        // 添加用户消息（如果存在）
        if (userMessage != null) {
            tempMessageList.add(userMessage);
        }

        // 添加额外的消息
        tempMessageList.addAll(messageList);

        List<Message> finalMessageList = new ArrayList<>();

        tempMessageList.forEach(message -> {
            List<Message> messages = ParseImageUtils.processImageMessage(message, size);
            finalMessageList.addAll(messages);
        });


        return finalMessageList;
    }



    public String getContent() {
        if (executedContent == null) {
            throw new IllegalStateException("No content available. Please call execute() first.");
        }
        return executedContent;
    }

    /**
     * 将结果转换为指定类型的对象
     * @param clazz 目标类型
     */
    public <R> R toObject(Class<R> clazz) {
        if (executedContent == null) {
            throw new IllegalStateException("No content available. Please call execute() first.");
        }
        
        try {
            BeanOutputConverter<R> outputConverter = new BeanOutputConverter<>(clazz, OBJECT_MAPPER);
            return outputConverter.convert(executedContent);
        } catch (Exception e) {
            log.error("Failed to convert response to object", e);
            throw new RuntimeException("Failed to convert response to object", e);
        }
    }

} 