package com.looksky.agents.models.parser.jsonoutput;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * JSON输出解析器
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class JsonOutputParser {

    private static final ObjectMapper objectMapper = new ObjectMapper().configure(SerializationFeature.INDENT_OUTPUT, false);


    /**
     * 解析LLM生成结果为JSON对象
     * @param result 生成结果
     * @param partial 是否解析部分JSON
     * @return 解析后的对象
     */
    public static JsonNode parseResult(String result, boolean partial) {
        if (result == null || result.isEmpty()) {
            return null;
        }
        String text = result.trim();

        try {
            if (partial) {
                return parsePartialResult(text);
            } else {
                return parseCompleteResult(text);
            }
        } catch (OutputParserException e) {
            if (partial) {
                return null;
            }
            throw e;
        }
    }

    /**
     * 解析部分JSON结果
     * @param text 要解析的文本
     * @return 解析后的对象，解析失败时返回null
     */
    private static JsonNode parsePartialResult(String text) {
        return JsonOutputParserUtils.parseJsonMarkdown(text, s -> JsonOutputParserUtils.parsePartialJson(s, objectMapper));
    }

    /**
     * 解析完整JSON结果
     * @param text 要解析的文本
     * @return 解析后的对象
     * @throws OutputParserException 当JSON解析失败时抛出
     */
    private static JsonNode parseCompleteResult(String text) throws OutputParserException {
        try {
            return JsonOutputParserUtils.parseJsonMarkdown(text, s -> {
                try {
                    return objectMapper.readValue(s, JsonNode.class);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            });
        } catch (RuntimeException e) {
            if (e.getCause() instanceof JsonProcessingException) {
                throw new OutputParserException("Invalid json output: " + text,  e.getCause());
            }
            throw e;
        }
    }

    /**
     * 解析文本为JSON对象
     * @param text 要解析的文本部分文本
     * @return 解析后的对象
     */
    public static JsonNode parse(String text) {
        return parseResult(text, true);
    }


}
