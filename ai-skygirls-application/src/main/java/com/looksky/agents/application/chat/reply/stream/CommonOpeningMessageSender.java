package com.looksky.agents.application.chat.reply.stream;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.reply.AbstractMessageSender;
import com.looksky.agents.data.client.service.GirlsDataService;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.agent.common.dto.AgentAdviceQuestions;
import com.looksky.agents.sdk.agent.common.dto.OpeningCacheDTO;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.common.enums.EventNameEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.common.enums.PageEnum;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.tryon.bo.ColorSeasonCardBO;
import com.looksky.agents.sdk.recommend.foryou.dto.PartitionRecomModelDTO;
import com.looksky.agents.sdk.recommend.foryou.enums.RecommendScenEnum;
import com.skygirls.biz.report.IosUserInfoDto;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.core.annotation.Order;

@Slf4j
@Order(10)
//@Component
public class CommonOpeningMessageSender extends AbstractMessageSender {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private GirlsDataService girlsDataService;

    private static final String LOADING_MESSAGE = "Analyzing your color season...";


    @Override
    protected boolean supports(PromptModel strategy) {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());
        String promptName = requestInput.getPage() + "_" + requestInput.getEnterPointEnum().getValue() + "_" + EventNameEnum.OPENING.getValue();
        return strategy.getName().equals(promptName);
    }


    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());
        String cacheKey = EventNameEnum.OPENING.getValue() + ":" + input.getPage() + ":" + input.getEnterPointEnum().getValue();

        if (PageEnum.FOR_YOU.getName().equals(input.getPage())) {
            buildForYouPromptData(input.getUserId());
        }

        VirtualCompletableFuture<ColorSeasonCardBO> future = null;

        IosUserInfoDto userInfo = Context.get(Context.Name.USER_INFO.getName());

        // 开启异步线程, 生成色彩季卡
        if (PageEnum.HOME.getName().equals(input.getPage()) && EnterPointEnum.HOME == input.getEnterPointEnum()) {
            future = this.generateColorSeasonCard(userInfo);
        }


        // 先去 redis 里面找有没有配置的内容
        RBucket<OpeningCacheDTO> bucket = redissonClient.getBucket(cacheKey, new TypedJsonJacksonCodec(OpeningCacheDTO.class));
        // 如果没有配置, 那么就执行 prompt 的结果
        if (!bucket.isExists()) {
            log.debug("{}, 无开场白设定, 执行prompt", cacheKey);
            // 缓存里面没有, 执行 prompt
            String messageStr = sendStreamMessage(strategy).block();
            sendLoading();
            extraSearchMessage(EventTypeEnum.TEXT.getType(), messageStr, hashMap, input.getPage(), future);
            return;
        }

        // 有相关配置
        OpeningCacheDTO openingCacheDTO = bucket.get();
        String replyStr = null;

        // 判断用户是否解锁

        if (userInfo != null && userInfo.getBasicUser() != null && userInfo.getBasicUser().getProductExpiredDateStamp() != null && userInfo.getBasicUser().getProductExpiredDateStamp() > 0) {
            // 如果用户解锁了
            // 判断是否有解锁内容
            if (CharSequenceUtil.isNotBlank(openingCacheDTO.getUnlock())) {
                log.debug("用户解锁了, 返回解锁内容");
                replyStr = openingCacheDTO.getUnlock();
            }
        } else {
            // 未解锁
            // 判断用户是否有邀请码
            if (userInfo != null && userInfo.getBasicUser() != null && CharSequenceUtil.isNotBlank(userInfo.getBasicUser().getRegisterCode())) {
                // 有邀请码
                if (CharSequenceUtil.isNotBlank(openingCacheDTO.getHaveRegisterCodeLock())) {
                    log.debug("用户有邀请码, 返回有邀请码内容");
                    replyStr = openingCacheDTO.getHaveRegisterCodeLock();
                }
            } else {
                // 没有邀请码
                if (CharSequenceUtil.isNotBlank(openingCacheDTO.getNoRegisterCodeLock())) {
                    log.debug("用户没有邀请码, 返回没有邀请码内容");
                    replyStr = openingCacheDTO.getNoRegisterCodeLock();
                }
            }
        }

        // 如果上面命中了, 那么返回内容
        if (CharSequenceUtil.isNotBlank(replyStr) && userInfo != null) {
            log.debug("返回开场白内容: {}", replyStr);

            replyStr = fillVariable(replyStr, userInfo);

            sendContent(initMessage(EventTypeEnum.TEXT.getType()), EventTypeEnum.TEXT.getType(), replyStr);
            sendLoading();
            extraSearchMessage(EventTypeEnum.TEXT.getType(), replyStr, hashMap, input.getPage(), future);
            return;
        }
        log.debug("开场白未命中, 执行prompt");

        // 如果还是空的, 那么执行 prompt
        String messageStr = sendStreamMessage(strategy).block();
        sendLoading();
        extraSearchMessage(EventTypeEnum.TEXT.getType(), messageStr, hashMap, input.getPage(), future);
    }

    /**
     * 填充配置中的变量
     *
     * @param replyStr
     * @param userInfo
     * @return
     */
    private String fillVariable(String replyStr, IosUserInfoDto userInfo) {
        PromptTemplate template = new PromptTemplate(replyStr);
        Prompt prompt = template.create(Map.of("user_name", userInfo.getBasicUser().getUsername()));
        return prompt.getContents();
    }

    private void extraSearchMessage(String eventType, Object message, HashMap<String, Object> hashMap, String page, VirtualCompletableFuture<ColorSeasonCardBO> future) {
        if (PageEnum.SEARCH.getName().equals(page)) {
            extraMessage(eventType, message, hashMap);
        }

        sendColorSeasonCardMessage(future);

        sendAdviceQuestionsMessage();
    }

    @Override
    protected void sendLoading() {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());
        if (PageEnum.HOME.getName().equals(requestInput.getPage()) && EnterPointEnum.HOME == requestInput.getEnterPointEnum()) {
            super.sendLoading(LOADING_MESSAGE);
        }
    }

    private void sendColorSeasonCardMessage(VirtualCompletableFuture<ColorSeasonCardBO> future) {
        if (future == null) {
            return;
        }

        ColorSeasonCardBO colorSeasonCardBO = null;
        try {
            colorSeasonCardBO = future.get();
        } catch (Exception e) {
            log.error("色彩季卡生成失败", e);
        }
        AgentMessageResp adviceMessage = initMessage(EventTypeEnum.COLOR_SEASON_CARD.getType());
        if (colorSeasonCardBO != null && CharSequenceUtil.isNotBlank(colorSeasonCardBO.getTryOnResultUrl())) {
            sendContent(adviceMessage, EventTypeEnum.COLOR_SEASON_CARD.getType(), colorSeasonCardBO);
        } else {
            log.error("色彩季卡生成失败");
            sendErrorMessage(adviceMessage, "Message generation is error issues, please click refresh to restart.");
        }

    }

    private void buildForYouPromptData(String userId) {
        // 从 redis 中随机获取一个版块的内容
        RecommendScenEnum scene = RecommendScenEnum.randomScen();
        // 获取这个版块的向量词
        String partitionData = "";
        String partitionName = "";
        RList<PartitionRecomModelDTO> list = redissonClient.getList(RedisKeyConstants.forYouKey(userId, scene.getName()), new TypedJsonJacksonCodec(PartitionRecomModelDTO.class));
        if (list.isExists()) {
            PartitionRecomModelDTO partitionRecomModelDTO = list.getLast();
            partitionData = JSONUtil.toJsonStr(partitionRecomModelDTO);
            partitionName = scene.getShowName();
        }

        Context.put("partitionData", partitionData);
        Context.put("partitionName", partitionName);

    }

    private void sendAdviceQuestionsMessage() {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());

        if (EnterPointEnum.STYLE_HACKS != requestInput.getEnterPointEnum()) {
            return;
        }


        String promptName = requestInput.getPage() + "_" + requestInput.getEnterPointEnum().getValue() + "_" + EventNameEnum.ADVICE_QUESTIONS.getValue();
        PromptModel promptModel = promptBusinessService.getPromptModelByNameWithMetaData(promptName);
        if (ObjectUtil.isEmpty(promptModel) || CharSequenceUtil.isBlank(promptModel.getName())) {
            return;
        }
        String result = commonRequestService.commonExecuteStrategy(promptModel, Collections.emptyMap());
        AgentAdviceQuestions questions = JSONUtil.toBean(result, AgentAdviceQuestions.class);
        AgentMessageResp adviceMessage = initMessage(EventTypeEnum.ADVICE_QUESTIONS.getType());
        sendContent(adviceMessage, EventTypeEnum.ADVICE_QUESTIONS.getType(), questions.getAdviceQuestions());
    }


    private VirtualCompletableFuture<ColorSeasonCardBO> generateColorSeasonCard(IosUserInfoDto userInfo) {
        return VirtualCompletableFuture.supplyAsync(() -> {
            ColorSeasonCardBO colorSeasonCardBO = new ColorSeasonCardBO();
            colorSeasonCardBO.setBestColorCards(userInfo.getAppUser().getBestColorCards());
            colorSeasonCardBO.setBestColors(userInfo.getAppUser().getBestColorNumbers());
            String tryOnColorSeasonUrl = girlsDataService.getTryOnColorSeasonUrl(userInfo.getAppUser().getUserId());

            if (CharSequenceUtil.isBlank(tryOnColorSeasonUrl)) {
                tryOnColorSeasonUrl = userInfo.getBasicUser().getProfileUrl();
            }

            colorSeasonCardBO.setTryOnResultUrl(tryOnColorSeasonUrl);
            colorSeasonCardBO.setColorSeason(userInfo.getAppUser().getColorSeason().getName());
            return colorSeasonCardBO;
        });
    }

}