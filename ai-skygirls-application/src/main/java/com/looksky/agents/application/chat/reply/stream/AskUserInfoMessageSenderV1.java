package com.looksky.agents.application.chat.reply.stream;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.reply.AbstractMessageSender;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import com.looksky.agents.sdk.agent.common.dto.AgentAdviceQuestions;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.common.enums.RoleTypeEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.conversation.Event;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.product.enums.SearchQuestionType;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 细化用户需求, 询问用户更多信息的回复, 然后发送快捷提问
 *
 * <AUTHOR>
 * @since 1.1.11
 **/
@Order(1)
@Component
@ApiVersion(platform = ApiVersion.PlatformType.ALL, version = "1.1.0")
public class AskUserInfoMessageSenderV1 extends AbstractMessageSender {

    /**
     * 细化用户需求 Prompt
     */
    private static final String ASK_USER_INFO_PROMPT = SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();

    /**
     * 建议问题的 Prompt
     */
    private static final String ADVICE_QUESTION_PROMPT = "ask_more_information_advice_questions";


    @Override
    protected boolean supports(PromptModel strategy) {
        return ASK_USER_INFO_PROMPT.equals(strategy.getName());
    }

    @Override
    protected void doSend(PromptModel prompt, HashMap<String, Object> hashMap) {

        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());

        String messageStr = sendStreamMessage(prompt).block();

        // 把 Agent 回复的内容放入 历史 中
        Event list = Event.builder().role(RoleTypeEnum.AGENT).content(messageStr).messageId(requestInput.getMessageId()).time(LocalDateTime.now()).build();
        List<Event> history = Context.get(Context.Name.EVENT_LIST.getName());
        history.add(list);

        // 发送建议的问题
        sendAdviceMessage();

        // 只保存细化的回复
        extraMessage(EventTypeEnum.TEXT.getType(), messageStr, hashMap);

    }


    /**
     * 发送建议问题
     */
    private void sendAdviceMessage() {
        String result = commonRequestService.commonExecuteStrategy(ADVICE_QUESTION_PROMPT);
        AgentAdviceQuestions questions = JSONUtil.toBean(result, AgentAdviceQuestions.class);
        AgentMessageResp adviceMessage = initMessage(EventTypeEnum.ADVICE_QUESTIONS.getType());
        sendContent(adviceMessage, EventTypeEnum.ADVICE_QUESTIONS.getType(), questions.getAdviceQuestions());
    }

    @Override
    protected void afterSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        super.afterSend(strategy, hashMap);
        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());
        conversationStatus.setRefiningRequirements(true);
    }

}