package com.looksky.agents.application.chat.reply.multiEvent;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.preference.enums.PreferenceTypeEnum;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.search.dto.CategoryFeedBackDTO;
import com.looksky.agents.sdk.agent.search.dto.CategorySelectorDTO;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import java.util.Collections;
import java.util.HashMap;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @ClassName CategorySelectorMessageSender
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 下午10:29
 * @Version 1.0
 **/
@Order(1)
@Component
public class CategorySelectorMessageSender extends MultiEventMessageSender {
    @Override
    protected boolean supports(PromptModel strategy) {
        return PromptNameEnum.INQUIRE_USER_SUBCATEGORY_PREFERENCE.getName().equals(strategy.getName());
    }

    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {

        String result = commonRequestService.commonExecuteStrategy(strategy, Collections.emptyMap());
        CategorySelectorDTO categorySelector = JSONUtil.toBean(result, CategorySelectorDTO.class);
        CategoryFeedBackDTO like = CategoryFeedBackDTO.builder().direction(PreferenceTypeEnum.LIKE.getValue()).category(categorySelector.getCategory()).build();

        sendContent(initMessage(EventTypeEnum.TEXT.getType()), EventTypeEnum.TEXT.getType(), categorySelector.getText());
        extraMessage(EventTypeEnum.TEXT.getType(), categorySelector.getText(), hashMap);

        sendContent(initMessage(EventTypeEnum.CONFIRM_CATEGORY.getType()), EventTypeEnum.CONFIRM_CATEGORY.getType(), like);
        extraMessage(EventTypeEnum.CONFIRM_CATEGORY.getType(), "Pushed the category selector to the user. ", hashMap);

    }

    @Override
    protected void afterSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        super.afterSend(strategy, hashMap);
        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());
        conversationStatus.setAskSubcategory(true);
    }
}
