package com.looksky.agents.application.chat.input.utils;

import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.common.utils.BooleanUtils;


public class ConvertEnterPointUtils {

    /**
     * 转换进入点
     * 
     * @param enterPoint 请求对象
     * @return 转换后的进入点
     */
    public static EnterPointEnum convert(String enterPoint, ConversationStatus conversationStatus) {

        EnterPointEnum rawEnterPointEnum = EnterPointEnum.fromValue(enterPoint, null);

        // 如果是 search 场景, 需要判断是推荐前还是推荐后
        if (EnterPointEnum.SEARCH == rawEnterPointEnum) {

            // 如果推过商品
            if (conversationStatus != null && BooleanUtils.isTrue(conversationStatus.getRecommendProduct())) {
                // 返回推荐后的进入点
                return EnterPointEnum.SEARCH_AFTER_RECOMMEND;
            } else {
                // 返回推荐前的进入点
                return EnterPointEnum.SEARCH_BEFORE_RECOMMEND;
            }
        } else {
            // 其他进入点, 直接返回
            return rawEnterPointEnum;
        }
    }
}