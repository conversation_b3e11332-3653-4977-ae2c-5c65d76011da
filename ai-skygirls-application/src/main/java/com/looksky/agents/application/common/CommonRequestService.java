package com.looksky.agents.application.common;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.application.chat.ChatModelFallbackService;
import com.looksky.agents.application.prompt.PromptBusinessService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.models.schema.JsonSchemaValidate;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import com.looksky.agents.sdk.common.dto.CommonModelRequestDTO;
import com.looksky.agents.sdk.enums.OutputModelEnum;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;

@Service
@RequiredArgsConstructor
public class CommonRequestService {
    private static final Logger log = LoggerFactory.getLogger(CommonRequestService.class);
    private final PromptBusinessService promptBusinessService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final int MAX_RETRIES = 1;
    private final ChatModelFallbackService chatModelFallbackService;

    public Object commonRequestModelWithObj(CommonModelRequestDTO commonModelRequestDTO) {
        log.info("通用执行策略, 请求参数:{}", JSONUtil.toJsonStr(commonModelRequestDTO));
        String result = commonRequestModelWithText(commonModelRequestDTO);
        try {
            return objectMapper.readTree(result);
        } catch (Exception e) {
            return result;
        }
    }

    public String commonRequestModelWithText(CommonModelRequestDTO commonModelRequestDTO) {
        String strategyName = commonModelRequestDTO.getStrategyName();
        Assert.notEmpty(strategyName, () -> new IllegalStateException("策略名称不能为空"));
        PromptModel promptModel =
            promptBusinessService.getPromptModelByNameWithMetaData(strategyName);
        RequestParamBO requestParam = promptBusinessService.convert(promptModel,
            commonModelRequestDTO.getVariable() == null ? new HashMap<>() :
                commonModelRequestDTO.getVariable());
        requestParam.setImageUrl(commonModelRequestDTO.getImages());
        String result = commonExecuteStrategy(requestParam);
        result = validateAndRetry(requestParam, result, MAX_RETRIES);
        return result;
    }


    public String commonExecuteStrategy(String strategyName) {
        return commonExecuteStrategy(strategyName, new HashMap<>());
    }

    public String commonExecuteStrategyCheck(String strategyName, Map<String, Object> variable) {
        log.info("执行策略:{}", strategyName);
        PromptModel promptModel = promptBusinessService.getPromptModelByNameWithMetaData(strategyName);
        RequestParamBO requestParam = promptBusinessService.convert(promptModel, variable);
        String result = commonExecuteStrategy(requestParam);
        result = validateAndRetry(requestParam, result, MAX_RETRIES);
        // 把所有的输出结果放到上下文中
        Context.put(strategyName, result);
        return result;
    }

    public String commonExecuteStrategy(String strategyName, Map<String, Object> variable) {
        log.info("执行策略:{}", strategyName);
        PromptModel promptModel =
            promptBusinessService.getPromptModelByNameWithMetaData(strategyName);
        String content = commonExecuteStrategy(promptModel, variable);
        log.info("{}策略返回结果:{}", strategyName, content);


        // 检查返回内容是否是有效的JSON
        Object parsedContent;
        if (JSONUtil.isTypeJSON(content)) {
            parsedContent = JSONUtil.parse(content);
        } else {
            parsedContent = content;
        }

        // 把所有的输出结果放到上下文中
        Context.put(strategyName, parsedContent);
        return content;
    }

    public String commonExecuteStrategy(PromptModel promptModel, Map<String, Object> variable) {
        RequestParamBO requestParam = promptBusinessService.convert(promptModel, variable);
        Context.put("input_" + promptModel.getName(), requestParam);
        return commonExecuteStrategy(requestParam);
    }

    public Flux<String> commonExecuteStrategyAsync(String strategyName,
                                                   Map<String, Object> variable) {
        log.info("执行策略:{}", strategyName);
        PromptModel promptModel =
            promptBusinessService.getPromptModelByNameWithMetaData(strategyName);
        RequestParamBO requestParam = promptBusinessService.convert(promptModel, variable);
        return commonExecuteStrategyAsync(requestParam);
    }


    public Flux<String> commonExecuteStrategyAsync( PromptModel promptModel,
                                                   Map<String, Object> variable) {
        log.info("执行策略:{}", promptModel.getName());
        RequestParamBO requestParam = promptBusinessService.convert(promptModel, variable);
        return commonExecuteStrategyAsync(requestParam);
    }

    public String commonExecuteStrategy(RequestParamBO requestParam) {
        return chatModelFallbackService.executeWithFallback(requestParam);
    }

    public Flux<String> commonExecuteStrategyAsync(RequestParamBO requestParam) {
        return chatModelFallbackService.executeWithFallbackAsync(requestParam);
    }


    public String validateAndRetry(RequestParamBO requestParam, String result, int maxRetries) {

        log.debug("开始执行 JSON Schema 验证");

        if (!StringUtils.hasText(requestParam.getJsonSchema())) {
            return result;
        }

        if (requestParam.getOutputType() != OutputModelEnum.FUNCTION_CALL) {
            return result;
        }

        int retryCount = 0;
        while (retryCount <= maxRetries) {
            try {
                boolean validate =
                    JsonSchemaValidate.validate(result, requestParam.getJsonSchema());

                if (validate) {
                    log.debug("JSON Schema 验证成功");
                    return result;
                }

                // 记录验证错误并重试
                log.warn("JSON Schema 验证失败,正在重试 {}/{}", retryCount + 1, maxRetries);

                if (retryCount++ < maxRetries) {
                    result = commonExecuteStrategy(requestParam);
                }
            } catch (Exception e) {
                log.error("JSON Schema 验证过程发生错误", e);
                if (retryCount++ < maxRetries) {
                    result = commonExecuteStrategy(requestParam);
                }
            }
        }

        log.error("JSON Schema 验证失败,已重试{}次,最终的结果为:{}", maxRetries, result);

        return result;

    }


}
