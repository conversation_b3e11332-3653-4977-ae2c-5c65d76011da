package com.looksky.agents.application.chat.preference.extraction.tag;

import com.looksky.agents.application.chat.preference.extraction.AbstractPreferenceExtractor;
import com.looksky.agents.application.chat.preference.extraction.SingleTagExtractService;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.data.redis.conversation.PreferenceDataService;
import com.looksky.agents.sdk.agent.common.enums.TagEnum;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.preference.SetPreferenceValue;

//@Component
@Deprecated(since = "2025-01-07")
public class LengthExtractor extends AbstractPreferenceExtractor<SetPreferenceValue> {

    public LengthExtractor(SingleTagExtractService singleTagExtractService,
                          PreferenceDataService preferenceDataService,
                          TagSystemTableService tagSystemTableService) {
        super(singleTagExtractService, preferenceDataService, tagSystemTableService);
    }

    @Override
    protected String getTagName() {
        return TagEnum.LENGTH.getName();
    }

    @Override
    protected SetPreferenceValue parseExtractedValue(String result) {
        return SetPreferenceValue.parseJson(result);
    }

    @Override
    protected SetPreferenceValue getCurrentPreference(ExtractedEntityObject userPreference) {
        return userPreference.getLengthPreference();
    }

    @Override
    protected void updatePreference(ExtractedEntityObject userPreference, SetPreferenceValue newValue) {
        userPreference.setLengthPreference(newValue);
    }

    @Override
    protected SetPreferenceValue mergePreference(SetPreferenceValue newValue, SetPreferenceValue currentValue) {
        return SetPreferenceValue.merge(newValue, currentValue);
    }
}