package com.looksky.agents.application.chat.reply.stream;

import cn.hutool.core.map.MapUtil;
import com.looksky.agents.application.chat.reply.AbstractMessageSender;
import com.looksky.agents.models.model.ModelEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import java.util.HashMap;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Order(100)
@Component
public class DeepSeekStreamMessageSender extends AbstractMessageSender {
    @Override
    protected boolean supports(PromptModel strategy) {
        return ModelEnum.AWS_DEEP_SEEK.getModelName().equals(strategy.getModelName());
    }

    @Override
    protected void doSend(PromptModel prompt, HashMap<String, Object> hashMap) {
        String messageStr = sendThinkStreamMessageNoEmoji(prompt.getName(), MapUtil.empty()).block();
        extraMessage(EventTypeEnum.TEXT.getType(), messageStr, hashMap);
    }

}