package com.looksky.agents.application.chat.reply.multiEvent;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.application.chat.recommend.PreferenceToSearchConverter;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.data.grpc.SearchGrpcClient;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.dto.CurrentCategoryTagDTO;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.search.bo.OutfitSuggestionList;
import com.looksky.agents.sdk.agent.search.bo.RecommendOutfitBO;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import com.looksky.agents.sdk.recommend.search.dto.response.SearchResponseDTO;
import jakarta.annotation.Resource;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @ClassName CategorySelectorMessageSender
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 下午10:29
 * @Version 1.0
 **/
@Slf4j
@Order(1)
@Component
public class OutfitSuggestMessageSender extends MultiEventMessageSender {

    @Resource
    private SearchGrpcClient girlsAgentSearchGrpcClient;

    @Resource
    private TagSystemTableService tagSystemTableService;


    @Override
    protected boolean supports(PromptModel strategy) {
        // 生成搜索过程
        return PromptNameEnum.INQUIRE_USER_SOLUTION.getName().equals(strategy.getName());
    }

    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {

        sendLoading();

        // 生成推荐方案
        ExtractedEntityObject preference = Context.get(Context.Name.PREFERENCE.getName());
        Set<String> categories = preference.getCurrentCategories();
        CurrentCategoryTagDTO currentCategoryTag = CurrentCategoryTagDTO.builder().build();
        if (ObjectUtil.isEmpty(categories)) {
            currentCategoryTag.setCategory(CategoryEnum.CLOTH.getName());
        } else {
            categories.stream().findFirst().ifPresent(currentCategoryTag::setCategory);
        }


        String block = sendThinkStreamMessageNoEmoji("thinking_about_outfit_solution", Map.of(Context.Name.CURRENT.name(), currentCategoryTag)).block();

        extraMessage(EventTypeEnum.TEXT.getType(), block, hashMap);

        sendContent(initMessage(EventTypeEnum.WAIT_SOLUTION.getType()), EventTypeEnum.WAIT_SOLUTION.getType());

        String result = commonRequestService.commonExecuteStrategy("structured_output_outfit_solution", Map.of(Context.Name.CURRENT.name(), currentCategoryTag, "input", block));

        OutfitSuggestionList outfitSuggestionList = JSONUtil.toBean(result, OutfitSuggestionList.class);

        filterOutfitSuggestionList(outfitSuggestionList);
        List<RecommendOutfitBO> recommendOutfitDTOS = generatePositiveWords(outfitSuggestionList);

        try {
            recommendOutfitDTOS = recommendOutfitProduct(recommendOutfitDTOS);
        } catch (Exception e) {
            log.error("推荐方案失败", e);
        }


        if (ObjectUtil.isEmpty(recommendOutfitDTOS) || ObjectUtil.isEmpty(recommendOutfitDTOS.getFirst().getSkcIds())) {
            log.warn("推荐方案为空, returnContents: {}", JSONUtil.toJsonStr(recommendOutfitDTOS));
            // 执行兜底回复
            String finalMessageStr = sendStreamMessage("query_no_products", Collections.emptyMap()).block();
            extraMessage(EventTypeEnum.TEXT.getType(), finalMessageStr, hashMap);
            return;
        }

        sendContent(initMessage(EventTypeEnum.CONFIRM_SOLUTION.getType()), EventTypeEnum.CONFIRM_SOLUTION.getType(), recommendOutfitDTOS);


        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());
        conversationStatus.setRecommendProduct(true);
    }

    @Override
    protected ArrayDeque<Object> generateReturnContents(PromptModel strategy) {
        sendContent(initMessage(EventTypeEnum.WAIT_SOLUTION.getType()), EventTypeEnum.WAIT_SOLUTION.getType(), "");

        OutfitSuggestionList outfitSuggestionList = buildOutfitTitleAndTag();
        filterOutfitSuggestionList(outfitSuggestionList);
        List<RecommendOutfitBO> recommendOutfitDTOS = generatePositiveWords(outfitSuggestionList);

        try {
            recommendOutfitDTOS = recommendOutfitProduct(recommendOutfitDTOS);
        } catch (Exception e) {
            log.error("推荐方案失败", e);
            return new ArrayDeque<>();
        }


        if (ObjectUtil.isEmpty(recommendOutfitDTOS) || ObjectUtil.isEmpty(recommendOutfitDTOS.getFirst().getSkcIds())) {
            return new ArrayDeque<>();
        }

        ArrayDeque<Object> deque = new ArrayDeque<>();
        deque.offer(recommendOutfitDTOS);


        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());
        conversationStatus.setRecommendProduct(true);

        return deque;
    }

    /**
     * 限制最多 4 条
     *
     * @param outfitSuggestionList
     */
    private void filterOutfitSuggestionList(OutfitSuggestionList outfitSuggestionList) {
        List<RecommendOutfitBO> outfitSuggestions = outfitSuggestionList.getOutfitSuggestions();
        if (ObjectUtil.isNotEmpty(outfitSuggestions) && outfitSuggestions.size() > 4) {
            outfitSuggestions.subList(4, outfitSuggestions.size()).clear();
        }
    }

    private List<RecommendOutfitBO> recommendOutfitProduct(List<RecommendOutfitBO> recommendOutfits) {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());

        List<SearchRequestDTO.GirlsStrategyTerm> searchTerms = recommendOutfits.stream().map(this::convertToVectorQuery).toList();

        SearchRequestDTO searchRequestDTO = SearchRequestDTO.builder()
            .userId(requestInput.getUserId())
            .season(requestInput.getSeason())
            .strategyTerms(searchTerms)
            .build();

        SearchResponseDTO searchResult = girlsAgentSearchGrpcClient.search(searchRequestDTO);

        if (ObjectUtil.isEmpty(searchResult) || ObjectUtil.isEmpty(searchResult.getStrategyResponses())) {
            return recommendOutfits;
        }

        List<SearchResponseDTO.StrategyResponse> strategyResponses = searchResult.getStrategyResponses().stream().toList();

        recommendOutfits.forEach(recommendOutfitDTO -> {
            String uuid = recommendOutfitDTO.getUuid();
            strategyResponses.stream().filter(strategyResponse -> strategyResponse.getSearchStrategy().equals(uuid)).findFirst().ifPresent(strategyResponse -> recommendOutfitDTO.setSkcIds(strategyResponse.getItems()));
        });

        // 过滤掉没有 skcId 的推荐
        return recommendOutfits.stream().filter(recommendOutfitDTO -> !ObjectUtil.isEmpty(recommendOutfitDTO.getSkcIds())).toList();


    }

    private SearchRequestDTO.GirlsStrategyTerm convertToVectorQuery(RecommendOutfitBO recommendOutfitDTO) {
        Set<String> category = tagSystemTableService.convertToSubCategory(Set.of(recommendOutfitDTO.getCategory()));
        SearchRequestDTO.GirlsStrategyTerm.GirlsStrategyTermBuilder builder = SearchRequestDTO.GirlsStrategyTerm.builder();

        ArrayList<SearchRequestDTO.VectorQuery> step1 = new ArrayList<>();
        ArrayList<SearchRequestDTO.VectorQuery> step2 = new ArrayList<>();

        Set<String> subCategorySet = tagSystemTableService.convertToSubCategory(category);

        SearchRequestDTO.SearchTerm searchTerm =
            SearchRequestDTO.SearchTerm.builder()
                .categories(new ArrayList<>(subCategorySet))
                .searchStrategy(recommendOutfitDTO.getUuid())
                .build();

        setTagValue(searchTerm);

        builder.searchTerm(searchTerm);


        // 构建 step1
        step1.add(SearchRequestDTO.VectorQuery.builder()
            .text(recommendOutfitDTO.getTitle())
            .weight(1)
            .build());

        builder.step1VectorQueries(step1);

        // 构建 step2. 使用 第三步的结果
        if (CharSequenceUtil.isNotBlank(recommendOutfitDTO.getPositivePreferences())) {
            step2.add(SearchRequestDTO.VectorQuery.builder()
                .text(recommendOutfitDTO.getTitle() + ", " + recommendOutfitDTO.getPositivePreferences())
                .weight(1)
                .build());
        }
        if (CharSequenceUtil.isNotBlank(recommendOutfitDTO.getNegativePreferences()) && !"null".equals(recommendOutfitDTO.getNegativePreferences())) {
            step2.add(SearchRequestDTO.VectorQuery.builder()
                .text(recommendOutfitDTO.getNegativePreferences())
                .weight(-1)
                .build());
        }


        if (ObjectUtil.isNotEmpty(step2)) {
            builder.step2VectorQueries(step2);
        }

        return builder.build();
    }

    private void setTagValue(SearchRequestDTO.SearchTerm vectorQueries) {
        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());
        PreferenceToSearchConverter.convertToSearchTerm(vectorQueries, userPreference);
    }


    public OutfitSuggestionList buildOutfitTitleAndTag() {
        ExtractedEntityObject preference = Context.get(Context.Name.PREFERENCE.getName());
        Set<String> categories = preference.getCurrentCategories();
        CurrentCategoryTagDTO currentCategoryTag = CurrentCategoryTagDTO.builder().build();
        if (ObjectUtil.isEmpty(categories)) {
            currentCategoryTag.setCategory(CategoryEnum.CLOTH.getName());
        } else {
            categories.stream().findFirst().ifPresent(currentCategoryTag::setCategory);
        }

        String result = commonRequestService.commonExecuteStrategy(PromptNameEnum.OUTFIT_SUGGESTION_NEW.getName(), Map.of(Context.Name.CURRENT.name(), currentCategoryTag));

        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(result, OutfitSuggestionList.class);
        } catch (Exception e) {
            log.error("推方案 Prompt 返回异常, Prompt 返回结果: {}", result, e);

        }
        return new OutfitSuggestionList();
    }


    /**
     * 生成正向词
     *
     * @param outfitSuggestionList
     * @return
     */
    private List<RecommendOutfitBO> generatePositiveWords(OutfitSuggestionList outfitSuggestionList) {
        outfitSuggestionList.getOutfitSuggestions().forEach(suggestion -> suggestion.setUuid(IdUtil.getSnowflakeNextIdStr()));
        return outfitSuggestionList.getOutfitSuggestions();
    }


}
