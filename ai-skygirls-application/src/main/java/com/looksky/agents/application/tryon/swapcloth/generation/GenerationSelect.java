package com.looksky.agents.application.tryon.swapcloth.generation;

import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class GenerationSelect {
    private final List<IRunWorkflow> workflows;
    private final RedissonClient redissonClient;
    private static final String COUNT_KEY = "swap_cloth_queue:%s";


    public String getWorkflowResult(String id, String imagePrompt, String faceImage, String modelImage) {

        WorkflowEnum runType = WorkflowEnum.MODEL;

        //long runPodCount = getCount(WorkflowEnum.RUN_POD);
        //if (runPodCount < 5) {
        //    runType = WorkflowEnum.RUN_POD;
        //} else {
        //    runType = WorkflowEnum.MODEL;
        //}

        log.info("runType: {}", runType);

        start(runType, id);

        String result = workflows.stream()
            .filter(workflow -> workflow.supports(runType))
            .findFirst()
            .orElseThrow(() -> new UnsupportedOperationException("No suitable workflow found"))
            .run(id, imagePrompt, faceImage, modelImage);

        complete(runType, id);


        return result;
    }

    public int getCount(WorkflowEnum workflowEnum) {
        return redissonClient.getSet(COUNT_KEY.formatted(workflowEnum)).size();
    }


    public void start(WorkflowEnum workflowEnum, String id) {
        redissonClient.getSet(COUNT_KEY.formatted(workflowEnum), StringCodec.INSTANCE).add(id);
    }


    public void complete(WorkflowEnum workflowEnum, String id) {
        boolean remove = redissonClient.getSet(COUNT_KEY.formatted(workflowEnum), StringCodec.INSTANCE).remove(id);
        if (!remove) {
            log.error("删除 {} {} 失败", workflowEnum, id);
        }
    }

    public boolean contains(WorkflowEnum workflowEnum, String id) {
        return redissonClient.getSet(COUNT_KEY.formatted(workflowEnum), StringCodec.INSTANCE).contains(id);
    }
}
