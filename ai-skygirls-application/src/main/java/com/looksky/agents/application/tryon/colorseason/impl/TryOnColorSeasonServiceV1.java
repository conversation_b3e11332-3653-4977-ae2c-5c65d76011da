package com.looksky.agents.application.tryon.colorseason.impl;

import ai.fal.client.ClientConfig;
import ai.fal.client.CredentialsResolver;
import ai.fal.client.FalClient;
import ai.fal.client.SubscribeOptions;
import ai.fal.client.queue.QueueStatus;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.common.model.tryOn.TryOnFalKeyConfig;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonParam;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.stereotype.Service;


/**
 * colorSeason 试色 service V1实现
 *
 * <AUTHOR>
 * @since 1.1.6
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class TryOnColorSeasonServiceV1 {

    private static final String PROMPT_TEMPLATE = "a {skin_depth} {skin_tone} skin, {weight_type} weight type,{hair_color} hair, Change the clothes to a {color} t-shirt, keep the face unchanged and set the background to white,formal potrait";

    private final Random random = new Random();
    private final TryOnFalKeyConfig tryOnFalKeyConfig;


    public String tryOnColorSeason(TryOnColorSeasonParam param) {
        String prompt = generatePrompt(param);
        return tryOn(prompt, param.getUserImage());
    }

    private String generatePrompt(TryOnColorSeasonParam param) {
        PromptTemplate promptTemplate = new PromptTemplate(PROMPT_TEMPLATE);
        Prompt prompt = promptTemplate.create(
            Map.of(
                "skin_tone", param.getSkinTone(),
                "skin_depth", param.getSkinDepth(),
                "weight_type", param.getWeightType(),
                "hair_color", param.getHairColor(),
                "color", param.getColor()
            ));
        String template = prompt.getContents();
        log.info("试色生成的提示词: {}", template);
        return template;
    }

    public String tryOn(String prompt, String userImage) {
        List<String> apiKeyList = tryOnFalKeyConfig.getApiKeyList();

        String apiKey = apiKeyList.get(random.nextInt(apiKeyList.size()));
        var falClient = FalClient.withConfig(ClientConfig.withCredentials(CredentialsResolver.fromApiKey(apiKey)));

        HashMap<String, Object> requestBody = new HashMap<>();
        requestBody.put("prompt", prompt);
        requestBody.put("reference_image_url", userImage);
        requestBody.put("image_size", Map.of(
            "width", 512,
            "height", 672
        ));
        requestBody.put("true_cfg", 1);
        requestBody.put("id_weight", 1);
        requestBody.put("guidance_scale", 2);
        requestBody.put("negative_prompt", "");
        requestBody.put("max_sequence_length", "128");
        requestBody.put("num_inference_steps", 20);
        requestBody.put("enable_safety_checker", false);
        requestBody.put("start_step", 0);

        log.info("试色请求参数: {}", JSONUtil.toJsonStr(requestBody));

        var result = falClient.subscribe("fal-ai/flux-pulid",
            SubscribeOptions.<FashnResult>builder()
                .input(requestBody)
                .logs(true)
                .resultType(FashnResult.class)
                .onQueueUpdate(update -> {
                    if (update instanceof QueueStatus.InProgress status) {
                        log.info("试色返回过程数据: {}", status.getLogs());
                    }
                })
                .build()
        );


        String requestId = result.getRequestId();
        log.info("试色请求: requestId:{}, 请求体: {}", requestId, JSONUtil.toJsonStr(requestBody));
        log.info("试色结果: {}", JSONUtil.toJsonStr(result));

        return result.getData().getImages().getFirst().getUrl();
    }

    @Data
    private static class FashnResult {
        private List<Image> images;
        private Timings timings;
        private long seed;
        private boolean hasNsfwConcepts;
        private String prompt;

        @Data
        public static class Image {
            private String url;
            private int width;
            private int height;
            private String contentType;
        }

        @Data
        public static class Timings {
            private double inference;
        }
    }
}
