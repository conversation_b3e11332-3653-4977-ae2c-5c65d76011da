package com.looksky.agents.application.provider;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.data.redis.product.ProductClient;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.skygirls.biz.im.dto.MessageRestDTO;
import java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class BrandInfoProvider implements DataProvider {
    private final ProductClient productClient;


    @Override
    public void getData() {
        try {
            RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());

            if (requestInput == null || requestInput.getEventDict() == null) {
                Context.put(Context.Name.BRAND_INFO_DATA.getName(), Collections.emptyMap());
                return;
            }

            MessageRestDTO.EventDict eventDict = requestInput.getEventDict();
            String skcId = eventDict.getSkcId();
            if (skcId == null) {
                Context.put(Context.Name.BRAND_INFO_DATA.getName(), Collections.emptyMap());
                return;
            }

            String brandInfo = productClient.getBrandInfoBySkcId(skcId);

            if (CharSequenceUtil.isEmpty(brandInfo)) {
                Context.put(Context.Name.BRAND_INFO_DATA.getName(), Collections.emptyMap());
                return;
            }

            JSONObject jsonObject = JSONUtil.parseObj(brandInfo);

            Context.put(Context.Name.BRAND_INFO_DATA.getName(), jsonObject);
        } catch (Exception e) {
            log.error("获取 品牌信息 失败", e);
            Context.put(Context.Name.BRAND_INFO_DATA.getName(), Collections.emptyMap());
        }

    }

    @Override
    public boolean supports(String key) {
        return key.equals("brandInfo");
    }

} 