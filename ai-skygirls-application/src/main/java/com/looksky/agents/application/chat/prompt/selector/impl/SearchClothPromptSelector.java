package com.looksky.agents.application.chat.prompt.selector.impl;

import cn.hutool.core.util.ObjectUtil;
import com.looksky.agents.application.chat.prompt.selector.PromptSelector;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.product.enums.SearchQuestionType;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.preference.SubjectivePreference;
import com.looksky.agents.sdk.agent.preference.CategoryPreference;
import com.looksky.agents.sdk.agent.preference.TagPreferences;
import com.looksky.agents.common.utils.BooleanUtils;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.data.redis.conversation.HistoryDataService;
import com.looksky.agents.infrastructure.context.Context;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 服装搜索问题选择器
 */
@Component
@RequiredArgsConstructor
public class SearchClothPromptSelector implements PromptSelector {

    private final HistoryDataService historyDataService;
    private final TagSystemTableService tagSystemTableService;

    @Override
    public String support() {
        return "search_cloth1111";
    }

    @Override
    public String select() {

        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());


        List<String> allSubCategory = tagSystemTableService.getSubCategories();
        List<String> allFirstCategory = tagSystemTableService.getFirstCategories();

        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());

        // 没有反馈任何偏好, 需要咨询用户
        if (ObjectUtil.isEmpty(userPreference)) {
            return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
        }

        // userPreference  不为空
        CategoryPreference categoryPreferences = userPreference.getCategoryPreferences();
        SubjectivePreference subjectivePreference = userPreference.getSubjectivePreference();

        if (ObjectUtil.isEmpty(categoryPreferences) || ObjectUtil.isEmpty(subjectivePreference)) {
            return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
        }


        AtomicInteger conversationNumber = conversationStatus.getConversationNumber() == null ? new AtomicInteger(0) : conversationStatus.getConversationNumber();

        Set<String> currCategory = categoryPreferences.getCategoryPreferences().keySet();

        // 对话历史超过三轮 并且还没有推荐过商品
        if (conversationNumber.get() >= 2 && BooleanUtils.isFalse(conversationStatus.getRecommendProduct())) {

            // 获取当前品类

            // 判断是否表达了二级品类
            if (TagSystemTableService.isInnerTag(currCategory, new HashSet<>(allSubCategory))) {
                return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
            } else {
                return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK_SOLUTION.getValue();
            }
        }

        AtomicInteger inquireUserInfoNumber = conversationStatus.getInquireUserInfoNumber() == null ? new AtomicInteger(0) : conversationStatus.getInquireUserInfoNumber();

        // todo
//        if (externInfo.isAskUser() && inquireUserInfoNumber.get() < 2) {
//            return SearchQuestionType.INQUIRE_USER_INFO.getValue();
//        }

        // 如果当前只表达了一个品列, 且是 cloth, 但是没有主观标签
        if (currCategory.size() == 1 && currCategory.contains(CategoryEnum.CLOTH.getName()) && ObjectUtil.isEmpty(subjectivePreference)) {
            if (ObjectUtil.isEmpty(categoryPreferences.getCategoryPreferences().get(CategoryEnum.CLOTH.getName()))) {
                return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
            }
        }


        // 如果有品类标签但没有主观标签
        if (ObjectUtil.isNotEmpty(categoryPreferences) && ObjectUtil.isEmpty(subjectivePreference)) {

            int tagNumber = getMaxTagNumber(categoryPreferences.getCategoryPreferences());

            // 如果包含二级品类
            if (TagSystemTableService.isInnerTag(currCategory, new HashSet<>(allSubCategory))) {
                if (tagNumber >= 2) {
                    return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
                } else {
                    if (BooleanUtils.isFalse(conversationStatus.getAskTag())) {
                        return SearchQuestionType.SUBCATEGORY_WITH_LESS_TWO_TAG_WITHOUT_ASK.getValue();
                    } else if (BooleanUtils.isFalse(conversationStatus.getRefiningRequirements())) {
                        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
                    } else {
                        return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
                    }
                }
            } else if (TagSystemTableService.isInnerTag(currCategory, new HashSet<>(allFirstCategory)) && tagNumber >= 3) {
                return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
            } else {
                if (conversationStatus.getAskSubcategory()) {
                    if (conversationStatus.getRefiningRequirements()) {
                        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK_SOLUTION.getValue();
                    } else {
                        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
                    }
                } else {
                    return SearchQuestionType.FIRST_CATEGORY_WITHOUT_SUBCATEGORY_WITHOUT_ASK_SUBCATEGORY.getValue();
                }
            }
        }

        // 既有品类标签又有主观标签
        else if (ObjectUtil.isNotEmpty(categoryPreferences) && ObjectUtil.isNotEmpty(subjectivePreference)) {
            int tagNumber = getMaxTagNumber(categoryPreferences.getCategoryPreferences());

            if (TagSystemTableService.isInnerTag(currCategory, new HashSet<>(allSubCategory))) {
                if (tagNumber >= 2) {
                    return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
                } else {
                    // 判断有没有细化过
                    if (BooleanUtils.isFalse(conversationStatus.getRefiningRequirements())) {
                        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
                    } else {
                        return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
                    }
                }
            } else if (TagSystemTableService.isInnerTag(currCategory, new HashSet<>(allFirstCategory)) && tagNumber >= 3) {
                return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
            } else {
                if (BooleanUtils.isFalse(conversationStatus.getRefiningRequirements())) {
                    return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
                } else {
                    if (BooleanUtils.isTrue(conversationStatus.getCategoryChange())) {
                        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
                    } else {
                        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK_SOLUTION.getValue();
                    }
                }
            }
        }

        // 仅有主观词
        if (ObjectUtil.isNotEmpty(subjectivePreference)) {
            if (BooleanUtils.isFalse(conversationStatus.getRefiningRequirements())) {
                return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
            } else {
                return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK_SOLUTION.getValue();
            }
        }

        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
    }

    /**
     * 获取最大标签数量, 除去 positive_requirement, negative_requirement, dislike_categories
     * @param categoryPreferences
     * @return
     */
    private int getMaxTagNumber(Map<String, TagPreferences> categoryPreferences) {
        int tagNumber = 0;

        for (Map.Entry<String, TagPreferences> entry : categoryPreferences.entrySet()) {
            if (CategoryEnum.CLOTH.getName().equals(entry.getKey())) {
                continue;
            }
            TagPreferences tagPref = entry.getValue();
            List<String> list = tagPref.getTagsPreferences().keySet().stream()
                    .filter(tag -> !tag.equals("positive_requirement") && !tag.equals("negative_requirement") && !tag.equals("dislike_categories"))
                    .toList();

            if (list.size() > tagNumber) {
                tagNumber = list.size();
            }
        }
        return tagNumber;
    }
} 