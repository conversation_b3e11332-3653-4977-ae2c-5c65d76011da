package com.looksky.agents.application.schedule;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.models.model.ModelEnum;
import com.looksky.agents.models.schema.JsonSchemaRequestBuilder;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.TreeMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.stereotype.Service;

/**
 * OpenAI监控服务
 * 用于监控OpenAI请求的耗时情况，并将数据保存到Redis中
 * 采用异步执行方式，确保定时任务按照精确的时间间隔执行
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OpenAIMonitoringService {
    private final OpenAiChatModel openAiChatModel;
    private final RedissonClient redissonClient;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final int RETENTION_DAYS = 7;
    private static final String REQUEST_BODY = "{\"systemPrompt\":\"# Role:FashionShoppingAssistant\\n\\n## Profile\\n\\n- Author: skylar\\n- Version: 1\\n- Language: English\\n- Description: You are a shopping assistant with 15 years of experience in the fashion industry, specializing in personalized styling advice for Western women. Your deep expertise in fashion aesthetics and Western women’s style preferences, combined with extensive knowledge of shopping scenarios, allows you to offer tailored and insightful guidance.\\n\\n### Skills\\n1.Fashion Expertise: Comprehensive knowledge of Western women's styles, trends, and aesthetics, allowing for personalized and relevant styling advice.\\n2.Advanced Issue Identification: Ability to accurately identify and address user concerns in shopping scenarios with precision and empathy.\\n3.Label Recognition: Proficiency in recognizing clothing labels mentioned by users and recommending options that align with their preferences.\\n4.Tailored Recommendations: Ability to recommend clothing labels based on user history, occasion, style preferences, age, and desired fashion outcome.\\n5.Conflict Detection: Expertise in identifying and resolving conflicts between current and past requests to ensure a consistent shopping experience.\\n6.Trend Awareness: Staying updated on fashion trends to provide cutting-edge recommendations.\\n7.Cultural Sensitivity: Understanding Western women’s lifestyle and cultural context to ensure appropriate style suggestions.\\n\\n\\n## Constraints\\n1.Issue Resolution: Accurate identification and resolution of user issues with clear, actionable solutions.\\n2.Label Recognition: Ensure no detail is overlooked in recognizing and addressing user preferences.\\n3.Personalized Recommendations: Deliver recommendations that align with the user’s goals, lifestyle, and needs.\\n4.\\n\\n## Workflow\\n1.Conversation Analysis: Thoroughly analyze user conversations to identify and categorize issues.\\n2.Label Recognition: Accurately recognize clothing labels mentioned by users and integrate them into recommendations.\\n3.Recommendation Process: Use user information, occasion, and style preferences to recommend the most suitable clothing labels.\\n4.Conflict Management: Monitor and resolve conflicts between current and past requests to ensure a seamless shopping experience.\\n5.Feedback Integration: Continuously refine recommendations based on user feedback.\",\"userPrompt\":\"## UserInfo\\nname:codetheory\\nage: 25\\nsex: Female\\nkibbeType: SOFT_CLASSIC\\nbodyShape: Hourglass\\nheight: 5' 7\\\"\\nweight: 132 lbs\\nheightType: MEDIUM\\nweightType: AVERAGE\\nproportions: BALANCED\uFEFF\\n\uFEFF\\n## Question type description\\n**app_introduce**:Users inquire about the LookSky or Skylar application, focusing on its features, development team, and operational aspects. This includes questions about registration, login, logout, password management, privacy policy, personal information collection, and purchasing products. Users might also ask about the application's mission, information sources, how to exit the program, the presence of private tags, and platform discount policies. These inquiries aim to understand how the application operates and what benefits it offers to users.\\n\uFEFF\\n**search_cloth**: Scenarios for search_cloth:\\n1 Users explicitly want clothes of a particular category or with specific tags.\\n2 Users inquire about attending a particular occasion and want to create a specific style, achieve a certain outfit effect, find clothes suitable for a particular season,and various other ways of searching for clothes. \\n3 When users specify preferences for certain brands, price ranges, or sizes (e.g., \\\"under $100,\\\" \\\"size 10\\\"). If the user mentions a specific price range (e.g., “These are too expensive, I need something between $9-$259”)\\n4 Users ask questions about the suitability of specific clothing items for their body type or occasion (e.g., “Are layered shirts and vests suitable for petite figures?”), clearly indicating a need for clothing advice.\\n5 users seeking clothing advice or inspiration.\\n6 Users agree with your viewpoint.\\n\uFEFF\\n**inquire_clothing_detail**:The current strategy can only be triggered after recommending a product. In the artificial intelligence search interface, users can query specific product details such as shipping policies, return and exchange policies, payment methods, discounts, care instructions, product descriptions, orders and after-sales services, price disputes, product matching/clothing suggestions, sizes, etc.\\n\uFEFF\\n**small_talk**: Idle talk refers to conversations initiated by users that are unrelated to the main functions of the platform, such as asking about personal interest topics such as the US presidential election, weather, movies, etc. Meanwhile, idle chat does not include understanding of the application, user feedback, suggestions, or evaluations.The user agrees that the solution recommended by Skylar does not belong to this strategy.\\n\uFEFF\\n**search_similar_cloth**: When a user explicitly requests to view products that are similar to a specific product, or expresses a preference for a product and wishes to modify its specific features, the \\\"search for similarity\\\" strategy will be triggered. For example, users may say \\\"look for a similar product to item 1\\\" or \\\"this piece of clothing is great, but can it be replaced with a V-neck?\\\" In these cases, the system will provide similar product recommendations that meet the user's needs.The user agrees that the solution recommended by Skylar does not belong to this strategy.\\n\uFEFF\\n**search_non_cloth**: Automatically activate this strategy when the system detects queries related to non-women’s clothing items, based on a predefined set of keywords (e.g., \\\"shoes,\\\" \\\"ankle boots,\\\" \\\"hats,\\\" \\\"accessories,\\\" \\\"cosmetics,\\\" \\\"children's clothing,\\\" \\\"men's clothing\\\") to manage user inquiries outside the platform's service scope.\\n\uFEFF\\n**feedback_too_expensive**:When users express that the price is too expensive while browsing products, but**without specifying a price range**, the system will implement strategies to assist the user.The user says they don't like it and it doesn't belong to this strategy.\\n\uFEFF\\n**recommand_product**：When users express impatience or directly request immediate product recommendations (e.g., \\\"Don't ask anymore, just give me the results\\\"), this strategy will be activated. We will promptly analyze the user's request and existing tags, and swiftly push relevant product cards.\\u00a0\\n\uFEFF\\n\uFEFF\\n## Conversation_history\\nAGENT: Hey codetheory! \uD83C\uDF38 Spring is in full swing in New York—perfect timing to explore chic outfits for your upcoming sorority events like mixers and brunches!\\n\\nAGENT: [\\\"\uD83D\uDC83 Soft pastel dresses for spring sorority mixers.\\\",\\\"✨ Midi dresses to flatter your hourglass shape.\\\",\\\"\uD83D\uDCAC What are the best spring brunch outfits for me?\\\",\\\"\uD83D\uDCAC Help me find a structured dress for formal dinners!\\\"]\\n\\n\uFEFF\\n\uFEFF\\n##Query\\nUSER:\uD83D\uDC83 Soft pastel dresses for spring sorority mixers.\\n\",\"modelName\":\"gpt-4o-2024-11-24\",\"temperature\":0,\"jsonSchema\":\"{\\n    \\\"type\\\": \\\"object\\\",\\n    \\\"title\\\": \\\"QuestionTypeSearch\\\",\\n    \\\"required\\\": [\\n        \\\"questionType\\\"\\n    ],\\n    \\\"properties\\\": {\\n        \\\"questionType\\\": {\\n            \\\"enum\\\": [\\n                \\\"app_introduce\\\",\\n                \\\"small_talk_in_search\\\",\\n                \\\"search_cloth\\\",\\n                \\\"search_similar_cloth\\\",\\n                \\\"search_non_cloth\\\",\\n                \\\"inquire_clothing_detail\\\",\\n                \\\"feedback_too_expensive\\\",\\n                \\\"recommend_product\\\"\\n            ],\\n            \\\"title\\\": \\\"QuestionTypeInSearchPage\\\",\\n            \\\"description\\\": \\\"questionType\\\"\\n        }\\n    },\\n    \\\"definitions\\\": {\\n        \\\"QuestionTypeInSearchPage\\\": {\\n            \\\"enum\\\": [\\n                \\\"app_introduce\\\",\\n                \\\"small_talk_in_search\\\",\\n                \\\"search_cloth\\\",\\n                \\\"search_similar_cloth\\\",\\n                \\\"search_non_cloth\\\",\\n                \\\"inquire_clothing_detail\\\",\\n                \\\"feedback_too_expensive\\\",\\n                \\\"recommand_product\\\"\\n            ],\\n            \\\"title\\\": \\\"QuestionTypeInSearchPage\\\",\\n            \\\"description\\\": \\\"An enumeration.\\\"\\n        }\\n    },\\n    \\\"additionalProperties\\\": false\\n}\",\"outputType\":\"JSON_SCHEMA\",\"streaming\":false,\"size\":0}";

    /**
     * 每秒执行一次OpenAI请求并记录耗时
     * 使用TestEnvironmentScheduled注解，只在指定环境生效
     * 通过异步执行整个方法，确保定时任务不会被阻塞
     * <p>
     * 优势：
     * 1. 定时任务本身执行很快，不会被长时间运行的请求阻塞
     * 2. 即使前一个请求还在执行，下一个请求也会准时开始
     * 3. 使用虚拟线程执行，资源消耗低
     */
    //@EnvironmentScheduled(cron = "*/20 * * * * ?", environments = {"dev"}) // 每秒执行一次
    //@EnvironmentScheduled(cron = "0 * * * * ?") // 每分钟执行一次
    public void scheduleMonitoring() {
        // 立即异步执行监控任务，不阻塞调度器
        // 使用虚拟线程执行器，每次创建新的虚拟线程
        VirtualCompletableFuture.runAsync(this::monitorOpenAILatency);
    }

    /**
     * 执行OpenAI请求并记录耗时
     * 这个方法会被异步调用，不会阻塞定时任务
     */
    public void monitorOpenAILatency() {
        // 在请求开始前记录时间点和时间戳
        LocalDateTime requestTime = LocalDateTime.now();
        String timeString = requestTime.format(TIME_FORMATTER);
        long startTime = System.currentTimeMillis();

        // 执行请求
        String content = executeRequest();
        // 计算耗时
        long latency = System.currentTimeMillis() - startTime;

        saveLatencyToRedis(requestTime, timeString, latency);

        log.info("监控OpenAI请求耗时: {}ms, 时间点: {}, 返回内容: {}", latency, timeString, content);

    }

    /**
     * 执行OpenAI请求  意图识别
     *
     * @return 请求结果
     */
    private String executeRequest() {
        RequestParamBO requestParamBO = JSONUtil.toBean(REQUEST_BODY, RequestParamBO.class);
        return new JsonSchemaRequestBuilder()
            .withChatModel(openAiChatModel)
            .withModel(ModelEnum.GPT_4O_2024_1120.getModelName())
            .withOutputType(requestParamBO.getOutputType())
            .withUserMessage(IdUtil.getSnowflakeNextIdStr() + requestParamBO.getUserPrompt())
            .withSystemMessage(requestParamBO.getSystemPrompt())
            .withTemperature(requestParamBO.getTemperature())
            .withJsonSchema(requestParamBO.getJsonSchema())
            .execute()
            .getContent();
    }

    /**
     * 保存耗时数据到Redis
     *
     * @param requestTime 请求时间
     * @param timeString  格式化后的时间字符串
     * @param latency     请求耗时（毫秒）
     */
    private void saveLatencyToRedis(LocalDateTime requestTime, String timeString, long latency) {
        String date = requestTime.format(DATE_FORMATTER);

        // 使用日期作为key，时间作为field，耗时作为value
        String key = RedisKeyConstants.openaiMonitoringLatencyKey(date);
        RMap<String, Long> map = redissonClient.getMap(key);
        map.put(timeString, latency);

        // 设置过期时间为7天
        map.expire(Duration.ofDays(RETENTION_DAYS));

        // 清理过期数据（超过7天的数据）
        cleanupExpiredData();
    }

    /**
     * 清理过期数据（超过7天的数据）
     */
    private void cleanupExpiredData() {
        LocalDate today = LocalDate.now();
        LocalDate expiryDate = today.minusDays(RETENTION_DAYS);

        // 删除过期的数据
        String expiredKey = RedisKeyConstants.openaiMonitoringLatencyKey(expiryDate.format(DATE_FORMATTER));
        redissonClient.getKeys().delete(expiredKey);
    }

    /**
     * 获取指定日期的OpenAI请求耗时数据
     *
     * @param date 日期，格式为yyyyMMdd，如果为null则获取当天数据
     * @return 耗时数据，key为时间（HH:mm:ss），value为耗时（毫秒）
     */
    public Map<String, Long> getLatencyData(String date) {
        if (date == null) {
            date = LocalDate.now().format(DATE_FORMATTER);
        }

        String key = RedisKeyConstants.openaiMonitoringLatencyKey(date);
        RMap<String, Long> map = redissonClient.getMap(key);

        if (map.isExists()) {
            return new TreeMap<>(map.readAllMap());
        }

        return new TreeMap<>();
    }

    /**
     * 获取最近7天的OpenAI请求平均耗时数据，按时间排序
     *
     * @return 平均耗时数据，key为日期（yyyy-MM-dd），value为按时间排序的耗时数据
     */
    public Map<String, Map<String, Long>> getAverageLatencyByDay() {
        Map<String, Map<String, Long>> result = new TreeMap<>();
        LocalDate today = LocalDate.now();

        for (int i = 0; i < RETENTION_DAYS; i++) {
            LocalDate date = today.minusDays(i);
            String dateStr = date.format(DATE_FORMATTER);
            String key = RedisKeyConstants.openaiMonitoringLatencyKey(dateStr);

            RMap<String, Long> map = redissonClient.getMap(key);
            // 使用TreeMap确保每天内的数据也按时间排序
            TreeMap<String, Long> sortedDayData = new TreeMap<>(map.readAllMap());
            
            if (!sortedDayData.isEmpty()) {
                result.put(dateStr, sortedDayData);
            }
        }

        return result;
    }
}
