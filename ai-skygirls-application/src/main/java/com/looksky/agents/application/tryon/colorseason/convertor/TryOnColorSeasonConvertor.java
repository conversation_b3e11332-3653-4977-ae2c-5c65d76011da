package com.looksky.agents.application.tryon.colorseason.convertor;

import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonParamV1;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonRequestV1;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * tryOn colorSeason 相关的转换器
 *
 * <AUTHOR>
 * @since 1.1.8
 **/
@Mapper(
    componentModel = MappingConstants.ComponentModel.SPRING,
    collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface TryOnColorSeasonConvertor {

    /**
     * 将 ColorSeason 请求参数转为 请求
     * @param param 请求参数
     * @return 请求
     */
    @Mapping(target = "modelImageUrl", source = "userImage")
    @Mapping(target = "garmentImageUrl", source = "clothImage")
    TryOnColorSeasonRequestV1 toTryOnColorSeasonRequest(TryOnColorSeasonParamV1 param);
}
