package com.looksky.agents.application.tryon.colorseason.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.tryon.colorseason.ITryOnColorSeasonService;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonParamV1;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

/**
 * colorSeason 试色 service, 使用 Replicate API 实现换白T
 *
 * <AUTHOR>
 * @since 1.1.12
 **/
@Slf4j
@Service
@Order(80) // 优先级最低，数字越小优先级越高
@RequiredArgsConstructor
public class TryOnColorSeasonServiceV5 implements ITryOnColorSeasonService {

    private static final String REPLICATE_API_URL = "https://api.replicate.com/v1/predictions";
    private static final String REPLICATE_MODEL_VERSION = "12b5a5a61e3419f792eb56cfc16eed046252740ebf5d470228f9b4cf2c861610";
    private static final String KEY = "****************************************";  // looksky1
    private static final String PROMPT = "Change the top into a plain white T-shirt";
    private static final HttpClient httpClient = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(30))
            .build();


    @Override
    public Duration getTimeout() {
        return Duration.ofSeconds(35);
    }

    @CollectEvent
    @Override
    public String tryOnWhiteT(TryOnColorSeasonParamV1 param) {
        try {
            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.set("version", REPLICATE_MODEL_VERSION);
            
            JSONObject input = new JSONObject();
            input.set("image", param.getUserImage());
            input.set("prompt", PROMPT);
            input.set("seed", 42);
            input.set("size_level", 768);
            
            requestBody.set("input", input);
            
            log.info("换白 T 请求参数: {}", requestBody);

            // 构建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(REPLICATE_API_URL))
                    .header("Authorization", "Bearer " + KEY)
                    .header("Content-Type", "application/json")
                    .timeout(Duration.ofMinutes(1))
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody.toString()))
                    .build();

            // 发送请求
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            
            // 处理响应
            if (response.statusCode() == 200 || response.statusCode() == 201) {
                JSONObject jsonResponse = JSONUtil.parseObj(response.body());
                log.info("换白 T 初始响应: {}", jsonResponse);
                
                // 获取预测ID和状态
                String predictionId = jsonResponse.getStr("id");
                String status = jsonResponse.getStr("status");
                
                // 如果状态是已完成，直接返回结果
                if ("succeeded".equals(status) && jsonResponse.containsKey("output")) {
                    log.info("换白 T 处理已完成，返回结果");
                    return jsonResponse.getStr("output");
                }
                
                // 如果状态是处理中，进行轮询等待
                if (predictionId != null) {
                    String resultUrl = pollForResult(predictionId);
                    if (resultUrl != null) {
                        return resultUrl;
                    }
                }
                
                log.error("换白 T 处理失败");
            } else {
                log.error("换白 T API调用失败，状态码: {}，响应: {}", response.statusCode(), response.body());
            }
        } catch (Exception e) {
            log.error("换白 T 失败", e);
        }

        return null;
    }
    
    /**
     * 轮询获取处理结果
     */
    private String pollForResult(String predictionId) {
        String getUrl = REPLICATE_API_URL + "/" + predictionId;
        
        // 先等待1秒
        try {
            TimeUnit.SECONDS.sleep(15);
        } catch (InterruptedException e) {
            log.error("等待过程中被中断", e);
            Thread.currentThread().interrupt();
            return null;
        }
        
        long startTime = System.currentTimeMillis();
        long timeout = TimeUnit.MINUTES.toMillis(1);
        long remainingTime = timeout;
        
        while (remainingTime > 0) {
            try {
                // 每次查询前等待1秒
                TimeUnit.SECONDS.sleep(1);
                
                // 构建查询请求
                HttpRequest pollRequest = HttpRequest.newBuilder()
                        .uri(URI.create(getUrl))
                        .header("Authorization", "Bearer " + KEY)
                        .timeout(Duration.ofSeconds(30))
                        .GET()
                        .build();
                
                // 发送请求
                HttpResponse<String> pollResponse = httpClient.send(pollRequest, HttpResponse.BodyHandlers.ofString());
                
                if (pollResponse.statusCode() == 200) {
                    JSONObject pollResult = JSONUtil.parseObj(pollResponse.body());
                    log.info("换白 T 轮询结果: {}", pollResult);
                    
                    String status = pollResult.getStr("status");
                    
                    // 如果处理完成，返回结果
                    if ("succeeded".equals(status) && pollResult.containsKey("output")) {
                        return pollResult.getStr("output");
                    } else if ("failed".equals(status)) {
                        log.error("换白 T 失败, 服务商返回: {}", JSONUtil.toJsonStr(pollResult));
                        return null;
                    }
                } else {
                    log.error("轮询请求失败，状态码: {}, 响应: {}", pollResponse.statusCode(), pollResponse.body());
                    return null;
                }
                
                // 更新剩余时间
                remainingTime = timeout - (System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                log.error("轮询过程发生异常", e);
                return null;
            }
        }
        
        log.error("换白 T 任务超时, {}", predictionId);
        return null;
    }
} 