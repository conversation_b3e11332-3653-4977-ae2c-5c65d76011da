package com.looksky.agents.application.chat.reply.multiEvent;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import java.util.ArrayDeque;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import lombok.Data;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @ClassName CategorySelectorMessageSender
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 下午10:29
 * @Version 1.0
 **/
@Order(1)
@Component
public class OpeningMessageSender extends MultiEventMessageSender {
    @Override
    protected boolean supports(PromptModel strategy) {
        return PromptNameEnum.isOpening(strategy.getName());
    }


    @Override
    protected ArrayDeque<Object> generateReturnContents(PromptModel strategy) {
        String result = commonRequestService.commonExecuteStrategy(strategy, Collections.emptyMap());
        OpeningMessage openingMessage = JSONUtil.toBean(result, OpeningMessage.class);
        ArrayDeque<Object> deque = new ArrayDeque<>();
        deque.offer(openingMessage.getGreeting());
        deque.offer(openingMessage.getFollowUpMessages());
        return deque;
    }

    @Override
    protected void afterSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        // 不改变任何会话状态
    }


    @Data
    static class OpeningMessage {
        private String greeting;
        @JsonProperty("follow_up_messages")
        private List<String> followUpMessages;

    }
}
