package com.looksky.agents.application.provider;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.data.opensearch.SkcSearchService;
import com.looksky.agents.infrastructure.context.Context;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class BrandAllNameProvider implements DataProvider {
    private final SkcSearchService skcSearchService;


    @Override
    public void getData() {
        try {
            List<String> allBrand = skcSearchService.findAllBrand();
            Context.put(Context.Name.BRAND_ALL_NAME_DATA.getName(), JSONUtil.toJsonStr(allBrand));
        } catch (Exception e) {
            log.error("获取 品牌信息 失败", e);
            Context.put(Context.Name.BRAND_ALL_NAME_DATA.getName(), Collections.emptyMap());
        }

    }

    @Override
    public boolean supports(String key) {
        return key.equals("brandAllName");
    }

} 