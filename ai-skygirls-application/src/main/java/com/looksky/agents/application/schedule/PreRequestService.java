package com.looksky.agents.application.schedule;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.common.annotation.EnvironmentScheduled;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.data.mysql.service.IPromptService;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PreRequestService {

    private final IPromptService promptService;

    private final CommonRequestService commonRequestService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @EnvironmentScheduled(cron = "0 0 0/6 * * ?")  // 每天凌晨1点执行
    public void buildRequest() {
        log.info("开始预请求 json schema");
        List<PromptModel> allJsonSchema = getAllJsonSchema();
        log.info("获取到{}个jsonSchema", allJsonSchema.size());

        allJsonSchema.forEach(promptModel -> commonRequestService.commonExecuteStrategy(promptModel,
            Collections.emptyMap()));
    }


    private List<PromptModel> getAllJsonSchema() {
        return promptService.list(new LambdaQueryWrapper<PromptModel>()
            .ne(PromptModel::getOutputType, 1))
            .stream()
            .map(promptModel -> {
                String jsonSchema = promptModel.getJsonSchema();
                // 如果jsonSchema不为空, 并且是一个标准的 json
                if (CharSequenceUtil.isNotBlank(jsonSchema) && isJson(jsonSchema)) {
                    promptModel.setSystemPrompt("test");
                    promptModel.setUserPrompt("test");
                    return promptModel;
                }
                return null;
            }).filter(Objects::nonNull).toList();
    }


    private boolean isJson(String jsonSchema) {
        try {
            objectMapper.readTree(jsonSchema);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

}
