package com.looksky.agents.application.chat.scene;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.application.prompt.PromptBusinessService;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.common.enums.EventNameEnum;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @ClassName GirlsSceneSelector
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/3 上午10:16
 * @Version 1.0
 **/
@Slf4j
@Order(1)
@Component
@RequiredArgsConstructor
public class TryOnSceneSelector extends BaseSceneSelector {

    private final PromptBusinessService promptBusinessService;
    private final CommonRequestService commonRequestService;


    @Override
    public boolean support(String eventName, EnterPointEnum enterPoint, String page) {
        return EventNameEnum.USER_SPEAK.getValue().equals(eventName) && EnterPointEnum.TRY_ON_DETAIL == enterPoint;
    }

    @Override
    protected SceneResult getSceneAndExternInfo(RequestInputDTO requestInput) {

        String intent = "girls_tryon_question_type";

        String result = commonRequestService.commonExecuteStrategy(intent);

        QuestionType bean = JSONUtil.toBean(result, QuestionType.class);


        String prompt = switch (bean.getQuestionType()) {
            case "outfit_ideas" -> "outfit_ideas";
            case "shipping_policy" -> "shipping_policy";
            case "return_policy" -> "return_policy";
            case "discount" -> "discount";
            default -> "small_talk_in_try_on";
        };

        PromptModel promptModel = promptBusinessService.getPromptModelByNameWithMetaData(prompt);

        // 请求推荐
        return new SceneResult(promptModel, null);
    }

    @Data
    static class QuestionType {
        private String questionType;
    }

}
