package com.looksky.agents.application.tryon.swapcloth;

import ai.fal.client.ClientConfig;
import ai.fal.client.CredentialsResolver;
import ai.fal.client.FalClient;
import ai.fal.client.SubscribeOptions;
import ai.fal.client.queue.QueueStatus;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.application.prompt.PromptBusinessService;
import com.looksky.agents.data.client.business.TryOnClient;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.infrastructure.exception.BusinessException;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothParam;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.SwapClothWorkflowRequest;
import com.yomahub.tlog.context.TLogContext;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SwapClothService {

    private final CommonRequestService commonRequestService;
    private final PromptBusinessService promptBusinessService;
    private final RedissonClient redissonClient;
    private final TryOnClient tryOnClient;


    private static final String API_KEY = "db3d938c-950d-4585-b73f-ffbd847cbbf4:0860aca8baedaa55e2cb44fa1fc9bb9f";
    private static final String ANALYZE_MODEL_IMAGE = "swap_cloth_analyze_model_image";
    private static final String ANALYZE_USER_FULL_BODY_IMAGE = "swap_cloth_analyze_user_full_body_image";
    private static final String GENERATE_IMAGE_PROMPT = "swap_cloth_generate_image_prompt";
    private static final String ANALYZE_USER_FULL_BODY_IMAGE_BACKUP = "swap_cloth_analyze_user_full_body_image_backup";
    private static final String ERROR_STR = "error";


    public String asyncSwapCloth(SwapClothParam swapClothParam) {
        VirtualCompletableFuture.runAsync(() -> swapCloth(swapClothParam));
        return TLogContext.getTraceId();
    }

    public String getSwapClothResult(String traceId) {
        RBucket<String> bucket = redissonClient.getBucket(RedisKeyConstants.swapClothKey(traceId));

        if (bucket.isExists()) {
            String result = bucket.get();
            if (StrUtil.isBlankIfStr(result) || ERROR_STR.equals(result)) {
                throw new BusinessException("换装失败");
            }
            return result;
        }

        return null;
    }

    private void swapCloth(SwapClothParam swapClothParam) {

        String url = ERROR_STR;

        try {
            VirtualCompletableFuture<String> modelPrompt = getModelPrompt(swapClothParam.getModelImage());
            VirtualCompletableFuture<String> userPrompt = getUserPrompt(swapClothParam.getFullBodyImage());

            String imagePrompt = getImagePrompt(userPrompt.join(), modelPrompt.join());

            String workflowResultUrl = executeWorkflow(imagePrompt, swapClothParam.getFaceImage(), swapClothParam.getModelImage());

            url = fashnSwapFace(workflowResultUrl, swapClothParam.getModelImage());
        } catch (Exception e) {
            log.error("换装失败", e);
        }


        RBucket<String> bucket = redissonClient.getBucket(RedisKeyConstants.swapClothKey(TLogContext.getTraceId()));
        bucket.set(url, Duration.ofDays(1));
    }


    @Data
    public static class FashnResult {
        private List<FashnResultImage> images;

        @Data
        public static class FashnResultImage {
            private String url;
            @JsonProperty("content_type")
            private String contentType;

            @JsonProperty("file_name")
            private String fileName;

            @JsonProperty("file_size")
            private long fileSize;

            private int width;
            private int height;
        }
    }

    public String fashnSwapFace(String tempImage, String modelImage) {


        var fal = FalClient.withConfig(ClientConfig.withCredentials(CredentialsResolver.fromApiKey(API_KEY)));

        var input = Map.of(
            "model_image", tempImage,
            "garment_image", modelImage,
            "category", "one-pieces"
        );
        var result = fal.subscribe("fashn/tryon",
            SubscribeOptions.<FashnResult>builder()
                .input(input)
                .logs(true)
                .resultType(FashnResult.class)
                .onQueueUpdate(update -> {
                    if (update instanceof QueueStatus.InProgress) {
                        log.info("换脸返回过程数据: {}", ((QueueStatus.InProgress) update).getLogs());
                    }
                })
                .build()
        );

        log.info("换脸结果: {}", JSONUtil.toJsonStr(result));

        return result.getData().getImages().getFirst().getUrl();

    }

    private List<String> getWorkflowResult(String promptId) {
        String url = "https://olzbdky68p0lg0-8509.proxy.runpod.net/history/" + promptId;
        String body;
        try (HttpResponse response = HttpRequest.get(url).execute()) {
            if (!response.isOk()) {
                throw new RuntimeException("获取工作流结果失败");
            }
            body = response.body();
        }
        if (StrUtil.isBlankIfStr(body == null)) {
            return new ArrayList<>();
        }
        log.info("工作流结果：{}", body);
        JSONObject jsonResult = JSONUtil.parseObj(body);

        List<String> finalImages = new ArrayList<>();

        // 获取指定promptId的输出结果
        JSONObject promptResult = jsonResult.getJSONObject(promptId);
        if (promptResult != null && promptResult.containsKey("outputs")) {
            JSONObject outputs = promptResult.getJSONObject("outputs");
            // 检查节点158的输出
            if (outputs.containsKey("158")) {
                JSONObject node158 = outputs.getJSONObject("158");
                if (node158.containsKey("text")) {
                    List<String> resUrls = node158.getJSONArray("text").toList(String.class);
                    for (String resUrl : resUrls) {
                        finalImages.add(resUrl);
                        log.info("生成文件: {}", resUrl);
                    }
                }
            }
        }

        return finalImages;
    }

    private String executeWorkflow(String imagePrompt, String faceImage, String modelImage) {


        SwapClothWorkflowRequest swapClothWorkflowRequest = new SwapClothWorkflowRequest();
        swapClothWorkflowRequest.setNewFaceImage(faceImage);
        swapClothWorkflowRequest.setNewClothImage(modelImage);
        swapClothWorkflowRequest.setNewPromptText(imagePrompt);

        JsonNode jsonNode = tryOnClient.swapClothWorkflow(swapClothWorkflowRequest);
        return jsonNode.get("img").asText();

    }

    /**
     * 记录工作流执行记录
     *
     * @param clientId 用来监控工作流是否执行完
     * @param promptId 用来获取结果
     */
    record WorkflowRecord(String clientId, String promptId) {
    }


    private String getImagePrompt(String userPrompt, String modelPrompt) {
        String result = commonRequestService.commonExecuteStrategy(GENERATE_IMAGE_PROMPT, Map.of("userPrompt", userPrompt, "modelPrompt", modelPrompt));
        return result.replace("\"", "\\\"");
    }


    private VirtualCompletableFuture<String> getModelPrompt(String modelImage) {
        return VirtualCompletableFuture.supplyAsync(() -> executeAnalyze(ANALYZE_MODEL_IMAGE, List.of(modelImage)));
    }


    private VirtualCompletableFuture<String> getUserPrompt(String userImage) {
        return VirtualCompletableFuture.supplyAsync(() -> executeAnalyze(ANALYZE_USER_FULL_BODY_IMAGE, List.of(userImage))).thenComposeAsync(userPrompt -> {
            if (checkUserPrompt(userPrompt)) {
                return VirtualCompletableFuture.completedFuture(userPrompt);
            }
            return VirtualCompletableFuture.supplyAsync(() -> getUserPromptStandby(userImage));
        });

    }

    private boolean checkUserPrompt(String prompt) {
        if (StrUtil.isBlankIfStr(prompt)) {
            return false;
        }

        if (prompt.contains("error") || prompt.contains("sorry")) {
            return false;
        }

        return true;
    }


    private String getUserPromptStandby(String bodyImage) {
        return executeAnalyze(ANALYZE_USER_FULL_BODY_IMAGE_BACKUP, List.of(bodyImage));
    }


    private String executeAnalyze(String promptName, List<String> modelImage) {
        PromptModel promptModel = promptBusinessService.getPromptModelByNameWithMetaData(promptName);
        RequestParamBO requestParam = promptBusinessService.convert(promptModel, MapUtil.empty());
        requestParam.setImageUrl(modelImage);
        return commonRequestService.commonExecuteStrategy(requestParam).replace("\"", "\\\"");
    }

}
