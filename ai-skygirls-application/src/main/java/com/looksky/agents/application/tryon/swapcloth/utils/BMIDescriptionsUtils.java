package com.looksky.agents.application.tryon.swapcloth.utils;

import com.looksky.agents.sdk.utils.user.BMIUtils;
import com.skygirls.biz.user.model.enums.MeasureTypeEnum;

public class BMIDescriptionsUtils {
    public static String bmiDescription(MeasureTypeEnum measureType, String height, String weight) {
        BMIUtils.BmiResult bmiResult = BMIUtils.calculateBmiAndHeightType(measureType, height, weight);
        double bmi = bmiResult.getBmi();
        return getBmiDescription(bmi);
    }


    private static String getBmiDescription(double bmi) {
        String prompt;
        if (bmi >= 40) {
            prompt = "Obesity Class 3 (Morbid obesity) body shape.";
        } else if (bmi >= 35) {
            prompt = "Obesity Class 2 (Severe obesity) body shape.";
        } else if (bmi >= 30) {
            prompt = "Obesity Class 1 (Moderate obesity) body shape.";
        } else if (bmi >= 25) {
            prompt = "Overweight body shape.";
        } else if (bmi >= 18.5) {
            prompt = "Normal weight body shape.";
        } else {
            prompt = "Underweight body shape.";
        }

        return prompt;
    }

    private static String getBmiDescription2(double bmi) {
        String prompt;
        if (bmi >= 38) {
            prompt = "Super Plus Size body shape.";
        } else if (bmi >= 32) {
            prompt = "Plus Size body shape.";
        } else if (bmi >= 28) {
            prompt = "Balanced Fuller body shape.";
        } else if (bmi >= 25) {
            prompt = "Full-figured body shape.";
        } else if (bmi >= 23) {
            prompt = "Average body shape.";
        } else if (bmi >= 18) {
            prompt = "Athletic body shape.";
        } else {
            prompt = "Slim body shape.";
        }
        return prompt;
    }
}
