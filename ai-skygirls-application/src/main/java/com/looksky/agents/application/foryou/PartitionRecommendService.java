package com.looksky.agents.application.foryou;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.common.utils.UserLocationUtils;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.application.foryou.convertor.ForYouConvertor;
import com.looksky.agents.data.client.service.GirlsDataService;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.data.redis.daily100.Daily100DataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.recommend.common.dto.ColorSeasonCacheDataDTO;
import com.looksky.agents.sdk.recommend.common.dto.Face2HairCacheDataDTO;
import com.looksky.agents.sdk.recommend.common.dto.UserPageKibbeCacheDataDTO;
import com.looksky.agents.sdk.recommend.foryou.dto.ForYouParam;
import com.looksky.agents.sdk.recommend.foryou.dto.ForYouPromptResultMultipleResult;
import com.looksky.agents.sdk.recommend.foryou.dto.PartitionRecomModelDTO;
import com.looksky.agents.sdk.recommend.foryou.enums.RecommendScenEnum;
import com.looksky.agents.sdk.utils.date.DateUtils;
import com.skygirls.biz.report.IosUserInfoDto;
import com.skygirls.biz.user.dto.UserLocationInfoDTO;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RList;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PartitionRecommendService {


    private final CommonRequestService commonRequestService;
    private final HttpServletRequest httpServletRequest;
    private final GirlsDataService girlsDataService;
    private final Daily100DataService daily100DataService;
    private final RedissonClient redissonClient;
    private final ForYouConvertor convertor;
    private final Random random = new Random();


    @SneakyThrows
    public ForYouParam forYouVectorQuery(ForYouParam forYouParam) {
        String lockKey = RedisKeyConstants.forYouLock(forYouParam.getUserId());
        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        try {
            locked = lock.tryLock(50, TimeUnit.SECONDS);
            if (!locked) {
                log.error("获取forYou锁失败, 请求参数: {}", JSONUtil.toJsonStr(forYouParam));
                return forYouParam;
            }

            UserLocationInfoDTO location = UserLocationUtils.getLocation(httpServletRequest);
            Context.put(Context.Name.USER_LOCATION.getName(), location);
            IosUserInfoDto userInfo = girlsDataService.getUserInfo(forYouParam.getUserId());
            checkParam(forYouParam);
            completeRequestDTO(forYouParam, userInfo);
            return forYouParam;
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    // 检查是否有传递客户端时间
    private void checkParam(ForYouParam forYouParam) {
        if (StrUtil.isBlankIfStr(forYouParam.getClientDayTime())) {
            forYouParam.setClientDayTime(DateUtils.getNowDate(null));
        }
    }


    // 转为 GirlsColorSeasonResponseDTO
    private void completeRequestDTO(ForYouParam forYouParam, IosUserInfoDto userInfo) {
        Map<String, Object> data = buildDaily100Data(userInfo, forYouParam.getUserId());
        // 分别执行每一个分区
        List<PartitionRecomModelDTO> taskResults = executePrompts(forYouParam, data);

        taskResultsConvertToPartitionResponseDTO(forYouParam, taskResults);
    }


    // 执行 prompt
    private List<PartitionRecomModelDTO> executePrompts(ForYouParam forYouParam, Map<String, Object> data) {
        List<String> partitionRecomScenes = forYouParam.getPartitionRecomScenes();
        List<VirtualCompletableFuture<PartitionRecomModelDTO>> taskList = new ArrayList<>();

        partitionRecomScenes.forEach(scene -> {
            VirtualCompletableFuture<PartitionRecomModelDTO> task = VirtualCompletableFuture.supplyAsync(() -> executePromptAndCache(forYouParam.getClientDayTime(), forYouParam.getUserId(), scene, data));
            taskList.add(task);
        });

        VirtualCompletableFuture.allOf(taskList.toArray(new VirtualCompletableFuture[0])).join();

        return taskList.stream().map(VirtualCompletableFuture::join).toList();
    }


    private PartitionRecomModelDTO executePromptAndCache(String clientDayTime, String userId, String scene, Map<String, Object> data) {

        // 先去缓存里面获取
        String currentKey = RedisKeyConstants.forYouKey(userId, scene);
        RList<PartitionRecomModelDTO> bucket = redissonClient.getList(currentKey, new TypedJsonJacksonCodec(PartitionRecomModelDTO.class));
        // 如果缓存中有, 直接返回
        if (bucket.isExists()) {
            PartitionRecomModelDTO last = bucket.getLast();
            if (last != null && clientDayTime.equals(last.getClientDayTime())) {
                log.info("{} 生成 forYou {} 板块当天缓存命中", userId, scene);
                return last;
            }
        }

        // 如果缓存中没有, 则生成
        VirtualCompletableFuture<String> modelResult = VirtualCompletableFuture.supplyAsync(() -> {
            log.info("{} 生成 forYou {} 板块开始", userId, scene);
            return commonRequestService.commonExecuteStrategy(scene, data);
        });

        try {
            // 设置超时时间为 20s
            String result = modelResult.get(20, TimeUnit.SECONDS);
            if (CharSequenceUtil.isNotBlank(result)) {
                log.info("{} 生成 forYou {} 板块完成", userId, scene);
                // 转为 PartitionRecomModelDTO
                PartitionRecomModelDTO partitionRecomModelDTO = convertToResult(scene, result, clientDayTime, userId);
                // 放入缓存
                cachePartitionRecomModelDTO(bucket, partitionRecomModelDTO);
                return partitionRecomModelDTO;
            }
        } catch (TimeoutException e) {
            // 如果超时, 那么返回昨天的数据
            log.info("{} 生成 forYou {} 板块超时", userId, scene, e);

            // 先检查是否有昨天的数据
            if (bucket.isExists()) {
                PartitionRecomModelDTO yesterdayData = bucket.getLast();
                if (yesterdayData != null) {
                    // 在后台异步处理当前请求的结果
                    CompletableFuture.runAsync(() -> {
                        try {
                            String result = modelResult.get(60, TimeUnit.SECONDS); // 给更长的超时时间
                            log.info("{} 生成 forYou {} 后台异步缓存开始", userId, scene);
                            if (CharSequenceUtil.isNotBlank(result)) {
                                PartitionRecomModelDTO partitionRecomModelDTO = convertToResult(scene, result, clientDayTime, userId);
                                cachePartitionRecomModelDTO(bucket, partitionRecomModelDTO);
                                log.info("{} 生成 forYou {} 后台异步缓存完成", userId, scene);
                            }
                        } catch (Exception ex) {
                            log.error("{} 生成 forYou {} 后台异步缓存失败", userId, scene, ex);
                        }
                    });
                    log.info("{} 生成 forYou {} 板块超时, 返回昨天的数据", userId, scene);
                    // 立即返回昨天的数据
                    return yesterdayData;
                }
            }


            // 如果没有昨天的数据，只能等待当前请求完成
            log.info("{} 生成 forYou {} 板块超时, 并且前一天也没有缓存, 等待模型返回结果", userId, scene);
            try {
                String result = modelResult.get(20, TimeUnit.SECONDS); // 给一个较长但有限的等待时间
                if (CharSequenceUtil.isNotBlank(result)) {
                    log.info("{} 生成 forYou {} 板块最终获取成功", userId, scene);
                    PartitionRecomModelDTO partitionRecomModelDTO = convertToResult(scene, result, clientDayTime, userId);
                    cachePartitionRecomModelDTO(bucket, partitionRecomModelDTO);
                    return partitionRecomModelDTO;
                }
            } catch (Exception ex) {
                log.error("{} 生成 forYou {} 板块最终获取失败", userId, scene, ex);
            }
        } catch (Exception e) {
            log.error("{} 生成 forYou {} 板块发生未知异常", userId, scene, e);
        }
        log.error("{} 生成 forYou {} 板块失败", userId, scene);
        return null;
    }

    private void cachePartitionRecomModelDTO(RList<PartitionRecomModelDTO> list, PartitionRecomModelDTO partitionRecomModelDTO) {
        if (list.size() >= 14) {
            list.removeFirst();
        }

        // 对 partitionRecomModelDTO 进行校验
        if (CollUtil.isEmpty(partitionRecomModelDTO.getRecallVectors())) {
            log.warn("{} 板块数据为空, 不进行缓存", partitionRecomModelDTO.getPartitionRecomScenes().getName());
            return;
        }

        log.info("缓存 forYou {} 板块数据", partitionRecomModelDTO.getPartitionRecomScenes().getName());
        list.addLast(partitionRecomModelDTO);
    }


    private void taskResultsConvertToPartitionResponseDTO(ForYouParam forYouParam, List<PartitionRecomModelDTO> taskResults) {

        if (CollUtil.isEmpty(taskResults)) {
            return;
        }

        List<PartitionRecomModelDTO> partitionList = new ArrayList<>();

        taskResults.forEach(taskResult -> {
            if (taskResult == null) {
                return;
            }
            partitionList.add(taskResult);
        });

        forYouParam.setPartitionRecomModels(partitionList);

    }


    private PartitionRecomModelDTO convertToResult(String scene, String result, String clientDayTime, String userId) {

        if (StrUtil.isBlankIfStr(result)) {
            return null;
        }

        RecommendScenEnum sceneEnum = RecommendScenEnum.getByName(scene);

        PartitionRecomModelDTO partitionRecomModelDTO;
        switch (sceneEnum) {
            case GIRLS_PARTITION_MATCH:

                partitionRecomModelDTO = convertResultToPartitionModel(result, sceneEnum);

                // 判断是第一次访问还是每日访问
                RBucket<Object> bucket = redissonClient.getBucket(RedisKeyConstants.forYouKey(userId, scene));
                // 如果存在, 则非第一次访问
                String themeKey = RedisKeyConstants.forYouMatchThemesFirstKey();
                if (bucket.isExists()) {
                    themeKey = RedisKeyConstants.forYouMatchThemesDailyKey();
                }

                RList<String> themeList = redissonClient.getList(themeKey, new TypedJsonJacksonCodec(String.class));

                // 随机从 themeList 中取一个主题
                String theme = themeList.get((this.random.nextInt(themeList.size())));
                partitionRecomModelDTO.setTitle(theme);
                break;
            case GIRLS_PARTITION_TREND:
                partitionRecomModelDTO = convertResultToPartitionModel(result, sceneEnum);
                partitionRecomModelDTO.setHashtag(null);
                break;
            case GIRLS_PARTITION_BASIC, GIRLS_PARTITION_TRY, GIRLS_PARTITION_BRAND, GIRLS_PARTITION_FESTIVAL, GIRLS_PARTITION_DISCOUNT:
            default:
                partitionRecomModelDTO = convertResultToPartitionModel(result, sceneEnum);
                break;
        }

        partitionRecomModelDTO.setClientDayTime(clientDayTime);

        return partitionRecomModelDTO;
    }

    private PartitionRecomModelDTO convertResultToPartitionModel(String result, RecommendScenEnum sceneEnum) {
        PartitionRecomModelDTO partitionRecomModelDTO;
        ForYouPromptResultMultipleResult multipleResult = JSONUtil.toBean(result, ForYouPromptResultMultipleResult.class);
        // 使用MapStruct映射器将ForYouPromptResultMultipleResult转换为PartitionRecomModelDTO
        partitionRecomModelDTO = convertor.toPartitionRecomModelDTO(multipleResult, sceneEnum);
        return partitionRecomModelDTO;
    }


    private Map<String, Object> buildDaily100Data(IosUserInfoDto userInfoDto,
                                                  String userId) {
        RequestInputDTO requestVo = buildRequestVo(userId);
        Context.put(Context.Name.REQUEST_INPUT.getName(), requestVo);
        Map<String, Object> daily100Data = new HashMap<>(Map.of(
            Context.Name.USER_INFO.getName(), userInfoDto,
            Context.Name.REQUEST_INPUT.getName(), requestVo
        ));

        enrichDaily100Data(daily100Data, userInfoDto);
        return daily100Data;
    }

    /**
     * 构建请求对象，包含用户位置信息
     *
     * @return 包含城市信息的请求对象
     */
    private RequestInputDTO buildRequestVo(String userId) {
        RequestInputDTO requestVo = RequestInputDTO.builder().build();

        UserLocationInfoDTO userLocation = Context.get(Context.Name.USER_LOCATION.getName());
        Optional.ofNullable(userId).ifPresent(requestVo::setUserId);
        requestVo.setSeason(DateUtils.getCurrentSeason());
        Optional.ofNullable(userLocation).ifPresent(location -> requestVo.setCity(location.getViewerCity()));


        return requestVo;
    }

    /**
     * 使用用户的Kibbe类型、颜色季节和脸型数据丰富Daily100数据
     *
     * @param daily100Data 待丰富的数据Map
     * @param userInfoDto  用户信息对象
     */
    private void enrichDaily100Data(Map<String, Object> daily100Data, IosUserInfoDto userInfoDto) {
        if (userInfoDto != null && userInfoDto.getAppUser() != null) {
            Optional.ofNullable(userInfoDto.getAppUser().getKibbeType()).ifPresent(kbType -> {
                UserPageKibbeCacheDataDTO kbTypeDto = daily100DataService.getKbType(kbType.getCode());
                daily100Data.put("kbType", kbTypeDto);
            });

            Optional.ofNullable(userInfoDto.getAppUser().getColorSeason()).ifPresent(colorSeason -> {
                ColorSeasonCacheDataDTO colorSeasonCacheDataDto = daily100DataService.getColorSeason(colorSeason.getName());
                daily100Data.put("colorSeason", colorSeasonCacheDataDto);
            });

            Optional.ofNullable(userInfoDto.getAppUser().getHairColor()).ifPresent(face2Hair -> {
                Face2HairCacheDataDTO face2HairCacheDataDto = daily100DataService.getFace2Hair(face2Hair.getName());
                daily100Data.put("face2Hair", face2HairCacheDataDto);
            });


            themeHistory(daily100Data, userInfoDto.getAppUser().getUserId(), RecommendScenEnum.GIRLS_PARTITION_TRY);
            themeHistory(daily100Data, userInfoDto.getAppUser().getUserId(), RecommendScenEnum.GIRLS_PARTITION_BASIC);
            themeHistory(daily100Data, userInfoDto.getAppUser().getUserId(), RecommendScenEnum.GIRLS_PARTITION_TREND);
            titleHistory(daily100Data, userInfoDto.getAppUser().getUserId(), RecommendScenEnum.GIRLS_PARTITION_FESTIVAL);
        }


        daily100Data.put("month", DateUtils.getMonth());
        daily100Data.put("nextMonth", DateUtils.getNextMonth());
        daily100Data.put("nextNextMonth", DateUtils.getNextNextMonth());
        likeBrands(daily100Data, userInfoDto);

        // todo 人工运营数据

    }

    private void likeBrands(Map<String, Object> daily100Data, IosUserInfoDto userInfoDto) {
        if (userInfoDto == null) {
            return;
        }
        List<String> likeBrands = new ArrayList<>();
        Optional.ofNullable(userInfoDto.getBasicUser()).flatMap(u -> Optional.ofNullable(u.getLikeBrands())).ifPresent(likeBrands::addAll);
        daily100Data.put("likeBrands", JSONUtil.toJsonStr(likeBrands));
    }


    private void themeHistory(Map<String, Object> daily100Data, String userId, RecommendScenEnum panel) {
        List<String> panelThemeHistory = getPanelThemeHistory(userId, panel);
        daily100Data.put(panel.getName() + "ThemeHistory", JSONUtil.toJsonStr(panelThemeHistory));
    }


    /**
     * 获取用户某个板块的主题
     *
     * @param userId
     * @param scene
     * @return
     */
    public List<String> getPanelThemeHistory(String userId, RecommendScenEnum scene) {

        String key = RedisKeyConstants.forYouKey(userId, scene.getName());
        ArrayList<String> results = new ArrayList<>();
        RList<PartitionRecomModelDTO> list = redissonClient.getList(key, new TypedJsonJacksonCodec(PartitionRecomModelDTO.class));
        if (!list.isExists() || list.isEmpty()) {
            return results;
        }

        list.forEach(model -> {
            if (model != null && model.getHashtag() != null) {
                results.add(model.getHashtag());
            }
        });

        return results;

    }


    private void titleHistory(Map<String, Object> daily100Data, String userId, RecommendScenEnum panel) {
        List<String> panelThemeHistory = getPanelTitleHistory(userId, panel);
        daily100Data.put(panel.getName() + "TitleHistory", JSONUtil.toJsonStr(panelThemeHistory));
    }

    /**
     * 获取用户某个板块的标题
     *
     * @param userId
     * @param scene
     * @return
     */
    public List<String> getPanelTitleHistory(String userId, RecommendScenEnum scene) {

        String key = RedisKeyConstants.forYouKey(userId, scene.getName());
        ArrayList<String> results = new ArrayList<>();
        RList<PartitionRecomModelDTO> list = redissonClient.getList(key, new TypedJsonJacksonCodec(PartitionRecomModelDTO.class));
        if (!list.isExists() || list.isEmpty()) {
            return results;
        }

        list.forEach(model -> {
            if (model != null && model.getTitle() != null) {
                results.add(model.getTitle());
            }
        });

        return results;

    }


}
