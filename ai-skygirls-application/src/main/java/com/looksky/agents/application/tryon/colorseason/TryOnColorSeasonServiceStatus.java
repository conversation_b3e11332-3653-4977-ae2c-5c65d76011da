package com.looksky.agents.application.tryon.colorseason;

import java.io.Serial;
import java.io.Serializable;
import java.time.Duration;
import lombok.Data;

/**
 * 颜色季节服务状态
 *
 * <AUTHOR>
 */
@Data
public class TryOnColorSeasonServiceStatus implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 服务是否可用
     */
    private boolean available = true;

    /**
     * 失败次数计数
     */
    private int failureCount = 0;

    /**
     * 最后一次失败时间戳
     */
    private long lastFailureTime = 0;

    /**
     * 服务超时时间(毫秒)
     */
    private long timeoutMillis = 30000;

    /**
     * 优先级
     */
    private int priority = Integer.MAX_VALUE;

    /**
     * 更新超时设置
     *
     * @param timeout 超时时间
     */
    public void updateTimeout(Duration timeout) {
        this.timeoutMillis = timeout.toMillis();
    }

    /**
     * 更新优先级
     *
     * @param priority 优先级
     */
    public void updatePriority(int priority) {
        this.priority = priority;
    }

    /**
     * 记录服务失败
     */
    public void recordFailure() {
        this.failureCount++;
        this.lastFailureTime = System.currentTimeMillis();
    }

    /**
     * 重置失败计数
     */
    public void resetFailureCount() {
        this.failureCount = 0;
    }

    /**
     * 标记服务为不可用
     */
    public void markAsUnavailable() {
        this.available = false;
    }

    /**
     * 恢复服务可用状态
     */
    public void markAsAvailable() {
        this.available = true;
        this.failureCount = 0;
    }
} 