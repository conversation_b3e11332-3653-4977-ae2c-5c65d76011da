package com.looksky.agents.application.tryon.swapcloth.generation;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.tryon.swapcloth.convertor.TryOnConvertor;
import com.looksky.agents.common.annotation.EnvironmentScheduled;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.data.redis.config.RedisCodecFactory;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothStatus;
import com.looksky.agents.sdk.tryon.swapcloth.enums.SwapClothStatusEnum;
import com.skygirls.biz.user.tryon.dto.SearchTryOnStatusReq;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CheckTask {
    private final RedissonClient redissonClient;
    private final RedisCodecFactory factory;
    private final GirlsClient girlsClient;
    private final TryOnConvertor tryOnConvertor;
    private final GenerationSelect generationSelect;


    @EnvironmentScheduled(cron = "0 */1 * * * ?", environments = {"test", "prod"})
    private void check() {
        checkQueue(SwapClothStatusEnum.GENERATING);
        checkQueue(SwapClothStatusEnum.FACE_SWAPPING);
    }

    private void checkQueue(SwapClothStatusEnum statusEnum) {
        RBlockingQueue<SwapClothStatus> blockingQueue = redissonClient.getBlockingQueue("swap_cloth_queue:" + statusEnum, factory.createCodec(SwapClothStatus.class));

        List<SwapClothStatus> objects = blockingQueue.readAll();

        objects.forEach(s -> {
            LocalDateTime createTime = s.getCreateTime();
            LocalDateTime now = LocalDateTime.now();

            // 如果超过 5 分钟
            if (now.minusMinutes(10).isAfter(createTime)) {

                log.info("{} 超过 10 分钟, 直接转为失败, 任务详情: {}", statusEnum.getDescription(), JSONUtil.toJsonStr(s));

                boolean remove = blockingQueue.remove(s);

                if (!remove) {
                    log.info("从 {} 队列移除失败: {}", statusEnum.getDescription(), JSONUtil.toJsonStr(s));
                } else {
                    log.info("从 {} 队列移除成功: {}", statusEnum.getDescription(), JSONUtil.toJsonStr(s));
                }

                s.setErrorMessage("任务超过 10 分钟未响应, 强制转到失败队列");

                // 添加到 fail 队列
                log.info("添加到 swap_cloth_queue:FAILED 队列: {}", JSONUtil.toJsonStr(s));
                redissonClient.getBlockingQueue("swap_cloth_queue:" + SwapClothStatusEnum.FAILED, factory.createCodec(SwapClothStatus.class)).add(s);

                // 判断在那个 workflow
                boolean contains = generationSelect.contains(WorkflowEnum.RUN_POD, s.getTraceId());

                if (contains) {
                    generationSelect.complete(WorkflowEnum.RUN_POD, s.getTraceId());
                } else {
                    generationSelect.complete(WorkflowEnum.MODEL, s.getTraceId());
                }

                // 发送通知
                log.info("发送失败通知: {}", JSONUtil.toJsonStr(s));
                SearchTryOnStatusReq callbackRequest = tryOnConvertor.toCallbackRequest(s.getSwapClothParam(), SwapClothStatusEnum.FAILED, null);
                girlsClient.tryonCallback(callbackRequest);
            }
        });
    }


}
