package com.looksky.agents.application.chat.reply.multiEvent;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.common.enums.TagValueEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.preference.enums.PreferenceTypeEnum;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @ClassName CategorySelectorMessageSender
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 下午10:29
 * @Version 1.0
 **/
@Slf4j
@Order(1)
@Component
public class TagSelectorMessageSender extends MultiEventMessageSender {

    @Resource
    private TagSystemTableService tagService;


    @Override
    protected boolean supports(PromptModel strategy) {
        // 生成搜索过程
        return PromptNameEnum.INQUIRE_USER_TAG_PREFERENCE.getName().equals(strategy.getName());
    }


    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        String result = commonRequestService.commonExecuteStrategy(strategy, Collections.emptyMap());

        JSONObject jsonObject = JSONUtil.parseObj(result);

        sendContent(initMessage(EventTypeEnum.TEXT.getType()), EventTypeEnum.TEXT.getType(), jsonObject.getStr("text"));
        extraMessage(EventTypeEnum.TEXT.getType(), jsonObject.getStr("text"), hashMap);

        sendContent(initMessage(EventTypeEnum.CONFIRM_TAG.getType()), EventTypeEnum.CONFIRM_TAG.getType(), generateTagSelector(jsonObject));
        extraMessage(EventTypeEnum.CONFIRM_TAG.getType(), "Pushed the tag selector to the user. ", hashMap);

    }


    private TagSelector generateTagSelector(JSONObject jsonObject) {
        Map<String, List<String>> tagMap = new HashMap<>();
        // 提取所有标签类别，排除"text"字段
        jsonObject.forEach((key, value) -> {
            if (!"text".equals(key) && value instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> tagList = (List<String>) value;
                tagMap.put(key, tagList);
            }
        });

        TagSelector tagSelector = new TagSelector();
        tagSelector.setDirection(PreferenceTypeEnum.LIKE.getValue());
        Map<String, TagSelectorValue> tag = new HashMap<>();

        AtomicInteger tagNumber = new AtomicInteger(0);

        tagMap.forEach((key, value) -> {
            if (ObjectUtil.isEmpty(value) || tagNumber.get() >= 5) {
                return;
            }

            value.removeAll(TagValueEnum.getBadValues());

            if (value.size() <= 1) {
                return;
            }

            TagSelectorValue tagSelectorValue = new TagSelectorValue();
            tagSelectorValue.setSelect(value);
            List<String> other = new ArrayList<>(tagService.getTagValues(key));
            other.removeAll(value);
            tagSelectorValue.setOther(other);
            tag.put(key, tagSelectorValue);
            tagNumber.incrementAndGet();
        });
        tagSelector.setTag(tag);

        return tagSelector;
    }


    @Override
    protected void afterSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        super.afterSend(strategy, hashMap);
        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());
        conversationStatus.setAskTag(true);
    }


    @Data
    static class TagSelectorValue {
        private List<String> select;
        private List<String> other;
    }

    @Data
    static class TagSelector {
        private String direction;
        private Map<String, TagSelectorValue> tag;
    }

}
