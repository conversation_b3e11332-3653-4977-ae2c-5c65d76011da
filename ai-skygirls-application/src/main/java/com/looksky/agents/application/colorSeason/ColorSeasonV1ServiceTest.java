package com.looksky.agents.application.colorSeason;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.common.utils.UserLocationUtils;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.recommend.colorseason.request.ColorSeasonRequestDTO;
import com.looksky.agents.sdk.recommend.common.dto.ColorSeasonCacheDataDTO;
import com.looksky.agents.sdk.recommend.common.dto.Face2HairCacheDataDTO;
import com.looksky.agents.sdk.recommend.daily100.bo.RegionalCategoryQuery;
import com.looksky.agents.sdk.recommend.common.dto.UserPageKibbeCacheDataDTO;
import com.looksky.agents.sdk.recommend.daily100.dto.request.Daily100TestRequestDTO;
import com.looksky.agents.sdk.utils.date.DateUtils;
import com.looksky.agents.data.client.business.LookSkyClientLocal;
import com.looksky.agents.data.client.service.GirlsDataService;
import com.looksky.agents.data.grpc.ColorSeasonGrpcClient;
import com.looksky.agents.data.redis.daily100.Daily100DataService;
import com.looksky.agents.infrastructure.context.Context;
import com.skygirls.biz.report.IosUserInfoDto;
import com.skygirls.biz.user.dto.UserLocationInfoDTO;
import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ColorSeasonV1ServiceTest {

    private static final String PROMPT_NAME = "for_you_color_season";

    private final ColorSeasonGrpcClient colorSeasonGrpcClient;
    private final CommonRequestService commonRequestService;
    private final HttpServletRequest httpServletRequest;
    private final GirlsDataService girlsDataService;
    private final Daily100DataService daily100DataService;

    private final LookSkyClientLocal localClient;

    public JSONObject seasonColor(ColorSeasonRequestDTO colorSeasonRequestDTO) {
        UserLocationInfoDTO location = UserLocationUtils.getLocation(httpServletRequest);
        Context.put(Context.Name.USER_LOCATION.getName(), location);
        IosUserInfoDto userInfo = girlsDataService.getUserInfo(colorSeasonRequestDTO.getUserId());
        convertToColorSeasonRequestDTO(colorSeasonRequestDTO, userInfo);
        return recommendHttpTest(colorSeasonRequestDTO);
    }




    private JSONObject recommendHttpTest(ColorSeasonRequestDTO colorSeasonRequestDTO) {
        Daily100TestRequestDTO daily100TestReqModel = Daily100TestRequestDTO.fromColorSeason(colorSeasonRequestDTO);
        return localClient.girlsColorSeasonTest(daily100TestReqModel);
    }


    // 执行 prompt
    private RegionalCategoryQuery executePrompt(Map<String, Object> data) {
        return JSONUtil.toBean(commonRequestService.commonExecuteStrategy(PROMPT_NAME, data), RegionalCategoryQuery.class);
    }

    // 转为 GirlsColorSeasonResponseDTO
    private void convertToColorSeasonRequestDTO(ColorSeasonRequestDTO colorSeasonRequestDTO, IosUserInfoDto userInfo) {
        Map<String, Object> data = buildDaily100Data(userInfo, colorSeasonRequestDTO);
        RegionalCategoryQuery regionalCategoryQuery = executePrompt(data);
        colorSeasonRequestDTO.setVectorRecall(regionalCategoryQuery.toCategoryVectorRecall());
        colorSeasonRequestDTO.setVectorQueryVersion(IdUtil.getSnowflakeNextIdStr());
        colorSeasonRequestDTO.setDate(DateUtils.getNowDate(null));
    }


    private Map<String, Object> buildDaily100Data(IosUserInfoDto userInfoDto,
                                                  ColorSeasonRequestDTO req) {
        RequestInputDTO requestVo = buildRequestVo(req);
        Context.put(Context.Name.REQUEST_INPUT.getName(), requestVo);
        Map<String, Object> daily100Data = new HashMap<>(Map.of(
            Context.Name.USER_INFO.getName(), userInfoDto,
            Context.Name.REQUEST_INPUT.getName(), requestVo
        ));

        enrichDaily100Data(daily100Data, userInfoDto);
        return daily100Data;
    }

    /**
     * 构建请求对象，包含用户位置信息
     *
     * @return 包含城市信息的请求对象
     */
    private RequestInputDTO buildRequestVo(ColorSeasonRequestDTO req) {
        RequestInputDTO requestVo =  RequestInputDTO.builder().build();

        UserLocationInfoDTO userLocation = Context.get(Context.Name.USER_LOCATION.getName());
        Optional.ofNullable(req.getUserId()).ifPresent(requestVo::setUserId);
        requestVo.setSeason(DateUtils.getCurrentSeason());
        Optional.ofNullable(userLocation).ifPresent(location -> requestVo.setCity(location.getViewerCity()));


        return requestVo;
    }

    /**
     * 使用用户的Kibbe类型、颜色季节和脸型数据丰富Daily100数据
     *
     * @param daily100Data 待丰富的数据Map
     * @param userInfoDto  用户信息对象
     */
    private void enrichDaily100Data(Map<String, Object> daily100Data, IosUserInfoDto userInfoDto) {

        if (userInfoDto != null && userInfoDto.getAppUser() != null) {
            UserPageKibbeCacheDataDTO kbType = daily100DataService.getKbType(
                userInfoDto.getAppUser().getKibbeType().getCode());
            daily100Data.put("kbType", kbType);

            ColorSeasonCacheDataDTO colorSeason = daily100DataService.getColorSeason(
                userInfoDto.getAppUser().getColorSeason().getName());
            daily100Data.put("colorSeason", colorSeason);


            Face2HairCacheDataDTO face2Hair = daily100DataService.getFace2Hair(
                userInfoDto.getAppUser().getFaceShape().getName());
            daily100Data.put("face2Hair", face2Hair);
        }


        daily100Data.put("month", DateUtils.getMonth());
        daily100Data.put("nextMonth", DateUtils.getNextMonth());
        daily100Data.put("nextNextMonth", DateUtils.getNextNextMonth());
    }


}
