package com.looksky.agents.application.recomReason;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.data.client.service.GirlsDataService;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.data.redis.daily100.Daily100DataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.product.ProductInfo;
import com.looksky.agents.sdk.recommend.common.dto.ColorSeasonCacheDataDTO;
import com.looksky.agents.sdk.recommend.common.dto.Face2HairCacheDataDTO;
import com.looksky.agents.sdk.recommend.common.dto.UserPageKibbeCacheDataDTO;
import com.looksky.agents.sdk.recommend.reason.dto.RecommendReasonDTO;
import com.skygirls.biz.report.IosUserInfoDto;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author：ch
 * @Date：2024/12/18 15:19
 * @Description girls版本的推荐理由
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GirlsRecomReasonService {

    private static final String STRATEGY_NAME = "get_girls_recomReason";

    private final CommonRequestService commonRequestService;
    private final GirlsDataService girlsDataService;
    private final Daily100DataService daily100DataService;
    private final RedissonClient redissonClient;
    private final GirlsClient girlsClient;

    @SneakyThrows
    public void getRecomReasonAsync(String userId, String season, String city, List<String> itemId) {


        VirtualCompletableFuture.runAsync(() -> {
            //生成推荐理由
            List<RecommendReasonDTO> reasonModels = buildRecomReason(userId, season, city, itemId);
            log.info("回调上传推荐理由,userId:{},itemId:{}",userId,CollUtil.join(itemId, ","));
            //回调上传推荐理由
            girlsClient.recomReasonCallback(reasonModels);
        });

    }

    @SneakyThrows
    public List<RecommendReasonDTO> buildRecomReasonByDaily100(String userId, String season, String city, List<String> allItemIds, Integer batchSize) {

        List<VirtualCompletableFuture<List<RecommendReasonDTO>>> futures = new ArrayList<>();

        for (int begin = 0; begin < allItemIds.size();) {

            int end = Math.min(allItemIds.size(), begin + batchSize);

            List<String> itemIds = allItemIds.subList(begin, end);

            VirtualCompletableFuture<List<RecommendReasonDTO>> future = VirtualCompletableFuture.supplyAsync(() -> buildRecomReason(userId, season, city, itemIds));

            futures.add(future);

            begin = end;
        }

        return futures.stream()
                .map(VirtualCompletableFuture::join)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }


    private List<RecommendReasonDTO> buildRecomReason(String userId, String season, String city, List<String> itemIds) {

        String lockKey = RedisKeyConstants.girlsRecomReasonLockKey(userId, CollUtil.join(itemIds, ","));
        RLock lock = redissonClient.getLock(lockKey);

        log.info("开始生成推荐理由，userId：{},ItemId:{}",userId,CollUtil.join(itemIds, ","));

        try {
            if(!lock.tryLock(10, TimeUnit.SECONDS)){
                log.error("生成推荐理由失败，分布式锁未释放，lockKey：{}",lockKey);
                return new ArrayList<>();
            }

            log.info("生成推荐理由，获取动态数据，lockKey：{}",lockKey);

            //获取用户信息
            IosUserInfoDto userInfo = girlsDataService.getUserInfo(userId);
            //获取商品信息
            List<ProductInfo> productCardList = girlsDataService.getProductInfo(itemIds);

            if(CollUtil.isEmpty(productCardList)){
                log.error("生成推荐理由失败，获取商品数据失败，lockKey：{}",lockKey);
                return new ArrayList<>();
            }

            //生成推荐理由
            HashMap<String, Object> promptDataModel = new HashMap<>(Map.of(Context.Name.USER_INFO.getName(), userInfo,Context.Name.PRODUCT_INFO.getName(),productCardList));

            promptDataModel.put("city",city);
            promptDataModel.put("season",season);
            promptDataModel.put("batchSize",productCardList.size());

            Face2HairCacheDataDTO face2Hair = daily100DataService.getFace2Hair(userInfo.getAppUser().getFaceShape().getName());
            UserPageKibbeCacheDataDTO kbType = daily100DataService.getKbType(userInfo.getAppUser().getKibbeType().getCode());
            ColorSeasonCacheDataDTO colorSeason = daily100DataService.getColorSeason(userInfo.getAppUser().getColorSeason().getName());

            promptDataModel.put("neckSuggestion",face2Hair.getNeckSuggestion());
            promptDataModel.put("userPageKibbe",kbType);
            promptDataModel.put("colorSeasonBestColor",CollUtil.join(colorSeason.getBestColors(),","));
            promptDataModel.put("colorSeasonAvoidColor",CollUtil.join(colorSeason.getBestColors(),","));

            log.info("生成推荐理由，开始请求GPT，lockKey：{}",lockKey);

            String promptResult = commonRequestService.commonExecuteStrategy(STRATEGY_NAME, promptDataModel);

            log.info("生成推荐理由，开始解析GPT返回结果，userId：{},ItemId:{}",userId,CollUtil.join(itemIds, ","));

            //解析结果
            List<String> itemResonList = StrUtil.splitTrim(promptResult, "&&&").stream().toList();
            List<RecommendReasonDTO> insertReasonModels = new ArrayList<>();
            for (String s : itemResonList) {

                try {
                    List<String> reasonList = StrUtil.splitTrim(s.replaceAll("`", "").replace("-", ""), "\n");

                    String itemId = StrUtil.removePrefix(reasonList.getFirst().replace("*", "").trim(), "itemId:").trim();

                    RecommendReasonDTO reasonNode = new RecommendReasonDTO();
                    reasonNode.setUserId(userId);
                    reasonNode.setItemId(itemId);
                    reasonNode.setPrologue(reasonList.get(1));
                    reasonList = reasonList.subList(2, reasonList.size()).stream().map(reason -> reason.replaceAll("\\d+\\.", "").trim()).collect(Collectors.toList());
                    reasonNode.setReason(JSONArray.toJSONString(reasonList));

                    insertReasonModels.add(reasonNode);
                }catch (Exception e){
                    log.info("解析单个推荐理由失败，lockKey：{},reason:{}",lockKey,s);
                }

            }

            log.info("生成推荐理由成功，lockKey：{}",lockKey);

            if(lock.isHeldByCurrentThread()){
                lock.unlock();
            }

            return insertReasonModels;
        }catch (Exception e){
            log.error("生成推荐理由异常，lockKey：{}，e：{}",lockKey, e.getMessage(),e);
        }finally {
            if(lock.isLocked()){
                lock.forceUnlock();
            }
        }

        return new ArrayList<>();
    }



}
