package com.looksky.agents.application.chat.reply.stream;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.reply.AbstractMessageSender;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.common.enums.EventNameEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.common.enums.PageEnum;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.tryon.dto.TryOnReplayDTO;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import com.skygirls.biz.im.dto.MessageRestDTO;
import com.skygirls.biz.report.IosUserInfoDto;
import com.skygirls.biz.user.tryon.dto.SearchTryOnStatusReq;
import com.skygirls.biz.user.tryon.model.TryOnReportModel;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Order(1)
@Component
public class TryOnDetailOpeningMessageSender extends AbstractMessageSender {

    @Resource
    private GirlsClient girlsClient;

    private static final String PROMPT = "tryOn_replay_effect";



    @Override
    protected boolean supports(PromptModel strategy) {
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());
        return PageEnum.TRY_ON.getName().equals(input.getPage()) && EnterPointEnum.TRY_ON_DETAIL == input.getEnterPointEnum() && EventNameEnum.OPENING.getValue().equals(input.getEvent().getEventName());
    }


    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());

        IosUserInfoDto userInfo = Context.get(Context.Name.USER_INFO.getName());

        SearchTryOnStatusReq request = new SearchTryOnStatusReq();
        request.setUserId(userInfo.getAppUser().getUserId());
        MessageRestDTO.EventDict eventDict = input.getEventDict();
        if (eventDict != null) {
            if (CharSequenceUtil.isNotBlank(eventDict.getSkcId())) {
                request.setSkcId(eventDict.getSkcId());
            }
            if (CharSequenceUtil.isNotBlank(eventDict.getSkuId())) {
                request.setSkuId(eventDict.getSkuId());
            }
        }

        TryOnReportModel tryOnReportModel = girlsClient.tryOnHomeCard(request).getData();

        // 发送卡片
        sendTryOnHomeCardMessage(tryOnReportModel);

        sendLoading();

        String uploadImage = tryOnReportModel.getUploadImage();
        String resultImage = tryOnReportModel.getResultImage();
        TryOnReplayDTO replayDTO = getPraise(uploadImage, resultImage);

        AgentMessageResp replayMessage = initMessage(EventTypeEnum.TEXT.getType());
        sendContent(replayMessage, EventTypeEnum.TEXT.getType(), replayDTO.getThreeLineReview());

        // 发送商品卡片
        sendProductCard(request.getSkuId() != null ? request.getSkuId() : tryOnReportModel.getSkuId());

    }

    private void sendTryOnHomeCardMessage(TryOnReportModel tryOnReportModel) {
        AgentMessageResp adviceMessage = initMessage(EventTypeEnum.TRY_ON_UPLOAD_IMAGE_CARD.getType());
        sendContent(adviceMessage, EventTypeEnum.TRY_ON_UPLOAD_IMAGE_CARD.getType(), tryOnReportModel);
    }

    private void sendProductCard(String skcId) {
        AgentMessageResp adviceMessage = initMessage(EventTypeEnum.TRY_ON_SKU_INFO.getType());
        adviceMessage.setSkuId(skcId);
        adviceMessage.setIsFinished(true);
        sendContent(adviceMessage);
    }

    public TryOnReplayDTO getPraise(String uploadImage, String resultImage) {
        PromptModel promptModel = promptBusinessService.getPromptModelByNameWithMetaData(PROMPT);

        RequestParamBO requestParam = promptBusinessService.convert(promptModel, Collections.emptyMap());

        if (CharSequenceUtil.isNotBlank(uploadImage) && CharSequenceUtil.isNotBlank(resultImage)) {
            requestParam.setImageUrl(List.of(uploadImage, resultImage));
        }
        String output = commonRequestService.commonExecuteStrategy(requestParam);
        return JSONUtil.toBean(output, TryOnReplayDTO.class);
    }

}