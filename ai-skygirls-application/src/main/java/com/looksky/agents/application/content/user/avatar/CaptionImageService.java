package com.looksky.agents.application.content.user.avatar;

import ai.fal.client.ClientConfig;
import ai.fal.client.CredentialsResolver;
import ai.fal.client.FalClient;
import ai.fal.client.SubscribeOptions;
import ai.fal.client.queue.QueueStatus;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.common.model.tryOn.TryOnFalKeyConfig;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.sdk.content.user.avatar.UserAvatarParam;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 描述 Image 服务
 *
 * <AUTHOR>
 * @since 1.2.0
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CaptionImageService {
    private final Random random = new Random();
    private final TryOnFalKeyConfig tryOnFalKeyConfig;
    private final static HashSet<String> PLUS = new HashSet<>(Set.of("plus", "extended_plus"));


    @CollectEvent(typeExpression = "#param.id", extExpression = "#param.url")
    public String captionImage(UserAvatarParam param) {


        List<String> apiKeyList = tryOnFalKeyConfig.getApiKeyList();
        String apiKey = apiKeyList.get(random.nextInt(apiKeyList.size()));

        FalClient falClient = FalClient.withConfig(ClientConfig.withCredentials(CredentialsResolver.fromApiKey(apiKey)));
        var input = Map.of(
            "image_url", param.getOriginalUrl()
        );
        log.info("描述图片请求参数: {}", input);
        var result = falClient.subscribe("fal-ai/florence-2-large/more-detailed-caption",
            SubscribeOptions.<CaptionImageResponse>builder()
                .input(input)
                .logs(true)
                .resultType(CaptionImageResponse.class)
                .onQueueUpdate(update -> {
                    if (update instanceof QueueStatus.InProgress status) {
                        log.info("描述图片返回过程数据: {}", status.getLogs());
                    }
                })
                .build()
        );


        log.info("描述图片 结果: {}", JSONUtil.toJsonStr(result));

        String prompt = result.getData().getResults();

        if (containsBlockedWords(prompt)) {
            return null;
        } else {
            String temp = replaceWoman(prompt);
            if (PLUS.contains(param.getWeightType())) {
                return replacePlus(temp);
            }
            return temp;
        }

    }


    private String replacePlus(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        
        // 创建不区分大小写的正则表达式模式
        String result;
        if (str.toLowerCase().contains("woman's")) {
            // 如果包含 woman's，则替换为 fat woman's
            result = str.replaceAll("(?i)woman's", "fat woman's");
        } else {
            // 否则替换 woman 为 fat woman
            result = str.replaceAll("(?i)woman", "fat woman");
        }
        
        return result;
    }

    @Data
    private static class CaptionImageResponse {
        private String results;
    }


    private String replaceWoman(String str) {
        return str.replace("girl", "woman").replace("Girl", "Woman");
    }


    private boolean containsBlockedWords(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        // 转换为小写进行检查，避免大小写影响
        String lowerText = text.toLowerCase();

        // 检查完整单词匹配，而不是部分匹配
        String[] words = lowerText.split("\\s+");
        for (String word : words) {
            // 去除标点符号后再判断
            word = word.replaceAll("[^a-zA-Z]", "");
            // 只检查 man 和 boy
            if (word.equals("man") || word.equals("boy")) {
                return true;
            }
        }
        return false;
    }
}
