package com.looksky.agents.application.chat.preference.extraction.tag;

import com.looksky.agents.application.chat.preference.extraction.AbstractPreferenceExtractor;
import com.looksky.agents.application.chat.preference.extraction.SingleTagExtractService;
import com.looksky.agents.common.utils.BeanMergeUtils;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.data.redis.conversation.PreferenceDataService;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.preference.PricePreference;
import org.springframework.stereotype.Component;

@Component
public class PriceExtractor extends AbstractPreferenceExtractor<PricePreference> {

    public PriceExtractor(SingleTagExtractService singleTagExtractService, PreferenceDataService preferenceDataService, TagSystemTableService tagSystemTableService) {
        super(singleTagExtractService, preferenceDataService, tagSystemTableService);
    }

    @Override
    protected String getTagName() {
        return CategoryEnum.PRICE.getName();
    }

    @Override
    protected PricePreference parseExtractedValue(String result) {
        return PricePreference.parseJson(result);
    }

    @Override
    protected PricePreference getCurrentPreference(ExtractedEntityObject userPreference) {
        return userPreference.getPricePreference();
    }

    @Override
    protected void updatePreference(ExtractedEntityObject userPreference, PricePreference newValue) {
        userPreference.setPricePreference(newValue);
    }

    @Override
    protected PricePreference mergePreference(PricePreference newValue, PricePreference currentValue) {
        return BeanMergeUtils.merge(new PricePreference(), currentValue, newValue);
    }
} 