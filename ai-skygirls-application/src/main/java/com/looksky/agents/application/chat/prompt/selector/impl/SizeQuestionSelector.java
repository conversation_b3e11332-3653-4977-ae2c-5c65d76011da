//package com.looksky.agents.application.chat.question.selector.impl;
//
//import cn.hutool.core.util.ObjectUtil;
//import com.graecove.biz.task.model.StepInfo;
//import com.looksky.agents.common.vo.RequestInput;
//import com.looksky.agent.product.enums.BodyMeasure;
//import com.looksky.agent.product.enums.SizeSibcategoryType;
//import com.looksky.agents.application.chat.question.selector.QuestionSelector;
//import com.looksky.agents.common.model.vo.resp.UserPersonalCenterData;
//import com.looksky.agents.data.conversation.model.Action;
//import com.looksky.agents.common.dto.ExternInfo;
//import com.looksky.agents.data.redis.product.enums.SizeType;
//import com.looksky.agents.data.redis.product.model.BodySize;
//import com.looksky.agents.data.redis.product.model.Product;
//import org.springframework.stereotype.Component;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 尺码相关问题选择器
// */
//@Component
//public class SizeQuestionSelector implements QuestionSelector {
//
//    @Override
//    public String supportQuestionType() {
//        return "size";
//    }
//
//    @Override
//    public String doSelectSubQuestion(
//            RequestInput requestInput,
//            List<Action> actionHistory,
//            ExternInfo metadata) {
//        // 分析用户的尺码相关问题，确定具体的尺码子问题类型
//        // TODO: 实现具体的选择逻辑
//        return "SIZE_RECOMMENDATION";
//    }
//
//    public void getUserTag(UserPersonalCenterData user) {
//        if (ObjectUtil.isNull(user)) {
//            return;
//        }
//        if (ObjectUtil.isNotEmpty(user.getBodyMeasurements())) {
//            StepInfo.BodyMeasurements bodyMeasurements = user.getBodyMeasurements();
//        }
//    }
//
//
//    public String selectSizeStrategy(RequestInput requestInput, List<Action> actionHistory, Map<String,Object> metadata) {
//        UserPersonalCenterData userPersonalCenterData = requestInput.getUser();
//        Product product = requestInput.getProduct();
//        String sizeType = product.getSizeType();
//
//
//        List<BodySize> bodySizeList = product.getBodySizeList().stream()
//                .filter(bs -> bs.getSizeType().equals(sizeType))
//                .toList();
//
//
////        Map<String,Object> userTagDict = user.getUserTagDict();
//        Map<String,Object> userTagDict = null;
//        Map<String,Object> keyBodyDict = getKeyBodySize(userTagDict);
//        Map<String,String> suitSizeDict = getFitSize(product, keyBodyDict);
//        Set<String> suitSizeValues = new HashSet<>(suitSizeDict.values());
//
//        // 如果衣服仅有一个可选尺码，且被标志为one_size，使用only_one_size策略
//        if(product.getSizeList().size() == 1 && isOneSize(product.getSizeList(), product.getSizeType())) {
//            return SizeSibcategoryType.ONLY_ONE_SIZE.getValue();
//        }
//
//        // 如果没有body size信息
//        if(bodySizeList.isEmpty()) {
//            if(containWeightAndHeight(keyBodyDict)) {
//                return SizeSibcategoryType.NO_BODY_SIZE.getValue();
//            } else {
//                return SizeSibcategoryType.NO_BODY_SIZE_WITHOUT_WEIGHT_HEIGHT.getValue();
//            }
//        }
//
//        // 检查尺码是否太大或太小
//        if(!Arrays.asList(SizeType.SIZE.getValue(), SizeType.NON_CLOTHING_SIZE.getValue()).contains(sizeType)
//            && !suitSizeValues.isEmpty()) {
//
//            List<Double> sizeList = normSizeList(sizeType, product.getSizeList());
//            double minSize = Collections.min(sizeList);
//            double maxSize = Collections.max(sizeList);
//            List<Double> normSizeValues = normSizeList(sizeType, new ArrayList<>(suitSizeValues));
//
//            if(normSizeValues.stream().allMatch(x -> minSize > x)) {
//                return SizeSibcategoryType.SIZE_TOO_LARGE.getValue();
//            } else if(normSizeValues.stream().anyMatch(x -> maxSize < x)) {
//                return SizeSibcategoryType.SIZE_TOO_SMALL.getValue();
//            }
//        }
//
//        // 根据三围数量判断策略
//        int measureNumber = getThreeMeasureNumber(keyBodyDict);
//        if(measureNumber == 3) {
//            if(suitSizeValues.size() == 1) {
//                if(keyBodyDict.containsKey(BodyMeasure.SHOULDER_WIDTH.getValue()) ||
//                   keyBodyDict.containsKey(BodyMeasure.BODY_SHAPE.getValue())) {
//                    return SizeSibcategoryType.THREE_MEASURES_IN_ONE_SIZE.getValue();
//                } else {
//                    return SizeSibcategoryType.THREE_MEASURES_IN_ONE_SIZE_WITHOUT_BODY_SHAPE.getValue();
//                }
//            } else if(suitSizeValues.size() == 2) {
//                return SizeSibcategoryType.THREE_MEASURES_IN_TWO_SIZE.getValue();
//            } else if(suitSizeValues.size() == 3) {
//                return SizeSibcategoryType.THREE_MEASURES_IN_THREE_SIZE.getValue();
//            }
//        } else if(measureNumber == 2) {
//            if(suitSizeValues.size() == 1) {
//                return SizeSibcategoryType.TWO_MEASURES_IN_ONE_SIZE.getValue();
//            }
//            if(suitSizeValues.size() == 2) {
//                return SizeSibcategoryType.TWO_MEASURES_IN_TWO_SIZE.getValue();
//            }
//        } else if(measureNumber == 0) {
//            if(containWeightAndHeight(keyBodyDict)) {
//                return SizeSibcategoryType.SIZE_BY_WEIGHT_HEIGHT.getValue();
//            }
//        }
//
//        return SizeSibcategoryType.INQUIRE_USER_BODY_MEASURE.getValue();
//    }
//
//    private boolean containWeightAndHeight(Map<String,Object> keyBodyDict) {
//        return keyBodyDict.containsKey(BodyMeasure.HEIGHT.getValue()) &&
//               keyBodyDict.containsKey(BodyMeasure.WEIGHT.getValue()) &&
//               keyBodyDict.get(BodyMeasure.HEIGHT.getValue()) != null &&
//               keyBodyDict.get(BodyMeasure.WEIGHT.getValue()) != null;
//    }
//
//    private int getThreeMeasureNumber(Map<String,Object> keyBodyDict) {
//        int count = 0;
//        if(keyBodyDict.containsKey(BodyMeasure.CHEST.getValue())) count++;
//        if(keyBodyDict.containsKey(BodyMeasure.WAIST.getValue())) count++;
//        if(keyBodyDict.containsKey(BodyMeasure.HIP.getValue())) count++;
//        return count;
//    }
//
//
//    private Map<String, Object> getKeyBodySize(Map<String, Object> userTagDict) {
//        Map<String, Object> keyBodyDict = new HashMap<>();
//
//        Arrays.asList(
//            BodyMeasure.HEIGHT,
//            BodyMeasure.WEIGHT,
//            BodyMeasure.CHEST,
//            BodyMeasure.WAIST,
//            BodyMeasure.HIP,
//            BodyMeasure.SHOULDER_WIDTH,
//            BodyMeasure.BODY_SHAPE
//        ).forEach(measure -> {
//            String key = measure.getValue();
//            if (userTagDict.containsKey(key)) {
//                Object value = formatSize(userTagDict.get(key));
//                if (value != null) {
//                    keyBodyDict.put(key, value);
//                }
//            }
//        });
//
//        return keyBodyDict;
//    }
//
//    private Object formatSize(Object sizeValue) {
//        if (sizeValue == null) return null;
//
//        if (sizeValue instanceof Number) {
//            return ((Number) sizeValue).doubleValue();
//        }
//
//        String strValue = sizeValue.toString().trim();
//
//        try {
//            if (strValue.contains("kg")) {
//                strValue = strValue.replace("kg", "").trim();
//                return Double.parseDouble(strValue) * 2.20462;
//            } else if (strValue.contains("lbs")) {
//                strValue = strValue.replace("lbs", "").trim();
//                return Double.parseDouble(strValue);
//            } else if (strValue.contains("cm")) {
//                strValue = strValue.replace("cm", "").trim();
//                return Double.parseDouble(strValue) * 0.3971;
//            } else if (strValue.contains("'")) {
//                strValue = strValue.replace("\"", "");
//                String[] parts = strValue.split("'");
//                if (parts.length == 2) {
//                    double feet = Double.parseDouble(parts[0].trim());
//                    double inches = Double.parseDouble(parts[1].trim());
//                    return feet * 12.0 + inches;
//                } else if (parts.length == 1) {
//                    return Double.parseDouble(parts[0].trim()) * 12.0;
//                }
//            } else if (strValue.contains("\"")) {
//                strValue = strValue.replace("\"", "").trim();
//                return Double.parseDouble(strValue);
//            } else {
//                return Double.parseDouble(strValue);
//            }
//        } catch (NumberFormatException e) {
//            return null;
//        }
//        return null;
//    }
//
//    private Map<String, String> getFitSize(Product product, Map<String, Object> keyBodyDict) {
//        Map<String, String> suitSizeDict = new HashMap<>();
//        Map<String, List<Map<String, Object>>> bodySizeRanges = getBodySizeRange(product);
//
//        for (String measure : Arrays.asList(
//            BodyMeasure.CHEST.getValue(),
//            BodyMeasure.WAIST.getValue(),
//            BodyMeasure.HIP.getValue()
//        )) {
//            if (keyBodyDict.containsKey(measure) &&
//                bodySizeRanges.containsKey(measure) &&
//                keyBodyDict.get(measure) != null) {
//
//                double userMeasure = ((Number) keyBodyDict.get(measure)).doubleValue();
//
//                for (Map<String, Object> item : bodySizeRanges.get(measure)) {
//                    double low = (double) item.get("low");
//                    double high = (double) item.get("high");
//                    String size = (String) item.get("size");
//
//                    if (low <= userMeasure && userMeasure < high) {
//                        suitSizeDict.put(measure, size);
//                    }
//                }
//
//                // Handle out of range cases
//                if (!suitSizeDict.containsKey(measure)) {
//                    List<Map<String, Object>> sizeRange = bodySizeRanges.get(measure);
//                    double minSize = sizeRange.stream()
//                        .mapToDouble(item -> (double) item.get("low"))
//                        .min()
//                        .orElse(0);
//                    double maxSize = sizeRange.stream()
//                        .mapToDouble(item -> (double) item.get("high"))
//                        .max()
//                        .orElse(0);
//
//                    if (userMeasure < minSize) {
//                        suitSizeDict.put(measure, sizeRange.get(0).get("size").toString());
//                    } else if (userMeasure > maxSize) {
//                        suitSizeDict.put(measure, sizeRange.get(sizeRange.size() - 1).get("size").toString());
//                    }
//                }
//            }
//        }
//
//        return suitSizeDict;
//    }
//
//    private List<Double> normSizeList(String sizeType, List<String> sizeList) {
//        // 这里需要实现尺码标准化的逻辑
//        // 可以通过查询数据库或配置文件获取尺码转换规则
//        return sizeList.stream()
//            .map(size -> convertSizeToNumber(sizeType, size))
//            .filter(Objects::nonNull)
//            .collect(Collectors.toList());
//    }
//
//    private Double convertSizeToNumber(String sizeType, String size) {
//        // 实现具体的尺码转换逻辑
//        // 这里需要根据实际业务规则来实现
//        return null; // 临时返回
//    }
//
//    private boolean isOneSize(List<String> sizeList, String sizeType) {
//        // 实现判断是否为均码的逻辑
//        return false; // 临时返回
//    }
//
//    private Map<String, List<Map<String, Object>>> getBodySizeRange(Product product) {
//        // 实现获取尺码范围的逻辑
//        return new HashMap<>(); // 临时返回
//    }
//
//}