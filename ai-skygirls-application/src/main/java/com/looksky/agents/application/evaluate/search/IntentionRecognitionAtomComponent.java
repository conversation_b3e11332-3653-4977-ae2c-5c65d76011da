package com.looksky.agents.application.evaluate.search;

import com.looksky.agents.application.evaluate.prompt.OptimizePromptService;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import com.looksky.agents.sdk.optimize.dto.OptimizePromptResultDTO;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 意图识别原子组件
 */
@Component
@RequiredArgsConstructor
public class IntentionRecognitionAtomComponent {
     public static final String INTENT_RECOGNITION = PromptNameEnum.INTENT_RECOGNITION.getName();

     private final OptimizePromptService optimizePromptService;

     public OptimizePromptResultDTO run(DatasetRecordDTO datasetRecord) {
         return optimizePromptService.batchRunPrompt(INTENT_RECOGNITION, datasetRecord);
     }

}
