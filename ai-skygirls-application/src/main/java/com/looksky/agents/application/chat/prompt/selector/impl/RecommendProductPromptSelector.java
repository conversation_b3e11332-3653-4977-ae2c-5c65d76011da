package com.looksky.agents.application.chat.prompt.selector.impl;

import cn.hutool.core.util.ObjectUtil;
import com.looksky.agents.application.chat.prompt.selector.PromptSelector;
import com.looksky.agents.sdk.agent.common.enums.IntentTypeEnum;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.infrastructure.context.Context;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * @ClassName RecommendProductPromptSelector
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/7 下午3:07
 * @Version 1.0
 **/
@Component
@RequiredArgsConstructor
public class RecommendProductPromptSelector implements PromptSelector {


    private final TagSystemTableService tagSystemTableService;

    @Override
    public String support() {
        return IntentTypeEnum.RECOMMEND_PRODUCT.getType();
    }

    @Override
    public String select() {

        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());

        // 判断偏好里面是否包含品类
        if (hasValidCategoryTagPreference(userPreference)) {

            Set<String> currentCategories = userPreference.getCurrentCategories();

            // 如果已经包含了二级品类, 那么返回 推荐商品
            if (tagSystemTableService.containsSubCategory(currentCategories)) {
                return PromptNameEnum.BUILD_SEARCH_PROCESS.getName();
            }
        }

        // 如果不包含品类, 那么返回 推解决方案
        return  PromptNameEnum.INQUIRE_USER_SOLUTION.getName();
    }

    private boolean hasValidCategoryTagPreference(ExtractedEntityObject mergedUserPreference) {
        return mergedUserPreference != null && ObjectUtil.isNotEmpty(mergedUserPreference.getCurrentCategories());
    }
}
