package com.looksky.agents.application.provider;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.infrastructure.context.Context;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName CategorySelectorSchemaProvider
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/3 下午4:10
 * @Version 1.0
 **/
@Component
@RequiredArgsConstructor
public class CategorySelectorSchemaProvider implements DataProvider {

    private final TagSystemTableService tagService;
    public String build() {
        ExtractedEntityObject preference = Context.get(Context.Name.PREFERENCE.getName());
        List<String> categories = new ArrayList<>();
        if (preference != null && preference.getCurrentCategories() != null) {
            categories.addAll(preference.getCurrentCategories());
        }

        List<String> subCategories = new ArrayList<>();
        if (categories.isEmpty()) {
            subCategories.addAll(tagService.getSubCategories());
        } else {
            String category = categories.getFirst();

            if (CategoryEnum.CLOTH.getName().equals(category)) {
                subCategories.addAll(tagService.getSubCategories());
            } else {
                subCategories.addAll(tagService.getSubCategoryByFirstCategory(category));
            }

        }

        return JSONUtil.toJsonStr(subCategories);
    }


    @Override
    public boolean supports(String namespace) {
        return "categorySelectorSchemaProvider".equals(namespace);
    }

    @Override
    public void getData() {
        Context.put("categorySelectorSchemaProvider", this);
    }
}
