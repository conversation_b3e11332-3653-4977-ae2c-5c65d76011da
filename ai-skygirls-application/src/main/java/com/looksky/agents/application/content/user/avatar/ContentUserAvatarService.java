package com.looksky.agents.application.content.user.avatar;

import cn.hutool.core.text.CharSequenceUtil;
import com.looksky.agents.application.content.user.username.UserNameGenerateService;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.content.user.avatar.UserAvatarParam;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 生成用户头像服务
 *
 * <AUTHOR>
 * @since 1.1.14
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ContentUserAvatarService {
    private final CaptionImageService captionImageService;
    private final Text2ImageService text2ImageService;
    private final UserNameGenerateService userNameGenerateService;

    @CollectEvent
    public List<UserAvatarParam> buildUserAvatar(List<UserAvatarParam> params) {


        List<VirtualCompletableFuture<UserAvatarParam>> futures = new ArrayList<>();
        
        for (UserAvatarParam param : params) {
            VirtualCompletableFuture<UserAvatarParam> future = VirtualCompletableFuture.supplyAsync(() -> {
                try {
                    String prompt = captionImageService.captionImage(param);
                    param.setPrompt(prompt);
                    // 判断是否有 prompt, 如果没有的话, 就不执行下一步
                    if (CharSequenceUtil.isBlank(prompt)) {
                        return param;
                    }
                    param.setUrl(text2ImageService.text2Image(param));
                    param.setUserName(userNameGenerateService.randomUserName());
                    return param;
                } catch (Exception e) {
                    log.error("生成用户头像失败，参数: {}", param, e);
                    return param;
                }
            });
            futures.add(future);
        }
        
        return futures.stream()
                .map(VirtualCompletableFuture::join)
                .toList();
    }
}
