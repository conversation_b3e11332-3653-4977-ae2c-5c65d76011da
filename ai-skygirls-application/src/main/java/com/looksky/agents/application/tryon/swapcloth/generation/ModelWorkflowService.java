package com.looksky.agents.application.tryon.swapcloth.generation;

import com.fasterxml.jackson.databind.JsonNode;
import com.looksky.agents.data.client.business.TryOnClient;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.SwapClothWorkflowRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ModelWorkflowService implements IRunWorkflow{

    @Resource
    private TryOnClient tryOnClient;

    @Override
    public String run(String id, String imagePrompt, String faceImage, String modelImage) {
        SwapClothWorkflowRequest swapClothWorkflowRequest = new SwapClothWorkflowRequest();
        swapClothWorkflowRequest.setId(id);
        swapClothWorkflowRequest.setNewFaceImage(faceImage);
        swapClothWorkflowRequest.setNewClothImage(modelImage);
        swapClothWorkflowRequest.setNewPromptText(imagePrompt);
        JsonNode jsonNode = tryOnClient.swapClothWorkflow(swapClothWorkflowRequest);
        return jsonNode.get("img").asText();
    }

    @Override
    public boolean supports(WorkflowEnum workflowEnum) {
        return WorkflowEnum.MODEL  == workflowEnum;
    }
}
