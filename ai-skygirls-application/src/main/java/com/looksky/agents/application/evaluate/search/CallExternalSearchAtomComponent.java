package com.looksky.agents.application.evaluate.search;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.recommend.QueryAndSearchService;
import com.looksky.agents.sdk.optimize.dto.OptimizePromptResultDTO;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import com.looksky.agents.sdk.perplex.dto.response.PerplexSearchResponse;
import com.looksky.agents.data.client.service.GirlsDataService;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 调用外部搜索原子组件
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CallExternalSearchAtomComponent {
    private final QueryAndSearchService queryAndSearchService;
    private final GirlsDataService girlsDataService;
    
    public OptimizePromptResultDTO run(DatasetRecordDTO datasetRecord) {

        Map<String, Object> data = datasetRecord.getData();
        CheckResult checkResult = checkData(data);

        if (Boolean.FALSE.equals(checkResult.pass())) {
            log.error("数据校验失败: {}", checkResult.message());
            return new OptimizePromptResultDTO(checkResult.message(), 0L, 0L);
        }

        girlsDataService.getUserInfo(data.get("user_id").toString());

        String query = data.get("query").toString();
        boolean needSearch = Boolean.parseBoolean(data.get("need_search").toString());

        QueryAndSearchService.NewQueryAndIsSearch search = new QueryAndSearchService.NewQueryAndIsSearch();
        search.setQuery(query);
        search.setNeedSearch(needSearch);
        long start = System.currentTimeMillis();
        PerplexSearchResponse openPerplexSearchResponse = queryAndSearchService.searchNewQuery(search);
        long end = System.currentTimeMillis();
        String content = JSONUtil.toJsonStr(openPerplexSearchResponse);
        Long totalTokens = 0L;
        return new OptimizePromptResultDTO(content, totalTokens, end - start);
    }
    
    
    record CheckResult(Boolean pass, String message){}

    private CheckResult checkData(Map<String, Object> data) {
        if (data == null) {
            return new CheckResult(false, "data 为空");
        }

        if (!data.containsKey("query")) {
            return new CheckResult(false, "data 中缺少 query 字段");
        }

        if (!data.containsKey("need_search")) {
            return new CheckResult(false, "data 中缺少 need_search 字段");
        } else if (ObjectUtil.isNull(data.get("need_search"))) {
            return new CheckResult(false, "need_search 为空");
        } else if (!Boolean.parseBoolean(data.get("need_search").toString())) {
            return new CheckResult(false, "不需要搜索");
        }

        if (!data.containsKey("user_id")) {
            return new CheckResult(false, "data 中缺少 user_id 字段");
        }

        return new CheckResult(true, "校验通过");
    }
}
