package com.looksky.agents.application.provider;

import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.data.client.service.LookSkyDataService;
import com.looksky.agents.infrastructure.context.Context;
import com.skygirls.biz.report.IosUserInfoDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UserInfoProvider implements DataProvider {
    private final LookSkyDataService lookSkyDataService;


    @Override
    public void getData() {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());
        String userId = requestInput.getUserId();
        IosUserInfoDto userInfo = lookSkyDataService.getUserInfo(userId);
        Context.put(Context.Name.USER_INFO.getName(), userInfo);

    }

    @Override
    public boolean supports(String key) {
        return key.equals(Context.Name.USER_INFO.getName());
    }

} 