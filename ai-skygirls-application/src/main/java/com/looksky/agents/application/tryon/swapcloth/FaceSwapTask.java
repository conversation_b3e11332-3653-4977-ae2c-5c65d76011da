package com.looksky.agents.application.tryon.swapcloth;

import ai.fal.client.FalClient;
import ai.fal.client.SubscribeOptions;
import ai.fal.client.queue.QueueStatus;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.looksky.agents.application.tryon.swapcloth.convertor.TryOnConvertor;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.data.redis.config.RedisCodecFactory;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothParam;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothStatus;
import com.looksky.agents.sdk.tryon.swapcloth.enums.SwapClothStatusEnum;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class FaceSwapTask extends SwapClothBaseService {

    @Resource
    private FalClient falClient;

    private Thread faceSwapThread;

    public FaceSwapTask(RedissonClient redissonClient, RedisCodecFactory factory, TryOnConvertor tryOnConvertor, GirlsClient girlsClient) {
        super(redissonClient, factory, tryOnConvertor, girlsClient);
    }


    @PostConstruct
    public void start() {
         //获取当前环境, 如果为 dev 环境, 则不启动
        if ("dev".equals(env)) {
            log.warn("当前环境为 dev, 不启动消费者线程");
            return;
        }
        startFaceSwapConsumer();
    }


    private void startFaceSwapConsumer() {
        faceSwapThread = Thread.ofVirtual().name("换脸线程").unstarted(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                SwapClothStatus status = getQueueTask(SwapClothStatusEnum.QUEUED_FOR_FACE_SWAP);
                if (status != null) {
                    VirtualCompletableFuture.runAsync(() -> {
                        try {
                            ensureQueue(status, SwapClothStatusEnum.FACE_SWAPPING);
                            SwapClothStatus copy = tryOnConvertor.copy(status);
                            String result = faceSwap(status);
                            removeFromQueue(copy, SwapClothStatusEnum.FACE_SWAPPING);
                            status.setResultImage(result);
                            ensureQueue(status, SwapClothStatusEnum.COMPLETED);
                        } catch (Exception e) {
                            removeFromQueue(status, SwapClothStatusEnum.FACE_SWAPPING);
                            log.error("换脸失败", e);
                            status.setErrorMessage(e.getMessage());
                            ensureQueue(status, SwapClothStatusEnum.FAILED);
                        }
                    }).orTimeout(5, TimeUnit.MINUTES)
                    .exceptionally(throwable -> {
                        removeFromQueue(status, SwapClothStatusEnum.FACE_SWAPPING);
                        log.error("换脸任务超时, 任务:{}", JSONUtil.toJsonStr(status));
                        status.setErrorMessage("换脸任务超时，执行时间超过5分钟");
                        ensureQueue(status, SwapClothStatusEnum.FAILED);
                        return null;
                    });
                } else {
                    try {
                        TimeUnit.SECONDS.sleep(2);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        });
        faceSwapThread.setDaemon(true);
        faceSwapThread.start();
    }


    private String faceSwap(SwapClothStatus status) {
        // 生成中
        SwapClothParam swapClothParam = status.getSwapClothParam();
        // 执行分析步骤
        return fashnSwapFace(status.getWorkflowResultUrl(), swapClothParam.getModelImage());
    }


    @Data
    static class FashnResult {
        private List<FashnResultImage> images;

        @Data
        static class FashnResultImage {
            private String url;
            @JsonProperty("content_type")
            private String contentType;

            @JsonProperty("file_name")
            private String fileName;

            @JsonProperty("file_size")
            private long fileSize;

            private int width;
            private int height;
        }
    }

    public String fashnSwapFace(String tempImage, String modelImage) {

        var input = Map.of(
            "model_image", tempImage,
            "garment_image", modelImage,
            "category", "one-pieces",
            "guidance_scale", 2.2
        );

        log.info("换脸请求:  请求体: {}", JSONUtil.toJsonStr(input));


        var result = falClient.subscribe("fashn/tryon",
            SubscribeOptions.<FashnResult>builder()
                .input(input)
                .logs(true)
                .resultType(FashnResult.class)
                .onQueueUpdate(update -> {
                    if (update instanceof QueueStatus.InProgress) {
                        log.info("换脸返回过程数据: {}", ((QueueStatus.InProgress) update).getLogs());
                    }
                })
                .build()
        );

        log.info("换脸结果: {}", JSONUtil.toJsonStr(result));

        return result.getData().getImages().getFirst().getUrl();

    }
}
