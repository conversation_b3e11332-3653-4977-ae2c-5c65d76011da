package com.looksky.agents.application.tryon.swapcloth.background.remove;

import java.io.Serializable;
import java.time.Duration;
import lombok.Data;

/**
 * 抠图服务状态
 * 管理服务的可用性、优先级、超时时间和失败次数
 */
@Data
public class RemoveBackgroundServiceStatus implements Serializable {
    private static final long serialVersionUID = 1L;

    // 服务是否可用
    private boolean available = true;
    
    // 服务优先级，数字越小优先级越高
    private int priority = Integer.MAX_VALUE;
    
    // 服务超时时间（毫秒）
    private long timeoutMillis = 30000;
    
    // 连续失败次数
    private int failureCount = 0;
    
    // 服务被标记为不可用的时间戳
    private long unavailableTime = 0;
    
    /**
     * 更新服务优先级
     */
    public void updatePriority(int priority) {
        this.priority = priority;
    }
    
    /**
     * 更新服务超时时间
     */
    public void updateTimeout(Duration timeout) {
        this.timeoutMillis = timeout.toMillis();
    }
    
    /**
     * 记录服务调用失败
     */
    public void recordFailure() {
        this.failureCount++;
    }
    
    /**
     * 重置失败计数
     */
    public void resetFailureCount() {
        this.failureCount = 0;
    }
    
    /**
     * 标记服务为不可用
     */
    public void markAsUnavailable() {
        this.available = false;
        this.unavailableTime = System.currentTimeMillis();
    }
    
    /**
     * 标记服务为可用
     */
    public void markAsAvailable() {
        this.available = true;
        this.failureCount = 0;
    }
} 