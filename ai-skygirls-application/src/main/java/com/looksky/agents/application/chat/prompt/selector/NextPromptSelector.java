package com.looksky.agents.application.chat.prompt.selector;

import com.looksky.agents.infrastructure.versioncompat.selector.VersionServiceSelector;
import com.looksky.agents.sdk.agent.common.enums.IntentTypeEnum;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Prompt 选择器, 支持版本兼容
 *
 * <AUTHOR>
 * @since 1.1.0
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class NextPromptSelector {
    private final List<PromptSelector> promptSelectors;
    private final VersionServiceSelector versionServiceSelector;

    public String select(IntentTypeEnum questionType) {
        List<PromptSelector> versionedSender = versionServiceSelector.getServiceList(PromptSelector.class);
        PromptSelector finalSelector = versionedSender.stream().filter(selector -> selector.support().equals(questionType.getType())).findFirst().map(selector -> {
                log.info("使用版本化的PromptSelector: {}", selector.getClass().getSimpleName());
                return selector;
            })
            .orElseGet(() -> promptSelectors.stream().filter(selector -> selector.support().equals(questionType.getType())).findFirst().map(selector -> {
                log.info("使用标准的PromptSelector: {}", selector.getClass().getSimpleName());
                return selector;
            }).orElse(null));
        if (finalSelector == null) {
            log.error("没有找到对应的PromptSelector, 请检查配置");
            return null;
        }
        return finalSelector.select();
    }
}
