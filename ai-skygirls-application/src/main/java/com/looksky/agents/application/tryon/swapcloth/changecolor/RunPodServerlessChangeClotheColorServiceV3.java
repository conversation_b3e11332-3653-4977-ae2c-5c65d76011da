package com.looksky.agents.application.tryon.swapcloth.changecolor;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.data.client.business.TryOnClient;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.exception.BusinessException;
import com.looksky.agents.sdk.tryon.runpod.CreateTaskRequest;
import com.looksky.agents.sdk.tryon.runpod.CreateTaskResponse;
import com.looksky.agents.sdk.tryon.runpod.TaskResultResponse;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.SwapClothChangeClothColorRequest;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.changecolor.RunPodChangeColorRequest;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 1.1.8
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class RunPodServerlessChangeClotheColorServiceV3 {

    private final TryOnClient tryOnClient;

    @Value("${http.third-party.headers.runpod-api.api-key}")
    private String apiKey;


    private String pollingResult(String id, String apiKey) {


        long startTime = System.currentTimeMillis();
        long timeout = TimeUnit.MINUTES.toMillis(1);
        long remainingTime = timeout - (System.currentTimeMillis() - startTime);
        while (remainingTime > 0) {
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                log.error("等待过程中被中断", e);
                Thread.currentThread().interrupt();
                return null;
            }

            TaskResultResponse result = tryOnClient.runPodServerlessChangeColorStatus(apiKey, id);

            if (result != null && "COMPLETED".equals(result.getStatus())) {
                Optional<String> urlOptional = Optional.ofNullable(result.getOutput()).map(TaskResultResponse.Output::getUrl);
                if (urlOptional.isPresent()) {
                    return urlOptional.get();
                }
                throw new BusinessException(String.format("RunPod 换色处理完了, 但是没有返回 url, 响应: %s", JSONUtil.toJsonStr(result)));
            }

            remainingTime = timeout - (System.currentTimeMillis() - startTime);
        }

        log.error("换色任务超时, {}", id);

        return null;
    }

    /**
     * 换色
     * @return 换色后的图片地址
     */
    @CollectEvent
    public String changeClothColor(SwapClothChangeClothColorRequest request) {
        RunPodChangeColorRequest runPodChangeColorRequest = new RunPodChangeColorRequest();
        runPodChangeColorRequest.setColorHex(request.getColor());
        runPodChangeColorRequest.setImageUrl(request.getImageUrl());

        CreateTaskRequest taskRequest = CreateTaskRequest.builder()
            .input(runPodChangeColorRequest)
            .build();

        String key = "Bearer " + apiKey;

        CreateTaskResponse createTaskResponse = tryOnClient.runPodServerlessChangeColor(key, taskRequest);
        return pollingResult(createTaskResponse.getId(), key);
    }

}
