package com.looksky.agents.application.chat.reply.utils;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.looksky.agents.application.chat.recommend.PreferenceToSearchConverter;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.data.grpc.SearchGrpcClient;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.trace.annotation.TraceMethod;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.preference.SubjectivePreference;
import com.looksky.agents.sdk.agent.search.bo.OutfitSuggestionList;
import com.looksky.agents.sdk.agent.search.bo.RecommendOutfitBO;
import com.looksky.agents.sdk.agent.search.dto.OutfitProductPlans;
import com.looksky.agents.sdk.agent.search.dto.PlansDetail;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import com.looksky.agents.sdk.recommend.search.dto.response.SearchResponseDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 推荐请求构建工具
 *
 * <AUTHOR>
 * @since 1.1.14
 **/
@Component
@RequiredArgsConstructor
public class RecommendRequestHelper {

    private final SearchGrpcClient girlsAgentSearchGrpcClient;

    private final TagSystemTableService tagSystemTableService;

    @TraceMethod(description = "组装数据, 请求推荐推方案")
    public OutfitProductPlans request(OutfitProductPlans outfitProductPlans) {
        return requestRecommend(outfitProductPlans);
    }

    private OutfitProductPlans requestRecommend(OutfitProductPlans outfitProductPlans) {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());

        List<SearchRequestDTO.GirlsStrategyTerm> searchTerms = outfitProductPlans.getPlans().stream().map(this::convertToVectorQuery).toList();

        SearchRequestDTO searchRequestDTO = SearchRequestDTO.builder()
            .userId(requestInput.getUserId())
            .season(getSeason())
            .strategyTerms(searchTerms)
            .build();

        SearchResponseDTO searchResult = girlsAgentSearchGrpcClient.search(searchRequestDTO);

        if (ObjectUtil.isEmpty(searchResult) || ObjectUtil.isEmpty(searchResult.getStrategyResponses())) {
            return outfitProductPlans;
        }

        List<SearchResponseDTO.StrategyResponse> strategyResponses = searchResult.getStrategyResponses().stream().toList();

        outfitProductPlans.getPlans().forEach(plansDetail -> {
            strategyResponses.stream().filter(strategyResponse -> strategyResponse.getSearchStrategy().equals(plansDetail.getTitle())).findFirst().ifPresent(strategyResponse -> {
                plansDetail.setProductIdList(strategyResponse.getItems());
            });
        });

        // 过滤掉没有 skcId 的推荐
        List<PlansDetail> detailList = outfitProductPlans.getPlans().stream().filter(recommendOutfitDTO -> !ObjectUtil.isEmpty(recommendOutfitDTO.getProductIdList())).toList();
        outfitProductPlans.setPlans(detailList);
        return outfitProductPlans;
    }


    private SearchRequestDTO.GirlsStrategyTerm convertToVectorQuery(PlansDetail plansDetail) {
        SearchRequestDTO.GirlsStrategyTerm.GirlsStrategyTermBuilder builder = SearchRequestDTO.GirlsStrategyTerm.builder();

        ArrayList<SearchRequestDTO.VectorQuery> step1 = new ArrayList<>();
        ArrayList<SearchRequestDTO.VectorQuery> step2 = new ArrayList<>();


        SearchRequestDTO.SearchTerm searchTerm = SearchRequestDTO.SearchTerm.builder()
            .searchStrategy(plansDetail.getTitle())
            .build();

        // 设置 category
        Optional.ofNullable(plansDetail.getCategory()).map(c -> tagSystemTableService.convertToSubCategory(Set.of(c))).ifPresent(cs -> searchTerm.setCategories(new ArrayList<>(cs)));

        setTagValue(searchTerm, plansDetail.getTagPreference());

        builder.searchTerm(searchTerm);


        // 构建 step1
        step1.add(SearchRequestDTO.VectorQuery.builder()
            .text(plansDetail.getTitle())
            .weight(1)
            .build());

        builder.step1VectorQueries(step1);

        // 构建 step2. 使用 第三步的结果
        if (CharSequenceUtil.isNotBlank(plansDetail.getPositivePreferences())) {
            step2.add(SearchRequestDTO.VectorQuery.builder()
                .text(plansDetail.getTitle() + ", " + plansDetail.getPositivePreferences())
                .weight(1)
                .build());
        }
        if (CharSequenceUtil.isNotBlank(plansDetail.getNegativePreferences()) && !"null".equals(plansDetail.getNegativePreferences())) {
            step2.add(SearchRequestDTO.VectorQuery.builder()
                .text(plansDetail.getNegativePreferences())
                .weight(-1)
                .build());
        }


        if (ObjectUtil.isNotEmpty(step2)) {
            builder.step2VectorQueries(step2);
        }

        //ArrayList<SearchRequestDTO.VectorQuery> tags = new ArrayList<>();
        //// 设置正负向标签词
        //if (CharSequenceUtil.isNotBlank(recommendOutfitDTO.getPositiveLabels()) && !"null".equals(recommendOutfitDTO.getPositiveLabels())) {
        //    tags.add(SearchRequestDTO.VectorQuery.builder().text(recommendOutfitDTO.getPositiveLabels()).weight(1).build());
        //}
        //if (CharSequenceUtil.isNotBlank(recommendOutfitDTO.getNegativeLabels()) && !"null".equals(recommendOutfitDTO.getNegativeLabels())) {
        //    tags.add(SearchRequestDTO.VectorQuery.builder().text(recommendOutfitDTO.getNegativeLabels()).weight(-1).build());
        //}
        //if (ObjectUtil.isNotEmpty(tags)) {
        //    builder.tagsVectorQuery(tags);
        //}

        return builder.build();
    }

    private void setTagValue(SearchRequestDTO.SearchTerm searchTerm, ExtractedEntityObject tagPreference) {
        PreferenceToSearchConverter.convertToSearchTerm(searchTerm, tagPreference);
    }


    @TraceMethod(description = "组装数据, 请求推荐推方案")
    public List<RecommendOutfitBO> request(OutfitSuggestionList outfitSuggestionList) {
        var recommendOutfitBOS = setUuid(outfitSuggestionList);
        return requestRecommend(recommendOutfitBOS);
    }

    /**
     * 给方案都设置 UUID 并返回方案列表
     *
     * @param outfitSuggestionList 方案
     * @return 方案列表
     */
    private List<RecommendOutfitBO> setUuid(OutfitSuggestionList outfitSuggestionList) {
        outfitSuggestionList.getOutfitSuggestions().forEach(suggestion -> suggestion.setUuid(IdUtil.getSnowflakeNextIdStr()));
        return outfitSuggestionList.getOutfitSuggestions();
    }

    private List<RecommendOutfitBO> requestRecommend(List<RecommendOutfitBO> recommendOutfits) {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());

        List<SearchRequestDTO.GirlsStrategyTerm> searchTerms = recommendOutfits.stream().map(this::convertToVectorQuery).toList();

        SearchRequestDTO searchRequestDTO = SearchRequestDTO.builder()
            .userId(requestInput.getUserId())
            .season(getSeason())
            .strategyTerms(searchTerms)
            .build();

        SearchResponseDTO searchResult = girlsAgentSearchGrpcClient.search(searchRequestDTO);

        if (ObjectUtil.isEmpty(searchResult) || ObjectUtil.isEmpty(searchResult.getStrategyResponses())) {
            return recommendOutfits;
        }

        List<SearchResponseDTO.StrategyResponse> strategyResponses = searchResult.getStrategyResponses().stream().toList();

        recommendOutfits.forEach(recommendOutfitDTO -> {
            String uuid = recommendOutfitDTO.getUuid();
            strategyResponses.stream().filter(strategyResponse -> strategyResponse.getSearchStrategy().equals(uuid)).findFirst().ifPresent(strategyResponse -> {
                recommendOutfitDTO.setSkcIds(strategyResponse.getItems());
            });
        });

        // 过滤掉没有 skcId 的推荐
        return recommendOutfits.stream().filter(recommendOutfitDTO -> !ObjectUtil.isEmpty(recommendOutfitDTO.getSkcIds())).toList();
    }


    private SearchRequestDTO.GirlsStrategyTerm convertToVectorQuery(RecommendOutfitBO recommendOutfitDTO) {
        Set<String> category = tagSystemTableService.convertToSubCategory(Set.of(recommendOutfitDTO.getCategory()));
        SearchRequestDTO.GirlsStrategyTerm.GirlsStrategyTermBuilder builder = SearchRequestDTO.GirlsStrategyTerm.builder();

        ArrayList<SearchRequestDTO.VectorQuery> step1 = new ArrayList<>();
        ArrayList<SearchRequestDTO.VectorQuery> step2 = new ArrayList<>();

        Set<String> subCategorySet = tagSystemTableService.convertToSubCategory(category);

        SearchRequestDTO.SearchTerm searchTerm = SearchRequestDTO.SearchTerm.builder()
            .categories(new ArrayList<>(subCategorySet))
            .searchStrategy(recommendOutfitDTO.getUuid())
            .build();

        setTagValue(searchTerm);

        builder.searchTerm(searchTerm);


        // 构建 step1
        step1.add(SearchRequestDTO.VectorQuery.builder()
            .text(recommendOutfitDTO.getTitle())
            .weight(1)
            .build());

        builder.step1VectorQueries(step1);

        // 构建 step2. 使用 第三步的结果
        if (CharSequenceUtil.isNotBlank(recommendOutfitDTO.getPositivePreferences())) {
            step2.add(SearchRequestDTO.VectorQuery.builder()
                .text(recommendOutfitDTO.getTitle() + ", " + recommendOutfitDTO.getPositivePreferences())
                .weight(1)
                .build());
        }
        if (CharSequenceUtil.isNotBlank(recommendOutfitDTO.getNegativePreferences()) && !"null".equals(recommendOutfitDTO.getNegativePreferences())) {
            step2.add(SearchRequestDTO.VectorQuery.builder()
                .text(recommendOutfitDTO.getNegativePreferences())
                .weight(-1)
                .build());
        }


        if (ObjectUtil.isNotEmpty(step2)) {
            builder.step2VectorQueries(step2);
        }

        //ArrayList<SearchRequestDTO.VectorQuery> tags = new ArrayList<>();
        //// 设置正负向标签词
        //if (CharSequenceUtil.isNotBlank(recommendOutfitDTO.getPositiveLabels()) && !"null".equals(recommendOutfitDTO.getPositiveLabels())) {
        //    tags.add(SearchRequestDTO.VectorQuery.builder().text(recommendOutfitDTO.getPositiveLabels()).weight(1).build());
        //}
        //if (CharSequenceUtil.isNotBlank(recommendOutfitDTO.getNegativeLabels()) && !"null".equals(recommendOutfitDTO.getNegativeLabels())) {
        //    tags.add(SearchRequestDTO.VectorQuery.builder().text(recommendOutfitDTO.getNegativeLabels()).weight(-1).build());
        //}
        //if (ObjectUtil.isNotEmpty(tags)) {
        //    builder.tagsVectorQuery(tags);
        //}

        return builder.build();
    }


    /**
     * 设置标签值, 这是抽词阶段抽取到的标签值
     *
     * @param vectorQueries 需要设置的方案组
     */
    private void setTagValue(SearchRequestDTO.SearchTerm vectorQueries) {
        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());
        PreferenceToSearchConverter.convertToSearchTerm(vectorQueries, userPreference);
    }


    public String getSeason() {

        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());

        String season = null;

        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());
        SubjectivePreference subjectivePreference = userPreference.getSubjectivePreference();
        if (ObjectUtil.isNotEmpty(subjectivePreference)) {
            season = subjectivePreference.getSeason();
        }

        if (StrUtil.isEmptyIfStr(season)) {
            season = requestInput.getSeason();
        }

        if (StrUtil.isEmptyIfStr(season)) {
            season = null;
        }

        return season;
    }


}
