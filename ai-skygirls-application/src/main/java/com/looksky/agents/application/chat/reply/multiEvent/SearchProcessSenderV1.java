package com.looksky.agents.application.chat.reply.multiEvent;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.recommend.QueryAndSearchService;
import com.looksky.agents.application.chat.recommend.SearchRecommendService;
import com.looksky.agents.data.redis.recommend.RecommendProductsDataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import com.looksky.agents.sdk.recommend.search.dto.response.SearchResponseDTO;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @ClassName CategorySelectorMessageSender
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 下午10:29
 * @Version 1.0
 **/
@Slf4j
@Order(1)
@Component
@ApiVersion(platform = ApiVersion.PlatformType.IOS, version = "1.1.0")
@ApiVersion(platform = ApiVersion.PlatformType.ANDROID, version = "1.1.9")
public class SearchProcessSenderV1 extends MultiEventMessageSender {

    @Resource
    private SearchRecommendService searchRecommendService;

    @Resource
    private QueryAndSearchService queryAndSearchService;

    @Resource
    private RecommendProductsDataService recommendProductsDataService;

    @Override
    protected boolean supports(PromptModel strategy) {
        return PromptNameEnum.BUILD_SEARCH_PROCESS.getName().equals(strategy.getName());
    }


    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {

        sendLoading();

        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());

        VirtualCompletableFuture<QueryAndSearchService.NewQueryAndIsSearch> newQueryAndIsSearchFuture = queryAndSearchService.generateNewQueryAndIsSearchAsync();

        // 获取新 query 和 是否需要搜索
        QueryAndSearchService.NewQueryAndIsSearch newQueryAndIsSearch = newQueryAndIsSearchFuture.join();
        Context.put("recommend_new_query", newQueryAndIsSearch.getQuery());

        log.info("当前的会话状态: {}", JSONUtil.toJsonStr(conversationStatus));

         if (newQueryAndIsSearch.isNeedSearch()) {
                queryAndSearchService.searchNewQuery(newQueryAndIsSearch);
         }

         // 生成思考过程
        String block = sendThinkStreamMessageNoEmoji("thinking_about_recommended_products", Collections.emptyMap()).block();

         log.info("DeepSeek 回复: \n{}", block);


        Context.put(Context.Name.SEARCH_PROCESS.getName(), block);

        // 生成正负向词
        VirtualCompletableFuture<SearchResponseDTO> recommendResult = searchRecommendService.recommendAsync();

        List<ItemDTO> recommendations = new ArrayList<>();
        boolean isDefault = true;

        try {
            SearchResponseDTO recommendSearchResponseDTO = recommendResult.get();
            recommendations.addAll(recommendSearchResponseDTO.getItems());
            isDefault = recommendSearchResponseDTO.isDefault();
        } catch (Exception e) {
            log.error("调用推荐系统失败", e);
        }

        if (ObjectUtil.isEmpty(recommendations)) {
            noProductIds(hashMap);
        } else {
            sendIds(recommendations);
        }
        //else {
        //    // 获取商品信息
        //    girlsDataService.getProductInfo(recommendations);
        //
        //    // 判断是否为兜底策略
        //    if (isDefault) {
        //        sendABackdropMessage(hashMap, recommendations);
        //    } else {
        //        sendNormalMessage(hashMap, recommendations);
        //    }
        //}
        conversationStatus.setRecommendProduct(true);

        recommendProductsDataService.save(recommendations.stream().map(ItemDTO::getItemId).toList());
    }


    // 找到了商品, 但是是推荐的兜底商品
    private void sendABackdropMessage(HashMap<String, Object> hashMap,
                                      List<ItemDTO> recommendations) {
        String finalText = sendStreamMessage("recommend_summary", Collections.emptyMap()).block();
        sendIds(recommendations);
        extraMessage(EventTypeEnum.TEXT.getType(), finalText, hashMap);

    }

    // 发送普通消息
    private void sendNormalMessage(HashMap<String, Object> hashMap, List<ItemDTO> recommendations) {
        String finalText = sendStreamMessage("recommend_summary", Collections.emptyMap()).block();
        sendIds(recommendations);
        extraMessage(EventTypeEnum.TEXT.getType(), finalText, hashMap);
    }


    // 没有找到商品的情况
    private void noProductIds(HashMap<String, Object> hashMap) {
        String finalMessageStr = sendStreamMessage("query_no_products", Collections.emptyMap()).block();
        extraMessage(EventTypeEnum.TEXT.getType(), finalMessageStr, hashMap);
    }

    // 发送商品
    private AgentMessageResp sendIds(List<ItemDTO> ids) {
        // 发送给前端
        AgentMessageResp agentMessageResp = initMessage(EventTypeEnum.PRODUCT_ID_LIST.getType());
        sendContent(agentMessageResp, EventTypeEnum.PRODUCT_ID_LIST.getType(), ids);
        return agentMessageResp;
    }


}
