package com.looksky.agents.application.prompt;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.template.FreemarkerService;
import com.looksky.agents.sdk.agent.prompt.model.PromptMetadataModel;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import com.looksky.agents.data.mysql.service.IPromptMetadataService;
import com.looksky.agents.data.mysql.service.IPromptService;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * @ClassName PromptConvert2RequestBo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/21 下午2:38
 * @Version 1.0
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class PromptBusinessService {
    private final IPromptService promptService;
    private final IPromptMetadataService metadataService;
    private final FreemarkerService freemarkerService;


    public PromptModel getPromptModelByNameWithMetaData(String promptName) {
        PromptModel promptModel = promptService.getPromptModelByName(promptName);
        PromptModel copy = promptModel.copy();
        setMetaData(copy);
        return copy;
    }

    public PromptModel getPromptModelByIdWithMetaData(String promptId) {
        PromptModel promptModel = promptService.getById(promptId);
        PromptModel copy = promptModel.copy();
        setMetaData(copy);
        return copy;
    }

    public RequestParamBO convert(PromptModel pm) {
        return convert(pm, new HashMap<>());
    }

    public RequestParamBO convert(PromptModel pm, Map<String, Object> variables) {
        RequestParamBO rpb = new RequestParamBO();
        try {
            BeanUtil.copyProperties(pm, rpb);
            String prompt = freemarkerService.parseTemplate(pm.getUserPrompt(), variables);
            rpb.setUserPrompt(prompt);

            String systemPrompt = freemarkerService.parseTemplate(pm.getSystemPrompt(), variables);
            rpb.setSystemPrompt(systemPrompt);

            String jsonSchema = freemarkerService.parseTemplate(pm.getJsonSchema(), variables);
            rpb.setJsonSchema(jsonSchema);

            rpb.setSize(pm.getImageSize());

            rpb.setPromptName(pm.getName());
            rpb.setDescription(pm.getDescription());
        } catch (Exception e) {
            String errorMessage = CharSequenceUtil.format("{} prompt渲染失败, bo对象", pm.getName(), JSONUtil.toJsonStr(rpb));
            log.error(errorMessage, e);
            throw new RuntimeException(errorMessage, e);
        }

        log.info("{} prompt渲染成功, bo对象: {}", pm.getName(), JSONUtil.toJsonStr(rpb));

        return rpb;
    }

    public void setMetaData(PromptModel pm) {
        if (StringUtils.hasText(pm.getId())) {
            List<PromptMetadataModel> metaData = metadataService.getMetaDataByPromptId(pm.getId());
            pm.setMetadataList(metaData);
        }
    }

    public PromptModel findPromptByMetadata(String value) {
        if (!StringUtils.hasText(value)) {
            return null;
        }
        PromptMetadataModel metadata = metadataService.findMetadata(PromptMetadataModel.tagValueMetaModel(value));
        if (metadata == null) {
            return null;
        }
        PromptModel promptModel = promptService.getById(metadata.getPromptId());
        if (promptModel == null) {
            return null;
        }
        promptModel.setMetadataList(Collections.singletonList(metadata));
        return promptModel;
    }
}
