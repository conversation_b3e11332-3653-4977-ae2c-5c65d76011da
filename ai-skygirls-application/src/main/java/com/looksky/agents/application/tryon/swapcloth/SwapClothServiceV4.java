package com.looksky.agents.application.tryon.swapcloth;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.tryon.swapcloth.convertor.TryOnConvertor;
import com.looksky.agents.common.model.tryOn.TryOnFashnKeyConfig;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.data.client.business.TryOnClient;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothParam;
import com.looksky.agents.sdk.tryon.swapcloth.dto.response.FashnRunResponse;
import com.looksky.agents.sdk.tryon.swapcloth.enums.SwapClothStatusEnum;
import com.skygirls.biz.user.tryon.dto.SearchTryOnStatusReq;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SwapClothServiceV4 {

    private final TryOnClient tryOnClient;
    private final TryOnConvertor tryOnConvertor;
    private final Random random = new Random();
    private final GirlsClient girlsClient;

    private final TryOnFashnKeyConfig tryOnFashnKeyConfig;


    public Boolean asyncSwapCloth(SwapClothParam swapClothParam) {

        log.info("{}开始换装, 换装参数: skcId: {}", swapClothParam.getId(), swapClothParam.getSkcId());

        VirtualCompletableFuture.runAsync(() -> {
            try {
                create(swapClothParam);
            } catch (Exception e) {
                log.error("换装失败", e);
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException ex) {
                    log.warn("tryOn 睡眠发生异常");
                    Thread.currentThread().interrupt();
                }
                callback(swapClothParam, SwapClothStatusEnum.FAILED, null);
            }
        });
        return true;
    }


    @SneakyThrows
    @CollectEvent
    public String create(SwapClothParam tryOnParam) {

        log.info("开始提交换装任务: {}", JSONUtil.toJsonStr(tryOnParam));

        List<String> apiKeyList = tryOnFashnKeyConfig.getApiKeyList();

        String apiKey = apiKeyList.get(random.nextInt(apiKeyList.size()));

        HashMap<String, Object> requestBody = new HashMap<>(Map.of(
            "model_image", tryOnParam.getFullBodyImage(),
            "garment_image", tryOnParam.getModelImage(),
            "category", tryOnParam.getCategory(),
            "mode", "balanced",
            "seed", random.nextInt(Integer.MAX_VALUE),
            "num_samples", 1,
            "moderation_level", "none",
            "segmentation_free", false,
            "garment_photo_type", tryOnParam.getType()
        ));

        FashnRunResponse fashnRunResponse = tryOnClient.tryOnRun(apiKey, requestBody);

        log.info("已经提交换装任务: {}", JSONUtil.toJsonStr(requestBody));


        String result = pollingResult(fashnRunResponse.getId(), apiKey);

        if (CharSequenceUtil.isNotBlank(result)) {
            callback(tryOnParam, SwapClothStatusEnum.COMPLETED, result);
        } else {
            log.error("换装任务失败, {}", JSONUtil.toJsonStr(tryOnParam));
            TimeUnit.SECONDS.sleep(1);
            callback(tryOnParam, SwapClothStatusEnum.FAILED, null);
        }

        return result;


    }

    private String pollingResult(String id, String apiKey) {

        // 先睡 15s
        try {
            TimeUnit.SECONDS.sleep(3);
        } catch (InterruptedException e) {
            log.error("等待过程中被中断", e);
            Thread.currentThread().interrupt();
            return null;
        }

        long startTime = System.currentTimeMillis();
        long timeout = TimeUnit.MINUTES.toMillis(1);
        long remainingTime = timeout - (System.currentTimeMillis() - startTime);
        while (remainingTime > 0) {
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                log.error("等待过程中被中断", e);
                Thread.currentThread().interrupt();
                return null;
            }

            FashnRunResponse fashnRunResponse = tryOnClient.truOnStatus(apiKey, id);
            log.info("换装完整响应: {}", JSONUtil.toJsonStr(fashnRunResponse));
            if (fashnRunResponse.getStatus().equals("completed")) {
                return fashnRunResponse.getOutput().getFirst();
            } else if (fashnRunResponse.getStatus().equals("failed")) {
                log.error("换装失败, 服务商返回: {}", JSONUtil.toJsonStr(fashnRunResponse));
                return null;
            }

            remainingTime = timeout - (System.currentTimeMillis() - startTime);
        }

        log.error("换装任务超时, {}", id);

        return null;
    }

    private void callback(SwapClothParam swapClothParam, SwapClothStatusEnum status, String resultImage) {
        SearchTryOnStatusReq callbackRequest = tryOnConvertor.toCallbackRequest(swapClothParam, status, resultImage);
        girlsClient.tryonCallback(callbackRequest);
    }
}
