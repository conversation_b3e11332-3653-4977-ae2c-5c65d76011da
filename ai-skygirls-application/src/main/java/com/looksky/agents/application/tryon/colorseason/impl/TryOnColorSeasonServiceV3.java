package com.looksky.agents.application.tryon.colorseason.impl;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.tryon.colorseason.ITryOnColorSeasonService;
import com.looksky.agents.application.tryon.colorseason.convertor.TryOnColorSeasonConvertor;
import com.looksky.agents.data.client.business.TryOnClient;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.exception.BusinessException;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonParamV1;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonRequestV1;
import com.looksky.agents.sdk.tryon.runpod.CreateTaskRequest;
import com.looksky.agents.sdk.tryon.runpod.CreateTaskResponse;
import com.looksky.agents.sdk.tryon.runpod.TaskResultResponse;
import java.time.Duration;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;


/**
 * colorSeason 试色 service, RunPod Serverless 版本
 *
 * <AUTHOR>
 * @since 1.1.8
 **/
@Slf4j
@Service
@RequiredArgsConstructor
@Order(60) // 优先级3，数字越小优先级越高
public class TryOnColorSeasonServiceV3 implements ITryOnColorSeasonService {

    private final TryOnClient tryOnClient;
    private final TryOnColorSeasonConvertor convertor;
    private final Random random = new Random();

    @Value("${http.third-party.headers.runpod-api.api-key}")
    private String apiKey;

    @Override
    public Duration getTimeout() {
        // 默认超时时间30秒
        return Duration.ofSeconds(25);
    }

    @CollectEvent
    @Override
    public String tryOnWhiteT(TryOnColorSeasonParamV1 param) {
        try {
            TryOnColorSeasonRequestV1 input = convertor.toTryOnColorSeasonRequest(param);
            input.setSeed(random.nextInt(Integer.MAX_VALUE));

            CreateTaskRequest request = CreateTaskRequest.builder()
                .input(input)
                .build();

            String key = "Bearer " + apiKey;

            CreateTaskResponse createTaskResponse = tryOnClient.tryOnWhiteT(key, request);
            return pollingResult(createTaskResponse.getId(), key);
        } catch (Exception e) {
            log.error("V3服务执行失败", e);
            return null;
        }
    }

    private String pollingResult(String id, String apiKey) {

        // 先睡 10s
        try {
            TimeUnit.SECONDS.sleep(10);
        } catch (InterruptedException e) {
            log.error("等待过程中被中断", e);
            Thread.currentThread().interrupt();
            return null;
        }

        long startTime = System.currentTimeMillis();
        long timeout = TimeUnit.MINUTES.toMillis(1);
        long remainingTime = timeout - (System.currentTimeMillis() - startTime);
        while (remainingTime > 0) {
            try {
                TimeUnit.SECONDS.sleep(2);
            } catch (InterruptedException e) {
                log.error("等待过程中被中断", e);
                Thread.currentThread().interrupt();
                return null;
            }

            TaskResultResponse result = tryOnClient.tryOnWhiteTStatus(apiKey, id);

            if (result != null && "COMPLETED".equals(result.getStatus())) {
                Optional<String> urlOptional = Optional.ofNullable(result.getOutput()).map(TaskResultResponse.Output::getUrl);
                if (urlOptional.isPresent()) {
                    return urlOptional.get();
                }
                throw new BusinessException(String.format("RunPod 换白 T处理完了, 但是没有返回 url, 响应: %s", JSONUtil.toJsonStr(result)));
            }

            remainingTime = timeout - (System.currentTimeMillis() - startTime);
        }

        log.error("换白 T任务超时, {}", id);

        return null;
    }
}
