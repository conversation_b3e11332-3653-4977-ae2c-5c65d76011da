package com.looksky.agents.application.chat.reply;

import com.looksky.agents.infrastructure.trace.annotation.TraceMethod;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * @ClassName SendReplyService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/29 下午6:40
 * @Version 1.0
 **/
@Component
@RequiredArgsConstructor
public class SendReplyService {

    private final MessageSenderFactory senderFactory;

    public List<String> sendMessage(PromptModel strategy) {
        AbstractMessageSender sender = senderFactory.getSender(strategy);
        return sender.send(strategy);
    }

}
