package com.looksky.agents.application.chat.reply.stream;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.application.chat.reply.AbstractMessageSender;
import com.looksky.agents.common.utils.BooleanUtils;
import com.looksky.agents.data.client.business.DataHubClient;
import com.looksky.agents.data.redis.conversation.StatusDataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.user.context.UserContext;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.dto.ShortcutInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.common.enums.EventNameEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.common.enums.PageEnum;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.datahub.message.dto.MsgReq;
import com.looksky.agents.sdk.datahub.message.enums.DirectionEnum;
import com.skygirls.biz.im.model.MessageModel;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Order(10)
@Component
public class SearchOpeningMessageSender extends AbstractMessageSender {

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private StatusDataService statusDataService;

    @Resource
    private DataHubClient dataHubClient;

    private static final List<String> CHECK_EVENT_NAMES = List.of("user_speak", "occasion_outfit", "season_style", "find_by_category");

    private static final String TITLE = "Try asking like this";



    @Override
    protected boolean supports(PromptModel strategy) {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());
        return PageEnum.SEARCH.getName().equals(requestInput.getPage()) && EnterPointEnum.DRESSING == requestInput.getEnterPointEnum() && EventNameEnum.OPENING.getValue().equals(requestInput.getEvent().getEventName());
    }


    @Override
    @SneakyThrows
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {

        String result;
        // 老客
        if (BooleanUtils.isTrue(statusDataService.getUserFirstSendMessageFlag(UserContext.getUserId()))) {
            result = commonRequestService.commonExecuteStrategy(strategy, MapUtil.empty());
        } else {
            // 新客
            result = commonRequestService.commonExecuteStrategy("search_new_user_dressing_opening_and_ask");
        }


        // 获取回复的消息和快捷输入

        MessageAndAsk messageAndAsk = objectMapper.readValue(result, MessageAndAsk.class);

        // 发送回复的消息
        sendContent(initMessage(EventTypeEnum.TEXT.getType()), EventTypeEnum.TEXT.getType(), messageAndAsk.openingMessage());

        // 发送快捷输入
        sendContent(initMessage(EventTypeEnum.SHORTCUT_INPUT.getType()), EventTypeEnum.SHORTCUT_INPUT.getType(), ShortcutInputDTO.builder().title(TITLE).tips(messageAndAsk.prompts()).build());

    }

    record MessageAndAsk(String openingMessage, List<String> prompts) {
    }


    private boolean checkUserFirstSendMessage() {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());
        String userId = requestInput.getUserId();

        // 调用递归方法从第1页开始检查
        return checkMessagesByPage(userId, 1);
    }

    /**
     * 递归检查用户历史消息中是否包含特定事件名
     *
     * @param userId 用户ID
     * @param pageNo 当前页码
     * @return true 如果没有找到特定事件名; false 如果找到
     */
    private boolean checkMessagesByPage(String userId, int pageNo) {
        // 需要检查的事件名称列表


        // 获取当前页的消息
        Page<MessageModel> messagePage = getMessageModelPage(userId, pageNo);

        // 如果页面为空或没有记录，结束递归
        if (messagePage == null || messagePage.getTotal() == 0) {
            return false;
        }

        List<MessageModel> messages = messagePage.getRecords();

        // 检查当前页的所有消息
        for (MessageModel message : messages) {
            String eventName = message.getEventName();
            if (eventName != null && CHECK_EVENT_NAMES.contains(eventName)) {
                log.debug("在第{}页找到匹配的事件名: {}", pageNo, eventName);
                return true;  // 找到匹配的事件名，返回false
            }
        }

        // 检查是否有下一页
        long pages = messagePage.getPages();

        if (pageNo < pages && pageNo < 5) {
            // 递归检查下一页
            log.debug("检查第{}页，未找到匹配事件，继续检查下一页", pageNo);
            return checkMessagesByPage(userId, pageNo + 1);
        }

        // 所有页面都已检查完毕，未找到匹配的事件名
        log.debug("所有{}页消息都已检查完毕，未找到匹配的事件名", pageNo);
        return true;
    }


    private Page<MessageModel> getMessageModelPage(String userId, int pageNo) {
        MsgReq msgReq = new MsgReq()
            .setDirection(DirectionEnum.SEND.getName())
            .setUserId(userId)
            .setPage(PageEnum.SEARCH.getName())
            .setPageNo(pageNo)
            .setEnterPoint(EnterPointEnum.DRESSING.getValue())
            .setPageSize(100);
        return dataHubClient.messageHistory(msgReq).getData();
    }


}