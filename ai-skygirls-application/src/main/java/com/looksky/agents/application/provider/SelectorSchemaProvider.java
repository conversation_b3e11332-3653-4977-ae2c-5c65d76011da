package com.looksky.agents.application.provider;

import com.looksky.agents.sdk.agent.common.enums.TagValueEnum;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.preference.CategoryAndTagPreference;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.infrastructure.context.Context;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @ClassName SelectorSchemaBuilder
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/27 上午11:19
 * @Version 1.0
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class SelectorSchemaProvider implements DataProvider {

    private final TagSystemTableService tagService;

    private static final List<String> NO_USER_LABEL = Arrays.asList(
        "trims_position", "design_detail_position", "pocket_type", "pocket_shape",
        "pocket_amt", "pocket_position", "other_design", "closure_type",
        "closure_position", "painting_style", "specific_pattern_type",
        "specific_pattern_arrangement", "specific_pattern_scale", "size_type",
        "transparency", "cuff", "elasticity", "back_height", "waistline",
        "back_shape", "back_detail", "process"
    );


    /**
     * 构建标签选择器需要的数据
     * @return tag 和 tag value
     */
    private Map<String, List<String>> build() {
        log.info("开始构建标签选择器的数据");
        return getTagAndOptionsByCategory();
    }


    /**
     * 获取标签选择器的可选标签
     * @return 标签选择器的可选标签
     */
    private Map<String, List<String>> getTagAndOptionsByCategory() {
        log.info("开始获取标签选择器的可选标签");

        // 获取用户在会话中的偏好
        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());

        log.info("用户在会话中的偏好为: {}", userPreference);

        // 获取类别列表
        List<String> categoryList = new ArrayList<>(userPreference.getCurrentCategories());

        log.info("用户当前类别列表为: {}", categoryList);

        // 获取当前表达的品类
        String category = tagService.obtainTheSmallestCategory(categoryList);

        // 获取标签体系中, 该品类下的所有标签和标签值
        Map<String, List<String>> allTagMap = tagService.getTagAndTagValueByCategory(category);
        CategoryAndTagPreference categoryAndTagPreference = userPreference.getCategoryAndTagPreference();
        categoryAndTagPreference.getTags().forEach(allTagMap::remove);

        return normLabelDict(allTagMap);
    }

    private Map<String, List<String>> normLabelDict(Map<String, List<String>> labelMap) {
        Map<String, List<String>> normLabelMap = new HashMap<>();

        labelMap.forEach((label, values) -> {
            if (NO_USER_LABEL.contains(label)) {
                return;
            }

//            String innerParentTag = tagService.getInnerParentTag(label);
            // value 可能需要筛选一下
            normLabelMap.computeIfAbsent(label, k -> new ArrayList<>()).addAll(filterOther(values));
        });

        log.info("最终返回的值为: {}", normLabelMap);

        return normLabelMap;
    }

    private List<String> filterOther(List<String> list) {
        return list.stream().filter(v -> !TagValueEnum.OTHER.getName().equals(v)).toList();
    }

    @Override
    public boolean supports(String namespace) {
        return "selectorSchema".equals(namespace);
    }

    @Override
    public void getData() {
        Map<String, List<String>> tagAndTagValues = build();
        Context.put("tagOptionsMap", tagAndTagValues);
    }
}
