package com.looksky.agents.application.evaluate.tryon;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import com.looksky.agents.sdk.optimize.dto.OptimizePromptResultDTO;
import java.util.Map;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 1.1.8
 **/
@Slf4j
@Component
public class GeminiTryOnAtomComponent {

    public OptimizePromptResultDTO run(DatasetRecordDTO datasetRecord) {
        long start = System.currentTimeMillis();
        Map<String, Object> map = Map.of("image_url", datasetRecord.getData().get("image_url"),
            "text_input", datasetRecord.getData().get("text_input"));
        try (HttpResponse response = HttpRequest.post("http://113.45.68.96:3010/generate-image").body(JSONUtil.toJsonStr(map)).execute()) {
            String result = response.body();
            log.info("请求结果: {}", result);
            ImageGenerationResponse bean = JSONUtil.toBean(result, ImageGenerationResponse.class);
            long end = System.currentTimeMillis();
            return new OptimizePromptResultDTO(bean.getImage_url(), 0L, end - start);
        } catch (Exception e) {
            log.error("请求失败: {}", e.getMessage());
            return new OptimizePromptResultDTO(e.getMessage(), 0L, 0L);
        }

    }

    @Data
    static class ImageGenerationResponse {
        private String text;
        private String image_url;
        private String error;
    }

}
