package com.looksky.agents.application.chat.reply.multiEvent;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.application.chat.recommend.QueryAndSearchService;
import com.looksky.agents.application.chat.reply.utils.RecommendRequestHelper;
import com.looksky.agents.data.client.utils.CommunicationHelper;
import com.looksky.agents.data.redis.conversation.HistoryDataService;
import com.looksky.agents.data.redis.recommend.RecommendProductsDataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.exception.BusinessException;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import com.looksky.agents.sdk.agent.common.dto.ThinkMessageDTO;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.search.bo.OutfitSuggestionList;
import com.looksky.agents.sdk.agent.search.bo.RecommendOutfitBO;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 推商品, 将推商品改为推方案
 *
 * @since  1.2.1
 * <AUTHOR>
 **/
@Slf4j
@Order(1)
@Component
@ApiVersion(platform = ApiVersion.PlatformType.IOS, version = "1.1.0")
@ApiVersion(platform = ApiVersion.PlatformType.ANDROID, version = "1.1.9")
public class SearchProcessSenderV2 extends MultiEventMessageSender {

    @Resource
    private QueryAndSearchService queryAndSearchService;

    @Resource
    private RecommendProductsDataService recommendProductsDataService;

    @Resource
    private RecommendRequestHelper recommendRequestHelper;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private HistoryDataService historyDataService;

    @Resource
    private CommunicationHelper communicationHelper;

    private static final String SEARCH_CONTENT = "Just a sec—I’m scanning the latest trends just for you to style your perfect look! ✨Curating 100% personalized picks based on your unique taste…";

    @Lazy
    @Resource
    private SearchProcessSenderV2 self;

    @Override
    protected boolean supports(PromptModel strategy) {
        return PromptNameEnum.BUILD_SEARCH_PROCESS.getName().equals(strategy.getName());
    }


    @SneakyThrows
    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {

        communicationHelper.sendLoading();

        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());

        // 获取新 query 和 是否需要搜索
        QueryAndSearchService.NewQueryAndIsSearch newQueryAndIsSearch = queryAndSearchService.generateNewQueryAndIsSearchAsync().join();
        Context.put("recommend_new_query", newQueryAndIsSearch.getQuery());

        log.info("当前的会话状态: {}", JSONUtil.toJsonStr(conversationStatus));

        // 如果需要在线搜索, 那么执行在线搜索
        AgentMessageResp message = null;
        if (newQueryAndIsSearch.isNeedSearch()) {
            ThinkMessageDTO thinkMessage = new ThinkMessageDTO();
            thinkMessage.setThinkContent(SEARCH_CONTENT);
            message = communicationHelper.sendMessage(EventTypeEnum.THINK, thinkMessage, false);
            queryAndSearchService.searchNewQuery(newQueryAndIsSearch);
        }

        String block;
        if (message != null) {
            // 生成思考过程
            block = sendThinkStreamMessageNoEmoji(message, "thinking_about_recommended_products", Collections.emptyMap()).block();
        } else {
            block = sendThinkStreamMessageNoEmoji("thinking_about_recommended_products", Collections.emptyMap()).block();
        }

        Optional.ofNullable(block).orElseThrow(() -> new BusinessException("DeepSeek 回复为空"));

        historyDataService.saveAgentHistory(block);

        log.debug("DeepSeek 回复: \n{}", block);


        Context.put(Context.Name.SEARCH_PROCESS.getName(), block);

        // 生成正负向词
        String result = commonRequestService.commonExecuteStrategy("structured_output_outfit_solution_3options", Map.of("input", block));
        // 请求推荐
        OutfitSuggestionList outfitSuggestionList = objectMapper.readValue(result, OutfitSuggestionList.class);

        List<RecommendOutfitBO> recommendOutfitDTOS = ListUtil.empty();

        try {
            recommendOutfitDTOS = recommendRequestHelper.request(outfitSuggestionList);
        } catch (Exception e) {
            log.error("推荐商品方案失败", e);
        }


        if (ObjectUtil.isEmpty(recommendOutfitDTOS) || ObjectUtil.isEmpty(recommendOutfitDTOS.getFirst().getSkcIds())) {
            log.warn("推荐商品方案为空, returnContents: {}", JSONUtil.toJsonStr(recommendOutfitDTOS));
            noProductIds();
            return;
        }

        // 发送商品的方案
        communicationHelper.sendMessage(EventTypeEnum.CONFIRM_SOLUTION, recommendOutfitDTOS);

        conversationStatus.setRecommendProduct(true);

        // 保存推的商品到 redis
        saveRecommendProduct(recommendOutfitDTOS);

    }

    // 没有找到商品的情况
    private void noProductIds() {
        String finalMessageStr = sendStreamMessage("query_no_products", Collections.emptyMap()).block();
        historyDataService.saveUserHistory(finalMessageStr);
    }

    /**
     * 保存推的商品到redis
     *
     * @param recommendOutfitDTOS 商品方案
     */
    private void saveRecommendProduct(List<RecommendOutfitBO> recommendOutfitDTOS) {
        // 保存推的商品到 redis
        List<String> allSkcIds = new ArrayList<>();
        for (RecommendOutfitBO outfit : recommendOutfitDTOS) {
            allSkcIds.addAll(outfit.getAllSkcIds());
        }

        recommendProductsDataService.save(allSkcIds);
    }

}
