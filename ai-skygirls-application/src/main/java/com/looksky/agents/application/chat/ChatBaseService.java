package com.looksky.agents.application.chat;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.reply.SendReplyService;
import com.looksky.agents.application.chat.scene.SceneSelectorManager;
import com.looksky.agents.data.client.business.PyAgentClient;
import com.looksky.agents.data.client.service.GirlsDataService;
import com.looksky.agents.data.client.service.LookSkyDataService;
import com.looksky.agents.data.redis.conversation.EventConvertService;
import com.looksky.agents.data.redis.conversation.HistoryDataService;
import com.looksky.agents.data.redis.conversation.PreferenceDataService;
import com.looksky.agents.data.redis.conversation.StatusDataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.exception.FeiShuNotifyHelper;
import com.looksky.agents.infrastructure.user.context.UserContext;
import com.looksky.agents.infrastructure.trace.annotation.TraceMethod;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.common.enums.EventNameEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.common.enums.PageEnum;
import com.looksky.agents.sdk.agent.common.enums.RoleTypeEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.conversation.Event;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.ext.RequestVo;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.utils.date.DateUtils;
import com.skygirls.biz.im.dto.MessageRestDTO;
import com.skygirls.biz.im.dto.MessageTypeEnum;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @ClassName ChatBaseService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/7 下午10:14
 * @Version 1.0
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatBaseService {

    private final SendReplyService replyService;

    private final EventConvertService convertEvent;

    private final GirlsDataService girlsDataService;
    private final HistoryDataService historyDataService;
    private final LookSkyDataService lookSkyDataService;

    private final SceneSelectorManager strategySelector;

    private final StatusDataService statusDataService;
    private final PreferenceDataService preferenceDataService;
    private final PyAgentClient pyAgentClient;
    private final FeiShuNotifyHelper feiShuNotifyHelper;

    @Lazy
    @Resource
    private ChatBaseService self;

    private static final List<String> CHECK_EVENT_NAMES = List.of("user_speak", "occasion_outfit", "season_style", "find_by_category");



    private boolean isDetail(RequestVo request) {

        if (!PageEnum.CHAT_BOX.getName().equals(request.getPage())) {
            return false;
        }

        String event = request.getEvent();

        if (!EventNameEnum.USER_SPEAK.getValue().equals(event) && !EventNameEnum.detailEvent(event)) {
            // 做映射
            request.setEvent(EventNameEnum.USER_CLICK.getValue());
            MessageRestDTO.EventDict eventDict = request.getEventDict();
            eventDict.setQuestionType(event);
            request.setEventDict(eventDict);
        }

        pyAgentClient.sendAgentMessage(request);

        return true;
    }


    @Async
    @TraceMethod(description = "接收到用户对话请求")
    public void chat(RequestVo request) {
        log.info(" chat 请求体 -------------> : {}", JSONUtil.toJsonStr(request));
        if (isDetail(request)) {
            return;
        }
        try {
            checkUserFirstSendMessageFlag(request.getEvent());

            // 初始化, 设置消息 ID
            RequestInputDTO requestInput = self.buildData(request);
            asStep(requestInput);
        } catch (Exception e) {
            log.error("agent chat 发生异常", e);
            sendErrorMessage();
            feiShuNotifyHelper.notify(e.getMessage());
        } finally {
            Context.clear();
        }
    }


    public RequestInputDTO buildData(RequestVo request) {

        // 获取用户信息
        lookSkyDataService.getUserInfo(request.getUserId());

        // 转换 event
        Event event = convertEvent.convert(request.getEvent(), request.getEventDict(), request.getMessageId(), request.getPage(), request.getEnterPoint());
        Context.put(Context.Name.EVENT.getName(), event);

        MessageTypeEnum messageTypeEnum = request.getEventDict() != null ? request.getEventDict().getMessageType() : null;


        String date = DateUtils.getDateTimeByMilliSeconds(request.getTime(), request.getZone());

        RequestInputDTO requestInput = RequestInputDTO.builder()
            .enterPointEnum(EnterPointEnum.fromValue(request.getEnterPoint(), messageTypeEnum))
            .messageId(request.getMessageId())
            .conversationId(request.getConversationId())
//                .user(userInfo)
            .userId(request.getUserId())
            .requestId(request.getRequestId())
            .event(event)
            .zone(request.getZone())
            .page(request.getPage())
            .eventDict(request.getEventDict())
            //.product(product)
            //.products(products)
            .connectionId(request.getConnectionId())
            .strategyName(request.getStrategyName())
            //.brand(brand)
            .date(date)
//                .season(request.getSeason())
            .city(request.getCity())
            .messageType(messageTypeEnum)
//                .isRetry(request.isRetry())
//                .status(status)
            .build();

        Context.put(Context.Name.REQUEST_INPUT.getName(), requestInput);

        loadConversationData(request);

        return requestInput;
    }

    private void checkUserFirstSendMessageFlag(String event) {
        if (CHECK_EVENT_NAMES.contains(event)) {
            // 用户级维度保存活跃状态为"是"
            statusDataService.saveFirstUserSendMessageFlag(UserContext.getUserId());
            log.info("保存用户状态为发送过消息: {}", event);
        }
    }


    private void loadConversationData(RequestVo requestVo) {
        String event = requestVo.getEvent();

        if (EventNameEnum.isClickButton(event)) {
            statusDataService.newConversation(true);
            return;
        }
        // 获取会话状态
        statusDataService.getStatus();
        // 获取历史消息
        historyDataService.getPrevHistory();
        // 获取偏好数据
        preferenceDataService.getPreference();

        // 保存用户消息
        Optional.ofNullable(requestVo.getEventDict().getText()).ifPresent(t -> historyDataService.saveHistory(RoleTypeEnum.USER, t));
    }




    public void asStep(RequestInputDTO requestInput) {
        // 策略选择
        PromptModel strategy = strategySelector.getPromptModel(requestInput);

        if (strategy == null) {
            return;
        }

        executeStrategy(strategy, requestInput);


        updateConversationInfo();

    }

    private void updateConversationInfo() {

        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());
        statusDataService.updateStatus(conversationStatus);

        ExtractedEntityObject preference = Context.get(Context.Name.PREFERENCE.getName());
        preferenceDataService.savePreference(preference);
    }


    public void executeStrategy(PromptModel strategy, RequestInputDTO requestInput) {

        if (ObjectUtil.isEmpty(strategy)) {
            return;
        }


        List<String> agentReplyList = replyService.sendMessage(strategy);
        if (agentReplyList != null) {
            List<Event> list = agentReplyList.stream().map(agentReply -> Event.builder().role(RoleTypeEnum.AGENT).content(agentReply).messageId(requestInput.getMessageId()).time(LocalDateTime.now()).build()).toList();
            historyDataService.saveHistory(list);
        }
    }


    public void sendErrorMessage() {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());
        Boolean intentChange = Context.get(Context.Name.NEW_CONVERSATION_STATUS.getName());

        AgentMessageResp message = new AgentMessageResp();
        message.setMessageId(IdUtil.getSnowflakeNextIdStr())
            .setConversationId(requestInput.getConversationId())
            .setEventName(requestInput.getEvent().getEventName())
            .setUserId(requestInput.getUserId())
            .setZone(requestInput.getZone())
            .setConnectionId(requestInput.getConnectionId())
            .setPage(requestInput.getPage())
            .setContentType(EventTypeEnum.TEXT.getType())
            .setRequestId(requestInput.getRequestId())
            .setEnterPoint(requestInput.getEnterPointEnum().getValue())
            .setContentOrder(1)
            .setIsFinished(false);

        Optional.ofNullable(requestInput.getEventDict()).ifPresent(eventDict -> message.setSkuId(eventDict.getSkuId()));
        Optional.ofNullable(requestInput.getEventDict()).ifPresent(eventDict -> message.setSkcId(eventDict.getSkcId()));

        Optional.ofNullable(requestInput.getEventDict()).ifPresent(eventDict -> message.setMessageType(eventDict.getMessageType()));

        message.setIntentChange(intentChange);

        girlsDataService.sendAgentMessage(message);

        message.setRequestId(null).setIsFinished(true).setCode(1).setErrorMessage("The server is busy, please try again later");

        girlsDataService.sendAgentMessage(message);
    }

}
