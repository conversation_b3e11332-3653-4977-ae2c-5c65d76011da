package com.looksky.agents.application.provider;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.common.enums.TagEnum;
import com.looksky.agents.sdk.agent.common.enums.TagValueEnum;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

/**
 * @ClassName TagDataProvider
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/27 下午4:31
 * @Version 1.0
 **/
@Component
@RequiredArgsConstructor
public class TagDataProvider implements DataProvider {

    private final TagSystemTableService tagSystemTableService;
    private final ObjectMapper objectMapper;

    public String getInnerTagEnumByCategory(String category) {
        return tagSystemTableService.getInnerTagEnumByCategory(category);
    }


    /**
     * 获取所有的品类词, 包括一级, 二级, 和 null
     *
     * @return 一个 list 的字符串
     */
    @SneakyThrows
    public String getCategoryEnum() {
        ArrayList<String> category = new ArrayList<>(tagSystemTableService.getCategoryEnum());
        return objectMapper.writeValueAsString(category);
    }

    /**
     * 获取所有的品类词, 但不包括 set 品类
     *
     * @return 一个不包括 set 品类的 list 的字符串
     */
    @SneakyThrows
    public String getCategoryEnumNoSet() {
        // 获取所有品类
        ArrayList<String> category = new ArrayList<>(tagSystemTableService.getCategoryEnum());

        // 获取 Set 的二级品类
        List<String> setCategory = new ArrayList<>(tagSystemTableService.getSubCategoryByFirstCategory(CategoryEnum.SET.getName()));
        setCategory.add(CategoryEnum.SET.getName());

        category.removeAll(setCategory);
        return objectMapper.writeValueAsString(category);
    }

    public String getEnumValues(String category, String tag) {
        return tagSystemTableService.getEnumValues(category, tag);
    }

    @SneakyThrows
    public String getFabricEnum() {
        Map<String, List<String>> allTag = tagSystemTableService.getTagAndTagValueByCategory(CategoryEnum.CLOTH.getName());
        List<String> fabricType = allTag.get(TagEnum.FABRIC_TYPE.getName());
        List<String> specialFabric = allTag.get(TagEnum.SPECIAL_FABRIC.getName());
        List<String> materialType = allTag.get(TagEnum.MATERIAL.getName());
        HashSet<String> fabric = new HashSet<>(fabricType);
        fabric.addAll(specialFabric);
        fabric.addAll(materialType);
        fabric.add(TagValueEnum.NULL.getName());
        return objectMapper.writeValueAsString(fabric.stream().sorted().toList());
    }

    @Override
    public void getData() {
        Context.put("tagService", this);
    }

    @Override
    public boolean supports(String key) {
        return "tagService".equals(key);
    }
}
