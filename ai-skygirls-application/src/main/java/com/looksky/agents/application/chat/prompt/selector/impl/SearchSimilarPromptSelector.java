package com.looksky.agents.application.chat.prompt.selector.impl;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.prompt.selector.PromptSelector;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.common.enums.IntentTypeEnum;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.product.enums.SearchQuestionType;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 相似服装搜索问题选择器
 */
@Slf4j
@Component
public class SearchSimilarPromptSelector implements PromptSelector {

    
    @Override
    public String support() {
        return IntentTypeEnum.SEARCH_SIMILAR_CLOTH.getType();
    }
    
    @Override
    public String select() {
        ExtractedEntityObject userPreference  = Context.get(Context.Name.PREFERENCE.getName());
        Integer clothIndex = null;
        try {
            clothIndex = userPreference.getCategoryAndTagPreference().getClothIndex();
        } catch (Exception e) {
            log.error("获取用户反馈的编号失败, 用户偏好: {}", JSONUtil.toJsonStr(userPreference), e);
        }
        if (clothIndex == null || clothIndex == 0) {
            log.error("意图为找相似, 但是用户反馈的编号有误, 本应该为找相似, 无 skcId 场景, 现返回推衣服场景");

            // 判断是应该推商品还是应该推方案
            Set<String> currentCategories = userPreference.getCurrentCategories();

            // 如果没有表达品类, 那么就推方案
            if (currentCategories.size() == 1 && currentCategories.contains(CategoryEnum.CLOTH.getName())) {
                 return PromptNameEnum.INQUIRE_USER_SOLUTION.getName();
            } else {
                // 否则进入推商品
                return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
            }
        } else {
            return PromptNameEnum.SIMILAR_SEARCH_IN_SEARCH_PAGE_WITH_INDEX.getName();
        }
    }
} 