package com.looksky.agents.application.tryon.swapcloth.background.remove.impl;

import ai.fal.client.ClientConfig;
import ai.fal.client.CredentialsResolver;
import ai.fal.client.FalClient;
import ai.fal.client.SubscribeOptions;
import ai.fal.client.queue.QueueStatus;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.tryon.swapcloth.background.remove.IRemoveBackgroundService;
import com.looksky.agents.common.model.tryOn.TryOnFalKeyConfig;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import java.util.List;
import java.util.Map;
import java.util.Random;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
@Order(20) // V1优先级较低
public class RemoveBackgroundServiceV1 implements IRemoveBackgroundService {

    private final Random random = new Random();
    private final TryOnFalKeyConfig tryOnFalKeyConfig;

    @CollectEvent
    @Override
    public String removeBackground(String imageUrl) {
        List<String> apiKeyList = tryOnFalKeyConfig.getApiKeyList();
        String apiKey = apiKeyList.get(random.nextInt(apiKeyList.size()));

        FalClient falClient = FalClient.withConfig(ClientConfig.withCredentials(CredentialsResolver.fromApiKey(apiKey)));

        try {
            var input = Map.of(
                "image_url", imageUrl,
                "model", "Portrait"
            );
            log.info("去除背景请求参数: {}", input);
            var result = falClient.subscribe("fal-ai/birefnet/v2",
                SubscribeOptions.<FashnResult>builder()
                    .input(input)
                    .logs(true)
                    .resultType(FashnResult.class)
                    .onQueueUpdate(update -> {
                        if (update instanceof QueueStatus.InProgress status) {
                            log.info("去除背景返回过程数据: {}", status.getLogs());
                        }
                    })
                    .build()
            );


            log.info("去除背景结果: {}", JSONUtil.toJsonStr(result));

            return result.getData().getImage().getUrl();
        } catch (Exception e) {
            log.error("换背景失败, 返回原始图片:{}", imageUrl, e);
        }

        return imageUrl;

    }


    @Data
    static class FashnResult {
        private FashnResultImage image;

        @Data
        static class FashnResultImage {
            private String url;
            private String content_type;
            private String file_name;
            private long file_size;
            private int width;
            private int height;
        }
    }
}
