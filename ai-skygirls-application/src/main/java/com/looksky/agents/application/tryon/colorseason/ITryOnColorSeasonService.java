package com.looksky.agents.application.tryon.colorseason;

import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonParamV1;
import java.time.Duration;

/**
 * 颜色季节试色服务通用接口
 * 所有版本的实现都实现此接口
 *
 * <AUTHOR>
 */
public interface ITryOnColorSeasonService {

    /**
     * 同步换白T
     * 
     * @param param 换白T参数
     * @return 换白T的结果URL
     */
    String tryOnWhiteT(TryOnColorSeasonParamV1 param);
    
    /**
     * 获取服务版本
     * 
     * @return 服务版本
     */
    default String getVersion() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * 获取服务超时时间
     * 
     * @return 服务超时时间
     */
    default Duration getTimeout() {
        // 默认超时时间30秒
        return Duration.ofSeconds(30);
    }
} 