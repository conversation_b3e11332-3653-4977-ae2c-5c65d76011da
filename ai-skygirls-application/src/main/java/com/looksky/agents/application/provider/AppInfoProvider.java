package com.looksky.agents.application.provider;

import com.looksky.agents.infrastructure.config.appinfo.AppInfoContext;
import com.looksky.agents.infrastructure.context.Context;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class AppInfoProvider implements DataProvider {

    @Override
    public void getData() {
        Context.put("appInfo", AppInfoContext.getAppInfoMap());
    }

    @Override
    public boolean supports(String key) {
        return key.equals("appInfo");
    }

} 