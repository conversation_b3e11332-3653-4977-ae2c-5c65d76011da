package com.looksky.agents.application.chat.preference.extraction.deprecated;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.sdk.agent.common.dto.CurrentCategoryTagDTO;
import com.looksky.agents.application.common.BatchTaskExecutor;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import com.looksky.agents.sdk.agent.common.enums.TagEnum;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.preference.LikePreference;
import com.looksky.agents.sdk.agent.preference.PreferenceValue;
import com.looksky.agents.sdk.agent.preference.TagPreferences;
import com.looksky.agents.infrastructure.context.Context;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * @ClassName LabelDimensionExtract
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/30 下午6:11
 * @Version 1.0
 **/
@Slf4j
//@Component
@RequiredArgsConstructor
@Deprecated(since = "2025-01-04, 不再使用统一结构", forRemoval = true)
public class TagDimensionExtract {
    private final ObjectMapper objectMapper;
    private final CommonRequestService commonRequestService;
    private final BatchTaskExecutor batchExecute;


    /**
     * 抽取标签维度
     * @param tagPreferences
     * @return
     */
    public Map<String, List<String>> extract(TagPreferences tagPreferences) {

        // 获取上一轮表达的品类
        ExtractedEntityObject prevPreference = Context.get(Context.Name.PREFERENCE.getName());
        List<String> prevCategories = new ArrayList<>();
        if (prevPreference != null && ObjectUtil.isNotEmpty(prevPreference.getCurrentCategories())) {
            prevCategories.addAll(prevPreference.getCurrentCategories());
        }

        // 如果本次抽取品类, 没有抽到品类, 且上次也没有维度值, 则直接返回空
        if (tagPreferences.getTagsPreferences().isEmpty() && ObjectUtil.isEmpty(prevCategories)) {
            return Collections.emptyMap();
        }

        // 取出抽取出的品类
        LikePreference preference = tagPreferences.getTagsPreferences().get(TagEnum.CATEGORY.getName());

        // 抽取品类列表没有维度值
        if (preference == null && ObjectUtil.isEmpty(prevCategories)) {
            return Collections.emptyMap();
        } else if (preference == null && ObjectUtil.isNotEmpty(prevCategories)) {
            Set<PreferenceValue> preferenceValues = prevCategories.stream().map(category -> {
                PreferenceValue value = new PreferenceValue();
                value.setTextValue(category);
                return value;
            }).collect(Collectors.toSet());
            preference = new LikePreference();
            preference.setLike(new HashSet<>(preferenceValues));
        } else if (preference != null && ObjectUtil.isEmpty(preference.getLike()) && ObjectUtil.isEmpty(prevCategories)) {
            return Collections.emptyMap();
        } else if (preference != null && ObjectUtil.isEmpty(preference.getLike()) && ObjectUtil.isNotEmpty(prevCategories)) {
            Set<PreferenceValue> preferenceValues = prevCategories.stream().map(category -> {
                PreferenceValue value = new PreferenceValue();
                value.setTextValue(category);
                return value;
            }).collect(Collectors.toSet());
            preference.setLike(new HashSet<>(preferenceValues));
        }

        Set<PreferenceValue> like = preference.getLike();
        List<BatchTaskExecutor.Task<String>> tasks = new ArrayList<>();

        for (PreferenceValue value : like) {
            String category = value.getTextValue();
            if (StringUtils.hasText(category)) {
                // 抽取这个品类的维度
                tasks.add(new BatchTaskExecutor.Task<>(category, () -> commonRequestService.commonExecuteStrategy(PromptNameEnum.EXTRACT_LABEL_DIMENSION.getName(), Map.of(Context.Name.CURRENT.getName(), CurrentCategoryTagDTO.builder().category(category).build()))));
            } else {
                log.warn("抽取标签维度时, 品类为空, tagPreferences: {}", JSONUtil.toJsonStr(tagPreferences));
            }
        }

        Map<String, String> result = batchExecute.execute(tasks);
        log.debug("模型输出的抽取标签维度结果: {}", JSONUtil.toJsonStr(result));

        Map<String, List<String>> dimensionMap = new HashMap<>();
        // 解析结果 遍历品类和维度
        result.forEach((category, dimension) -> {
            try {
                // 都是存储为 items: [tag1, tag2] 的形式
                Map<String, List<String>> map = objectMapper.readValue(dimension, new TypeReference<>() {});
                List<String> items = map.getOrDefault("items", Collections.emptyList());
                if (ObjectUtil.isNotEmpty(items)) {
                    dimensionMap.put(category, items);
                }
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        });

        log.debug("过滤后的抽取标签维度结果: {}", JSONUtil.toJsonStr(dimensionMap));

        return dimensionMap;
    }
}
