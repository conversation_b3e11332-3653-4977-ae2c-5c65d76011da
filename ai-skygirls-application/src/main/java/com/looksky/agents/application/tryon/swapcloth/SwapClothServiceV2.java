package com.looksky.agents.application.tryon.swapcloth;

import ai.fal.client.FalClient;
import ai.fal.client.SubscribeOptions;
import ai.fal.client.queue.QueueStatus;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.application.prompt.PromptBusinessService;
import com.looksky.agents.application.tryon.swapcloth.convertor.TryOnConvertor;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.data.client.business.TryOnClient;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.data.redis.config.RedisCodecFactory;
import com.looksky.agents.infrastructure.exception.BusinessException;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothParam;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothStatus;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.SwapClothWorkflowRequest;
import com.looksky.agents.sdk.tryon.swapcloth.enums.SwapClothStatusEnum;
import com.skygirls.biz.user.tryon.dto.SearchTryOnStatusReq;
import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;

@Slf4j
//@Service
@RequiredArgsConstructor
public class SwapClothServiceV2 {

    private final CommonRequestService commonRequestService;
    private final PromptBusinessService promptBusinessService;
    private final RedissonClient redissonClient;
    private final TryOnClient tryOnClient;
    private final RedisCodecFactory factory;
    private final TryOnConvertor tryOnConvertor;
    private final GirlsClient girlsClient;
    private final FalClient falClient;

    private static final String ANALYZE_MODEL_IMAGE = "swap_cloth_analyze_model_image";
    private static final String ANALYZE_USER_FULL_BODY_IMAGE = "swap_cloth_analyze_user_full_body_image";
    private static final String GENERATE_IMAGE_PROMPT = "swap_cloth_generate_image_prompt";
    private static final String ANALYZE_USER_FULL_BODY_IMAGE_BACKUP = "swap_cloth_analyze_user_full_body_image_backup";
    private static final String ERROR_STR = "error";
    private static final String QUEUE_NAME = "swap_cloth_queue:";
    private Thread analysisThread;
    private Thread generationThread;
    private Thread faceSwapThread;
    @Value("${spring.profiles.active}")
    private String env;


    public String asyncSwapCloth(SwapClothParam swapClothParam) {
        VirtualCompletableFuture.runAsync(() -> create(swapClothParam));
        return "success";
    }

    public String getSwapClothResult(String traceId) {
        RBucket<String> bucket = redissonClient.getBucket(RedisKeyConstants.swapClothKey(traceId));

        if (bucket.isExists()) {
            String result = bucket.get();
            if (StrUtil.isBlankIfStr(result) || ERROR_STR.equals(result)) {
                throw new BusinessException("换装失败");
            }
            return result;
        }

        return null;
    }


    private void ensureQueue(SwapClothStatus status, SwapClothStatusEnum statusEnum) {
        status.setStatus(statusEnum);
        status.setCreateTime(status.getUpdateTime());
        status.setUpdateTime(LocalDateTime.now());
        // 添加到队列里面
        RBlockingQueue<SwapClothStatus> queue = redissonClient.getBlockingQueue(QUEUE_NAME + statusEnum, factory.createCodec(SwapClothStatus.class));
        log.info("添加到 {} 队列: {}", statusEnum.getDescription(), JSONUtil.toJsonStr(status));
        queue.add(status);
        SearchTryOnStatusReq callbackRequest = tryOnConvertor.toCallbackRequest(status.getSwapClothParam(), statusEnum, status.getResultImage());
        girlsClient.tryonCallback(callbackRequest);
    }

    @SneakyThrows
    private SwapClothStatus getQueueTask(SwapClothStatusEnum statusEnum) {
        RBlockingQueue<SwapClothStatus> queue = redissonClient.getBlockingQueue(QUEUE_NAME + statusEnum, factory.createCodec(SwapClothStatus.class));
        SwapClothStatus poll = queue.poll(1, TimeUnit.SECONDS);
        if (poll == null) {
            return null;
        }
        log.info("获取 {} 队列中获取到任务: {}", statusEnum.getDescription(), JSONUtil.toJsonStr(poll));
        return poll;
    }

    private void removeFromQueue(SwapClothStatus status, SwapClothStatusEnum statusEnum) {
        RBlockingQueue<SwapClothStatus> queue = redissonClient.getBlockingQueue(QUEUE_NAME + statusEnum, factory.createCodec(SwapClothStatus.class));
        boolean remove = queue.remove(status);
        if (remove) {
            log.info("从 {} 队列中删除任务: {}", statusEnum.getDescription(), JSONUtil.toJsonStr(status));
        } else {
            log.info("从 {} 队列中删除任务失败: {}", statusEnum.getDescription(), JSONUtil.toJsonStr(status));
        }
    }


    public void create(SwapClothParam swapClothParam) {
        SwapClothStatus status = new SwapClothStatus();
        status.setTraceId(swapClothParam.getId());
        status.setCreateTime(LocalDateTime.now());
        status.setUpdateTime(LocalDateTime.now());
        status.setSwapClothParam(swapClothParam);
        ensureQueue(status, SwapClothStatusEnum.QUEUED_FOR_ANALYSIS);
    }

    @PostConstruct
    public void startAll() {
        // 获取当前环境, 如果为 dev 环境, 则不启动
        if ("dev".equals(env)) {
            log.warn("当前环境为 dev, 不启动消费者线程");
            return;
        }
        startAnalysisConsumer();
        startGenerationConsumer();
        startFaceSwapConsumer();
    }

    /**
     * 停止所有消费者线程
     */
    public void stopAll() {
        if (analysisThread != null) {
            analysisThread.interrupt();
        }
        if (generationThread != null) {
            generationThread.interrupt();
        }
        if (faceSwapThread != null) {
            faceSwapThread.interrupt();
        }
    }


    private void startAnalysisConsumer() {
        analysisThread = Thread.ofVirtual().name("分析线程").unstarted(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                SwapClothStatus status = getQueueTask(SwapClothStatusEnum.QUEUED_FOR_ANALYSIS);
                if (status != null) {
                    VirtualCompletableFuture.runAsync(() -> {
                        try {
                            ensureQueue(status, SwapClothStatusEnum.ANALYZING);
                            SwapClothStatus copy = tryOnConvertor.copy(status);
                            String imagePrompt = analyze(status);
                            removeFromQueue(copy, SwapClothStatusEnum.ANALYZING);
                            status.setPromptContent(imagePrompt);
                            ensureQueue(status, SwapClothStatusEnum.QUEUED_FOR_GENERATION);
                        } catch (Exception e) {
                            removeFromQueue(status, SwapClothStatusEnum.ANALYZING);
                            log.error("分析失败", e);
                            status.setErrorMessage(e.getMessage());
                            ensureQueue(status, SwapClothStatusEnum.FAILED);
                        }
                    });
                } else {
                    try {
                        TimeUnit.SECONDS.sleep(2);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }

            }
        });
        analysisThread.setDaemon(true);
        analysisThread.start();
    }


    private String analyze(SwapClothStatus status) {
        SwapClothParam swapClothParam = status.getSwapClothParam();
        VirtualCompletableFuture<String> modelPrompt = getModelPrompt(swapClothParam.getModelImage());
        VirtualCompletableFuture<String> userPrompt = getUserPrompt(swapClothParam.getFullBodyImage());
        return getImagePrompt(userPrompt.join(), modelPrompt.join());
    }

    private void startGenerationConsumer() {
        generationThread = Thread.ofVirtual().name("生成线程").unstarted(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                SwapClothStatus status = getQueueTask(SwapClothStatusEnum.QUEUED_FOR_GENERATION);
                if (status != null) {
                    VirtualCompletableFuture.runAsync(() -> {
                        try {
                            ensureQueue(status, SwapClothStatusEnum.GENERATING);
                            SwapClothStatus copy = tryOnConvertor.copy(status);
                            String generate = generate(status);
                            removeFromQueue(copy, SwapClothStatusEnum.GENERATING);
                            status.setWorkflowResultUrl(generate);
                            ensureQueue(status, SwapClothStatusEnum.QUEUED_FOR_FACE_SWAP);
                        } catch (Exception e) {
                            removeFromQueue(status, SwapClothStatusEnum.GENERATING);
                            log.error("{} 生成失败", JSONUtil.toJsonStr(status), e);
                            status.setErrorMessage(e.getMessage());
                            ensureQueue(status, SwapClothStatusEnum.FAILED);
                        }
                    });
                } else {
                    try {
                        TimeUnit.SECONDS.sleep(2);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        });
        generationThread.setDaemon(true);
        generationThread.start();
    }

    private String generate(SwapClothStatus status) {
        // 生成中
        SwapClothParam swapClothParam = status.getSwapClothParam();
        // 执行分析步骤
        return executeWorkflow(status.getPromptContent(), swapClothParam.getFaceImage(), swapClothParam.getModelImage());
    }

    private void startFaceSwapConsumer() {
        faceSwapThread = Thread.ofVirtual().name("换脸线程").unstarted(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                SwapClothStatus status = getQueueTask(SwapClothStatusEnum.QUEUED_FOR_FACE_SWAP);
                if (status != null) {
                    VirtualCompletableFuture.runAsync(() -> {
                        try {
                            ensureQueue(status, SwapClothStatusEnum.FACE_SWAPPING);
                            SwapClothStatus copy = tryOnConvertor.copy(status);
                            String result = faceSwap(status);
                            removeFromQueue(copy, SwapClothStatusEnum.FACE_SWAPPING);
                            status.setResultImage(result);
                            ensureQueue(status, SwapClothStatusEnum.COMPLETED);
                        } catch (Exception e) {
                            removeFromQueue(status, SwapClothStatusEnum.FACE_SWAPPING);
                            log.error("换脸失败", e);
                            status.setErrorMessage(e.getMessage());
                            ensureQueue(status, SwapClothStatusEnum.FAILED);
                        }
                    });
                } else {
                    try {
                        TimeUnit.SECONDS.sleep(2);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        });
        faceSwapThread.setDaemon(true);
        faceSwapThread.start();
    }


    private String faceSwap(SwapClothStatus status) {
        // 生成中
        SwapClothParam swapClothParam = status.getSwapClothParam();
        // 执行分析步骤
        return fashnSwapFace(status.getWorkflowResultUrl(), swapClothParam.getModelImage());
    }


    @Data
    static class FashnResult {
        private List<FashnResultImage> images;

        @Data
        static class FashnResultImage {
            private String url;
            @JsonProperty("content_type")
            private String contentType;

            @JsonProperty("file_name")
            private String fileName;

            @JsonProperty("file_size")
            private long fileSize;

            private int width;
            private int height;
        }
    }

    public String fashnSwapFace(String tempImage, String modelImage) {

        var input = Map.of(
            "model_image", tempImage,
            "garment_image", modelImage,
            "category", "one-pieces"
        );
        var result = falClient.subscribe("fashn/tryon",
            SubscribeOptions.<FashnResult>builder()
                .input(input)
                .logs(true)
                .resultType(FashnResult.class)
                .onQueueUpdate(update -> {
                    if (update instanceof QueueStatus.InProgress) {
                        log.info("换脸返回过程数据: {}", ((QueueStatus.InProgress) update).getLogs());
                    }
                })
                .build()
        );

        String requestId = result.getRequestId();
        log.info("换脸请求: requestId:{}, 请求体: {}", requestId, JSONUtil.toJsonStr(input));
        log.info("换脸结果: {}", JSONUtil.toJsonStr(result));

        return result.getData().getImages().getFirst().getUrl();

    }

    private String executeWorkflow(String imagePrompt, String faceImage, String modelImage) {
        SwapClothWorkflowRequest swapClothWorkflowRequest = new SwapClothWorkflowRequest();
        swapClothWorkflowRequest.setNewFaceImage(faceImage);
        swapClothWorkflowRequest.setNewClothImage(modelImage);
        swapClothWorkflowRequest.setNewPromptText(imagePrompt);
        JsonNode jsonNode = tryOnClient.swapClothWorkflow(swapClothWorkflowRequest);
        return jsonNode.get("img").asText();
    }


    private String getImagePrompt(String userPrompt, String modelPrompt) {
        String result = commonRequestService.commonExecuteStrategy(GENERATE_IMAGE_PROMPT, Map.of("userPrompt", userPrompt, "modelPrompt", modelPrompt));
        return result.replace("\"", "\\\"");
    }


    private VirtualCompletableFuture<String> getModelPrompt(String modelImage) {
        return VirtualCompletableFuture.supplyAsync(() -> executeAnalyze(ANALYZE_MODEL_IMAGE, List.of(modelImage)));
    }


    private VirtualCompletableFuture<String> getUserPrompt(String userImage) {
        return VirtualCompletableFuture.supplyAsync(() -> executeAnalyze(ANALYZE_USER_FULL_BODY_IMAGE, List.of(userImage)))
            .thenComposeAsync(userPrompt -> {
                if (checkUserPrompt(userPrompt)) {
                    return VirtualCompletableFuture.completedFuture(userPrompt);
                }
                return VirtualCompletableFuture.supplyAsync(() -> getUserPromptStandby(userImage));
            });

    }

    private boolean checkUserPrompt(String prompt) {
        if (StrUtil.isBlankIfStr(prompt)) {
            return false;
        }

        if (prompt.contains("error") || prompt.contains("sorry")) {
            return false;
        }

        return true;
    }


    private String getUserPromptStandby(String bodyImage) {
        return executeAnalyze(ANALYZE_USER_FULL_BODY_IMAGE_BACKUP, List.of(bodyImage));
    }


    private String executeAnalyze(String promptName, List<String> modelImage) {
        PromptModel promptModel = promptBusinessService.getPromptModelByNameWithMetaData(promptName);
        RequestParamBO requestParam = promptBusinessService.convert(promptModel, MapUtil.empty());
        requestParam.setImageUrl(modelImage);
        return commonRequestService.commonExecuteStrategy(requestParam).replace("\"", "\\\"");
    }


}
