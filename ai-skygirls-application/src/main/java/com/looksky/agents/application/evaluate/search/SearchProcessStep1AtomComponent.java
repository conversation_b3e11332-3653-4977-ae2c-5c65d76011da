package com.looksky.agents.application.evaluate.search;

import com.looksky.agents.application.evaluate.prompt.OptimizePromptService;
import com.looksky.agents.sdk.optimize.dto.OptimizePromptResultDTO;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 生成搜索过程的第一步
 * 需要用到的数据: user_id, season, history, query
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SearchProcessStep1AtomComponent {
    private final OptimizePromptService optimizePromptService;


    private static final String PROMPT_NAME = "search_process_part1";
    
    public OptimizePromptResultDTO run(DatasetRecordDTO datasetRecord) {

        Map<String, Object> data = datasetRecord.getData();
        CheckResult checkResult = checkData(data);

        if (Boolean.FALSE.equals(checkResult.pass())) {
            log.error("数据校验失败: {}", checkResult.message());
            return new OptimizePromptResultDTO(checkResult.message(), 0L, 0L);
        }

        return optimizePromptService.batchRunPrompt(PROMPT_NAME, datasetRecord);
    }
    
    
    record CheckResult(Boolean pass, String message){}

    private CheckResult checkData(Map<String, Object> data) {
        if (data == null) {
            return new CheckResult(false, "data 为空");
        }

        if (!data.containsKey("query")) {
            return new CheckResult(false, "data 中缺少 query 字段");
        }

        if (!data.containsKey("user_id")) {
            return new CheckResult(false, "data 中缺少 user_id 字段");
        }

        return new CheckResult(true, "校验通过");
    }
}
