package com.looksky.agents.application.chat.recommend;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.looksky.agents.sdk.agent.common.enums.TagEnum;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.preference.PricePreference;
import com.looksky.agents.sdk.agent.preference.SetPreferenceValue;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PreferenceToSearchConverter {

    /**
     * 将用户偏好转换为搜索条件
     *
     * @param searchTerm 搜索条件
     * @param preference 用户偏好
     */
    public static void convertToSearchTerm(SearchRequestDTO.SearchTerm searchTerm, ExtractedEntityObject preference) {

        if (preference == null) {
            return;
        }

        // 去除 品类为 cloth 的
        handleCategory(searchTerm);

        // 处理价格偏好（使用专门的字段）
        handlePricePreference(searchTerm, preference.getPricePreference());

        // 处理品牌偏好（使用专门的字段）
        handleBrandPreference(searchTerm, preference.getBrandPreference());

        // 处理尺码偏好
        handlerSizePreference(searchTerm, preference.getSizePreference());

        // 处理通用标签偏好
        List<SearchRequestDTO.UserPreference> userPreferences = new ArrayList<>();

        // 处理材质偏好
        addTagPreference(userPreferences, preference.getFabricPreference(), TagEnum.FABRIC_TYPE);

        // 处理其他通用标签偏好
        addTagPreference(userPreferences, preference.getSaturationPreference(), TagEnum.SATURATION);
        addTagPreference(userPreferences, preference.getBrightnessPreference(), TagEnum.BRIGHTNESS);
        addTagPreference(userPreferences, preference.getSleeveLengthPreference(), TagEnum.SLEEVE_LENGTH);
        addTagPreference(userPreferences, preference.getLengthPreference(), TagEnum.LENGTH);
        addTagPreference(userPreferences, preference.getFitPreference(), TagEnum.FIT);
        addTagPreference(userPreferences, preference.getWaistlinePreference(), TagEnum.WAISTLINE);
        addTagPreference(userPreferences, preference.getWaistFitPreference(), TagEnum.WAIST_FIT);
        addTagPreference(userPreferences, preference.getRisePreference(), TagEnum.RISE);
        addTagPreference(userPreferences, preference.getFunctionPreference(), TagEnum.FUNCTION);
        addTagPreference(userPreferences, preference.getCareInstructionPreference(), TagEnum.CARE_INSTRUCTION);
        addTagPreference(userPreferences, preference.getProcessPreference(), TagEnum.PROCESS);
        addTagPreference(userPreferences, preference.getMaterialPreference(), TagEnum.MATERIAL);
        addTagPreference(userPreferences, preference.getElasticityPreference(), TagEnum.ELASTICITY);
        addTagPreference(userPreferences, preference.getNecklineShapePreference(), TagEnum.NECKLINE_SHAPE);
        addTagPreference(userPreferences, preference.getNecklineDetailPreference(), TagEnum.NECKLINE_DETAIL);
        addTagPreference(userPreferences, preference.getStylePreference(), TagEnum.STYLE);

        if (ObjectUtil.isNotEmpty(userPreferences)) {
            searchTerm.setUserPreferences(userPreferences);
        }
    }

    private static void handlerSizePreference(SearchRequestDTO.SearchTerm searchTerm, SetPreferenceValue sizePreference) {
        if (sizePreference == null || sizePreference.isEmpty()) {
            return;
        }
        Set<String> like = sizePreference.getLike();
        Set<String> dislike = sizePreference.getDislike();

        if (CollUtil.isNotEmpty(like)) {
            searchTerm.setSizeTypes(new ArrayList<>(like));
        }

        if (CollUtil.isNotEmpty(dislike)) {
            log.warn("Size preference dislike is not supported, will be ignored: {}", dislike);
        }
    }

    /**
     * 处理品类, 如果 searchTerm 中有品类是 {@link  com.looksky.agents.sdk.agent.common.enums.CategoryEnum#CLOTH}, 则去除品类, 这样推荐就会在全文搜 <br/>
     * 如果传 {@link com.looksky.agents.sdk.agent.common.enums.CategoryEnum#CLOTH} 的话, 推荐会将其当成一个品类, 但是标签体系内是没有这个标签的, 所以可能会报错
     *
     * @param searchTerm
     */
    private static void handleCategory(SearchRequestDTO.SearchTerm searchTerm) {
        Optional.ofNullable(searchTerm.getCategories()).ifPresent(categories -> categories.removeIf(category -> category.equals("cloth")));
    }

    private static void handlePricePreference(SearchRequestDTO.SearchTerm searchTerm, PricePreference pricePreference) {
        if (pricePreference == null || pricePreference.isEmpty()) {
            return;
        }
        searchTerm.setPriceRange(SearchRequestDTO.PriceRange.builder()
            .minPrice(pricePreference.getLow())
            .maxPrice(pricePreference.getHigh())
            .normPrice(pricePreference.getNorm())
            .build());
    }


    private static void handleBrandPreference(SearchRequestDTO.SearchTerm searchTerm, SetPreferenceValue brandPreference) {
        if (brandPreference == null || brandPreference.isEmpty()) {
            return;
        }
        Set<String> like = brandPreference.getLike();
        Set<String> dislike = brandPreference.getDislike();

        if (ObjectUtil.isNotEmpty(like)) {
            searchTerm.setBrands(new ArrayList<>(like));
        }

        if (ObjectUtil.isNotEmpty(dislike)) {
            searchTerm.setDislikeBrands(new ArrayList<>(dislike));
        }
    }

    private static void addTagPreference(List<SearchRequestDTO.UserPreference> userPreferences,
                                  SetPreferenceValue preferenceValue,
                                  TagEnum tagEnum) {
        if (preferenceValue == null || preferenceValue.isEmpty()) {
            return;
        }
        SearchRequestDTO.UserPreference preference = buildUserPreference(preferenceValue, tagEnum);
        if (preference == null || preference.isEmpty()) {
            return;
        }
        userPreferences.add(preference);
    }

    private static SearchRequestDTO.UserPreference buildUserPreference(@NotNull SetPreferenceValue value,
                                                                TagEnum tagEnum) {
        Set<String> like = value.getLike();
        Set<String> dislike = value.getDislike();

        var builder = SearchRequestDTO.UserPreference.builder();

        if (ObjectUtil.isNotEmpty(like)) {
            builder.like(new ArrayList<>(like));
        }

        if (ObjectUtil.isNotEmpty(dislike)) {
            builder.disLike(new ArrayList<>(dislike));
        }

        builder.tagType(tagEnum.getName());

        return builder.build();
    }
}