package com.looksky.agents.application.chat.reply.multiEvent;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.recommend.QueryAndSearchService;
import com.looksky.agents.application.chat.recommend.SearchRecommendService;
import com.looksky.agents.common.utils.BooleanUtils;
import com.looksky.agents.common.utils.ProductNumberFilteringUtils;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.data.redis.recommend.RecommendProductsDataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.search.dto.FilterStepDTO;
import com.looksky.agents.sdk.agent.search.dto.SearchProcessDTO;
import com.looksky.agents.sdk.agent.search.dto.SearchProcessPart2DTO;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import com.looksky.agents.sdk.recommend.search.dto.response.SearchResponseDTO;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @ClassName CategorySelectorMessageSender
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 下午10:29
 * @Version 1.0
 **/
@Slf4j
@Order(1)
@Component
@ApiVersion(platform = ApiVersion.PlatformType.ANDROID, version = "1.1.0")
public class SearchProcessSender extends MultiEventMessageSender {

    @Resource
    private SearchRecommendService searchRecommendService;

    @Resource
    private QueryAndSearchService queryAndSearchService;

    @Resource
    private RecommendProductsDataService recommendProductsDataService;

    @Resource
    private TagSystemTableService tagSystemTableService;

    @Override
    protected boolean supports(PromptModel strategy) {
        return PromptNameEnum.BUILD_SEARCH_PROCESS.getName().equals(strategy.getName());
    }


    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {

        sendLoading();

        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());


        VirtualCompletableFuture<QueryAndSearchService.NewQueryAndIsSearch> newQueryAndIsSearchFuture;
        // 并行执行生成第一段话, 是否搜索, 搜索过程
        if (BooleanUtils.isFalse(conversationStatus.getShowSearchProcess())) {
            VirtualCompletableFuture<String> sendFirstContentFuture = sendFirstContent();

            newQueryAndIsSearchFuture = queryAndSearchService.generateNewQueryAndIsSearchAsync();

            VirtualCompletableFuture.allOf(sendFirstContentFuture, newQueryAndIsSearchFuture).join();
            extraMessage(EventTypeEnum.TEXT.getType(), sendFirstContentFuture.join(), hashMap);

        } else {
            newQueryAndIsSearchFuture = queryAndSearchService.generateNewQueryAndIsSearchAsync();

            VirtualCompletableFuture.allOf(newQueryAndIsSearchFuture).join();
        }


        // 获取新 query 和 是否需要搜索
        QueryAndSearchService.NewQueryAndIsSearch newQueryAndIsSearch = newQueryAndIsSearchFuture.join();
        Context.put("recommend_new_query", newQueryAndIsSearch.getQuery());

        log.info("当前的会话状态: {}", JSONUtil.toJsonStr(conversationStatus));

        boolean show = false;

        if (BooleanUtils.isFalse(conversationStatus.getShowSearchProcess())) {

            if (newQueryAndIsSearch.isNeedSearch()) {
                // 需要搜索
                VirtualCompletableFuture<SearchProcessMessage> sendSearchProcess = sendSearchProcess();

                // 在线搜索
                VirtualCompletableFuture<SearchProcessPart2DTO> searchProcessPart2 = VirtualCompletableFuture.supplyAsync(() -> {
                    queryAndSearchService.searchNewQuery(newQueryAndIsSearch);
                    String result = commonRequestService.commonExecuteStrategy("search_process_part2", Collections.emptyMap());
                    return JSONUtil.toBean(result, SearchProcessPart2DTO.class);
                });


                VirtualCompletableFuture.allOf(sendSearchProcess, searchProcessPart2).join();

                SearchProcessMessage searchProcessMessage = sendSearchProcess.join();
                SearchProcessDTO searchProcessDTO = searchProcessMessage.searchProcessDTO();
                List<SearchProcessDTO.Reason> reasons = searchProcessMessage.searchProcessDTO().getReasons();

                SearchProcessDTO.Reason reason = searchProcessPart2.join().toReason();
                FilterStepDTO first = searchProcessMessage.filterStepDTOS.getFirst();
                reason.setFilterNumbers(first.getAfterCount());
                reason.setCandidatesNumbers(first.getBeforeCount());

                reasons.add(reason);

                searchProcessDTO.setSearch(false);
                searchProcessDTO.setReasons(reasons);

                Context.put(Context.Name.SEARCH_PROCESS.getName(), SearchProcessDTO.toPreferenceData(searchProcessDTO));
                // 把以前的消息拿过来
                AgentMessageResp message = searchProcessMessage.message();
                message.setContentOrder(message.getContentOrder() + 1);
                sendContent(message, EventTypeEnum.SEARCH_PROCESS.getType(), searchProcessDTO);
                sendLoading();
            } else {
                // 不需要搜索, 直接生成所有的搜索过程
                String result = commonRequestService.commonExecuteStrategy("search_process_all_final", Collections.emptyMap());
                SearchProcessDTO searchProcessDTO = JSONUtil.toBean(result, SearchProcessDTO.class);
                setFilterNumbers(searchProcessDTO, false);
                AgentMessageResp agentMessageResp = initMessage(EventTypeEnum.TEXT.getType());
                Context.put(Context.Name.SEARCH_PROCESS.getName(), SearchProcessDTO.toPreferenceData(searchProcessDTO));
                sendContent(agentMessageResp, EventTypeEnum.SEARCH_PROCESS.getType(), searchProcessDTO, true);
                sendLoading();
            }

            // 标识已经展示过搜索过程
            show = true;

        }

        // 生成正负向词
        VirtualCompletableFuture<SearchResponseDTO> recommendResult = searchRecommendService.recommendAsync();

        List<ItemDTO> recommendations = new ArrayList<>();
        boolean isDefault = true;

        try {
            SearchResponseDTO recommendSearchResponseDTO = recommendResult.get();
            recommendations.addAll(recommendSearchResponseDTO.getItems());
            isDefault = recommendSearchResponseDTO.isDefault();
        } catch (Exception e) {
            log.error("调用推荐系统失败", e);
        }

        if (ObjectUtil.isEmpty(recommendations)) {
            noProductIds(hashMap);
        } else {
            sendIds(recommendations);
        }
        //
        //else {
        //    // 获取商品信息
        //    girlsDataService.getProductInfo(recommendations);
        //
        //    // 判断是否为兜底策略
        //    if (isDefault) {
        //        sendABackdropMessage(hashMap, recommendations);
        //    } else {
        //        sendNormalMessage(hashMap, recommendations);
        //    }
        //}
        conversationStatus.setRecommendProduct(true);

        // 如果展示过搜索过程, 则设置状态
        if (show) {
            conversationStatus.setShowSearchProcess(true);
        }
        recommendProductsDataService.save(recommendations.stream().map(ItemDTO::getItemId).toList());
    }

    private List<FilterStepDTO> setFilterNumbers(SearchProcessDTO searchProcessDTO, boolean search) {
        List<SearchProcessDTO.Reason> reasons = searchProcessDTO.getReasons();

        List<FilterStepDTO> filterStepDTOS = generateRandomFilterNumber(reasons.size(), search);
        reasons.forEach(reason -> {
            FilterStepDTO filterStepDTO = filterStepDTOS.removeFirst();
            reason.setFilterNumbers(filterStepDTO.getAfterCount());
            reason.setCandidatesNumbers(filterStepDTO.getBeforeCount());
        });
        return filterStepDTOS;
    }


    /**
     * 总结用户需求
     *
     * @return
     */
    private VirtualCompletableFuture<String> sendFirstContent() {
        return VirtualCompletableFuture.supplyAsync(() -> {
            String message = sendStreamMessage("search_process_first_reply", Collections.emptyMap()).block();
            sendLoading();
            return message;
        });

    }


    private VirtualCompletableFuture<SearchProcessMessage> sendSearchProcess() {
        return VirtualCompletableFuture.supplyAsync(() -> {
            String result = commonRequestService.commonExecuteStrategy("search_process_part1", Collections.emptyMap());
            SearchProcessDTO searchProcessDTO = JSONUtil.toBean(result, SearchProcessDTO.class);
            List<FilterStepDTO> filterStepDTOS = setFilterNumbers(searchProcessDTO, true);
            AgentMessageResp agentMessageResp = initMessage(EventTypeEnum.TEXT.getType());
            // 发送搜索过程的第一部分
            sendContent(agentMessageResp, EventTypeEnum.SEARCH_PROCESS.getType(), searchProcessDTO, false).thenRun(() -> {
                agentMessageResp.setContentOrder(agentMessageResp.getContentOrder() + 1);
                searchProcessDTO.setSearch(true);
                // 发送在线搜索
                sendContent(agentMessageResp, EventTypeEnum.SEARCH_PROCESS.getType(), searchProcessDTO, false);
            });
            return new SearchProcessMessage(agentMessageResp, searchProcessDTO, filterStepDTOS);
        });
    }

    record SearchProcessMessage(AgentMessageResp message, SearchProcessDTO searchProcessDTO, List<FilterStepDTO> filterStepDTOS) {
    }


    private List<FilterStepDTO> generateRandomFilterNumber(int size, boolean isSearch) {
        if (isSearch) {
            size += 1;
        }
        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());
        String subCategory;
        Set<String> currentCategories = userPreference.getCurrentCategories();
        if (!currentCategories.isEmpty()) {
            subCategory = currentCategories.stream().findFirst().orElse(CategoryEnum.CLOTH.getName());
        } else {
            subCategory = CategoryEnum.CLOTH.getName();
        }

        // 转换为二级
        if (tagSystemTableService.getFirstCategories().contains(subCategory)) {
            subCategory = tagSystemTableService.getSubCategoryByFirstCategory(subCategory).getFirst();
        }

        return ProductNumberFilteringUtils.getFilterStepDTOs(size, subCategory);
    }


    // 找到了商品, 但是是推荐的兜底商品
    private void sendABackdropMessage(HashMap<String, Object> hashMap, List<ItemDTO> recommendations) {
        String finalText = sendStreamMessage("recommend_summary", Collections.emptyMap()).block();
        sendIds(recommendations);
        extraMessage(EventTypeEnum.TEXT.getType(), finalText, hashMap);

    }

    // 发送普通消息
    private void sendNormalMessage(HashMap<String, Object> hashMap, List<ItemDTO> recommendations) {
        String finalText = sendStreamMessage("recommend_summary", Collections.emptyMap()).block();
        sendIds(recommendations);
        extraMessage(EventTypeEnum.TEXT.getType(), finalText, hashMap);
    }


    // 没有找到商品的情况
    private void noProductIds(HashMap<String, Object> hashMap) {
        String finalMessageStr = sendStreamMessage("query_no_products", Collections.emptyMap()).block();
        extraMessage(EventTypeEnum.TEXT.getType(), finalMessageStr, hashMap);
    }

    // 发送商品
    private AgentMessageResp sendIds(List<ItemDTO> ids) {
        // 发送给前端
        AgentMessageResp agentMessageResp = initMessage(EventTypeEnum.PRODUCT_ID_LIST.getType());
        sendContent(agentMessageResp, EventTypeEnum.PRODUCT_ID_LIST.getType(), ids);
        return agentMessageResp;
    }


}
