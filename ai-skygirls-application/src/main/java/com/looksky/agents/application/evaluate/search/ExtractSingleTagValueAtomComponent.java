package com.looksky.agents.application.evaluate.search;

import com.looksky.agents.application.chat.preference.extraction.SingleTagExtractService;
import com.looksky.agents.application.evaluate.prompt.OptimizePromptService;
import com.looksky.agents.sdk.optimize.dto.OptimizePromptResultDTO;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 提取单个标签值原子组件
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExtractSingleTagValueAtomComponent {

    private final OptimizePromptService optimizePromptService;
    private final SingleTagExtractService singleTagExtractService;


    public OptimizePromptResultDTO run(DatasetRecordDTO datasetRecord) {
        Map<String, Object> data = datasetRecord.getData();
        CheckResult checkResult = checkData(data);
        if (Boolean.FALSE.equals(checkResult.pass())) {
            return new OptimizePromptResultDTO(checkResult.message, 0L, 0L);
        }

        String tagName = data.get("tagName").toString();

        PromptModel promptModel = singleTagExtractService.getPromptForTag(tagName);

        return optimizePromptService.batchRunPromptByPromptModel(promptModel, datasetRecord);
    }


    record CheckResult(Boolean pass, String message){}

    private CheckResult checkData(Map<String, Object> data) {
        if (data == null) {
            return new CheckResult(false, "data 为空");
        }

        if (!data.containsKey("tagName")) {
            return new CheckResult(false, "data 中缺少 tagName 字段");
        }

        return new CheckResult(true, "校验通过");
    }

}
