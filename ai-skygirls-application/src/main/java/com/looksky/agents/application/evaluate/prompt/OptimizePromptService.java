package com.looksky.agents.application.evaluate.prompt;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.ChatModelFallbackService;
import com.looksky.agents.application.prompt.PromptBusinessService;
import com.looksky.agents.application.template.FreemarkerService;
import com.looksky.agents.sdk.optimize.dto.OptimizePromptResultDTO;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import com.looksky.agents.sdk.agent.ext.RequestVo;
import com.looksky.agents.data.client.service.GirlsDataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.models.schema.JsonSchemaRequestBuilder;
import freemarker.template.TemplateException;
import java.io.IOException;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class OptimizePromptService {

    private static final String USER_ID_STR = "user_id";
    private static final String PRODUCT_ID_STR = "product_id";
    private static final String CITY_STR = "city";
    private static final String SEASON_STR = "season";
    private final GirlsDataService girlsDataService;
    private final FreemarkerService freemarkerService;
    private final PromptBusinessService promptBusinessService;
    private final ChatModelFallbackService chatModelFallbackService;




    public OptimizePromptResultDTO batchRunPrompt(String promptName, DatasetRecordDTO datasetRecord) {
        // 构建 requestParamBO
        PromptModel promptModel = promptBusinessService.getPromptModelByNameWithMetaData(promptName);

        return batchRunPromptByPromptModel(promptModel, datasetRecord);
    }

    public OptimizePromptResultDTO batchRunPromptByPromptModel(PromptModel promptModel, DatasetRecordDTO datasetRecord) {
        RequestParamBO requestParamBO = convertToRequestParamBO(promptModel, datasetRecord);

        return optimizePrompt(requestParamBO);
    }

    private RequestParamBO convertToRequestParamBO(PromptModel promptModel, DatasetRecordDTO datasetRecord) {
        RequestParamBO requestParamBO = new RequestParamBO();
        requestParamBO.setUserPrompt(promptModel.getUserPrompt());
        requestParamBO.setSystemPrompt(promptModel.getSystemPrompt());
        requestParamBO.setJsonSchema(promptModel.getJsonSchema());
        requestParamBO.setModelName(promptModel.getModelName());
        requestParamBO.setTemperature(promptModel.getTemperature());
        requestParamBO.setOutputType(promptModel.getOutputType());
        requestParamBO.setSize(promptModel.getImageSize());
        requestParamBO.setData(datasetRecord.getData());
        return requestParamBO;
    }


    @SneakyThrows
    public OptimizePromptResultDTO optimizePrompt(RequestParamBO requestParamBO) {

        // 处理特殊数据
        handleData(requestParamBO.getData());

        // 尝试直接使用传递过来的数据进行渲染
        tryUseData(requestParamBO);

        // 渲染模版变量
        renderTemplate(requestParamBO);

        // 发送请求
        // 测试耗时
        long start = System.currentTimeMillis();
        JsonSchemaRequestBuilder execute = chatModelFallbackService.execute(requestParamBO);
        //AzureJsonSchemaRequestBuilder execute = chatModelFallbackService.executeAzure(requestParamBO);
        long end = System.currentTimeMillis();
        String content = execute.getContent();
        Long totalTokens = execute.getChatResponse().getMetadata().getUsage().getTotalTokens().longValue();

        return new OptimizePromptResultDTO(content, totalTokens, end - start);
    }

    private void tryUseData(RequestParamBO requestParamBO) {
        if (requestParamBO.getData() == null) {
            return;
        }

        Map<String, Object> dataMap = JSONUtil.parseObj(requestParamBO.getData()).toBean(Map.class);
        
        // 处理 userPrompt
        String userPrompt = requestParamBO.getUserPrompt();
        if (userPrompt != null) {
            userPrompt = replaceTemplateVariables(userPrompt, dataMap);
            requestParamBO.setUserPrompt(userPrompt);
        }
        
        // 处理 systemPrompt
        String systemPrompt = requestParamBO.getSystemPrompt();
        if (systemPrompt != null) {
            systemPrompt = replaceTemplateVariables(systemPrompt, dataMap);
            requestParamBO.setSystemPrompt(systemPrompt);
        }
    }

    private String replaceTemplateVariables(String template, Map<String, Object> dataMap) {
        if (template == null || template.isEmpty()) {
            return template;
        }

        // 1. 处理 ${namespace.variable} 格式
        Pattern namespacePattern = Pattern.compile("\\$\\{([\\w]+)\\.([\\w]+)}");
        Matcher namespaceMatcher = namespacePattern.matcher(template);
        StringBuilder sb = new StringBuilder();
        
        while (namespaceMatcher.find()) {
            String namespace = namespaceMatcher.group(1);
            String variable = namespaceMatcher.group(2);
            
            // 从dataMap中获取对应的值
            Object namespaceObj = dataMap.get(namespace);
            if (namespaceObj instanceof Map<?, ?> namespaceMap) {
                Object value = namespaceMap.get(variable);
                if (value != null) {
                    namespaceMatcher.appendReplacement(sb, Matcher.quoteReplacement(value.toString()));
                }
            }
        }
        namespaceMatcher.appendTail(sb);
        template = sb.toString();
        
        // 2. 处理 <@macro params/> 格式
        Pattern macroPattern = Pattern.compile("<@([\\w]+)\\s*(?:[^>]*?)/?>");
        Matcher macroMatcher = macroPattern.matcher(template);
        sb = new StringBuilder();
        
        while (macroMatcher.find()) {
            String macroName = macroMatcher.group(1);
            // 从dataMap中获取对应的宏值
            Object macroValue = dataMap.get(macroName);
            if (macroValue != null) {
                macroMatcher.appendReplacement(sb, Matcher.quoteReplacement(macroValue.toString()));
            }
        }
        macroMatcher.appendTail(sb);
        return sb.toString();
    }

    private void renderTemplate(RequestParamBO requestParamBO)
        throws TemplateException, IOException {

        Map<String, Object> dataMap = JSONUtil.parseObj(requestParamBO.getData()).toBean(Map.class);

        String userPrompt = freemarkerService.parseTemplate(requestParamBO.getUserPrompt(),
            dataMap);
        requestParamBO.setUserPrompt(userPrompt);

        String systemPrompt = freemarkerService.parseTemplate(requestParamBO.getSystemPrompt(),
            dataMap);
        requestParamBO.setSystemPrompt(systemPrompt);

        String jsonSchema = freemarkerService.parseTemplate(requestParamBO.getJsonSchema(),
            dataMap);
        requestParamBO.setJsonSchema(jsonSchema);
    }

    private void handleData(Object dataObj) {
        if (dataObj == null) {
            return;
        }

        JSONObject data = JSONUtil.parseObj(dataObj);

        Map<String, Object> dataMap = data.toBean(Map.class);
        Context.putAll(dataMap);

        // 判断是否有特殊数据
        if (data.containsKey(PRODUCT_ID_STR)) {
            String productId = data.getStr(PRODUCT_ID_STR);
            girlsDataService.getProductInfo(productId);
        }

        RequestVo requestVo = new RequestVo();
        if (data.containsKey(CITY_STR)) {
            requestVo.setCity(data.getStr(CITY_STR));
        }

        if (data.containsKey(SEASON_STR)) {
            requestVo.setSeason(data.getStr(SEASON_STR));
        }

        if (data.containsKey(USER_ID_STR)) {
            String userId = data.getStr(USER_ID_STR);
            girlsDataService.getUserInfo(userId);
            requestVo.setUserId(userId);
        }

        Context.put(Context.Name.REQUEST_INPUT.getName(), requestVo);
    }
}
