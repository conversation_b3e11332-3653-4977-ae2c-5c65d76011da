package com.looksky.agents.application.chat.prompt.selector.impl;

import com.looksky.agents.application.chat.prompt.selector.PromptSelector;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 服装搜索问题选择器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SearchNoClothSelector implements PromptSelector {

    @Override
    public String support() {
        return "search_non_cloth";
    }

    @Override
    public String select() {
        return "search_non_cloth";
    }


}