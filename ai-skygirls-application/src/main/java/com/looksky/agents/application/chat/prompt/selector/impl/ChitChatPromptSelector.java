package com.looksky.agents.application.chat.prompt.selector.impl;

import com.looksky.agents.application.chat.prompt.selector.PromptSelector;
import org.springframework.stereotype.Component;

/**
 * @ClassName ChitChatQuestionSelector
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/29 上午11:40
 * @Version 1.0
 **/
@Component
public class ChitChatPromptSelector implements PromptSelector {
    @Override
    public String support() {
        return "small_talk_in_search";
    }

    @Override
    public String select() {
        return "small_talk_in_search";
    }
}
