package com.looksky.agents.application.chat.prompt.selector.impl;

import com.looksky.agents.application.chat.prompt.selector.PromptSelector;
import com.looksky.agents.sdk.agent.common.enums.IntentTypeEnum;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import org.springframework.stereotype.Component;

@Component
public class AppIntroducePromptSelector implements PromptSelector {
    @Override
    public String support() {
        return IntentTypeEnum.APP_INTRODUCE.getType();
    }

    @Override
    public String select() {
        return PromptNameEnum.APP_INTRODUCE.getName();
    }
}
