package com.looksky.agents.application.tryon.swapcloth;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.tryon.swapcloth.convertor.TryOnConvertor;
import com.looksky.agents.application.tryon.swapcloth.generation.GenerationSelect;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.data.redis.config.RedisCodecFactory;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothParam;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothStatus;
import com.looksky.agents.sdk.tryon.swapcloth.enums.SwapClothStatusEnum;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GenerationTask extends SwapClothBaseService {

    private Thread generationThread;

    @Resource
    private GenerationSelect generationSelect;

    public GenerationTask(RedissonClient redissonClient, RedisCodecFactory factory, TryOnConvertor tryOnConvertor, GirlsClient girlsClient) {
        super(redissonClient, factory, tryOnConvertor, girlsClient);
    }


    @PostConstruct
    public void start() {
        // 获取当前环境, 如果为 dev 环境, 则不启动
        if ("dev".equals(env)) {
            log.warn("当前环境为 dev, 不启动消费者线程");
            return;
        }
        startGenerationConsumer();
    }

    private void startGenerationConsumer() {
        generationThread = Thread.ofVirtual().name("生成线程").unstarted(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                SwapClothStatus status = getQueueTask(SwapClothStatusEnum.QUEUED_FOR_GENERATION);
                if (status != null) {
                    VirtualCompletableFuture.runAsync(() -> {
                        try {
                            ensureQueue(status, SwapClothStatusEnum.GENERATING);
                            SwapClothStatus copy = tryOnConvertor.copy(status);
                            String generate = generate(status);
                            removeFromQueue(copy, SwapClothStatusEnum.GENERATING);
                            status.setWorkflowResultUrl(generate);
                            ensureQueue(status, SwapClothStatusEnum.QUEUED_FOR_FACE_SWAP);
                        } catch (Exception e) {
                            removeFromQueue(status, SwapClothStatusEnum.GENERATING);
                            log.error("{} 生成失败", JSONUtil.toJsonStr(status), e);
                            status.setErrorMessage(e.getMessage());
                            ensureQueue(status, SwapClothStatusEnum.FAILED);
                        }
                    }).orTimeout(5, TimeUnit.MINUTES)
                    .exceptionally(throwable -> {
                        removeFromQueue(status, SwapClothStatusEnum.GENERATING);
                        log.error("生成任务超时, 任务:{}", JSONUtil.toJsonStr(status));
                        status.setErrorMessage("生成任务超时，执行时间超过5分钟");
                        ensureQueue(status, SwapClothStatusEnum.FAILED);
                        return null;
                    });
                } else {
                    try {
                        TimeUnit.SECONDS.sleep(2);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        });
        generationThread.setDaemon(true);
        generationThread.start();
    }

    private String generate(SwapClothStatus status) {
        // 生成中
        SwapClothParam swapClothParam = status.getSwapClothParam();
        return generationSelect.getWorkflowResult(swapClothParam.getId(), status.getPromptContent(), swapClothParam.getFaceImage(), swapClothParam.getModelImage());
    }

}
