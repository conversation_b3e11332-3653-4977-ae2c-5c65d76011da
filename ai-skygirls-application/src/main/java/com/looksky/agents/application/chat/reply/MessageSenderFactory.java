package com.looksky.agents.application.chat.reply;

import com.looksky.agents.infrastructure.versioncompat.selector.VersionServiceSelector;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class MessageSenderFactory {
    // 注入所有消息发送器实现
    private final List<AbstractMessageSender> senders;
    // 注入版本选择器
    private final VersionServiceSelector versionServiceSelector;

    public AbstractMessageSender getSender(PromptModel strategy) {
        List<AbstractMessageSender> versionedSender = versionServiceSelector.getServiceList(AbstractMessageSender.class);
        return versionedSender.stream().filter(sender -> sender.supports(strategy)).findFirst()
            .map(sender -> {
                log.info("使用版本化MessageSender: {}", sender.getClass().getSimpleName());
                return sender;
            })
            .orElseGet(() -> senders.stream().filter(sender -> sender.supports(strategy)).findFirst()
                .map(sender -> {
                    log.info("使用标准MessageSender: {}", sender.getClass().getSimpleName());
                    return sender;
                })
                .orElseThrow(() -> new UnsupportedOperationException("No suitable sender found")));
    }
}