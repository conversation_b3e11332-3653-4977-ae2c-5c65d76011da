package com.looksky.agents.application.evaluate.recommend;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.data.client.business.LookSkyClientLocal;
import com.looksky.agents.data.client.service.GirlsDataService;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import com.looksky.agents.sdk.optimize.dto.OptimizePromptResultDTO;
import com.looksky.agents.sdk.product.ProductInfo;
import com.looksky.agents.sdk.recommend.common.dto.VectorRecallModelDTO;
import com.looksky.agents.sdk.recommend.daily100.bo.RegionalCategoryQuery;
import com.looksky.agents.sdk.recommend.daily100.dto.response.Daily100TestResponseDTO;
import com.looksky.agents.sdk.recommend.foryou.dto.ForYouParam;
import com.looksky.agents.sdk.recommend.foryou.dto.PartitionRecomModelDTO;
import com.looksky.agents.sdk.recommend.foryou.enums.RecommendScenEnum;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ForYouHttpTestAtomComponent {

    private final LookSkyClientLocal localClient;

    private final GirlsDataService girlsDataService;
    private final ObjectMapper objectMapper;

    @SneakyThrows
    public OptimizePromptResultDTO run(DatasetRecordDTO datasetRecord, String name) {

        Map<String, Object> data = datasetRecord.getData();


        if (data == null || data.get("input") == null) {
            log.error("数据校验失败: {}", datasetRecord);
            return new OptimizePromptResultDTO("该数据不包含 input 数据", 0L, 0L);
        }

        long start = System.currentTimeMillis();

        Object inputStr = data.get("input");

        ForYouParam forYouTestRequestDTO = new ForYouParam();


        forYouTestRequestDTO.setUserId(data.get("user_id").toString());

        RegionalCategoryQuery categoryQuery = objectMapper.readValue(inputStr.toString(), RegionalCategoryQuery.class);

        List<VectorRecallModelDTO> categoryVectorRecall = categoryQuery.toCategoryVectorRecall();

        PartitionRecomModelDTO partitionRecomModelDTO = new PartitionRecomModelDTO();
        partitionRecomModelDTO.setPartitionRecomScenes(RecommendScenEnum.getByName(name));
        partitionRecomModelDTO.setRecallVectors(categoryVectorRecall);


        forYouTestRequestDTO.setPartitionRecomModels(List.of(partitionRecomModelDTO));


        JSONObject jsonObject = recommendHttp(forYouTestRequestDTO);

        Daily100TestResponseDTO result = jsonObject.getBeanList("data", Daily100TestResponseDTO.class).getFirst();

        List<String> itemList = result.getItemList();

        List<ProductInfo> productInfoList = girlsDataService.getProductInfo(itemList);

        // 给 girlsDaily100TestRespModel 回填数据
        setProductImg(result.getTotalItemList(), productInfoList);

        result.getRecallItemMap().forEach((k, v) -> setProductImg(v, productInfoList));


        long end = System.currentTimeMillis();


        return new OptimizePromptResultDTO(JSONUtil.toJsonStr(result), 0L, end - start);
    }

    private JSONObject recommendHttp(ForYouParam forYouTestRequestDTO) {
        return localClient.partitionDailyTest(forYouTestRequestDTO);
    }

    private void setProductImg(List<Daily100TestResponseDTO.GirlsDaily100TestItem> girlsDaily100TestItemList, List<ProductInfo> productInfoList) {
        for (Daily100TestResponseDTO.GirlsDaily100TestItem girlsDaily100TestItem : girlsDaily100TestItemList) {
            String itemId = girlsDaily100TestItem.getItemId();
            productInfoList.stream().filter(productCard -> productCard.getSkcId().equals(itemId)).findFirst().ifPresent(productCard -> girlsDaily100TestItem.setUrl(productCard.getImages().getFirst()));
        }
    }

}
