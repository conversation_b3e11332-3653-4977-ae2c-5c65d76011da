package com.looksky.agents.application.chat.reply.stream;

import cn.hutool.core.text.CharSequenceUtil;
import com.looksky.agents.application.chat.reply.AbstractMessageSender;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.dto.OpeningCacheDTO;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.common.enums.EventNameEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.common.enums.PageEnum;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.skygirls.biz.im.dto.MessageRestDTO;
import com.skygirls.biz.report.IosUserInfoDto;
import com.skygirls.biz.user.tryon.dto.SearchTryOnStatusReq;
import com.skygirls.biz.user.tryon.enums.TryOnStatusEnum;
import com.skygirls.biz.user.tryon.model.TryOnReportModel;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Order(1)
@Component
public class TryOnOpeningMessageSender extends AbstractMessageSender {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private GirlsClient girlsClient;


    @Override
    protected boolean supports(PromptModel strategy) {
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());
        return PageEnum.TRY_ON.getName().equals(input.getPage()) &&
            (EnterPointEnum.TRY_ON_UPLOAD_PHOTO == input.getEnterPointEnum() || EnterPointEnum.TRY_ON_PROCESSING == input.getEnterPointEnum()) &&
            EventNameEnum.OPENING.getValue().equals(input.getEvent().getEventName());
    }


    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        AgentMessageResp message = initMessageAsync(EventTypeEnum.TEXT.getType());

        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());

        IosUserInfoDto userInfo = Context.get(Context.Name.USER_INFO.getName());

        SearchTryOnStatusReq request = new SearchTryOnStatusReq();
        MessageRestDTO.EventDict eventDict = input.getEventDict();
        if (eventDict != null) {
            if (CharSequenceUtil.isNotBlank(eventDict.getSkcId())) {
                request.setSkcId(eventDict.getSkcId());
            }
            if (CharSequenceUtil.isNotBlank(eventDict.getSkuId())) {
                request.setSkuId(eventDict.getSkuId());
            }
        }
        request.setUserId(userInfo.getAppUser().getUserId());
        TryOnReportModel tryOnReportModel = girlsClient.tryOnHomeCard(request).getData();


        String cacheKey = EventNameEnum.OPENING.getValue() + ":" + input.getPage() + ":" + input.getEnterPointEnum().getValue();

        // 先去 redis 里面找有没有配置的内容
        RBucket<OpeningCacheDTO> bucket = redissonClient.getBucket(cacheKey, new TypedJsonJacksonCodec(OpeningCacheDTO.class));
        // 如果没有配置, 那么就执行 prompt 的结果 一般不会执行这个
        if (!bucket.isExists()) {
            log.debug("{}, 无开场白设定, 执行prompt", cacheKey);
            // 缓存里面没有, 执行 prompt
            String messageStr = sendStreamMessage(strategy).block();
            sendLoading();
            extraSearchMessage(EventTypeEnum.TEXT.getType(), messageStr, hashMap, input.getPage());
            sendTryOnHomeCardMessage(tryOnReportModel);
            return;
        }


        OpeningCacheDTO tryOnOpeningCacheDTO = bucket.get();
        // 有相关配置
        String replyStr = null;
        if (tryOnReportModel == null || tryOnReportModel.getTryOnStatus() == null) {
            replyStr = tryOnOpeningCacheDTO.getNotUploaded();
        } else {
            if (tryOnReportModel.getTryOnStatus() == TryOnStatusEnum.NEED_UPLOAD_IMG) {
                replyStr = tryOnOpeningCacheDTO.getNotUploaded();
            } else if (tryOnReportModel.getTryOnStatus() == TryOnStatusEnum.UPLOAD_IMG) {
                replyStr = tryOnOpeningCacheDTO.getUploaded();
            } else if (tryOnReportModel.getTryOnStatus() == TryOnStatusEnum.PROCESSING) {
                replyStr = tryOnOpeningCacheDTO.getAnalysis();
            } else {
                replyStr = "todo";
            }
        }


        // 如果上面命中了, 那么返回内容
        if (CharSequenceUtil.isNotBlank(replyStr)) {
            log.debug("返回开场白内容: {}", replyStr);

            if (tryOnReportModel!= null && (tryOnReportModel.getTryOnStatus() == TryOnStatusEnum.NEED_UPLOAD_IMG || tryOnReportModel.getTryOnStatus() == TryOnStatusEnum.UPLOAD_IMG)) {
                replyStr = fillVariable(replyStr, userInfo);
                sendContent(message, EventTypeEnum.TEXT.getType(), replyStr);
                sendLoading();
                extraSearchMessage(EventTypeEnum.TEXT.getType(), replyStr, hashMap, input.getPage());
            }

            sendTryOnHomeCardMessage(tryOnReportModel);

            return;
        }
        log.debug("开场白未命中, 执行prompt");

        // 如果还是空的, 那么执行 prompt
        String messageStr = sendStreamMessage(strategy).block();
        sendLoading();
        extraSearchMessage(EventTypeEnum.TEXT.getType(), messageStr, hashMap, input.getPage());
        sendTryOnHomeCardMessage(tryOnReportModel);

    }

    /**
     * 填充配置中的变量
     *
     * @param replyStr
     * @param userInfo
     * @return
     */
    private String fillVariable(String replyStr, IosUserInfoDto userInfo) {
        PromptTemplate template = new PromptTemplate(replyStr);
        Prompt prompt = template.create(Map.of("user_name", userInfo.getBasicUser().getUsername()));
        return prompt.getContents();
    }

    private void extraSearchMessage(String eventType, Object message, HashMap<String, Object> hashMap, String page) {
        extraMessage(eventType, message, hashMap);
    }

    private void sendTryOnHomeCardMessage(TryOnReportModel tryOnReportModel) {
        AgentMessageResp adviceMessage = initMessage(EventTypeEnum.TRY_ON_UPLOAD_IMAGE_CARD.getType());
        sendContent(adviceMessage, EventTypeEnum.TRY_ON_UPLOAD_IMAGE_CARD.getType(), tryOnReportModel);

    }


}