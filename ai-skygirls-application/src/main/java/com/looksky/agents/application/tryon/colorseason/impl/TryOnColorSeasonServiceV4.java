package com.looksky.agents.application.tryon.colorseason.impl;

import ai.fal.client.ClientConfig;
import ai.fal.client.CredentialsResolver;
import ai.fal.client.FalClient;
import ai.fal.client.SubscribeOptions;
import ai.fal.client.queue.QueueStatus;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.tryon.colorseason.ITryOnColorSeasonService;
import com.looksky.agents.common.model.tryOn.TryOnFalKeyConfig;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonParamV1;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Random;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;


/**
 * colorSeason 试色 service, 四步方案, 换白T 使用 fal 的新方案
 *
 * <AUTHOR>
 * @since 1.1.12
 **/
@Slf4j
@Service
@RequiredArgsConstructor
@Order(90) // 优先级最低，数字越小优先级越高
public class TryOnColorSeasonServiceV4 implements ITryOnColorSeasonService {

    private final Random random = new Random();
    private final TryOnFalKeyConfig tryOnFalKeyConfig;
    private static final String PROMPT = "Change the top into a plain white T-shirt";

    @Override
    public Duration getTimeout() {
        return Duration.ofSeconds(35);
    }

    @CollectEvent
    @Override
    public String tryOnWhiteT(TryOnColorSeasonParamV1 param) {
        List<String> apiKeyList = tryOnFalKeyConfig.getApiKeyList();
        String apiKey = apiKeyList.get(random.nextInt(apiKeyList.size()));

        FalClient falClient = FalClient.withConfig(ClientConfig.withCredentials(CredentialsResolver.fromApiKey(apiKey)));

        try {
            var input = Map.of(
                "prompt", PROMPT,
                "image_url", param.getUserImage(),
                "enable_safety_checker", false,
                "seed", 42,
                "negative_prompt", "",
                "guidance_scale", 4,
                "num_inference_steps", 30
            );
            log.info("换白 T 请求参数: {}", input);
            var result = falClient.subscribe("fal-ai/step1x-edit",
                SubscribeOptions.<FashnResult>builder()
                    .input(input)
                    .logs(true)
                    .resultType(FashnResult.class)
                    .onQueueUpdate(update -> {
                        if (update instanceof QueueStatus.InProgress status) {
                            log.info("换白 T 返回过程数据: {}", status.getLogs());
                        }
                    })
                    .build()
            );


            log.info("换白 T 结果: {}", JSONUtil.toJsonStr(result));

            return result.getData().getImages().getFirst().getUrl();
        } catch (Exception e) {
            log.error("换白 T 失败", e);
        }

        return null;
    }

    @Data
    static class FashnResult {
        private List<FashnResultImage> images;

        private Long seed;
        private List<Boolean> has_nsfw_concepts;

        @Data
        static class FashnResultImage {
            private String url;
            private String content_type;
            private int width;
            private int height;
        }
    }
}
