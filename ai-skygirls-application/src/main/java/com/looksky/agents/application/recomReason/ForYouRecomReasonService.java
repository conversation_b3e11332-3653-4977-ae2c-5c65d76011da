package com.looksky.agents.application.recomReason;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.application.foryou.PartitionRecommendService;
import com.looksky.agents.data.client.service.GirlsDataService;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.data.redis.daily100.Daily100DataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.product.ProductInfo;
import com.looksky.agents.sdk.recommend.common.dto.ColorSeasonCacheDataDTO;
import com.looksky.agents.sdk.recommend.common.dto.Face2HairCacheDataDTO;
import com.looksky.agents.sdk.recommend.common.dto.UserPageKibbeCacheDataDTO;
import com.looksky.agents.sdk.recommend.foryou.enums.RecommendScenEnum;
import com.looksky.agents.sdk.recommend.reason.dto.RecommendReasonDTO;
import com.looksky.agents.sdk.utils.date.DateUtils;
import com.skygirls.biz.report.IosUserInfoDto;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

/**
 * @Author：ch
 * @Date：2024/12/18 15:19
 * @Description girls版本的推荐理由
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ForYouRecomReasonService {

    private static final String STRATEGY_NAME = "get_girls_recomReason";

    private static final String RECOMMEND_REASON_SUFFIX = "_recommend_reason";

    private final CommonRequestService commonRequestService;
    private final GirlsDataService girlsDataService;
    private final Daily100DataService daily100DataService;
    private final RedissonClient redissonClient;
    private final PartitionRecommendService partitionRecommendService;

    @SneakyThrows
    public List<RecommendReasonDTO> buildRecomReasonByForYou(String userId, String season, String city, List<String> allItemIds, Integer batchSize, RecommendScenEnum scene) {

        List<VirtualCompletableFuture<List<RecommendReasonDTO>>> futures = new ArrayList<>();

        for (int begin = 0; begin < allItemIds.size(); ) {

            int end = Math.min(allItemIds.size(), begin + batchSize);

            List<String> itemIds = allItemIds.subList(begin, end);

            VirtualCompletableFuture<List<RecommendReasonDTO>> future = VirtualCompletableFuture.supplyAsync(() -> buildRecomReason(userId, season, city, itemIds, scene));

            futures.add(future);

            begin = end;
        }

        return futures.stream()
            .map(VirtualCompletableFuture::join)
            .flatMap(List::stream)
            .collect(Collectors.toList());
    }


    private List<RecommendReasonDTO> buildRecomReason(String userId, String season, String city, List<String> itemIds, RecommendScenEnum scene) {

        String lockKey = RedisKeyConstants.forYouRecomReasonLockKey(userId, CollUtil.join(itemIds, ","));
        RLock lock = redissonClient.getLock(lockKey);

        log.info("开始生成推荐理由，userId：{},ItemId:{}", userId, CollUtil.join(itemIds, ","));

        try {
            log.info("生成推荐理由，获取动态数据，lockKey：{}", lockKey);
            if (!lock.tryLock(2, TimeUnit.SECONDS)) {
                log.warn("生成推荐理由失败，分布式锁未释放，lockKey：{}", lockKey);
                return new ArrayList<>();
            }


            //获取用户信息
            IosUserInfoDto userInfo = girlsDataService.getUserInfo(userId);
            //获取商品信息
            List<ProductInfo> productCardList = girlsDataService.getProductInfo(itemIds);

            if (CollUtil.isEmpty(productCardList)) {
                log.error("生成推荐理由失败，获取商品数据失败，lockKey：{}", lockKey);
                return new ArrayList<>();
            }

            HashMap<String, Object> promptDataModel = getPromptDataModel(season, city, userInfo, productCardList, scene);

            log.info("生成推荐理由，开始请求GPT，lockKey：{}", lockKey);


            String promptResult = executeScenePrompt(scene, promptDataModel);


            log.info("生成推荐理由，开始解析GPT返回结果，userId：{},ItemId:{}", userId, CollUtil.join(itemIds, ","));

            //解析结果
            List<String> itemResonList = CharSequenceUtil.splitTrim(promptResult, "&&&").stream().toList();
            List<RecommendReasonDTO> insertReasonModels = new ArrayList<>();
            for (String s : itemResonList) {

                try {
                    List<String> reasonList = CharSequenceUtil.splitTrim(s.replaceAll("`", "").replace("-", ""), "\n");

                    if (reasonList.isEmpty()) {
                        continue;
                    }

                    String itemId = CharSequenceUtil.removePrefix(reasonList.getFirst().replace("*", "").trim(), "itemId:").trim();

                    RecommendReasonDTO reasonNode = new RecommendReasonDTO();
                    reasonNode.setUserId(userId);
                    reasonNode.setItemId(itemId);
                    reasonNode.setPrologue(reasonList.get(1));
                    reasonList = reasonList.subList(2, reasonList.size()).stream().map(reason -> reason.replaceAll("\\d+\\.", "").trim()).collect(Collectors.toList());
                    reasonNode.setReason(JSONArray.toJSONString(reasonList));
                    reasonNode.setScene(scene);

                    insertReasonModels.add(reasonNode);
                } catch (Exception e) {
                    log.info("解析单个推荐理由失败，lockKey：{},reason:{}", lockKey, s, e);
                }

            }

            log.info("生成推荐理由成功，lockKey：{}", lockKey);

            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }

            return insertReasonModels;
        } catch (Exception e) {
            log.error("生成推荐理由异常，lockKey：{}，e：{}", lockKey, e.getMessage(), e);
        } finally {
            if (lock.isLocked()) {
                lock.forceUnlock();
            }
        }

        return new ArrayList<>();
    }


    /**
     * 执行分区 prompt
     *
     * @param scene
     * @param promptDataModel
     * @return
     */
    private String executeScenePrompt(RecommendScenEnum scene, HashMap<String, Object> promptDataModel) {
        return switch (scene) {
            case GIRLS_PARTITION_BASIC, GIRLS_PARTITION_MATCH, GIRLS_PARTITION_TRY, GIRLS_PARTITION_BRAND, GIRLS_PARTITION_FESTIVAL, GIRLS_PARTITION_TREND, GIRLS_PARTITION_DISCOUNT ->
                commonRequestService.commonExecuteStrategy(scene.getName() + RECOMMEND_REASON_SUFFIX, promptDataModel);
            default -> commonRequestService.commonExecuteStrategy(STRATEGY_NAME, promptDataModel);
        };
    }


    @NotNull
    private HashMap<String, Object> getPromptDataModel(String season, String city, IosUserInfoDto userInfo, List<ProductInfo> productCardList, RecommendScenEnum scene) {
        //生成推荐理由
        HashMap<String, Object> promptDataModel = new HashMap<>(Map.of(Context.Name.USER_INFO.getName(), userInfo, Context.Name.PRODUCT_INFO.getName(), productCardList));

        promptDataModel.put("city", city);
        promptDataModel.put("season", Optional.ofNullable(season).orElse(DateUtils.getCurrentSeason()));
        promptDataModel.put("batchSize", productCardList.size());

        Face2HairCacheDataDTO face2Hair = daily100DataService.getFace2Hair(userInfo.getAppUser().getFaceShape().getName());
        UserPageKibbeCacheDataDTO kbType = daily100DataService.getKbType(userInfo.getAppUser().getKibbeType().getCode());
        ColorSeasonCacheDataDTO colorSeason = daily100DataService.getColorSeason(userInfo.getAppUser().getColorSeason().getName());

        promptDataModel.put("neckSuggestion", face2Hair.getNeckSuggestion());
        promptDataModel.put("userPageKibbe", kbType);
        promptDataModel.put("colorSeasonBestColor", CollUtil.join(colorSeason.getBestColors(), ","));
        promptDataModel.put("colorSeasonAvoidColor", CollUtil.join(colorSeason.getBestColors(), ","));
        promptDataModel.put("themeName", scene.getShowName());


        switch (scene) {
            case GIRLS_PARTITION_BASIC, GIRLS_PARTITION_MATCH, GIRLS_PARTITION_TRY, GIRLS_PARTITION_BRAND, GIRLS_PARTITION_FESTIVAL, GIRLS_PARTITION_TREND, GIRLS_PARTITION_DISCOUNT:
            default:
                // 从 从向量词里面拿到推荐的主题
                List<String> panelThemeHistory = partitionRecommendService.getPanelThemeHistory(userInfo.getAppUser().getUserId(), scene);
                if (CollUtil.isNotEmpty(panelThemeHistory)) {
                    promptDataModel.put("themeData", panelThemeHistory);
                } else {
                    promptDataModel.put("themeData", new ArrayList<>());
                }
                break;
        }

        return promptDataModel;
    }


    public static void main(String[] args) {
        String s = """
            ```
            &&&itemId:1768532684894760961
            Olive is the new neutral—perfectly blending timeless charm with modern vibes! \s
            1. Buy this if you adore a collared neckline that frames your elegant oval face and adds a polished touch to your look. \s
            2. The stretch ribbed knit hugs your balanced hourglass figure beautifully, offering a comfy yet flattering fit. \s
            3. Perfect for winter layering in Ashburn—pair it with a chic trench or your fave scarf for cozy brunches or campus strolls. \s
            ```""";

        List<String> reasonList = CharSequenceUtil.splitTrim(s.replaceAll("`", "").replace("-", ""), "\n");

        String itemId = CharSequenceUtil.removePrefix(reasonList.getFirst().replace("*", "").trim(), "itemId:").trim();

        RecommendReasonDTO reasonNode = new RecommendReasonDTO();
        reasonNode.setItemId(itemId);
        reasonNode.setPrologue(reasonList.get(1));
        reasonList = reasonList.subList(2, reasonList.size()).stream().map(reason -> reason.replaceAll("\\d+\\.", "").trim()).collect(Collectors.toList());
        reasonNode.setReason(JSONArray.toJSONString(reasonList));

        System.out.println(JSONUtil.toJsonStr(reasonNode));
    }

}
