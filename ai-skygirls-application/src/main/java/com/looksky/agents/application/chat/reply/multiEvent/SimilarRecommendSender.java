package com.looksky.agents.application.chat.reply.multiEvent;

import com.looksky.agents.application.chat.recommend.SimilarRecommendService;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.data.redis.recommend.RecommendProductsDataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.thread.VirtualThreadExecutor;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.preference.CategoryAndTagPreference;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import com.looksky.agents.sdk.product.ProductInfo;
import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Callable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @ClassName CategorySelectorMessageSender
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 下午10:29
 * @Version 1.0
 **/
@Slf4j
@Order(1)
@Component
public class SimilarRecommendSender extends MultiEventMessageSender {

    @Resource
    private SimilarRecommendService similarRecommendService;

    @Resource
    private RecommendProductsDataService recommendProductsDataService;
    @Resource
    private TagSystemTableService tagSystemTableService;

    @Override
    protected boolean supports(PromptModel strategy) {
        // 生成搜索过程
        return PromptNameEnum.SIMILAR_SEARCH_IN_SEARCH_PAGE_WITH_INDEX.getName().equals(strategy.getName());
    }

    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {


        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());

        Integer clothIndex = userPreference.getCategoryAndTagPreference().getClothIndex();
        String skcId = recommendProductsDataService.get(clothIndex);
        ProductInfo productInfo = girlsDataService.getProductInfo(skcId);

        List<Callable<Object>> tasks = List.of(
            () -> generateReplyContent(strategy),
            () -> getRecommendId(productInfo)
        );


        List<Object> results = VirtualThreadExecutor.executeWithResults(tasks);

        Object agentMessage = results.getFirst();
        extraMessage(EventTypeEnum.TEXT.getType(), agentMessage, hashMap);

    }


    private String generateReplyContent(PromptModel strategy) {
        return sendStreamMessage(strategy).block();
    }

    private List<ItemDTO> getRecommendId(ProductInfo productInfo) {
        AgentMessageResp agentMessageResp = initMessageAsync(EventTypeEnum.PRODUCT_ID_LIST.getType());
        List<ItemDTO> ids = similarRecommend(generateNewQuery(), productInfo);
        sendContent(agentMessageResp, EventTypeEnum.PRODUCT_ID_LIST.getType(), ids);
        return ids;
    }


    private String generateNewQuery() {
        return commonRequestService.commonExecuteStrategy(
            PromptNameEnum.SIMILAR_NEW_QUERY.getName(), Collections.emptyMap());
    }

    private List<ItemDTO> similarRecommend(String query, ProductInfo productInfo) {
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());

        // 需要判断用户
        return similarRecommendService.similarRecommend(productInfo.getSkcId(),
            query, input.getUserId(), getCategory());
    }

    private List<String> getCategory() {
        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());
        CategoryAndTagPreference categoryAndTagPreference =
            userPreference.getCategoryAndTagPreference();
        Set<String> allCategories = categoryAndTagPreference.getAllCategories();
        if (allCategories.isEmpty()) {
            return Collections.emptyList();
        } else if (allCategories.contains(CategoryEnum.CLOTH.getName())) {
            return Collections.emptyList();
        } else if (tagSystemTableService.containsSubCategory(allCategories)) {
            // 用户表达的是二级品类
            return new ArrayList<>(allCategories);
        } else if (tagSystemTableService.containsFirstCategory(allCategories)) {
            // 用户表达的一级品类, 只取第一个
            List<String> subCategoryByFirstCategory = new ArrayList<>();
            allCategories.stream().findFirst().ifPresent(c -> subCategoryByFirstCategory.addAll(tagSystemTableService.getSubCategoryByFirstCategory(c)));

            return subCategoryByFirstCategory;
        }

        return Collections.emptyList();

    }

}
