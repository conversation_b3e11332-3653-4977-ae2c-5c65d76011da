package com.looksky.agents.application.chat.reply.multiEvent;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.reply.AbstractMessageSender;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.prompt.model.PromptMetadataModel;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.ArrayDeque;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName MultiEventMessageSender
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 下午9:56
 * @Version 1.0
 **/
@Order(100)
@Component
public class MultiEventMessageSender extends AbstractMessageSender {
    @Override
    protected boolean supports(PromptModel strategy) {
        List<PromptMetadataModel> returnEvents = strategy.getMetadataList().stream()
                .filter(metadata -> metadata.getMetadataKey().equals("returnEvents"))
                .toList();
        return ObjectUtil.isNotEmpty(returnEvents);
    }

    @Override
    protected void beforeSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        // 将所有事件提取出来
        ArrayDeque<String> returnEvents = strategy.getMetadataList().stream()
                .filter(metadata -> metadata.getMetadataKey().equals("returnEvents"))
                .flatMap(metadata -> JSONUtil.toList(metadata.getMetadataValue(), String.class).stream())
                .collect(Collectors.toCollection(ArrayDeque::new));

        hashMap.put("returnEvents", returnEvents);
    }


    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        @SuppressWarnings("unchecked")
        ArrayDeque<String> returnEvents = (ArrayDeque<String>) hashMap.get("returnEvents");

        ArrayDeque<Object> returnContents = generateReturnContents(strategy);

        while (!returnEvents.isEmpty()) {
            String returnEvent = returnEvents.poll();
            Object returnContent = returnContents.poll();
            AgentMessageResp agentMessageResp = initMessage(returnEvent);
            extraMessage(returnEvent, returnContent, hashMap);
            sendContent(agentMessageResp, returnEvent, returnContent);
        }
    }

    protected ArrayDeque<Object> generateReturnContents(PromptModel strategy) {
        return new ArrayDeque<>();
    }



}
