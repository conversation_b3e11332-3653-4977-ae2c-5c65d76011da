package com.looksky.agents.application.chat.prompt.selector.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.application.chat.prompt.selector.PromptSelector;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.common.utils.BooleanUtils;
import com.looksky.agents.data.client.utils.CommunicationHelper;
import com.looksky.agents.data.redis.conversation.HistoryDataService;
import com.looksky.agents.infrastructure.trace.annotation.TraceMethod;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import com.looksky.agents.sdk.agent.common.dto.ShortcutInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.product.enums.SearchQuestionType;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 服装搜索问题选择器
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ApiVersion(platform = ApiVersion.PlatformType.ALL, version = "1.2.0")
public class GirlsPromptSelectorV3 implements PromptSelector {

    private final CommonRequestService commonRequestService;
    private final ObjectMapper objectMapper;
    private final CommunicationHelper communicationHelper;
    private final HistoryDataService historyDataService;

    @Lazy
    @Resource
    private GirlsPromptSelectorV3 self;

    private static final String NEXT_ACTION = "recommended_next_steps_for_the_scenario";


    @Override
    public String support() {
        return "search_cloth";
    }

    @SneakyThrows
    @Override
    @TraceMethod(description = "获取 搜索衣服意图的下一步应该执行的流程")
    public String select() {
        String nextStr = commonRequestService.commonExecuteStrategy(NEXT_ACTION);

        NextAction nextAction = objectMapper.readValue(nextStr, NextAction.class);

        if (self.checkNeedFollowUp(nextAction)) {
            return null;
        }

        return self.handleSystemNextAction(nextAction);

    }

    @SneakyThrows
    String handleSystemNextAction(NextAction nextAction) {
        return switch (nextAction.systemNextAction()) {
            case "direct_product_recommendation" -> self.handleDirectProductRecommendation();
            case "style_plan_recommendation" -> self.handleStylePlanRecommendation();
            case "simple_follow_up", "multi_selection_follow_up", "clarification_follow_up" -> self.handleFollowUp(nextAction);
            case "fallback_response", "fallback_to_default" -> self.handleFallback(nextAction);
            default -> throw new IllegalStateException("Unexpected value: " + nextAction.systemNextAction());
        };
    }

    @TraceMethod(description = "处理兜底逻辑")
    String handleFallback(NextAction nextAction) {
        if (CharSequenceUtil.isNotBlank(nextAction.fallbackResponse())) {
            followUp(nextAction.fallbackResponse(), null);
        }

        if (nextAction.quickActions() != null && !nextAction.quickActions().isEmpty() && BooleanUtils.isFalse(nextAction.fallbackProducts())) {
            followUp(null, nextAction.quickActions());
        }

        if (BooleanUtils.isTrue(nextAction.fallbackProducts())) {
            return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
        }
        return null;
    }


    @TraceMethod(description = "判断为追问, 处理追问逻辑")
    String handleFollowUp(NextAction nextAction) {
        followUp(nextAction.followUpPrompt(), nextAction.quickActions());
        return null;
    }

    @TraceMethod(description = "判断为推商品, 返回推商品的流程")
    String handleDirectProductRecommendation() {
        return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
    }

    @TraceMethod(description = "判断为推方案, 返回推方案的流程")
    String handleStylePlanRecommendation() {
        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK_SOLUTION.getValue();
    }


    @TraceMethod(description = "判断是否需要追问")
    boolean checkNeedFollowUp(NextAction nextAction) {
        if ("yes".equals(nextAction.needFollowUp())) {
            followUp(nextAction.followUpPrompt(), nextAction.quickActions());
            return true;
        }
        return false;
    }

    private void followUp(String content, List<String> shortcutInput) {
        if (CharSequenceUtil.isNotEmpty(content)) {
            communicationHelper.sendMessage(EventTypeEnum.TEXT, content);
            historyDataService.saveAgentHistory(content);
        }

        if (CollUtil.isNotEmpty(shortcutInput)) {
            communicationHelper.sendMessage(EventTypeEnum.SHORTCUT_INPUT, ShortcutInputDTO.builder().tips(shortcutInput).build());
        }
    }


    /**
     * 模型决策下一步应该采取的行动
     *
     * @param systemNextAction 行动枚举
     * @param needFollowUp     是否需要追问
     * @param followUpPrompt   追问的问题
     * @param fallbackResponse 兜底回复
     * @param fallbackProducts 推商品
     * @param quickActions     快捷回复
     */
    private record NextAction(
        @JsonProperty("intent_code") String intentCode,
        @JsonProperty("intent_label") String intentLabel,
        @JsonProperty("need_follow_up") String needFollowUp,
        @JsonProperty("recommendation_strategy") String recommendationStrategy,
        @JsonProperty("follow_up_prompt") String followUpPrompt,
        @JsonProperty("system_next_action") String systemNextAction,
        @JsonProperty("fallback_response") String fallbackResponse,
        @JsonProperty("fallback_products") Boolean fallbackProducts,
        @JsonProperty("quick_actions") List<String> quickActions

    ) {
    }


}