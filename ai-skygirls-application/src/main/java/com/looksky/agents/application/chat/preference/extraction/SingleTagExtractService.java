package com.looksky.agents.application.chat.preference.extraction;

import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.application.prompt.PromptBusinessService;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import com.looksky.agents.sdk.agent.common.dto.CurrentCategoryTagDTO;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import com.looksky.agents.common.utils.ResourceUtils;
import com.looksky.agents.infrastructure.context.Context;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SingleTagExtractService {
    private final PromptBusinessService promptBusinessService;
    private final CommonRequestService commonRequestService;

    /**
     * 抽取单个标签的具体值
     * @param category 品类, 可能为一级, 也可能为二级, cloth
     * @param tagName 标签名
     * @return 抽取的用户偏好
     */
    public String extractSingleTagPreference(String category, String tagName) {
        log.info("抽取具体的{}标签值", tagName);

        // 获取标签对应的prompt
        PromptModel prompt = getPromptForTag(tagName);
        // 构建请求参数
        RequestParamBO strategy = promptBusinessService.convert(prompt, 
            Map.of(Context.Name.CURRENT.getName(), 
                CurrentCategoryTagDTO.builder()
                    .category(category)
                    .tagName(tagName)
                    .build()));

        // 执行抽取
        return commonRequestService.commonExecuteStrategy(strategy);
    }

    /**
     * 获取标签对应的prompt模型
     * @param tagName 标签名
     * @return Prompt模型
     */
    public PromptModel getPromptForTag(String tagName) {
        PromptModel prompt = promptBusinessService.findPromptByMetadata(tagName);
        if (prompt == null) {
            log.warn("未找到标签[{}]对应的prompt，使用默认prompt", tagName);
            prompt = promptBusinessService.getPromptModelByNameWithMetaData(
                PromptNameEnum.EXTRACT_SINGLE_USER_PREFERENCE.getName());
        }
        prompt.setJsonSchema(ResourceUtils.innerTagModelOutputStructure(tagName));
        return prompt;
    }
} 