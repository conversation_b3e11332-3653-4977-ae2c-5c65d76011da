package com.looksky.agents.application.chat.reply;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.graecove.common.ABTestFlagResp;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.application.prompt.PromptBusinessService;
import com.looksky.agents.common.utils.ABTestFlagUtils;
import com.looksky.agents.common.utils.StringMatchUtils;
import com.looksky.agents.data.client.service.GirlsDataService;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.exception.FeiShuNotifyHelper;
import com.looksky.agents.infrastructure.interceptor.GrpcResponseHeaderInterceptor;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.infrastructure.thread.VirtualThreadExecutor;
import com.looksky.agents.infrastructure.versioncompat.annotation.VersionCompatible;
import com.looksky.agents.models.parseOutput.StreamingTextBuffer;
import com.looksky.agents.models.parseOutput.ThinkTextBuffer;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.dto.ThinkMessageDTO;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import com.skygirls.biz.report.IosUserInfoDto;
import com.skygirls.biz.user.dto.AllReportDto;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

@Component
@VersionCompatible
public abstract class AbstractMessageSender {

    private static final Logger log = LoggerFactory.getLogger(AbstractMessageSender.class);

    @Resource
    protected GirlsDataService girlsDataService;

    @Resource
    protected CommonRequestService commonRequestService;

    @Resource
    protected PromptBusinessService promptBusinessService;

    @Resource
    protected RedissonClient redissonClient;

    @Resource
    protected FeiShuNotifyHelper feiShuNotifyHelper;

    private final Scheduler messageScheduler = Schedulers.fromExecutor(VirtualThreadExecutor.get());

    // 模板方法
    public final List<String> send(PromptModel strategy) {
        if (!supports(strategy)) {
            return ListUtil.empty();
        }

        try {
            HashMap<String, Object> hashMap = new HashMap<>();

            //AgentMessageResp message = initMessage(EventTypeEnum.TEXT.getType());

            // 前置处理
            beforeSend(strategy, hashMap);

            // 核心发送逻辑
            doSend(strategy, hashMap);

            // 后置处理
            afterSend(strategy, hashMap);

            return transparentMessage(hashMap);
        } catch (Exception e) {
            handleError(e);
            feiShuNotifyHelper.notify(e.getMessage() + "\n" + Arrays.stream(e.getStackTrace()).map(StackTraceElement::toString).toList());
            return ListUtil.empty();
        }
    }


    protected AgentMessageResp initMessageAsync(String type) {

        AgentMessageResp message = buildInitMessageBody(type);

        AgentMessageResp asyncMessage = new AgentMessageResp();


        BeanUtil.copyProperties(message, asyncMessage);

        sendInitialMessageAsync(asyncMessage);

        message.setRequestId(null);

        return message;
    }

    protected AgentMessageResp buildInitMessageBody(String type) {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());
        Boolean intentChange = Context.get(Context.Name.NEW_CONVERSATION_STATUS.getName());

        AgentMessageResp message = new AgentMessageResp();
        message.setMessageId(IdUtil.getSnowflakeNextIdStr())
//                .setIsShow(true)
            .setConversationId(requestInput.getConversationId())
            .setEventName(requestInput.getEvent().getEventName())
            .setUserId(requestInput.getUserId())
            .setZone(requestInput.getZone())
            .setConnectionId(requestInput.getConnectionId())
            .setPage(requestInput.getPage())
            .setContentType(type)
            .setRequestId(requestInput.getRequestId())
            .setEnterPoint(requestInput.getEnterPointEnum().getValue())
            .setContentOrder(1)
            .setAbTestFlagResp(getAbTestFlag())
            .setIsFinished(false);

        Optional.ofNullable(requestInput.getEventDict()).ifPresent(eventDict -> message.setSkuId(eventDict.getSkuId()));
        Optional.ofNullable(requestInput.getEventDict()).ifPresent(eventDict -> message.setSkcId(eventDict.getSkcId()));

        Optional.ofNullable(requestInput.getEventDict()).ifPresent(eventDict -> message.setMessageType(eventDict.getMessageType()));

        message.setIntentChange(intentChange);
        return message;
    }


    protected AgentMessageResp initMessage(String type) {
        AgentMessageResp message = buildInitMessageBody(type);

        sendInitialMessage(message);
        message.setRequestId(null);

        return message;
    }

    protected void sendInitialMessage(AgentMessageResp message) {
        girlsDataService.initAgentMessage(message);
        message.setRequestId(null);
    }


    protected void sendInitialMessageAsync(AgentMessageResp message) {
        VirtualCompletableFuture.runAsync(() -> sendInitialMessage(message));
    }


    // 策略支持判断
    protected abstract boolean supports(PromptModel strategy);

    // 具体发送实现
    protected void doSend(PromptModel strategy,
                          HashMap<String, Object> hashMap) {

    }

    // 可选的钩子方法
    protected void beforeSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        // 默认空实现
    }

    protected void afterSend(PromptModel strategy, HashMap<String, Object> hashMap) {

        //sendSuggestMessage();

        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());
        AtomicInteger conversationNumber =
            conversationStatus.getConversationNumber() == null ? new AtomicInteger(0) :
                conversationStatus.getConversationNumber();
        conversationNumber.incrementAndGet();
        conversationStatus.setConversationNumber(conversationNumber);
        Context.put(Context.Name.STATUS.getName(), conversationStatus);
    }


    /**
     * 已经不需要这个了, 现在 Search 页面开场白改为了 search_dressing_opening_and_ask, 会自带需要问的问题
     */
    @Deprecated(since = "1.2.2")
    protected void sendSuggestMessage() {

        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());

        String page = requestInput.getPage();
        String enterPoint = requestInput.getEnterPointEnum().getValue();
        String eventName = requestInput.getEvent().getEventName();

        RSet<String> adviceQuestionList = redissonClient.getSet(RedisKeyConstants.adviceQuestionKey(page, enterPoint, eventName));
        if (!adviceQuestionList.isExists() || adviceQuestionList.isEmpty()) {
            return;
        }
        Set<String> randomQuestion = new HashSet<>();
        try {
            randomQuestion.addAll(handlerAdviceQuestion(adviceQuestionList.random(2)));
        } catch (Exception e) {
            log.error("处理推荐问题的用户信息失败", e);
            adviceQuestionList.random(10).stream().filter(question -> !question.contains("{")).limit(2).forEach(randomQuestion::add);
        }


        sendContent(initMessage(EventTypeEnum.ADVICE_QUESTIONS.getType()), EventTypeEnum.ADVICE_QUESTIONS.getType(), randomQuestion);

    }

    protected Set<String> handlerAdviceQuestion(Set<String> randomQuestion) {
        // 先判断里面是否包含花括号
        Set<String> result = new HashSet<>();
        boolean isContain = randomQuestion.stream().anyMatch(question -> question.contains("{"));
        if (isContain) {

            IosUserInfoDto userInfo = Context.get(Context.Name.USER_INFO.getName());
            AllReportDto appUser = userInfo.getAppUser();
            String kibbeType = appUser.getKibbeType().getCode().replace("_", "");
            String bodyShape = appUser.getBodyShape().getCode().replace("_", "");
            String heightType = appUser.getHeightType().getCode();
            String colorSeason = appUser.getColorSeason().getName().replace("_", "");

            HashMap<String, Object> map = new HashMap<>();
            map.put("kibbeType", kibbeType);
            map.put("bodyType", bodyShape);
            map.put("heightType", heightType);
            map.put("colorSeason", colorSeason);

            for (String s : randomQuestion) {
                PromptTemplate template = new PromptTemplate(s);
                Prompt prompt = template.create(map);
                result.add(prompt.getContents());
            }

            return result;

        } else {
            return randomQuestion;
        }

    }


    protected List<String> transparentMessage(HashMap<String, Object> hashMap) {
        Object agentReplyObj = hashMap.get("agentReply");
        if (agentReplyObj != null) {
            @SuppressWarnings("unchecked")
            List<String> agentReply = (List<String>) agentReplyObj;
            return agentReply;
        } else {
            return ListUtil.empty();
        }
    }

    protected void extraMessage(String eventType, Object message, HashMap<String, Object> hashMap) {
        if (message == null) {
            return;
        }

        String msgStr;
        if (!EventTypeEnum.TEXT.getType().equals(eventType)) {
            msgStr = JSONUtil.toJsonStr(message);
        } else {
            msgStr = (String) message;
        }

        log.debug("保存消息事件名: {}, 消息内容:{}", eventType, message);

        Object agentReplyObj = hashMap.get("agentReply");
        ArrayList<String> agentReply;
        if (agentReplyObj != null) {
            agentReply = (ArrayList<String>) agentReplyObj;
            agentReply.add(msgStr);
        } else {
            agentReply = new ArrayList<>();
            agentReply.add(msgStr);
        }
        hashMap.put("agentReply", agentReply);
    }


    protected void handleError(Throwable e) {
        // 默认错误处理
        sendErrorMessage(initMessage(EventTypeEnum.TEXT.getType()), "The server is busy, please try again later");
        log.error("发送消息失败", e);
    }

    protected VirtualCompletableFuture<Void> sendContent(AgentMessageResp message, String content) {
        return VirtualCompletableFuture.runAsync(() -> {
            message.setContentValue(content);
            girlsDataService.sendAgentMessage(message);
        });
    }

    protected VirtualCompletableFuture<Void> sendContent(AgentMessageResp message, String type,
                                                  Object content) {
        return sendContent(message, type, content, true);
    }


    protected VirtualCompletableFuture<Void> sendContent(AgentMessageResp message, String type,
                                                  Object content, boolean isFinished) {
        return VirtualCompletableFuture.runAsync(() -> {
            message.setContentValue(content);
            message.setContentType(type);
            message.setIsFinished(isFinished);
            girlsDataService.sendAgentMessage(message);
        });
    }

    protected void sendContent(AgentMessageResp message) {
        girlsDataService.sendAgentMessage(message);
    }


    protected void sendErrorMessage(AgentMessageResp message, String error) {
        message.setIsFinished(true)
            .setContentValue(null)
            .setCode(1)
            .setErrorMessage(error);
        girlsDataService.sendAgentMessage(message);
    }

    protected void sendFinalMessage(AgentMessageResp message, Object content) {
        message.setContentValue(content)
            .setIsFinished(true);
        girlsDataService.sendAgentMessage(message);
    }


    protected void handleComplete(StreamingTextBuffer buffer, AgentMessageResp message) {
        String finalText = buffer.getFinalText();
        sendFinalMessage(message, finalText);
    }

    protected void handleThinkComplete(ThinkTextBuffer buffer, AgentMessageResp message, ThinkMessageDTO thinkMessageDTO) {
        thinkMessageDTO.setText(buffer.getFinalText());
        sendFinalMessage(message, thinkMessageDTO);
    }

    protected void handleChunk(String chunk, StreamingTextBuffer buffer, AgentMessageResp message) {
        buffer.appendChunk(chunk);

        if (buffer.hasEnoughNewWords()) {
            buffer.processAndUpdate();
            message.setContentValue(buffer.getCurrentText());
            girlsDataService.sendAsyncAgentMessage(message);
        }
    }


    protected void handleNoThinkChunk(String chunk, ThinkTextBuffer buffer, AgentMessageResp message) {
        buffer.appendChunk(chunk);
        if (buffer.hasEnoughNewWords()) {
            buffer.processAndUpdate();
            // 获取当前是哪个内容
            if (buffer.getNormalTextStarted()) {
                message.setContentValue(buffer.getCurrentText());
                girlsDataService.sendAsyncAgentMessage(message);
            }
        }
    }

    protected Mono<String> sendNoThinkStreamMessage(String prompt, Map<String, Object> variable) {
        AgentMessageResp message = initMessageAsync(EventTypeEnum.THINK.getType());
        ThinkTextBuffer textBuffer = new ThinkTextBuffer();
        ThinkMessageDTO thinkMessageDTO = new ThinkMessageDTO();

        return scheduleOperation(commonRequestService.commonExecuteStrategyAsync(prompt, variable)
            .doOnNext(chunk -> handleThinkChunk(chunk, textBuffer, message, thinkMessageDTO))
            .doOnError(error -> sendErrorMessage(message, error.getMessage()))
            .doOnComplete(() -> handleThinkComplete(textBuffer, message, thinkMessageDTO))
            .then(Mono.fromCallable(textBuffer::getFinalText)));
    }

    protected void handleThinkChunk(String chunk, ThinkTextBuffer buffer, AgentMessageResp message, ThinkMessageDTO thinkMessageDTO) {
        buffer.appendChunk(chunk);

        if (buffer.hasEnoughNewWords()) {
            buffer.processAndUpdate();
            // 获取当前是哪个内容
            if (buffer.getNormalTextStarted()) {
                if (buffer.isGetThinkFinishMessage()) {
                    thinkMessageDTO.setThinkContent(buffer.getThinkFinalText());
                    message.setContentValue(thinkMessageDTO);
                    girlsDataService.sendAgentMessage(message);
                }
                thinkMessageDTO.setText(buffer.getCurrentText());
            } else {
                thinkMessageDTO.setThinkContent(buffer.getThinkText());
            }
            message.setContentValue(thinkMessageDTO);
            girlsDataService.sendAsyncAgentMessage(message);
        }
    }



    protected void handleThinkChunkNoEmoji(String chunk, ThinkTextBuffer buffer, AgentMessageResp message, ThinkMessageDTO thinkMessageDTO) {

        buffer.appendChunk(StringMatchUtils.filterDeepSeekOutput(chunk));

        if (buffer.hasEnoughNewWords()) {
            buffer.processAndUpdate();
            // 获取当前是哪个内容
            if (buffer.getNormalTextStarted()) {
                if (buffer.isGetThinkFinishMessage()) {
                    thinkMessageDTO.setThinkContent(buffer.getThinkFinalText());
                    message.setContentValue(thinkMessageDTO);
                    girlsDataService.sendAgentMessage(message);
                }
                thinkMessageDTO.setText(buffer.getCurrentText());
            } else {
                thinkMessageDTO.setThinkContent(buffer.getThinkText());
            }
            message.setContentValue(thinkMessageDTO);
            girlsDataService.sendAsyncAgentMessage(message);
        }
    }


    protected Mono<String> sendThinkStreamMessageNoEmoji(String prompt, Map<String, Object> variable) {
        AgentMessageResp message = initMessageAsync(EventTypeEnum.THINK.getType());
        ThinkTextBuffer textBuffer = new ThinkTextBuffer();
        ThinkMessageDTO thinkMessageDTO = new ThinkMessageDTO();

        return scheduleOperation(commonRequestService.commonExecuteStrategyAsync(prompt, variable)
            .doOnNext(chunk -> handleThinkChunkNoEmoji(chunk, textBuffer, message, thinkMessageDTO))
            .doOnError(error -> sendErrorMessage(message, error.getMessage()))
            .doOnComplete(() -> handleThinkComplete(textBuffer, message, thinkMessageDTO))
            .then(Mono.fromCallable(textBuffer::getFinalText)));
    }

    protected Mono<String> sendThinkStreamMessageNoEmoji(AgentMessageResp message, String prompt, Map<String, Object> variable) {
        ThinkTextBuffer textBuffer = new ThinkTextBuffer();
        ThinkMessageDTO thinkMessageDTO = new ThinkMessageDTO();

        return scheduleOperation(commonRequestService.commonExecuteStrategyAsync(prompt, variable)
            .doOnNext(chunk -> handleThinkChunkNoEmoji(chunk, textBuffer, message, thinkMessageDTO))
            .doOnError(error -> sendErrorMessage(message, error.getMessage()))
            .doOnComplete(() -> handleThinkComplete(textBuffer, message, thinkMessageDTO))
            .then(Mono.fromCallable(textBuffer::getFinalText)));
    }


    protected Mono<String> sendThinkStreamMessage(String prompt, Map<String, Object> variable) {
        AgentMessageResp message = initMessageAsync(EventTypeEnum.THINK.getType());
        ThinkTextBuffer textBuffer = new ThinkTextBuffer();
        ThinkMessageDTO thinkMessageDTO = new ThinkMessageDTO();

        return scheduleOperation(commonRequestService.commonExecuteStrategyAsync(prompt, variable)
            .doOnNext(chunk -> handleThinkChunk(chunk, textBuffer, message, thinkMessageDTO))
            .doOnError(error -> sendErrorMessage(message, error.getMessage()))
            .doOnComplete(() -> handleThinkComplete(textBuffer, message, thinkMessageDTO))
            .then(Mono.fromCallable(textBuffer::getFinalText)));
    }

    protected Mono<String> sendThinkStreamMessage(RequestParamBO requestParamBO) {
        AgentMessageResp message = initMessageAsync(EventTypeEnum.THINK.getType());
        ThinkTextBuffer textBuffer = new ThinkTextBuffer();
        ThinkMessageDTO thinkMessageDTO = new ThinkMessageDTO();

        return scheduleOperation(commonRequestService.commonExecuteStrategyAsync(requestParamBO)
            .doOnNext(chunk -> handleThinkChunk(chunk, textBuffer, message, thinkMessageDTO))
            .doOnError(error -> sendErrorMessage(message, error.getMessage()))
            .doOnComplete(() -> handleThinkComplete(textBuffer, message, thinkMessageDTO))
            .then(Mono.fromCallable(textBuffer::getFinalText)));
    }


    protected Mono<String> sendThinkStreamMessage(PromptModel promptModel) {
        AgentMessageResp message = initMessageAsync(EventTypeEnum.THINK.getType());
        ThinkTextBuffer textBuffer = new ThinkTextBuffer();
        ThinkMessageDTO thinkMessageDTO = new ThinkMessageDTO();

        return scheduleOperation(commonRequestService.commonExecuteStrategyAsync(promptModel, Collections.emptyMap())
            .doOnNext(chunk -> handleThinkChunk(chunk, textBuffer, message, thinkMessageDTO))
            .doOnError(error -> sendErrorMessage(message, error.getMessage()))
            .doOnComplete(() -> handleThinkComplete(textBuffer, message, thinkMessageDTO))
            .then(Mono.fromCallable(textBuffer::getFinalText)));
    }


    protected <T> Mono<T> scheduleOperation(Mono<T> operation) {
        return operation.publishOn(messageScheduler);
    }

    protected Mono<String> sendStreamMessage(String prompt, Map<String, Object> variable) {
        AgentMessageResp message = initMessageAsync(EventTypeEnum.TEXT.getType());
        StreamingTextBuffer textBuffer = new StreamingTextBuffer();

        return scheduleOperation(commonRequestService.commonExecuteStrategyAsync(prompt, variable)
            .doOnNext(chunk -> handleChunk(chunk, textBuffer, message))
            .doOnError(error -> sendErrorMessage(message, error.getMessage()))
            .doOnComplete(() -> handleComplete(textBuffer, message))
            .then(Mono.fromCallable(textBuffer::getFinalText)));
    }


    protected Mono<String> sendStreamMessage(RequestParamBO promptModel) {
        AgentMessageResp message = initMessageAsync(EventTypeEnum.TEXT.getType());
        StreamingTextBuffer textBuffer = new StreamingTextBuffer();

        return scheduleOperation(commonRequestService.commonExecuteStrategyAsync(promptModel)
            .doOnNext(chunk -> handleChunk(chunk, textBuffer, message))
            .doOnError(error -> sendErrorMessage(message, error.getMessage()))
            .doOnComplete(() -> handleComplete(textBuffer, message))
            .then(Mono.fromCallable(textBuffer::getFinalText)));
    }

    protected Mono<String> sendStreamMessage(PromptModel promptModel) {
        AgentMessageResp message = initMessageAsync(EventTypeEnum.TEXT.getType());
        StreamingTextBuffer textBuffer = new StreamingTextBuffer();

        return scheduleOperation(commonRequestService.commonExecuteStrategyAsync(promptModel, Collections.emptyMap())
            .doOnNext(chunk -> handleChunk(chunk, textBuffer, message))
            .doOnError(error -> sendErrorMessage(message, error.getMessage()))
            .doOnComplete(() -> handleComplete(textBuffer, message))
            .then(Mono.fromCallable(textBuffer::getFinalText)));
    }


    protected ABTestFlagResp getAbTestFlag() {
        Object abTestFlag = Context.get(GrpcResponseHeaderInterceptor.KEY_NAME);
        return ABTestFlagUtils.parse(abTestFlag);
    }

    protected void sendLoading() {
        AgentMessageResp agentMessageResp = initMessage(EventTypeEnum.LOADING.getType());
        sendContent(agentMessageResp, EventTypeEnum.LOADING.getType(), null);
    }

    protected void sendLoading(String content) {
        AgentMessageResp agentMessageResp = initMessage(EventTypeEnum.LOADING.getType());
        sendContent(agentMessageResp, EventTypeEnum.LOADING.getType(), content);
    }
}