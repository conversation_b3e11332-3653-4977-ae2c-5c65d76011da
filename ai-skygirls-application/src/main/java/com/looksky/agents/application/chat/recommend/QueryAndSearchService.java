package com.looksky.agents.application.chat.recommend;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.data.client.service.OpenPerplexService;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.infrastructure.trace.annotation.TraceMethod;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import com.looksky.agents.sdk.perplex.dto.request.PerplexSearchRequest;
import com.looksky.agents.sdk.perplex.dto.response.PerplexSearchResponse;
import com.skygirls.biz.report.IosUserInfoDto;
import com.skygirls.biz.user.dto.AllReportDto;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;

/**
 * @ClassName SearchProcessService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/6 下午8:25
 * @Version 1.0
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class QueryAndSearchService {

    @Value("${spring.ai.openai.api-key}")
    private String openAiKey;

    private final CommonRequestService commonRequestService;
    private final OpenPerplexService openPerplexService;
    @Lazy
    @Resource
    private QueryAndSearchService self;  // 自注入

    public VirtualCompletableFuture<PerplexSearchResponse> queryAndSearchAsync() {
        return VirtualCompletableFuture.supplyAsync(this::queryAndSearch);
    }

    public PerplexSearchResponse queryAndSearch() {
        NewQueryAndIsSearch newQueryAndIsSearch = generateNewQueryAndIsSearch();
        return searchNewQuery(newQueryAndIsSearch);
    }

    @TraceMethod(description = "判断为需要调用外部搜索")
    @CollectEvent(extExpression = "#newQueryAndIsSearch.query")
    public PerplexSearchResponse searchNewQuery(NewQueryAndIsSearch newQueryAndIsSearch) {

        String newQuery = self.generateSearchQuery(newQueryAndIsSearch.getQuery());

        // 判断是否需要调用搜索
        if (newQueryAndIsSearch.isNeedSearch()) {
            //return searchQuery(newQuery);
            return self.searchQueryFromOpenAi(newQuery);
        } else {
            return self.defaultQuery(newQuery);
        }
    }

    @TraceMethod(description = "不用调用外部搜索")
    PerplexSearchResponse defaultQuery(String query) {
        PerplexSearchResponse search = new PerplexSearchResponse();
        search.setSearchContent(query);
        Context.put(Context.Name.SEARCH_RESULT.getName(), search);
        return search;
    }

    @TraceMethod(description = "给新 query 添加用户信息")
    String generateSearchQuery(String query) {
        StringBuilder sb = new StringBuilder();
        IosUserInfoDto userInfo = Context.get(Context.Name.USER_INFO.getName());

        AllReportDto appUser = userInfo.getAppUser();
        if (appUser != null) {

            if (appUser.getAge() != null) {
                sb.append("age is ").append(appUser.getAge()).append(",");
            }

            if (appUser.getBodyShape() != null) {
                sb.append("bodyShape is ").append(appUser.getBodyShape()).append(",");
            }
        }

        sb.append(query);

        return sb.toString();
    }
    @TraceMethod(description = "调用OpenAI搜素")
    PerplexSearchResponse searchQueryFromOpenAi(String query) {
        OpenAiChatModel chatModel = OpenAiChatModel.builder().openAiApi(OpenAiApi.builder().apiKey(openAiKey).build()).defaultOptions(OpenAiChatOptions.builder().model("gpt-4o-search-preview").build()).build();
        log.debug("调用 OpenAI 外部搜索");
        String chatResponse = chatModel.call(query);
        PerplexSearchResponse search = new PerplexSearchResponse();
        search.setLlmResponse(chatResponse);
        log.debug("外部搜索返回结果: {}", chatResponse);

        Context.put(Context.Name.SEARCH_RESULT.getName(), chatResponse);
        return search;

    }

    PerplexSearchResponse searchQuery(String query) {
        PerplexSearchResponse search;
        try {

            PerplexSearchRequest searchRequest = new PerplexSearchRequest();
            searchRequest.setQuery(query);

            search = openPerplexService.search(searchRequest);
        } catch (Exception e) {
            log.error("请求外部搜索出现错误", e);
            search = new PerplexSearchResponse();
        }

        Context.put(Context.Name.SEARCH_RESULT.getName(), JSONUtil.toJsonStr(search));
        return search;
    }

    @TraceMethod(description = "生成新 query, 并判断是否需要搜索")
    public NewQueryAndIsSearch generateNewQueryAndIsSearch() {
        String result = commonRequestService.commonExecuteStrategy(PromptNameEnum.ENW_QUERY_AND_IS_SEARCH.getName());
        return JSONUtil.toBean(result, NewQueryAndIsSearch.class);
    }

    public VirtualCompletableFuture<NewQueryAndIsSearch> generateNewQueryAndIsSearchAsync() {
        return VirtualCompletableFuture.supplyAsync(this::generateNewQueryAndIsSearch);
    }

    @Data
    public static class NewQueryAndIsSearch {
        private String query;
        @JsonProperty("need_search")
        private boolean needSearch;
    }




}
