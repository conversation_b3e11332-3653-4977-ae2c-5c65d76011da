package com.looksky.agents.application.foryou.convertor;

import cn.hutool.core.text.CharSequenceUtil;
import com.looksky.agents.sdk.recommend.common.dto.VectorRecallModelDTO;
import com.looksky.agents.sdk.recommend.foryou.dto.ForYouCategoryQuery;
import com.looksky.agents.sdk.recommend.foryou.dto.ForYouPromptResultMultipleResult;
import com.looksky.agents.sdk.recommend.foryou.dto.PartitionRecomModelDTO;
import com.looksky.agents.sdk.recommend.foryou.enums.RecommendScenEnum;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;

/**
 * ForYou模块的MapStruct映射器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ForYouConvertor {
    /**
     * 将ForYouPromptResultMultipleResult转换为PartitionRecomModelDTO
     *
     * @param result ForYouPromptResultMultipleResult对象
     * @param scenEnum 推荐场景枚举
     * @return PartitionRecomModelDTO对象
     */
    @Mapping(target = "partitionRecomScenes", source = "scenEnum")
    @Mapping(target = "hashtag", expression = "java(result.getTheme() != null ? result.getTheme().getThemeTitle() : null)")
    @Mapping(target = "title", expression = "java(result.getTheme() != null ? result.getTheme().getMarketingMessage() : null)")
    @Mapping(target = "recallVectors", source = "result", qualifiedByName = "toCategoryVectorRecall")
    PartitionRecomModelDTO toPartitionRecomModelDTO(ForYouPromptResultMultipleResult result, RecommendScenEnum scenEnum);

    /**
     * 将ForYouPromptResultMultipleResult转换为VectorRecallModelDTO列表
     *
     * @param result ForYouPromptResultMultipleResult对象
     * @return VectorRecallModelDTO列表
     */
    @Named("toCategoryVectorRecall")
    default List<VectorRecallModelDTO> toCategoryVectorRecall(ForYouPromptResultMultipleResult result) {
        if (result == null || result.getCategoryQueries() == null) {
            return Collections.emptyList();
        }
        
        return result.getCategoryQueries().stream()
                .map(this::categoryToVectorRecall)
                .toList();
    }
    
    /**
     * 将单个ForYouCategoryQuery转换为VectorRecallModelDTO
     *
     * @param category ForYouCategoryQuery对象
     * @return VectorRecallModelDTO对象
     */
    @Named("categoryToVectorRecall")
    default VectorRecallModelDTO categoryToVectorRecall(ForYouCategoryQuery category) {
        List<SearchRequestDTO.VectorQuery> vectorQueries = createVectorQueries(
                category.getPositiveQuery(), 
                category.getNegativeQuery()
        );

        var builder = VectorRecallModelDTO.builder().vectorQuery(vectorQueries);
        if (CharSequenceUtil.isNotBlank(category.getCategory())) {
            builder.recallStrategy(category.getCategory());
        } else {
            builder.recallStrategy(category.getFilterCategory());
        }
        Optional.ofNullable(category.getFilterCategory()).ifPresent( c -> builder.mustSubCategory(List.of(c)));

        return builder.build();
    }
    
    /**
     * 创建向量查询列表
     *
     * @param positiveQuery 正向查询
     * @param negativeQuery 负向查询
     * @return 向量查询列表
     */
    @Named("createVectorQueries")
    default List<SearchRequestDTO.VectorQuery> createVectorQueries(String positiveQuery, String negativeQuery) {
        return Stream.of(
                SearchRequestDTO.VectorQuery.builder()
                        .text(positiveQuery)
                        .weight(1)
                        .build(),
                SearchRequestDTO.VectorQuery.builder()
                        .text(negativeQuery)
                        .weight(-1)
                        .build()
        ).filter(query -> CharSequenceUtil.isNotBlank(query.getText())).toList();
    }
} 