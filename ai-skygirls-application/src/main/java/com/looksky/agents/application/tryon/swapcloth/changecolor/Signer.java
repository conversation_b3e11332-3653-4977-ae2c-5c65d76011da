package com.looksky.agents.application.tryon.swapcloth.changecolor;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 美图的签名工具类
 *
 * @since  1.1.8
 * <AUTHOR>
 **/
@SuppressWarnings("all")
public class Signer {
    public static final String BasicDateFormat = "yyyyMMdd'T'HHmmss'Z'";
    public static final String Algorithm = "SDK-HMAC-SHA256";
    public static final String HeaderXDate = "X-Sdk-Date";
    public static final String HeaderHost = "Host";
    public static final String HeaderAuthorization = "Authorization";
    public static final String HeaderContentSha256 = "X-Sdk-Content-Sha256";

    private String Key;
    private String Secret;

    public Signer(String key, String secret) {
        this.Key = key;
        this.Secret = secret;
    }

    public String signStringToSign(String stringToSign, String signingKey) {
        try {
            Mac sha256Hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(signingKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256Hmac.init(secretKey);
            byte[] signedBytes = sha256Hmac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(signedBytes);
        } catch (Exception e) {
            throw new RuntimeException("Unable to calculate a request signature: " + e.getMessage(), e);
        }
    }

    public String authHeaderValue(String signature, String accessKey, List<String> signedHeaders) {
        StringBuilder signedHeadersStr = new StringBuilder();
        for (String header : signedHeaders) {
            if (signedHeadersStr.length() > 0) {
                signedHeadersStr.append(";");
            }
            signedHeadersStr.append(header);
        }
        String headerValue = Algorithm + " Access=" + accessKey + ", SignedHeaders=" + signedHeadersStr.toString() + ", Signature=" + signature;
        return "Bearer " + Base64.getEncoder().encodeToString(headerValue.getBytes(StandardCharsets.UTF_8));
    }

    public String sortQueryParams(String queryParam) {
        if (queryParam == null ||queryParam.equals("")){
            return "";
        } 
        String[] params = queryParam.split("&");
        Arrays.sort(params);
        for (int i = 0; i < params.length; i++) {
          String[] keyValue = params[i].split("=");
          try {
              String key = URLEncoder.encode(keyValue[0], "UTF-8");
              String value = keyValue.length > 1 ? URLEncoder.encode(keyValue[1], "UTF-8") : "";
              params[i] = key + "=" + value;
          } catch (UnsupportedEncodingException e) {
              throw new RuntimeException("Failed to encode query parameters: " + e.getMessage(), e);
          }
        }
        return String.join("&", params);
    }

    public String canonicalRequest(String method, String url, Map<String, String> headers, String body, List<String> signedHeaders) throws URISyntaxException {
        URI uri = new URI(url);
        String canonicalURI = canonicalUri(uri.getPath());

        String canonicalQueryString = uri.getQuery();
        if (canonicalQueryString == null) {
            canonicalQueryString = "";
        }
        canonicalQueryString = sortQueryParams(canonicalQueryString);
       
        String canonicalHeaders = canonicalHeaders(headers, signedHeaders);
        String signedHeadersStr = String.join(";", signedHeaders);
        String locBody = body;
        if (locBody == null) {
            locBody = "";
        }
        String hexencode = "";
        if (headers.get(HeaderContentSha256) != null) {
          hexencode = headers.get(HeaderContentSha256);
        }else{
          hexencode = hash(locBody);
        }
        return method + "\n" +
                canonicalURI + "\n" +
                canonicalQueryString + "\n" +
                canonicalHeaders + "\n" +
                signedHeadersStr.toString() + "\n" +
                hexencode;
    }

    public String canonicalUri(String path) {
        if (path == null || path.isEmpty() || !path.endsWith("/")) {
            path = path + "/";
        }
        return path;
    }

    public String canonicalHeaders(Map<String, String> headers, List<String> signedHeaders) {
        Map<String, String> lowheaders = new HashMap<>();
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            lowheaders.put(entry.getKey().toLowerCase(), entry.getValue().trim());
        }
        List<String> a = new ArrayList<>();
        for (String key : signedHeaders) {
            a.add(key + ':' + lowheaders.get(key));
        }
        return join("\n", a);
    }

    public List<String> signedHeaders(Map<String, String> headers) {
        List<String> signedHeaders = new ArrayList<>();
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            signedHeaders.add(entry.getKey().toLowerCase());
        }
        Collections.sort(signedHeaders);
        return signedHeaders;
    }

    public Map<String,String> sign(String url, String method, Map<String,String> headers, String body) throws URISyntaxException {
        LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(BasicDateFormat);

        if (!headers.containsKey(HeaderXDate)) {
            headers.put(HeaderXDate, now.format(formatter));
        } else {
            now = LocalDateTime.parse(headers.get(HeaderXDate), formatter);
        }
        List<String> signedHeaders = this.signedHeaders(headers);
        String canonicalRequest = this.canonicalRequest(method, url, headers, body, signedHeaders);
        String stringToSign = this.stringToSign(canonicalRequest, now.format(formatter));
        String signature = this.signStringToSign(stringToSign, this.Secret);

        headers.put(HeaderAuthorization, this.authHeaderValue(signature, this.Key, signedHeaders));
        return headers;
    }

    public String stringToSign(String canonicalRequest, String timeFormat) {
        return Algorithm + "\n" +
                timeFormat + "\n" +
                hash(canonicalRequest);
    }

    private static String join(String delimiter, List<String> list) {
        StringBuilder sb = new StringBuilder();
        for (String item : list) {
            if (sb.length() > 0) {
                sb.append(delimiter);
            }
            sb.append(item);
        }
        return sb.toString();
    }

    private static String hash(String text) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(text.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hash);
        } catch (Exception e) {
            throw new RuntimeException("Unable to calculate a SHA-256 hash: " + e.getMessage(), e);
        }
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}