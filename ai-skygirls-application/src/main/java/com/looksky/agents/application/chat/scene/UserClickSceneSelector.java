package com.looksky.agents.application.chat.scene;


import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.common.enums.EventNameEnum;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Order(1)
@Component
public class UserClickSceneSelector extends BaseSceneSelector {

    @Override
    public boolean support(String eventName, EnterPointEnum enterPoint, String page) {
        return EventNameEnum.USER_CLICK.getValue().equals(eventName);
    }

    @Override
    protected SceneResult getSceneAndExternInfo(RequestInputDTO requestInput) {
        return null;
    }

} 