package com.looksky.agents.application.chat.reply.stream;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.reply.AbstractMessageSender;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import com.looksky.agents.sdk.agent.common.dto.ShortcutInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.product.enums.SearchQuestionType;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import java.util.HashMap;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 细化用户需求, 询问用户更多信息的回复, 然后发送快捷提问
 *
 * <AUTHOR>
 * @since 1.1.11
 **/
@Order(1)
@Component
@ApiVersion(platform = ApiVersion.PlatformType.ALL, version = "1.1.12")
public class AskUserInfoMessageSenderV2 extends AbstractMessageSender {

    /**
     * 细化用户需求 Prompt
     */
    private static final String ASK_USER_INFO_PROMPT = SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();

    /**
     * 建议问题的 Prompt
     */
    private static final String ADVICE_QUESTION_PROMPT = "ask_more_information_and_shortcut_input";


    @Override
    protected boolean supports(PromptModel strategy) {
        return ASK_USER_INFO_PROMPT.equals(strategy.getName());
    }

    @Override
    protected void doSend(PromptModel prompt, HashMap<String, Object> hashMap) {

        String result = commonRequestService.commonExecuteStrategy(ADVICE_QUESTION_PROMPT);

        ShortcutInputDTO shortcutInput = JSONUtil.toBean(result, ShortcutInputDTO.class);

        sendFinalMessage(initMessage(EventTypeEnum.TEXT.getType()), shortcutInput.getResponse());

        sendFinalMessage(initMessage(EventTypeEnum.SHORTCUT_INPUT.getType()), shortcutInput);

        extraMessage(EventTypeEnum.TEXT.getType(), shortcutInput.getResponse(), hashMap);

    }

    @Override
    protected void afterSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        super.afterSend(strategy, hashMap);
        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());
        conversationStatus.setRefiningRequirements(true);
    }

}