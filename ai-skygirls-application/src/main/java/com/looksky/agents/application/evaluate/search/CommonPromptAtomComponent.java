package com.looksky.agents.application.evaluate.search;

import com.looksky.agents.application.evaluate.prompt.OptimizePromptService;
import com.looksky.agents.sdk.optimize.dto.OptimizePromptResultDTO;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 生成新的查询并判断是否需要搜索原子组件
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CommonPromptAtomComponent {

    private final OptimizePromptService optimizePromptService;

    public OptimizePromptResultDTO run(String promptName, DatasetRecordDTO datasetRecord) {
        return optimizePromptService.batchRunPrompt(promptName, datasetRecord);
    }
}
