package com.looksky.agents.application.evaluate.tryon;

import com.looksky.agents.application.tryon.colorseason.impl.TryOnColorSeasonServiceV4;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import com.looksky.agents.sdk.optimize.dto.OptimizePromptResultDTO;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonParamV1;
import jakarta.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 1.1.8
 **/
@Slf4j
@Component
public class RunPodServerlessTryOnAtomComponent {

    @Resource
    private TryOnColorSeasonServiceV4 tryOnColorSeasonServiceV4;

    public OptimizePromptResultDTO run(DatasetRecordDTO datasetRecord) {
        long start = System.currentTimeMillis();
        try {
            TryOnColorSeasonParamV1 param = new TryOnColorSeasonParamV1();

            Map<String, Object> data = datasetRecord.getData();
            if (data != null) {
                Optional.ofNullable((String) data.get("userId")).ifPresent(param::setUserId);
                Optional.ofNullable((String) data.get("userImage")).ifPresent(param::setUserImage);
                Optional.ofNullable((String) data.get("clothImage")).ifPresent(param::setClothImage);
            }

            String url = tryOnColorSeasonServiceV4.tryOnWhiteT(param);
            long end = System.currentTimeMillis();
            return new OptimizePromptResultDTO(url, 0L, end - start);

        } catch (Exception e) {
            log.error("请求失败: {}", e.getMessage());
            long end = System.currentTimeMillis();
            return new OptimizePromptResultDTO(e.getMessage(), 0L, end - start);
        }

    }

}
