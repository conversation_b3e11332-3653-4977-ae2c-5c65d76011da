package com.looksky.agents.application.tryon.colorseason;

import com.looksky.agents.data.redis.config.RedisCodecFactory;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.exception.BusinessException;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.infrastructure.thread.VirtualThreadExecutor;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonParamV1;
import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

/**
 * 颜色季节服务门面，提供自动容错功能
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TryOnColorSeasonFacade {

    private static final String SERVICE_STATUS_KEY = "tryon:colorseason:service:status";
    // 服务保底机制：重置所有服务时，每个服务只给一次尝试机会，避免在服务全部不可用且全部有问题时反复重置导致资源浪费
    private static final String RESET_ALL_FLAG_KEY = "tryon:colorseason:reset:all:flag";
    // 保底机制重置冷却时间
    private static final Duration RESET_ALL_COOLDOWN = Duration.ofSeconds(5);
    // 整个方法的全局超时时间（60秒）
    private static final Duration GLOBAL_TIMEOUT = Duration.ofSeconds(60);
    // 默认优先级（如果从服务实现获取失败）
    private static final int DEFAULT_PRIORITY = Integer.MAX_VALUE;
    // 失败阈值
    private static final int FAILURE_THRESHOLD = 3;

    private final RedissonClient redissonClient;
    private final RedisCodecFactory redisCodecFactory;

    // 所有可用的服务实现，按照优先级排序
    private final List<ITryOnColorSeasonService> serviceImpls;

    // 优先级排序的服务实现映射
    private Map<String, ITryOnColorSeasonService> versionToServiceMap;

    @PostConstruct
    public void init() {
        // 创建版本到服务的映射
        versionToServiceMap = serviceImpls.stream().collect(Collectors.toMap(ITryOnColorSeasonService::getVersion, service -> service));

        // 初始化服务状态
        initServiceStatus();
    }

    /**
     * 获取服务的优先级
     *
     * @param service 服务实现
     * @return 优先级数值（数字越小优先级越高）
     */
    private int getServicePriority(ITryOnColorSeasonService service) {
        Order order = AnnotationUtils.findAnnotation(service.getClass(), Order.class);
        return order != null ? order.value() : DEFAULT_PRIORITY;
    }

    /**
     * 初始化服务状态
     */
    private void initServiceStatus() {
        RMap<String, TryOnColorSeasonServiceStatus> statusMap = redissonClient.getMap(SERVICE_STATUS_KEY, redisCodecFactory.createMapCodec(String.class, TryOnColorSeasonServiceStatus.class));
        versionToServiceMap.forEach((version, service) -> {
            if (!statusMap.containsKey(version)) {
                TryOnColorSeasonServiceStatus status = new TryOnColorSeasonServiceStatus();
                // 从服务实现类获取优先级
                status.updatePriority(getServicePriority(service));
                // 从服务实现类获取超时时间
                status.updateTimeout(service.getTimeout());
                statusMap.put(version, status);
                log.info("初始化服务 {} 状态: 优先级={}, 超时={}ms", version, status.getPriority(), status.getTimeoutMillis());
            }
        });

    }

    /**
     * 重置所有服务的状态为可用
     *
     * @return 是否执行了重置操作
     */
    private boolean resetAllServicesIfNeeded() {
        // 获取上次重置的时间戳
        RBucket<Long> resetTimeBucket = redissonClient.getBucket(RESET_ALL_FLAG_KEY);
        Long lastResetTime = resetTimeBucket.get();
        long currentTime = System.currentTimeMillis();

        // 如果上次重置时间为空，或者已经超过冷却时间，则执行重置
        if (lastResetTime == null || (currentTime - lastResetTime) > RESET_ALL_COOLDOWN.toMillis()) {
            RMap<String, TryOnColorSeasonServiceStatus> statusMap = redissonClient.getMap(SERVICE_STATUS_KEY, redisCodecFactory.createMapCodec(String.class, TryOnColorSeasonServiceStatus.class));

            // 记录所有服务状态
            log.warn("执行全部服务重置保底机制：所有服务不可用，将尝试重置所有服务状态");

            // 重置所有服务状态
            for (String version : versionToServiceMap.keySet()) {
                TryOnColorSeasonServiceStatus status = statusMap.get(version);
                if (status != null) {
                    status.markAsAvailable();
                    // 设置一个较低的失败计数阈值，一次失败就再次标记为不可用
                    status.setFailureCount(FAILURE_THRESHOLD - 1);
                    statusMap.put(version, status);
                    log.info("重置服务 {} 为可用状态", version);
                }
            }

            // 更新重置时间戳
            resetTimeBucket.set(currentTime);
            return true;
        }

        // 在冷却期内，无法再次重置
        log.warn("所有服务都不可用，但上次全局重置时间距今不足 {} 分钟，暂不执行全局重置", RESET_ALL_COOLDOWN.toMinutes());
        return false;
    }

    /**
     * 对外提供的换白T服务入口，自动容错，并有60秒全局超时限制
     */
    @CollectEvent
    public String tryOnWhiteT(TryOnColorSeasonParamV1 param) {
        return CompletableFuture.supplyAsync(() -> {
                try {
                    return executeWithAutoFailover(param);
                } catch (Exception e) {
                    log.error("执行换白T过程中发生异常", e);
                    throw new BusinessException("执行换白T失败: " + e.getMessage());
                }
            }, VirtualThreadExecutor.get())
            .orTimeout(GLOBAL_TIMEOUT.toMillis(), TimeUnit.MILLISECONDS)
            .exceptionally(throwable -> {
                Throwable cause = throwable instanceof ExecutionException ? throwable.getCause() : throwable;

                if (cause instanceof TimeoutException) {
                    log.error("换白T全局执行超时，已超过{}秒", GLOBAL_TIMEOUT.getSeconds());
                    throw new BusinessException("换白T请求处理超时，请稍后重试");
                } else {
                    log.error("换白T执行过程中发生未预期异常", cause);
                    throw new BusinessException("换白T处理失败: " + cause.getMessage());
                }
            })
            .join();
    }

    private List<String> getAvailableVersionsTryResetting(RMap<String, TryOnColorSeasonServiceStatus> statusMap) {
        // 获取所有可用服务版本，按优先级排序
        List<String> availableVersions = getAvailableVersions(statusMap);

        // 如果没有可用服务，尝试重置所有服务
        if (availableVersions.isEmpty()) {
            boolean reset = resetAllServicesIfNeeded();
            if (reset) {
                // 重新获取可用服务
                availableVersions = getAvailableVersions(statusMap);
            }

            // 如果重置后仍然没有可用服务，返回失败
            if (availableVersions.isEmpty()) {
                log.error("所有颜色季节服务都不可用，且无法执行全局重置");
                throw new BusinessException("所有颜色季节服务都不可用");
            }
        }
        return availableVersions;
    }

    /**
     * 执行换白T的核心逻辑，带自动容错功能
     */
    private String executeWithAutoFailover(TryOnColorSeasonParamV1 param) {
        // 获取服务状态
        RMap<String, TryOnColorSeasonServiceStatus> statusMap = redissonClient.getMap(SERVICE_STATUS_KEY, redisCodecFactory.createMapCodec(String.class, TryOnColorSeasonServiceStatus.class));

        // 获取所有可用服务版本并尝试重置，按优先级排序
        List<String> availableVersions = getAvailableVersionsTryResetting(statusMap);

        log.info("尝试按优先级调用服务: {}", String.join("->", availableVersions));

        // 依次尝试调用服务，直到成功或全部失败
        for (String version : availableVersions) {
            ITryOnColorSeasonService service = versionToServiceMap.get(version);
            TryOnColorSeasonServiceStatus status = statusMap.get(version);

            if (service == null || status == null) {
                continue;
            }

            try {
                // 使用异步调用，设置超时
                String result = executeWithTimeout(service, param, status.getTimeoutMillis());

                if (result != null) {
                    // 调用成功，重置失败计数
                    if (status.getFailureCount() > 0) {
                        status.resetFailureCount();
                        statusMap.put(version, status);
                    }
                    log.info("服务 {} 调用成功", version);
                    return result;
                }

                // 返回空结果，视为失败
                handleServiceFailure(statusMap, version, status);
                log.warn("服务 {} 返回空结果，尝试下一个服务", version);

            } catch (Exception e) {
                // 处理异常，标记失败
                handleServiceFailure(statusMap, version, status);
                if (e.getCause() instanceof TimeoutException) {
                    log.error("服务 {} 调用超时 ({}ms)，尝试下一个服务", version, status.getTimeoutMillis());
                } else {
                    log.error("服务 {} 调用异常: {}，尝试下一个服务", version, e.getMessage());
                }
            }
        }

        log.error("所有可用服务调用失败");
        throw new BusinessException("所有颜色季节服务调用失败");
    }

    /**
     * 获取所有可用的服务版本列表，按优先级排序
     */
    private List<String> getAvailableVersions(RMap<String, TryOnColorSeasonServiceStatus> statusMap) {
        return versionToServiceMap.keySet().stream()
            .filter(version -> Optional.ofNullable(statusMap.get(version))
                .map(TryOnColorSeasonServiceStatus::isAvailable)
                .orElse(false))
            .sorted(Comparator.comparingInt(v -> Optional.ofNullable(statusMap.get(v)).map(TryOnColorSeasonServiceStatus::getPriority).orElse(DEFAULT_PRIORITY)))
            .collect(Collectors.toCollection(ArrayList::new));
    }


    /**
     * 在限定时间内执行服务调用
     */
    private String executeWithTimeout(ITryOnColorSeasonService service, TryOnColorSeasonParamV1 param, long timeoutMillis) {
        try {
            VirtualCompletableFuture<String> future = VirtualCompletableFuture.supplyAsync(() -> service.tryOnWhiteT(param));
            return future.orTimeout(timeoutMillis, TimeUnit.MILLISECONDS).join();
        } catch (Exception e) {
            log.error("服务调用异常", e);
            throw e;
        }
    }

    /**
     * 处理服务调用失败
     */
    private void handleServiceFailure(RMap<String, TryOnColorSeasonServiceStatus> statusMap, String version, TryOnColorSeasonServiceStatus status) {
        // 记录失败
        status.recordFailure();

        // 如果达到失败阈值，标记为不可用
        if (status.getFailureCount() >= FAILURE_THRESHOLD) { // 使用固定的失败阈值
            status.markAsUnavailable();
            log.warn("服务 {} 已达到失败阈值 {}，标记为不可用", version, FAILURE_THRESHOLD);
        }

        // 更新状态
        statusMap.put(version, status);
    }
} 