package com.looksky.agents.application.colorSeason;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.common.utils.UserLocationUtils;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.data.client.service.GirlsDataService;
import com.looksky.agents.data.grpc.ColorSeasonGrpcClient;
import com.looksky.agents.data.redis.daily100.Daily100DataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.recommend.colorseason.request.ColorSeasonRequestDTO;
import com.looksky.agents.sdk.recommend.colorseason.response.ColorSeasonResponseDTO;
import com.looksky.agents.sdk.recommend.common.dto.ColorSeasonCacheDataDTO;
import com.looksky.agents.sdk.recommend.common.dto.Face2HairCacheDataDTO;
import com.looksky.agents.sdk.recommend.common.dto.UserPageKibbeCacheDataDTO;
import com.looksky.agents.sdk.recommend.daily100.bo.RegionalCategoryQuery;
import com.looksky.agents.sdk.utils.date.DateUtils;
import com.skygirls.biz.report.IosUserInfoDto;
import com.skygirls.biz.user.dto.UserLocationInfoDTO;
import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ColorSeasonV1Service {

    private static final String PROMPT_NAME = "for_you_color_season";

    private final ColorSeasonGrpcClient colorSeasonGrpcClient;
    private final CommonRequestService commonRequestService;
    private final HttpServletRequest httpServletRequest;
    private final GirlsDataService girlsDataService;
    private final Daily100DataService daily100DataService;
    private final ObjectMapper objectMapper;

    public ColorSeasonResponseDTO seasonColor(ColorSeasonRequestDTO colorSeasonRequestDTO) {
        UserLocationInfoDTO location = UserLocationUtils.getLocation(httpServletRequest);
        Context.put(Context.Name.USER_LOCATION.getName(), location);
        IosUserInfoDto userInfo = girlsDataService.userInfoPreRegistration(colorSeasonRequestDTO.getUserId());
        convertToColorSeasonRequestDTO(colorSeasonRequestDTO, userInfo);
        return recommend(colorSeasonRequestDTO);
    }



    // 执行 prompt
    @SneakyThrows
    private RegionalCategoryQuery executePrompt(Map<String, Object> data) {
        RegionalCategoryQuery categoryQuery = objectMapper.readValue(commonRequestService.commonExecuteStrategy(PROMPT_NAME, data), RegionalCategoryQuery.class);
        // 将 categoryQuery 中 所有的 category 都替换为 filterCategory
        categoryQuery.getRegionCategories().forEach(category -> category.setCategory(category.getFilterCategory()));
        return categoryQuery;
    }

    // 转为 GirlsColorSeasonResponseDTO
    private void convertToColorSeasonRequestDTO(ColorSeasonRequestDTO colorSeasonRequestDTO, IosUserInfoDto userInfo) {
        Map<String, Object> data = buildDaily100Data(userInfo, colorSeasonRequestDTO);
        RegionalCategoryQuery regionalCategoryQuery = executePrompt(data);
        colorSeasonRequestDTO.setVectorRecall(regionalCategoryQuery.toCategoryVectorRecall());
        colorSeasonRequestDTO.setVectorQueryVersion(IdUtil.getSnowflakeNextIdStr());
        colorSeasonRequestDTO.setDate(DateUtils.getNowDate(null));
    }

    private ColorSeasonResponseDTO recommend(ColorSeasonRequestDTO colorSeasonRequestDTO) {
        return colorSeasonGrpcClient.colorSeason(colorSeasonRequestDTO);
    }


    private Map<String, Object> buildDaily100Data(IosUserInfoDto userInfoDto,
                                                  ColorSeasonRequestDTO req) {
        RequestInputDTO requestVo = buildRequestVo(req, userInfoDto);
        Context.put(Context.Name.REQUEST_INPUT.getName(), requestVo);
        Map<String, Object> daily100Data = new HashMap<>(Map.of(
            Context.Name.USER_INFO.getName(), userInfoDto,
            Context.Name.REQUEST_INPUT.getName(), requestVo
        ));

        enrichDaily100Data(daily100Data, userInfoDto);
        return daily100Data;
    }

    /**
     * 构建请求对象，包含用户位置信息
     *
     * @return 包含城市信息的请求对象
     */
    private RequestInputDTO buildRequestVo(ColorSeasonRequestDTO req, IosUserInfoDto userInfoDto) {
        RequestInputDTO requestVo = RequestInputDTO.builder().build();

        Optional.ofNullable(req.getUserId()).ifPresent(requestVo::setUserId);

        Optional.ofNullable(userInfoDto.getUserSeasonAndCityCacheDto()).ifPresent(userSeasonAndCityCacheDto -> {
            requestVo.setCity(userSeasonAndCityCacheDto.getCity());
            requestVo.setSeason(userSeasonAndCityCacheDto.getSeason());
        });

        if (StrUtil.isBlankIfStr(requestVo.getCity())) {
            UserLocationInfoDTO userLocation = UserLocationUtils.getLocation(httpServletRequest);
            Optional.ofNullable(userLocation).ifPresentOrElse(location -> requestVo.setCity(location.getViewerCity()), () -> log.error("未获取到用户位置信息"));
        }

        if (StrUtil.isBlankIfStr(requestVo.getSeason())) {
            requestVo.setSeason(DateUtils.getCurrentSeason());
        }

        return requestVo;
    }

    /**
     * 使用用户的Kibbe类型、颜色季节和脸型数据丰富Daily100数据
     *
     * @param daily100Data 待丰富的数据Map
     * @param userInfoDto  用户信息对象
     */
    private void enrichDaily100Data(Map<String, Object> daily100Data, IosUserInfoDto userInfoDto) {

        if (userInfoDto != null && userInfoDto.getAppUser() != null) {
            Optional.ofNullable(userInfoDto.getAppUser().getKibbeType()).ifPresent(kbType -> {
                UserPageKibbeCacheDataDTO kbTypeDto = daily100DataService.getKbType(kbType.getCode());
                daily100Data.put("kbType", kbTypeDto);
            });

            Optional.ofNullable(userInfoDto.getAppUser().getColorSeason()).ifPresent(colorSeason -> {
                ColorSeasonCacheDataDTO colorSeasonCacheDataDto = daily100DataService.getColorSeason(colorSeason.getName());
                daily100Data.put("colorSeason", colorSeasonCacheDataDto);
            });

            Optional.ofNullable(userInfoDto.getAppUser().getHairColor()).ifPresent(face2Hair -> {
                Face2HairCacheDataDTO face2HairCacheDataDto = daily100DataService.getFace2Hair(face2Hair.getName());
                daily100Data.put("face2Hair", face2HairCacheDataDto);
            });
        }


        daily100Data.put("month", DateUtils.getMonth());
        daily100Data.put("nextMonth", DateUtils.getNextMonth());
        daily100Data.put("nextNextMonth", DateUtils.getNextNextMonth());
    }


}
