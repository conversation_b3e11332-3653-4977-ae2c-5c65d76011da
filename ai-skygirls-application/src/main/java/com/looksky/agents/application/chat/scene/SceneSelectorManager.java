package com.looksky.agents.application.chat.scene;


import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SceneSelectorManager {
    
    private final List<BaseSceneSelector> selectors;

    /**
     * 获取策略选择器
     * @param eventName
     * @param enterPointEnum
     * @return
     */
    public BaseSceneSelector getSceneSelector(String eventName, EnterPointEnum enterPointEnum, String page) {
        return selectors.stream()
                .filter(selector -> selector.support(eventName, enterPointEnum, page))
                .findFirst()
                .orElse(getDefaultSelector()); 
    }

    public PromptModel getPromptModel(RequestInputDTO requestInput) {
        return getSceneSelector(requestInput.getEvent().getEventName(), requestInput.getEnterPointEnum(), requestInput.getPage()).getScene(requestInput);
    }
    
    private BaseSceneSelector getDefaultSelector() {
        return selectors.stream()
                .filter(selector -> selector instanceof EventSceneSelector)
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("No default strategy selector found"));
    }
} 