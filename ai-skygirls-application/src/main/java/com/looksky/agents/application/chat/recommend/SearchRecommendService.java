package com.looksky.agents.application.chat.recommend;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.data.grpc.SearchGrpcClient;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.TagValueEnum;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.preference.SubjectivePreference;
import com.looksky.agents.sdk.agent.search.bo.CategoriesPositiveAndNegativeQueries;
import com.looksky.agents.sdk.agent.search.bo.UserRequirements;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import com.looksky.agents.sdk.recommend.search.dto.response.SearchResponseDTO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName SearchRecommendService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/6 下午10:04
 * @Version 1.0
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class SearchRecommendService {
    private final CommonRequestService commonRequestService;
    private final SearchGrpcClient girlsAgentSearchGrpcClient;
    private final TagSystemTableService tagSystemTableService;

    public VirtualCompletableFuture<SearchResponseDTO> recommendAsync() {
        return VirtualCompletableFuture.supplyAsync(this::recommend);
    }

    public SearchResponseDTO recommend() {
        CategoriesPositiveAndNegativeQueries userRequirements = generateSearchParam();
        return recommendProduct(userRequirements);
    }

    private CategoriesPositiveAndNegativeQueries generateSearchParam() {
        HashMap<String, Object> maps = new HashMap<>();
        ExtractedEntityObject prevPreference = Context.get(Context.Name.PREFERENCE.getName());

        Set<String> currentCategories = prevPreference.getCurrentCategories();
        maps.put("currentCategories", new ArrayList<>(currentCategories));


        String prompt = "search_convert_to_vector";


        String result = commonRequestService.commonExecuteStrategyCheck(prompt, maps);
        return JSONUtil.toBean(result, CategoriesPositiveAndNegativeQueries.class);
    }

    public SearchResponseDTO recommendProduct(CategoriesPositiveAndNegativeQueries userRequirements) {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());

        SearchRequestDTO searchRequestDTO = SearchRequestDTO.builder()
            .userId(requestInput.getUserId())
            .season(getSeason(requestInput))
            .strategyTerms(buildGirlsStrategyTerm(userRequirements))
            .build();

        try {
            return girlsAgentSearchGrpcClient.search(searchRequestDTO);
        } catch (Exception e) {
            log.error("grpc 接口请求失败, 返回空结果");
            return new SearchResponseDTO();
        }


    }

    private String getSeason(RequestInputDTO requestInput) {
        String season = null;

        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());
        SubjectivePreference subjectivePreference = userPreference.getSubjectivePreference();
        if (ObjectUtil.isNotEmpty(subjectivePreference)) {
            season = subjectivePreference.getSeason();
        }

        if (StrUtil.isEmptyIfStr(season)) {
            season = requestInput.getSeason();
        }

        if (StrUtil.isEmptyIfStr(season)) {
            season = null;
        }

        return season;
    }


    private void setTagValue(SearchRequestDTO.SearchTerm vectorQueries) {
        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());
        PreferenceToSearchConverter.convertToSearchTerm(vectorQueries, userPreference);
    }

    private List<SearchRequestDTO.GirlsStrategyTerm> buildGirlsStrategyTerm(CategoriesPositiveAndNegativeQueries userRequirements) {

        List<UserRequirements> categories = userRequirements.getCategories();
        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());

        // 如果只有一个品类, 且当前品类不为空, 且为一个时,  则使用当前品类, 不使用 prompt 中输出的品类
        if (categories.size() == 1 && !userPreference.getCurrentCategories().isEmpty() && userRequirements.getCategories().size() == 1) {
            UserRequirements first = categories.getFirst();
            userPreference.getCurrentCategories().stream()
                .findFirst()
                .ifPresent(first::setCategory);
            categories = new ArrayList<>(List.of(first));
        }

        return categories.stream().map(this::decompositionPlan).flatMap(List::stream).toList();

    }


    private List<SearchRequestDTO.GirlsStrategyTerm> decompositionPlan(UserRequirements r) {

        String category = r.getCategory();
        String categoryDescription = r.getBasicNeeds().getCategoryDescription();
        String basePositive = r.getBasicNeeds().getPositivePreferences();
        String baseNegative = r.getBasicNeeds().getNegativePreferences();
        Set<String> subCategorySet = tagSystemTableService.convertToSubCategory(new HashSet<>(Set.of(category.toLowerCase())));

        return r.getPersonalizedRefinement().stream().map(preferenceStep -> {
            SearchRequestDTO.GirlsStrategyTerm.GirlsStrategyTermBuilder builder = SearchRequestDTO.GirlsStrategyTerm.builder();

            ArrayList<SearchRequestDTO.VectorQuery> step1 = new ArrayList<>();
            ArrayList<SearchRequestDTO.VectorQuery> step2 = new ArrayList<>();
            ArrayList<SearchRequestDTO.VectorQuery> step3 = new ArrayList<>();
            SearchRequestDTO.SearchTerm searchTerm =
                SearchRequestDTO.SearchTerm.builder()
                    .categories(new ArrayList<>(subCategorySet))
                    .searchStrategy(preferenceStep.getTitle())
                    .build();


            setTagValue(searchTerm);

            builder.searchTerm(searchTerm);

            // 构建 step1
            step1.add(SearchRequestDTO.VectorQuery.builder()
                .text(categoryDescription)
                .weight(1)
                .build());

            builder.step1VectorQueries(step1);


            // 构建 step2. 使用 第三步的结果
            if (TagValueEnum.isNotNull(preferenceStep.getPositivePreferences())) {
                step2.add(SearchRequestDTO.VectorQuery.builder()
                    .text(categoryDescription + ", " + preferenceStep.getPositivePreferences())
                    .weight(1)
                    .build());
            }

            if (TagValueEnum.isNotNull(preferenceStep.getNegativePreferences())) {
                step2.add(SearchRequestDTO.VectorQuery.builder()
                    .text(preferenceStep.getNegativePreferences())
                    .weight(-1)
                    .build());
            }

            if (ObjectUtil.isNotEmpty(step2)) {
                builder.step2VectorQueries(step2);
            }

            // 和 step3 的数据, 使用 第二步的结果
            if (TagValueEnum.isNotNull(r.getBasicNeeds().getPositivePreferences())) {
                step3.add(SearchRequestDTO.VectorQuery.builder()
                    .text(categoryDescription + ", " + basePositive)
                    .weight(1)
                    .build());
            }
            if (TagValueEnum.isNotNull(r.getBasicNeeds().getNegativePreferences())) {
                step3.add(SearchRequestDTO.VectorQuery.builder()
                    .text(baseNegative)
                    .weight(-1)
                    .build());
            }

            if (ObjectUtil.isNotEmpty(step3)) {
                builder.step3VectorQueries(step3);
            }

            return builder.build();

        }).collect(Collectors.toCollection(ArrayList::new));
    }


}
