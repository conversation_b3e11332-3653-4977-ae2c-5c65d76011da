package com.looksky.agents.application.tryon.swapcloth.background;

import ai.fal.client.ClientConfig;
import ai.fal.client.CredentialsResolver;
import ai.fal.client.FalClient;
import ai.fal.client.SubscribeOptions;
import ai.fal.client.queue.QueueStatus;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.tryon.swapcloth.SwapClothService;
import com.looksky.agents.common.model.tryOn.TryOnFalKeyConfig;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import java.util.List;
import java.util.Map;
import java.util.Random;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 1.1.8
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ReplaceBackgroundService {
    private final Random random = new Random();
    private final TryOnFalKeyConfig tryOnFalKeyConfig;
    private static final String REPLACE_BACKGROUND_IMAGE = "https://cdn.lookskyai.com/upload/agent/df313a2fb4d2e0673ef45838beaf1b59.jpeg";
    @CollectEvent
    public String replaceBackground(String imageUrl) {
        List<String> apiKeyList = tryOnFalKeyConfig.getApiKeyList();
        String apiKey = apiKeyList.get(random.nextInt(apiKeyList.size()));
        log.info("使用的 key: {}", apiKey);

        FalClient falClient = FalClient.withConfig(ClientConfig.withCredentials(CredentialsResolver.fromApiKey(apiKey)));

        try {
            var input = Map.of(
                "image_url", imageUrl,
                "ref_image_url", REPLACE_BACKGROUND_IMAGE,
                "fast", true,
                "prompt", ""
            );
            log.info("更换背景请求参数: {}", input);
            var result = falClient.subscribe("fal-ai/bria/background/replace",
                SubscribeOptions.<SwapClothService.FashnResult>builder()
                    .input(input)
                    .logs(true)
                    .resultType(SwapClothService.FashnResult.class)
                    .onQueueUpdate(update -> {
                        if (update instanceof QueueStatus.InProgress status) {
                            log.info("更换背景返回过程数据: {}", status.getLogs());
                        }
                    })
                    .build()
            );

            log.info("更换背景结果: {}", JSONUtil.toJsonStr(result));

            return result.getData().getImages().getFirst().getUrl();
        } catch (Exception e) {
            log.error("换背景失败, 返回原始图片:{}", imageUrl, e);
        }

        return imageUrl;

    }
}
