package com.looksky.agents.application.chat.preference.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.search.bo.EcommercePreference;
import com.looksky.agents.sdk.agent.preference.SubjectivePreference;
import com.looksky.agents.sdk.agent.preference.CategoryPreference;
import com.looksky.agents.sdk.agent.preference.LikePreference;
import com.looksky.agents.sdk.agent.preference.PreferenceValue;
import com.looksky.agents.sdk.agent.preference.TagPreferences;
import com.looksky.agents.common.utils.BeanMergeUtils;
import com.looksky.agents.data.client.service.TagSystemTableService;

import java.util.*;
import java.util.stream.Collectors;


public class PreferenceMergeUtils {

    /**
     * 合并标签的偏好
     *
     * @param curr 当前偏好
     * @param prev 前一轮偏好
     */
    public static TagPreferences mergeTagPreference(TagPreferences curr, TagPreferences prev) {

        if (curr == null || prev == null) {
            return prev;
        }

        Map<String, LikePreference> currTags = curr.getTagsPreferences();
        Map<String, LikePreference> prevTags = prev.getTagsPreferences();

        if (curr.isEmpty()) {
            return prev;
        }

        if (prev.isEmpty()) {
            return curr;
        }

        if (ObjectUtil.isEmpty(currTags)) {
            currTags = new HashMap<>();
        }

        TagPreferences tagPreferences = new TagPreferences();


        for (Map.Entry<String, LikePreference> prePreferenceEntry : prevTags.entrySet()) {
            String tag = prePreferenceEntry.getKey();
            LikePreference prevTagValues = prePreferenceEntry.getValue();

            // 如果当前偏好中没有这个标签, 直接继承之前的偏好
            if (!currTags.containsKey(tag)) {
                currTags.put(tag, prevTagValues);
                continue;
            }

            // todo 正负词, 待定
            if (handleSpecialTags(tag, currTags.get(tag), prevTagValues)) {
                continue;
            }

            // 处理喜欢/不喜欢冲突
            handleLikeDislikeConflict(currTags.get(tag), prevTagValues);

            // 继承之前的偏好
            inheritPreviousPreferences(currTags.get(tag), prevTagValues);
        }


        tagPreferences.setTagsPreferences(currTags);

        // 处理特殊标签存储, 例如 price, size
        Map<String, Map<String, LikePreference>> currSpeTags = curr.getSpecialTagsPreferences();
        Map<String, Map<String, LikePreference>> prevSpeTags = prev.getSpecialTagsPreferences();

        if (ObjectUtil.isEmpty(currSpeTags)) {
            currSpeTags = new HashMap<>();
        }

        if (ObjectUtil.isNotEmpty(currSpeTags)) {
            for (Map.Entry<String, Map<String, LikePreference>> prevSpeTagPref : prevSpeTags.entrySet()) {
                String tag = prevSpeTagPref.getKey();
                Map<String, LikePreference> attribute = prevSpeTagPref.getValue();

                // 如果当前偏好中没有这个标签, 直接继承之前的偏好
                if (!currSpeTags.containsKey(tag)) {
                    currSpeTags.put(tag, attribute);
                    continue;
                }

                for (Map.Entry<String, LikePreference> attributePref : attribute.entrySet()) {
                    String attributeName = attributePref.getKey();
                    LikePreference prevAttributeValues = attributePref.getValue();

                    // 如果没有表达过该属性, 直接继承之前的属性偏好
                    if (!currSpeTags.get(tag).containsKey(attributeName)) {
                        currSpeTags.get(tag).put(attributeName, prevAttributeValues);
                        continue;
                    }

                    // 处理喜欢/不喜欢冲突
                    handleLikeDislikeConflict(currSpeTags.get(tag).get(attributeName), prevAttributeValues);

                    // 继承之前的偏好
                    inheritPreviousPreferences(currSpeTags.get(tag).get(attributeName), prevAttributeValues);

                }
            }
        }

        tagPreferences.setSpecialTagsPreferences(currSpeTags);

        return tagPreferences;
    }


    /**
     * 处理特殊标签(positive_requirement, negative_requirement)
     */
    private static boolean handleSpecialTags(String tag, LikePreference currentValue,
                                             LikePreference previousValue) {

        return switch (tag) {
            case "positive_requirement", "negative_requirement" -> {
                if (ObjectUtil.isEmpty(currentValue.getLike())) {
                    currentValue.setLike(previousValue.getLike());
                } else {
                    currentValue.getLike().addAll(previousValue.getLike());
                }

                if (ObjectUtil.isEmpty(currentValue.getDislike())) {
                    currentValue.setDislike(previousValue.getDislike());
                } else {
                    currentValue.getDislike().addAll(previousValue.getDislike());
                }

                yield true;
            }
            default -> false;
        };
    }


    /**
     * 处理喜欢/不喜欢的冲突情况
     */
    private static void handleLikeDislikeConflict(LikePreference curr,
                                                  LikePreference prev) {
        // 如果本轮的喜欢为空, 那么使用上一轮的喜欢, 并且需要排除本轮的不喜欢 和 上一轮的不喜欢(一般不用)
        if (ObjectUtil.isEmpty(curr.getLike())) {
            if (ObjectUtil.isEmpty(prev.getLike())) {
                return;
            }

            Set<PreferenceValue> newLike = prev.getLike().stream()
                    .filter(value -> ObjectUtil.isNotEmpty(curr.getDislike()) && !curr.getDislike().contains(value))
                    .filter(value -> ObjectUtil.isNotEmpty(prev.getDislike()) && !prev.getDislike().contains(value))
                    .collect(Collectors.toCollection(HashSet::new));


            curr.setLike(newLike);
        }
        // 如果本轮的喜欢不为空, 那么就使用本轮的喜欢, 因为这是用户明确表达的, 并不继承上一轮的喜欢
        // pass

        // 如果本轮的不喜欢为空, 那么使用上一轮的不喜欢, 并且需要排除本轮的喜欢和上一轮的喜欢
        if (ObjectUtil.isEmpty(curr.getDislike())) {
            if (ObjectUtil.isEmpty(prev.getDislike())) {
                return;
            }

            Set<PreferenceValue> newDislike = prev.getDislike().stream()
                    .filter(value -> ObjectUtil.isNotEmpty(curr.getLike()) && !curr.getLike().contains(value))
                    .filter(value -> ObjectUtil.isNotEmpty(prev.getLike()) && !prev.getLike().contains(value))
                    .collect(Collectors.toCollection(HashSet::new));

            curr.setDislike(newDislike);
        } else {
            // 如果本轮的不喜欢不为空, 那么使用本轮的不喜欢 + 前一轮的不喜欢, 并且需要排除本轮的喜欢 和 上一轮的喜欢(一般不用)
            curr.getDislike().addAll(prev.getDislike());

            Set<PreferenceValue> filterDislike = curr.getDislike().stream()
                    .filter(value -> ObjectUtil.isNotEmpty(curr.getLike()) && !curr.getLike().contains(value))
                    .collect(Collectors.toCollection(HashSet::new));

            curr.setDislike(filterDislike);
        }
    }

    /**
     * 继承之前的偏好
     */
    private static void inheritPreviousPreferences(LikePreference currentValue,
                                                   LikePreference previousValue) {

        if (ObjectUtil.isEmpty(currentValue.getOther())) {
            currentValue.setOther(previousValue.getOther());
        } else {
            // 这里 other 一般都为文本, 并且只有一条
            Set<PreferenceValue> currOther = currentValue.getOther();
            Set<PreferenceValue> prevOther = previousValue.getOther();
            assert currOther.size() == 1 && prevOther.size() == 1;

            PreferenceValue preferenceValue = currOther.stream().findFirst().get();
            preferenceValue.setTextValue(preferenceValue.getTextValue() + "\n" + prevOther.stream().findFirst().get().getTextValue());
        }
    }


    /**
     * 合并用户偏好
     */
    public static ExtractedEntityObject mergePreference(ExtractedEntityObject currentPreference,
                                                        ExtractedEntityObject previousPreference) {

        // 如果没有历史偏好，直接返回当前偏好
        if (ObjectUtil.isEmpty(previousPreference)) {
            return currentPreference;
        }

        String currentCategory;


        // 处理当前品类
        if (ObjectUtil.isNotEmpty(previousPreference.getCurrentCategories())) {
            Set<String> categories = new HashSet<>(previousPreference.getCurrentCategories());
            categories.addAll(currentPreference.getCurrentCategories());

            // 取前一轮的最小值
            currentCategory = SpringUtil.getBean(TagSystemTableService.class).obtainTheSmallestCategory(new ArrayList<>(categories));
        } else {
            currentCategory = SpringUtil.getBean(TagSystemTableService.class).obtainTheSmallestCategory(new ArrayList<>(currentPreference.getCurrentCategories()));
        }

        // 合并电商偏好
        EcommercePreference newEcommercePreference = BeanMergeUtils.merge(
                new EcommercePreference(),
                previousPreference.getEcommercePreference(),
                currentPreference.getEcommercePreference()
        );


        // 获取用户上一轮表达的品类偏好
        CategoryPreference currentCategoryTagPreference = currentPreference.getCategoryPreferences();
        CategoryPreference previousCategoryTagPreference = previousPreference.getCategoryPreferences();

        // 从 当前品类偏好 中过滤出通用标签, 并将其从 当前品类偏好 中移除
        CategoryPreference currentCommonTagPreference = filterAndRemove(currentCategoryTagPreference, CategoryEnum.CLOTH.getName());

        // 从 前一轮品类偏好 中过滤出通用标签, 并将其从 前一轮品类偏好 中移除
        CategoryPreference previousCommonTagPreference = filterAndRemove(previousCategoryTagPreference, CategoryEnum.CLOTH.getName());


        SubjectivePreference currentSubjectPreference = currentPreference.getSubjectivePreference();
        SubjectivePreference newSubjectPreference;
        // 根据是否有主观偏好分别处理
        if (ObjectUtil.isEmpty(currentSubjectPreference)) {
            // 没有主观偏好, 直接继承之前的主观偏好
            newSubjectPreference = previousPreference.getSubjectivePreference();
            handleNoSubjectivePreference(currentCategoryTagPreference, previousCategoryTagPreference, currentCommonTagPreference, previousCommonTagPreference);
        } else {
            newSubjectPreference = BeanMergeUtils.merge(new SubjectivePreference(), previousPreference.getSubjectivePreference(), currentSubjectPreference);

            handleWithSubjectivePreference(currentCategoryTagPreference, previousCategoryTagPreference, currentCommonTagPreference, previousCommonTagPreference);
        }

        // 组装最终结果

        return ExtractedEntityObject
                .builder()
                .subjectivePreference(newSubjectPreference)
                .ecommercePreference(newEcommercePreference)
                .categoryPreferences(currentCategoryTagPreference)
                .currentCategories(new HashSet<>(List.of(currentCategory)))
                .build();
    }


    /**
     * 处理没有主观偏好的情况
     *
     * @param currentCategoryTagPreference  当前品类偏好
     * @param previousCategoryTagPreference 前一次品类偏好
     * @param currentCommonTagPreference    当前通用偏好
     * @param previousCommonTagPreference   前一次通用偏好
     */

    private static void handleNoSubjectivePreference(
            CategoryPreference currentCategoryTagPreference,
            CategoryPreference previousCategoryTagPreference,
            CategoryPreference currentCommonTagPreference,
            CategoryPreference previousCommonTagPreference) {


        // 如果当前表达了具体品类偏好
        if (ObjectUtil.isNotEmpty(currentCategoryTagPreference)) {
            mergePreferenceNoConflictWithCurrentCategory(
                    currentCategoryTagPreference,
                    previousCategoryTagPreference,
                    currentCommonTagPreference,
                    previousCommonTagPreference
            );
        }
        // 如果只表达了通用标签偏好
        else if (ObjectUtil.isNotEmpty(currentCommonTagPreference)) {
            mergePreferenceNoConflictWithCommonCategory(
                    currentCategoryTagPreference,
                    previousCategoryTagPreference,
                    currentCommonTagPreference,
                    previousCommonTagPreference
            );
        }
    }

    private static CategoryPreference filterAndRemove(CategoryPreference preferences, String category) {
        if (preferences == null) {
            return new CategoryPreference(new HashMap<>());
        }

        if (ObjectUtil.isEmpty(preferences.getCategoryPreferences())) {
            preferences.setCategoryPreferences(new HashMap<>());
            return new CategoryPreference(new HashMap<>());
        }

        Map<String, TagPreferences> mutableMap = new HashMap<>(preferences.getCategoryPreferences());
        TagPreferences removed = mutableMap.remove(category);
        preferences.setCategoryPreferences(mutableMap);

        CategoryPreference categoryPreference = new CategoryPreference();
        if (ObjectUtil.isNotEmpty(removed)) {
            categoryPreference.setCategoryPreferences(new HashMap<>(Map.of(category, removed)));
        }
        return categoryPreference;
    }

    /**
     * 处理有主观偏好的情况
     */
    private static void handleWithSubjectivePreference(
            CategoryPreference currentCategoryTagPreference,
            CategoryPreference previousCategoryTagPreference,
            CategoryPreference currentCommonTagPreference,
            CategoryPreference previousCommonTagPreference) {

        // 处理品类标签偏好
        if (ObjectUtil.isNotEmpty(currentCategoryTagPreference)) {
            // 没有冲突，合并当前和历史的品类偏好
            mergePreferenceNoConflictWithCurrentCategory(
                    currentCategoryTagPreference,
                    previousCategoryTagPreference,
                    currentCommonTagPreference,
                    previousCommonTagPreference
            );
//            }
        }

        // 处理通用标签偏好
        if (ObjectUtil.isNotEmpty(currentCommonTagPreference)) {

            // 没有冲突，合并通用标签
            mergePreferenceNoConflictWithCommonCategory(
                    currentCategoryTagPreference,
                    previousCategoryTagPreference,
                    currentCommonTagPreference,
                    previousCommonTagPreference
            );
//            }
        }

        // 如果没有新的品类标签偏好，使用之前的
        if (ObjectUtil.isEmpty(currentCategoryTagPreference)) {
            currentCategoryTagPreference = previousCategoryTagPreference;
        }
    }


    /**
     * 合并当前品类的偏好（无冲突）
     *
     * @param currCate 当前品类偏好
     * @param prevCate 前一次品类偏好
     * @param currComm 当前通用偏好
     * @param prevComm 前一次通用偏好
     */
    private static void mergePreferenceNoConflictWithCurrentCategory(
            CategoryPreference currCate,
            CategoryPreference prevCate,
            CategoryPreference currComm,
            CategoryPreference prevComm) {

        // 合并通用品类偏好
        merge(currComm, prevComm, CategoryEnum.CLOTH.getName(), CategoryEnum.CLOTH.getName(), currComm);

        // 遍历当前偏好, 对每个品类进行处理
        currCate.getCategoryPreferences().forEach((category, currTag) -> {

            // 合并当前品类偏好
            merge(currCate, prevCate, category, category, currCate);


            // 把通用品类偏好合并到当前品类偏好
            merge(currCate, currComm, category, CategoryEnum.CLOTH.getName(), currCate);

            // 合并一级品类偏好
            if (prevCate == null || ObjectUtil.isEmpty(prevCate.getCategoryPreferences())) {
                return;
            }

            String currentFirstCategory = SpringUtil.getBean(TagSystemTableService.class).getFirstCategoryBySubCategory(category);
            TagPreferences prevFirstCategoryPreference = prevCate.getCategoryPreferences().get(currentFirstCategory);
            if (ObjectUtil.isNotEmpty(prevFirstCategoryPreference)) {
                merge(currCate, prevCate, category, currentFirstCategory, currCate);
            }
        });
    }

    /**
     * 合并两个品类的偏好
     *
     * @param curr     当前偏好
     * @param prev     历史偏好
     * @param currCate 当前需要合并的品类
     * @param prevCate 历史需要合并的品类
     * @param update   最终的结果值
     */
    private static void merge(CategoryPreference curr, CategoryPreference prev, String currCate, String prevCate, CategoryPreference update) {

        // 如果没有历史偏好, 直接返回
        if (prev == null) {
            return;
        }

        // 如果当前偏好为空, 则初始化
        if (curr == null) {
            curr = new CategoryPreference();
            curr.setCategoryPreferences(new HashMap<>());
        }

        Map<String, TagPreferences> currPrev = curr.getCategoryPreferences();
        Map<String, TagPreferences> prevPrev = prev.getCategoryPreferences();

        // 如果当前偏好为空, 则直接返回
        if (ObjectUtil.isEmpty(prevPrev)) {
            return;
        }

        // 如果当前偏好为空, 则直接使用历史偏好
        if (ObjectUtil.isEmpty(currPrev)) {
            update.setCategoryPreferences(prevPrev);
            return;
        }

        // 合并单个 tag 偏好
        TagPreferences newData = mergeTagPreference(
                curr.getCategoryPreferences().get(currCate),
                prev.getCategoryPreferences().get(prevCate)
        );

        // 如果合并后, 没有数据, 则直接返回
        if (ObjectUtil.isEmpty(newData)) {
            return;
        }

        update.setCategoryPreferences(new HashMap<>(Map.of(currCate, newData)));
    }

    /**
     * 合并通用品类的偏好（无冲突）
     */
    private static void mergePreferenceNoConflictWithCommonCategory(
            CategoryPreference currentCategoryTagPreference,
            CategoryPreference previousCategoryTagPreference,
            CategoryPreference currentCommonTagPreference,
            CategoryPreference previousCommonTagPreference) {

        // 合并通用品类偏好
        merge(currentCommonTagPreference, previousCommonTagPreference, CategoryEnum.CLOTH.getName(), CategoryEnum.CLOTH.getName(), currentCommonTagPreference);

        // 把通用品类偏好合并到当前品类偏好
        currentCategoryTagPreference = currentCommonTagPreference;

        // 判断上一轮是否有具体品类的偏好
        if (ObjectUtil.isNotEmpty(previousCategoryTagPreference)) {
            // 遍历上一轮的品类偏好
            for (Map.Entry<String, TagPreferences> entry : previousCategoryTagPreference.getCategoryPreferences().entrySet()) {
                String category = entry.getKey();
                merge(currentCategoryTagPreference, previousCategoryTagPreference, category, category, currentCategoryTagPreference);

            }
        }
    }
}