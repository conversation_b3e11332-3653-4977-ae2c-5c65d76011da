package com.looksky.agents.application.chat.preference.extraction.deprecated;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.input.utils.SimilarTagUtils;
import com.looksky.agents.application.common.BatchTaskExecutor;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.application.prompt.PromptBusinessService;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import com.looksky.agents.sdk.agent.common.enums.TagEnum;
import com.looksky.agents.sdk.agent.common.dto.CurrentCategoryTagDTO;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.preference.CategoryPreference;
import com.looksky.agents.sdk.agent.preference.TagPreferences;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import com.looksky.agents.common.utils.ResourceUtils;
import com.looksky.agents.infrastructure.context.Context;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName TagValueExtract
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/30 下午6:21
 * @Version 1.0
 **/
@Slf4j
//@Component
@RequiredArgsConstructor
@Deprecated(since = "2025-01-04, 不再使用统一结构", forRemoval = true)
public class TagValueExtract {
    private final CommonRequestService commonRequestService;
    private final BatchTaskExecutor batchExecute;
    private final PromptBusinessService promptBusinessService;


    public CategoryPreference extract(Map<String, List<String>> categoryDimension) {

        if (ObjectUtil.isEmpty(categoryDimension)) {
            return new CategoryPreference();
        }

        log.debug("-----------> 待抽取标签具体的维度值: {} ", JSONUtil.toJsonStr(categoryDimension));

        ArrayList<BatchTaskExecutor.Task<String>> tasks = new ArrayList<>();

        categoryDimension.forEach((category, labelTypeList) -> {

            // 如果没有标签列表, 直接返回
            if (ObjectUtil.isEmpty(labelTypeList)) {
                return;
            }

            // 人工处理相似标签
            labelTypeList = SimilarTagUtils.normSimilarTag(category, labelTypeList);
            labelTypeList.forEach(labelType -> {
                String taskName = String.format("%s:%s", category, labelType);
                tasks.add(new BatchTaskExecutor.Task<>(taskName, () -> extractUserSingleTypePreference(category, labelType)));
            });
        });

        Map<String, String> result = batchExecute.execute(tasks);

        log.debug("<--------------- 抽取标签具体的维度值结果: {} ", JSONUtil.toJsonStr(result));

        return processBatchResults(result);
    }

    private CategoryPreference processBatchResults(Map<String, String> modelResult) {


        CategoryPreference categoryPreference = new CategoryPreference();
        HashMap<String, TagPreferences> preferencesHashMap = new HashMap<>();

        for (Map.Entry<String, String> entry : modelResult.entrySet()) {
            String[] parts = entry.getKey().split(":");
            String category = parts[0];
            String parentTag = parts[1];

            if (TagEnum.isPriceOrSize(parentTag)) {
                // 特殊处理 price 和 size
                TagPreferences tagPreferences = TagPreferences.parseSpecialJson(entry.getValue());
                preferencesHashMap.put(category, tagPreferences);
            } else {
                TagPreferences tagPreferences = TagPreferences.parseJson(entry.getValue());
                preferencesHashMap.put(category, tagPreferences);
            }
        }
        categoryPreference.setCategoryPreferences(preferencesHashMap);
        return categoryPreference;
    }

    /**
     * 通用的抽取标签具体值, 包括不喜欢的品类和标签值
     * @param category 品类, 可能为一级, 也可能为二级, cloth
     * @param tagName 标签名
     * @return 抽取的用户偏好
     */
    public String extractUserSingleTypePreference(String category,String tagName) {

        log.info("抽取具体的{}标签值时", tagName);

        // 先去找 metadata , tag 为 labelType 的 promptId, 然后找到该 promptId 的 prompt
        PromptModel prompt = promptBusinessService.findPromptByMetadata(tagName);

        // 如果没有找到 promptId, 那么使用默认的 prompt
        if (prompt == null) {
            log.warn("抽取具体的标签值时, 未找到标签为: {} 的 prompt, 使用默认的 prompt", tagName);
            prompt = promptBusinessService.getPromptModelByNameWithMetaData(PromptNameEnum.EXTRACT_SINGLE_USER_PREFERENCE.getName());
        }

        prompt.setJsonSchema(ResourceUtils.innerTagModelOutputStructure(tagName));

        RequestParamBO strategy = promptBusinessService.convert(prompt, Map.of(Context.Name.CURRENT.getName(), CurrentCategoryTagDTO.builder().category(category).tagName(tagName).build()));

        // 提取的用户偏好
        return commonRequestService.commonExecuteStrategy(strategy);
    }

}
