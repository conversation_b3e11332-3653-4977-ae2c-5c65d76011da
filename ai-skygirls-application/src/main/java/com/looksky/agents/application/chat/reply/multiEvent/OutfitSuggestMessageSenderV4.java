package com.looksky.agents.application.chat.reply.multiEvent;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.application.chat.reply.utils.OutfitToTagPreferenceNode;
import com.looksky.agents.application.chat.reply.utils.RecommendProductNodeUtils;
import com.looksky.agents.application.chat.reply.utils.RecommendRequestHelper;
import com.looksky.agents.data.client.utils.CommunicationHelper;
import com.looksky.agents.data.redis.conversation.HistoryDataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.exception.BusinessException;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import com.looksky.agents.sdk.agent.common.dto.CurrentCategoryTagDTO;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.search.bo.OutfitSuggestionList;
import com.looksky.agents.sdk.agent.search.dto.OutfitProductPlans;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 推方案
 *
 * <AUTHOR>
 * @since 1.2.1
 **/
@Slf4j
@Order(1)
@Component
@ApiVersion(platform = ApiVersion.PlatformType.IOS, version = "1.2.6")
@ApiVersion(platform = ApiVersion.PlatformType.ANDROID, version = "1.2.5")
public class OutfitSuggestMessageSenderV4 extends MultiEventMessageSender {


    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private RecommendRequestHelper recommendRequestHelper;

    @Resource
    private HistoryDataService historyDataService;

    @Resource
    private CommunicationHelper communicationHelper;

    @Resource
    private OutfitToTagPreferenceNode outfitToTagPreferenceNode;


    @Override
    protected boolean supports(PromptModel strategy) {
        // 生成搜索过程
        return PromptNameEnum.INQUIRE_USER_SOLUTION.getName().equals(strategy.getName());
    }

    @SneakyThrows
    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {

        sendLoading();

        // 生成推荐方案
        ExtractedEntityObject preference = Context.get(Context.Name.PREFERENCE.getName());
        Set<String> categories = preference.getCurrentCategories();
        CurrentCategoryTagDTO currentCategoryTag = CurrentCategoryTagDTO.builder().build();
        if (ObjectUtil.isEmpty(categories)) {
            currentCategoryTag.setCategory(CategoryEnum.CLOTH.getName());
        } else {
            currentCategoryTag.setCategory(categories.stream().findFirst().get());
        }


        String block = sendThinkStreamMessageNoEmoji("thinking_about_outfit_solution_json_output", Map.of(Context.Name.CURRENT.name(), currentCategoryTag)).block();

        Optional.ofNullable(block).orElseThrow(() -> new BusinessException("DeepSeek 回复为空"));

        log.debug("DeepSeek 回复: \n{}", block);

        historyDataService.saveAgentHistory(block);

        Context.put(Context.Name.SEARCH_PROCESS.getName(), block);

        sendContent(initMessage(EventTypeEnum.WAIT_SOLUTION.getType()), EventTypeEnum.WAIT_SOLUTION.getType(), "");

        OutfitProductPlans outfitPlans = objectMapper.readValue(block, OutfitProductPlans.class);

        VirtualCompletableFuture<Void> vectorWordFuture = extractVectorWord(block, outfitPlans, currentCategoryTag);
        VirtualCompletableFuture<Void> tagPreferenceFuture = outfitToTagPreferenceNode.extract(outfitPlans);

        VirtualCompletableFuture.allOf(vectorWordFuture, tagPreferenceFuture).join();

        outfitPlans = recommendRequestHelper.request(outfitPlans);

        if (outfitPlans.getRecommendItem().isEmpty()) {
            log.warn("推荐方案为空, returnContents: {}", JSONUtil.toJsonStr(outfitPlans));
            String finalMessageStr = sendStreamMessage("query_no_products", Collections.emptyMap()).block();
            historyDataService.saveUserHistory(finalMessageStr);
            return;
        }

        communicationHelper.sendMessage(EventTypeEnum.PLANS, outfitPlans);


        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());
        conversationStatus.setRecommendProduct(true);
    }

    private VirtualCompletableFuture<Void> extractVectorWord(String block, OutfitProductPlans outfitPlans, CurrentCategoryTagDTO currentCategoryTag) {

        return VirtualCompletableFuture.runAsync(() -> {
            // 生成正负向词
            String result = commonRequestService.commonExecuteStrategy("structured_output_outfit_solution", Map.of(Context.Name.CURRENT.name(), currentCategoryTag, "input", block));

            // 请求推荐
            OutfitSuggestionList outfitSuggestionList = null;
            try {
                outfitSuggestionList = objectMapper.readValue(result, OutfitSuggestionList.class);
            } catch (JsonProcessingException e) {
                throw new BusinessException("解析 OutfitSuggestionList JSON 失败", e);
            }
            // 合并属性
            RecommendProductNodeUtils.mergeOutfitSuggestToPlan(outfitPlans, outfitSuggestionList);
        });

    }


}
