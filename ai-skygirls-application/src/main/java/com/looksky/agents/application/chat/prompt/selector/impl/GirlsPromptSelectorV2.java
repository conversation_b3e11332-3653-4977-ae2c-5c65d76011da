package com.looksky.agents.application.chat.prompt.selector.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.application.chat.prompt.selector.PromptSelector;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.product.enums.SearchQuestionType;
import com.skygirls.biz.im.dto.MessageRestDTO;
import com.skygirls.biz.report.IosUserInfoDto;
import com.skygirls.biz.user.dto.AllReportDto;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

/**
 * 服装搜索问题选择器
 */
@Slf4j
//@Component
@RequiredArgsConstructor
@ApiVersion(platform = ApiVersion.PlatformType.ALL, version = "1.1.12")
public class GirlsPromptSelectorV2 implements PromptSelector {

    private final TagSystemTableService tagSystemTableService;
    private final RedissonClient redissonClient;
    private final CommonRequestService commonRequestService;
    private final ObjectMapper objectMapper;

    private static final String NEXT_ACTION = "search_next_action";


    @Override
    public String support() {
        return "search_cloth";
    }

    @SneakyThrows
    @Override
    public String select() {

        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());

        String text = Optional.ofNullable(input.getEventDict()).map(MessageRestDTO.EventDict::getText).orElse(null);

        String adviceNextStep = checkIsAdviceQuestion(text);
        if (CharSequenceUtil.isNotBlank(adviceNextStep)) {
            log.info("用户输入了建议问题, 直接推荐商品, 执行 prompt: {}", adviceNextStep);
            return adviceNextStep;
        }

        String nextStr = commonRequestService.commonExecuteStrategy(NEXT_ACTION);

        NextAction nextAction = objectMapper.readValue(nextStr, NextAction.class);

        return switch (nextAction.recommendationType()) {
            case "DirectProductRecommendation" -> SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
            case "RecommendationOfMultipleStyles" -> SearchQuestionType.SUBJECTIVE_WITHOUT_ASK_SOLUTION.getValue();
            case "SimpleFollowUpQuestion" -> SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
            case "MultipleSelectionFollowUp" -> SearchQuestionType.SELECTOR.getValue();
            default -> throw new IllegalStateException("Unexpected value: " + nextAction.recommendationType());
        };


    }


    /**
     * 模型决策下一步应该采取的行动
     * @param recommendationType 行动枚举
     */
     private record NextAction(@JsonProperty("recommendation_type") String recommendationType) {}




    private String checkIsAdviceQuestion(String userInput) {

        if (CharSequenceUtil.isBlank(userInput)) {
            return null;
        }
        // 需要去除 变量信息, 在进行 md5 的计算
        IosUserInfoDto userInfo = Context.get(Context.Name.USER_INFO.getName());
        String originText = getOriginText(userInput, userInfo);


        String md5Key = DigestUtil.md5Hex(originText);

        RBucket<String> bucket = redissonClient.getBucket(RedisKeyConstants.adviceQuestionNextStep(md5Key));

        // 如果不存在, 那么直接返回
        if (bucket.isExists()) {
            return bucket.get();
        }

        return null;

    }

    @NotNull
    private static String getOriginText(String userInput, IosUserInfoDto userInfo) {
        AllReportDto appUser = userInfo.getAppUser();
        String kibbeType = appUser.getKibbeType().getCode().replace("_", "");
        String bodyShape = appUser.getBodyShape().getCode().replace("_", "");
        String heightType = appUser.getHeightType().getCode();
        String colorSeason = appUser.getColorSeason().getName().replace("_", "");


        return userInput.replace(kibbeType, "{kibbeType}")
            .replace(bodyShape, "{bodyType}")
            .replace(heightType, "{heightType}")
            .replace(colorSeason, "{colorSeason}");
    }


    public boolean isSwimwear(Set<String> categorySet) {
        if (categorySet == null || categorySet.isEmpty()) {
            return false;
        }

        List<String> categoryList = new ArrayList<>(tagSystemTableService.getSubCategoryByFirstCategory(CategoryEnum.SWIMWEAR.getName()));
        categoryList.add(CategoryEnum.SWIMWEAR.getName());

        return categorySet.stream().anyMatch(categoryList::contains);

    }


} 