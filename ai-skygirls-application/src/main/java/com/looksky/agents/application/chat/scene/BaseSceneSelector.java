package com.looksky.agents.application.chat.scene;

import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.conversation.ExternInfo;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;

public abstract class BaseSceneSelector {


    protected static class SceneModels {
        // 策略名称常量
        public static class Names {
            protected Names() {
            }
        }

        // 基础策略解析结果接口
        public interface ParsedResults {
        }

    }

    public record SceneResult(PromptModel strategy, ExternInfo externInfo) {
    }

    public abstract boolean support(String eventName, EnterPointEnum enterPoint, String page);

    public PromptModel getScene(RequestInputDTO requestInput) {
        SceneResult result = getSceneAndExternInfo(requestInput);
        saveUserExternInfo(requestInput, result.externInfo());
        return result.strategy();
    }

    protected abstract SceneResult getSceneAndExternInfo(
            RequestInputDTO requestInput);

    protected void saveUserExternInfo(RequestInputDTO requestInput, ExternInfo externInfo) {
        // 实现保存外部信息的逻辑
        System.out.println("======================================");
    }
} 