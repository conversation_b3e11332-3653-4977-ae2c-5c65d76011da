package com.looksky.agents.application.tryon.colorseason.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.looksky.agents.application.tryon.colorseason.ITryOnColorSeasonService;
import com.looksky.agents.application.tryon.colorseason.convertor.TryOnColorSeasonConvertor;
import com.looksky.agents.data.client.business.TryOnClient;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonParamV1;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonRequestV1;
import java.time.Duration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * colorSeason 试色 service, 四步方案
 *
 * <AUTHOR>
 * @since 1.1.8
 **/
@Slf4j
//@Service
@RequiredArgsConstructor
//@Order(90) // 优先级2，数字越小优先级越高
public class TryOnColorSeasonServiceV2 implements ITryOnColorSeasonService {

    private static final Duration TIMEOUT = Duration.ofSeconds(25);
    private final TryOnClient tryOnClient;
    private final TryOnColorSeasonConvertor convertor;


    @Override
    public Duration getTimeout() {
        return TIMEOUT;
    }

    @Override
    public String tryOnWhiteT(TryOnColorSeasonParamV1 param) {
        try {
            TryOnColorSeasonRequestV1 request = convertor.toTryOnColorSeasonRequest(param);
            JsonNode result = tryOnClient.colorSeasonV1(request);
            log.info("tryOnColorSeason result: {}", result);
            return result.get("url").asText();
        } catch (Exception e) {
            log.error("V2服务执行失败", e);
            return null;
        }
    }
}
