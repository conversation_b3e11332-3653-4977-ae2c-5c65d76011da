package com.looksky.agents.application.chat.preference.extraction;

import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.data.redis.conversation.PreferenceDataService;
import com.looksky.agents.infrastructure.context.Context;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public abstract class AbstractPreferenceExtractor<T> {
    protected final SingleTagExtractService singleTagExtractService;
    protected final PreferenceDataService preferenceDataService;
    protected final TagSystemTableService tagSystemTableService;

    public void extract() {
        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());
        userPreference.getCurrentCategories().stream().findFirst().ifPresent(
                category -> {
                    if (!validateCategoryTag(category)) {
                        return;
                    }
                    String tagName = getTagName();
                    String result = singleTagExtractService.extractSingleTagPreference(category, tagName);
                    log.info("品类[{}]下标签[{}]抽取结果: {}", category, tagName, result);
                    T extractedValue = parseExtractedValue(result);
                    T merged = mergePreference(extractedValue, getCurrentPreference(userPreference));
                    updatePreference(userPreference, merged);
                }
        );
        preferenceDataService.savePreference(userPreference);
    }

    /**
     * 验证品类是否包含指定标签
     * @param category 品类名称
     * @return 是否包含该标签
     */
    protected boolean validateCategoryTag(String category) {
        boolean isCategoryTag = tagSystemTableService.isCategoryTag(category, getTagName());
        if (!isCategoryTag) {
            log.info("品类[{}]下不包含标签[{}]，跳过抽取", category, getTagName());
            return false;
        }
        return true;
    }

    // 获取标签名称
    protected abstract String getTagName();

    // 解析抽取的值
    protected abstract T parseExtractedValue(String result);

    // 获取当前偏好
    protected abstract T getCurrentPreference(ExtractedEntityObject userPreference);

    // 更新偏好
    protected abstract void updatePreference(ExtractedEntityObject userPreference, T newValue);

    // 合并偏好
    protected abstract T mergePreference(T newValue, T currentValue);
} 