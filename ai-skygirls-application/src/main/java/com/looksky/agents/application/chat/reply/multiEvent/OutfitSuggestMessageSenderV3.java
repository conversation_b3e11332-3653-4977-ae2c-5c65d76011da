package com.looksky.agents.application.chat.reply.multiEvent;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.application.chat.reply.utils.RecommendRequestHelper;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.trace.annotation.TraceMethod;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import com.looksky.agents.sdk.agent.common.dto.CurrentCategoryTagDTO;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.search.bo.OutfitSuggestionList;
import com.looksky.agents.sdk.agent.search.bo.RecommendOutfitBO;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 推方案
 *
 * <AUTHOR>
 * @since 1.2.1
 **/
@Slf4j
@Order(1)
@Component
@ApiVersion(platform = ApiVersion.PlatformType.IOS, version = "1.1.0")
@ApiVersion(platform = ApiVersion.PlatformType.ANDROID, version = "1.2.1")
public class OutfitSuggestMessageSenderV3 extends MultiEventMessageSender {


    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private RecommendRequestHelper recommendRequestHelper;


    @Override
    protected boolean supports(PromptModel strategy) {
        // 生成搜索过程
        return PromptNameEnum.INQUIRE_USER_SOLUTION.getName().equals(strategy.getName());
    }

    @SneakyThrows
    @Override
    @TraceMethod(description = "进入推方案流程")
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {

        sendLoading();


        // 生成推荐方案
        ExtractedEntityObject preference = Context.get(Context.Name.PREFERENCE.getName());
        Set<String> categories = preference.getCurrentCategories();
        CurrentCategoryTagDTO currentCategoryTag = CurrentCategoryTagDTO.builder().build();
        if (ObjectUtil.isEmpty(categories)) {
            currentCategoryTag.setCategory(CategoryEnum.CLOTH.getName());
        } else {
            currentCategoryTag.setCategory(categories.stream().findFirst().get());
        }


        String block = buildRecommendOutFitStr(currentCategoryTag, hashMap);

        OutfitSuggestionList outfitSuggestionList = extractOutFitSchema(currentCategoryTag, block);

        List<RecommendOutfitBO> recommendOutfitDTOS = ListUtil.empty();

        try {
            recommendOutfitDTOS = recommendRequestHelper.request(outfitSuggestionList);
        } catch (Exception e) {
            log.error("推荐方案失败", e);
        }


        if (ObjectUtil.isEmpty(recommendOutfitDTOS) || ObjectUtil.isEmpty(recommendOutfitDTOS.getFirst().getSkcIds())) {
            log.warn("推荐方案为空, returnContents: {}", JSONUtil.toJsonStr(recommendOutfitDTOS));
            // 执行兜底回复
            String finalMessageStr = sendStreamMessage("query_no_products", Collections.emptyMap()).block();
            extraMessage(EventTypeEnum.TEXT.getType(), finalMessageStr, hashMap);
            return;
        }

        sendContent(initMessage(EventTypeEnum.CONFIRM_SOLUTION.getType()), EventTypeEnum.CONFIRM_SOLUTION.getType(), recommendOutfitDTOS);


        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());
        conversationStatus.setRecommendProduct(true);
    }

    @TraceMethod(description = "提取推荐方案结构")
    private OutfitSuggestionList extractOutFitSchema(CurrentCategoryTagDTO currentCategoryTag, String block) throws JsonProcessingException {
        String result = commonRequestService.commonExecuteStrategy("structured_output_outfit_solution", Map.of(Context.Name.CURRENT.name(), currentCategoryTag, "input", block));
        return objectMapper.readValue(result, OutfitSuggestionList.class);
    }

    @TraceMethod(description = "DeepSeek 思考需要推荐的方案")
    private String buildRecommendOutFitStr(CurrentCategoryTagDTO currentCategoryTag, HashMap<String, Object> hashMap) {
        String block = sendThinkStreamMessageNoEmoji("thinking_about_outfit_solution", Map.of(Context.Name.CURRENT.name(), currentCategoryTag)).block();
        extraMessage(EventTypeEnum.TEXT.getType(), block, hashMap);

        sendContent(initMessage(EventTypeEnum.WAIT_SOLUTION.getType()), EventTypeEnum.WAIT_SOLUTION.getType(), "");
        return block;
    }


}
