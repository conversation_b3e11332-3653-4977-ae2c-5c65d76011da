package com.looksky.agents.application.chat.product.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.sdk.product.enums.SizeType;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.data.redis.product.ProductClient;
import com.looksky.agents.infrastructure.exception.BusinessException;
import com.looksky.agents.sdk.product.BodySize;
import com.looksky.agents.sdk.product.Product;
import com.looksky.agents.sdk.product.ProductDTO;
import com.looksky.agents.sdk.product.ProductSize;
import com.looksky.agents.sdk.product.SkcInfo;
import com.looksky.agents.sdk.product.SpuInfo;
import com.looksky.agents.sdk.product.Tag;
import com.skygirls.biz.im.dto.MessageRestDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProductService {
    private final RedissonClient redissonClient;
    private final ObjectMapper objectMapper;
    private final ProductClient productClient;

    public Product getProduct(MessageRestDTO.EventDict eventDict) {
        String productId = eventDict.getProductId();
        String skcId = eventDict.getSkcId();

        if (StringUtils.hasText(productId) || StringUtils.hasText(skcId)) {
            if (StringUtils.hasText(productId) && StringUtils.hasText(skcId)) {
                return getProductInfo(productId, skcId);
            } else {
                log.error("eventDict 缺少 skcId 或 productId, eventDict: {}", JSONUtil.toJsonStr(eventDict));
                throw new RuntimeException("eventDict 缺少 skcId 或 productId");
            }
        }
        return Product.builder().build();
    }



    /**
     * 批量获取商品信息, 暂时不用, 因为 event 里面没有 product_id_group_list 了
     *
     * @param eventDict 包含商品ID组列表的事件字典
     * @return 商品列表
     */
    public List<Product> getMultiProduct(Map<String, Object> eventDict) {

        if (ObjectUtil.isEmpty(eventDict)) {
            return Collections.emptyList();
        }

        if (!eventDict.containsKey("product_id_group_list")) {
            return Collections.emptyList();
        }

        List<Map<String, String>> productIdGroupList = (List<Map<String, String>>) eventDict.get("product_id_group_list");
        List<Product> products = new ArrayList<>();

        try {
            for (Map<String, String> productIdGroup : productIdGroupList) {
                if (productIdGroup != null && !productIdGroup.isEmpty()) {
                    // 获取skc_id和spu_id
                    String skcId = productIdGroup.getOrDefault("skc_id", null);
                    String spuId = productIdGroup.getOrDefault("product_id", null);

                    // 获取商品信息
                    if (spuId != null) {
                        Product product = getProductInfo(spuId, skcId);
                        if (product != null) {
                            products.add(product);
                        }
                    }
                }
            }
            return products;
        } catch (Exception e) {
            log.error("获取商品信息失败, product_id_group_list: {}", productIdGroupList, e);
            throw new BusinessException(String.format("获取商品信息失败, product_id_group_list: %s", productIdGroupList));
        }
    }

    public Product getProductInfo(String spuId, String skcId) {
        System.out.println("获取商品详情, spuId: " + spuId + ", skcId: " + skcId);
        try {
            // 1. 获取SPU基础信息
            ProductDTO spuProductInfo = productClient.getProductByProductId(spuId);
            if (spuProductInfo == null) {
                log.error("无法获取SPU信息, spuId: {}", spuId);
                return null;
            }

            // 2. 从topics中提取品牌和价格类型信息
            String brand = "";
            String priceType = "";
            for (Tag tag : spuProductInfo.getTopics()) {
                if ("brand".equals(tag.getType())) {
                    brand = tag.getValue();
                }
                if ("price".equals(tag.getType())) {
                    priceType = tag.getValue();
                }
            }

            // 3. 获取SPU和SKC的详细信息
            SpuInfo spuInfo = spuProductInfo.getInfo().getSpu();
//            Map<String, Object> spuInfo = productInfo.getInfo().getSpu();
            ProductDTO skcProductInfo = productClient.getProductBySkcId(skcId);
//            ProductDTO skc = getProductInfoSkc(skcId);
            if (skcProductInfo == null) {
                log.error("无法获取SKC信息, skcId: {}", skcId);
                return null;
            }

            SkcInfo skcInfo = skcProductInfo.getInfo().getSkc();
//            Map<String, Object> skcInfo = skc.getInfo().getSkc();

            String categoryId = skcInfo.getSystemCategoryId();
            String sizeType = spuInfo.getSizeType();

            // 4. 获取商品尺码信息
            List<BodySize> bodySizeList = null;
            try {
                bodySizeList = getBodySizeList(spuInfo, categoryId, sizeType);
            } catch (Exception e) {
                log.error("获取尺码列表失败, spuId: {}, skcId: {}", spuId, skcId, e);
            }

            // 5. 合并标签信息
            // todo 改为使用 tag 而不是使用 map
            Map<String, String> tags = new HashMap<>();
            if (spuProductInfo.getTags()!= null) {
                for (Tag tag : spuProductInfo.getTags()) {
                    tags.put(tag.getType(), tag.getValue());
                }
            }
            if (skcProductInfo.getTags() != null) {
                for (Tag tag : skcProductInfo.getTags()) {
                    tags.put(tag.getType(), tag.getValue());
                }
            }

            // 6. 获取价格信息

            String price = skcInfo.getPrice() != null ? skcInfo.getPrice().toString() : spuInfo.getPrice() != null ? spuInfo.getPrice().toString() : "";
            String highestPrice = skcInfo.getHighestPrice() != null ? skcInfo.getHighestPrice().toString() : spuInfo.getSkcHighestPriceList() != null ? spuInfo.getSkcHighestPriceList().getFirst().toString() : "";
            String originalPrice = skcInfo.getOriginalPrice() != null ? skcInfo.getOriginalPrice().toString() : spuInfo.getOriginalPrice() != null ? spuInfo.getOriginalPrice().toString() : "";
            String highestOriginalPrice = skcInfo.getHighestOriginalPrice() != null ? skcInfo.getHighestOriginalPrice().toString() : spuInfo.getSkcHighestOriginalPriceList() != null ? spuInfo.getSkcHighestOriginalPriceList().getFirst().toString() : "";

            // 7. 获取弹性信息
            // 不做, redis 里面并没有这个 key
//            String elasticity = getSkcElasticity(skcId);
//            if (StrUtil.isBlank(elasticity)) {
//                elasticity = ElasticityType.NONE.getValue();
//            }
//            tags.put(ProductTagType.ELASTICITY.getValue(), elasticity.toLowerCase());

            // 8. 获取尺码列表
            List<String> sizeList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(spuInfo.getSizeList())) {
                sizeList = spuInfo.getSizeList();
                if (StrUtil.isNotBlank(sizeType)) {
                    sizeList = normSizeList(sizeType, sizeList);
                }
            }

            // 9. 构建Product对象
            return Product.builder()
                    .skcId(skcId)
                    .spuId(spuId)
                    .title(spuInfo.getTitle())
                    .firstCategory(spuProductInfo.getFirstCategory().getFirst().getValue())
                    .subCategory(spuProductInfo.getSubCategory().getFirst().getValue())
                    .description(spuInfo.getDesc())
                    .features(spuInfo.getFeatures())
                    .brand(brand)
                    .colorValueList(spuInfo.getColorValueList())
                    .colorList(spuInfo.getColorList())
                    .sizeList(sizeList)
                    .itemTagDict(tags)
                    .priceType(priceType)
                    .price(price)
                    .highestPrice(highestPrice)
                    .originPrice(originalPrice)
                    .highestOriginalPrice(highestOriginalPrice)
                    .bodySizeList(bodySizeList)
                    .sizeType(sizeType)
                    .build();

        } catch (Exception e) {
            log.error("获取商品信息失败, spuId: {}, skcId: {}", spuId, skcId, e);
            return null;
        }
    }

    private List<BodySize> getBodySizeList(SpuInfo spuInfo, String categoryId, String sizeType) {
        List<BodySize> bodySizeList = new ArrayList<>();


        // 1. 从product_size_map获取尺码信息
        if (ObjectUtil.isNotEmpty(spuInfo.getProductSizeMap())) {

            List<ProductSize> productSizeMapList = spuInfo.getProductSizeMap();

//            List<Map<String, Object>> productSizeMapList = (List<Map<String, Object>>) spuInfo.get("product_size_map");

            productSizeMapList = productSizeMapList.stream()
                    .filter(item -> "bodySize".equals(item.getScope()) && StringUtils.hasText(item.getSize()))
                    .toList();
            
            // 仅处理bodySize类型的数据
//            productSizeMapList = productSizeMapList.stream()
//                .filter(item -> "bodySize".equals(item.get("scope")) && item.get("size") != null)
//                .collect(Collectors.toList());

            // todo
            convertProductSizeToBodySize(productSizeMapList, sizeType);
//            bodySizeList = convertProductSizeToBodySize(productSizeMapList, sizeType);
        }
        // 2. 如果没有尺码信息,尝试从size_map获取  all:spu:1777509875658596354
        else if (ObjectUtil.isNotEmpty(spuInfo.getSizeMap())) {
            String sizeMap = spuInfo.getSizeMap();
            try {
                List<Map<String, Object>> sizeMapList = objectMapper.readValue(sizeMap, new TypeReference<>() {});
                sizeMapList = sizeMapList.stream()
                        .filter(item -> categoryId.equals(item.get("category_id")))
                        .toList();
                // todo
                convertCategorySizeToBodySize(sizeMapList, sizeType);
//                bodySizeList = convertCategorySizeToBodySize(sizeMapList, sizeType);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }

        // 3. 处理特殊尺码(00, 000)
        processSpecialSizes(bodySizeList);

        
        // 4. 处理ONE_SIZE
        if (!isOneSize(spuInfo.getSizeList(), spuInfo.getSizeType())) {
            bodySizeList = bodySizeList.stream()
                .filter(bodySize -> !bodySize.getSizeList().contains("OS"))
                .collect(Collectors.toList());
        }
        
        return bodySizeList;
    }

    private void processSpecialSizes(List<BodySize> bodySizeList) {
        for (BodySize bodySize : bodySizeList) {
            List<String> newSizeList = new ArrayList<>();
            for (String size : bodySize.getSizeList()) {
                if ("00".equals(size)) {
                    newSizeList.add("-1");
                } else if ("000".equals(size)) {
                    newSizeList.add("-2");
                } else {
                    newSizeList.add(size);
                }
            }
            bodySize.setSizeList(newSizeList);
        }
    }

    private boolean isOneSize(List<String> sizeList, String sizeType) {
        if (sizeList == null) {
            return false;
        }
        return sizeList.stream()
            .map(size -> size.toLowerCase().replace("-","").replace("_","").replace("/","").replace(" ",""))
            .anyMatch(ONE_SIZE_ALIAS::containsKey);
    }

    private static final Map<String, String> ONE_SIZE_ALIAS = Map.of(
        "one", "OS",
        "os", "OS", 
        "onsz", "OS",
        "onesize", "OS"
    );

    private String getSkcElasticity(String skcId) {
        String key = StrUtil.format(RedisKeyConstants.PRODUCT_SKC_ELASTICITY, skcId);
        RBucket<String> bucket = redissonClient.getBucket(key, StringCodec.INSTANCE);
        String value = bucket.get();
        if (StrUtil.isNotBlank(value)) {
            try {
                Map<String, String> elasticityMap = objectMapper.readValue(value, Map.class);
                return elasticityMap.get("elasticity");
            } catch (JsonProcessingException e) {
                log.error("解析弹性信息失败: {}", value, e);
            }
        }
        return null;
    }

    private List<String> normSizeList(String sizeType, List<String> sizeList) {
        if (SizeType.SIZE.getValue().equals(sizeType) || 
            SizeType.NON_CLOTHING_SIZE.getValue().equals(sizeType)) {
            return sizeList;
        }
        
        return sizeList.stream()
            .map(this::getFirstNumberGroup)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    private String getFirstNumberGroup(String size) {
        Pattern pattern = Pattern.compile("\\d+(\\.\\d+)?");
        Matcher matcher = pattern.matcher(size);
        if (matcher.find()) {
            String number = matcher.group(0);
            if ("00".equals(number)) {
                return "-1";
            } else if ("000".equals(number)) {
                return "-2";
            }
            return number;
        }
        return null;
    }


    private static final Map<String, String> IN_BODY_MEASURE_MAP = Map.of(
            // 英寸单位的测量映射
            "chest", "CHEST",
            "bust", "CHEST",
            "waist", "WAIST",
            "hip", "HIP",
            "inseam", "INSEAM"
            // 可以根据需要添加更多映射
    );

    private static final Map<String, String> CM_BODY_MEASURE_MAP = Map.of(
            // 厘米单位的测量映射
            "chest_cm", "CHEST",
            "bust_cm", "CHEST",
            "waist_cm", "WAIST",
            "hip_cm", "HIP",
            "inseam_cm", "INSEAM"
            // 可以根据需要添加更多映射
    );

    private static final Map<String, String> NORM_SIZE_MAP = Map.of(
            "XXS", "XXS",
            "XS", "XS",
            "S", "S",
            "M", "M",
            "L", "L",
            "XL", "XL",
            "XXL", "XXL",
            "XXXL", "XXXL",
            "XXXXL", "XXXXL"
    );

    private List<BodySize> convertProductSizeToBodySize(List<ProductSize> productSizeMapList, String sizeType) {
        List<BodySize> bodySizeList = new ArrayList<>();

        //for (ProductSize productSize : productSizeMapList) {
        //
        //}

        for (ProductSize productSize : productSizeMapList) {

            String label = productSize.getLabel();
            String sizeStr = productSize.getSize();

            if (IN_BODY_MEASURE_MAP.containsKey(label)) {
                String newLabel = IN_BODY_MEASURE_MAP.get(label);
                List<String> sizeList = Arrays.stream(sizeStr.trim().split(","))
                        .map(size -> {
                            String upperSize = size.toUpperCase();
                            return NORM_SIZE_MAP.getOrDefault(upperSize, size);
                        })
                        .collect(Collectors.toList());

                BodySize bodySize = BodySize.builder()
                        .label(newLabel)
                        .value(productSize.getValue())
                        .sizeType(sizeType)
                        .sizeList(sizeList)
                        .unit("IN")
                        .build();

                bodySizeList.add(bodySize);
            }

            if (CM_BODY_MEASURE_MAP.containsKey(label)) {
                String newLabel = CM_BODY_MEASURE_MAP.get(label);
                List<String> sizeList = Arrays.stream(sizeStr.trim().split(","))
                        .map(size -> {
                            String upperSize = size.toUpperCase();
                            return NORM_SIZE_MAP.getOrDefault(upperSize, size);
                        })
                        .collect(Collectors.toList());

                BodySize bodySize = BodySize.builder()
                        .label(newLabel)
                        .value(productSize.getValue())
                        .sizeType(sizeType)
                        .sizeList(sizeList)
                        .unit("CM")
                        .build();

                bodySizeList.add(bodySize);
            }
        }

        return bodySizeList;
    }

    private List<BodySize> convertCategorySizeToBodySize(List<Map<String, Object>> categorySizeMapList, String sizeType) {
        List<BodySize> bodySizeList = new ArrayList<>();

        for (Map<String, Object> categorySizeMap : categorySizeMapList) {
            String bust = (String) categorySizeMap.get("bust");
            String hip = (String) categorySizeMap.get("hips");
            String waist = (String) categorySizeMap.get("waist");

            // 遍历所有可能的尺码类型
            for (Map.Entry<String, String> entry : size_map_type_dict.entrySet()) {
                String sizeMapType = entry.getKey();
                String mappedSizeType = entry.getValue();

                if (categorySizeMap.containsKey(sizeMapType)) {
                    String sizeStr = (String) categorySizeMap.get(sizeMapType);
                    List<String> sizeList = Arrays.stream(sizeStr.trim().split(","))
                            .map(String::toUpperCase)
                            .collect(Collectors.toList());

                    // 创建胸围尺码
                    BodySize bustBodySize = BodySize.builder()
                            .label("CHEST")
                            .value(bust)
                            .sizeType(mappedSizeType)
                            .sizeList(sizeList)
                            .unit("IN")
                            .build();

                    // 创建臀围尺码
                    BodySize hipBodySize = BodySize.builder()
                            .label("HIP")
                            .value(hip)
                            .sizeType(mappedSizeType)
                            .sizeList(sizeList)
                            .unit("IN")
                            .build();

                    // 创建腰围尺码
                    BodySize waistBodySize = BodySize.builder()
                            .label("WAIST")
                            .value(waist)
                            .sizeType(mappedSizeType)
                            .sizeList(sizeList)
                            .unit("IN")
                            .build();

                    bodySizeList.addAll(Arrays.asList(bustBodySize, hipBodySize, waistBodySize));
                }
            }
        }

        return bodySizeList;
    }

    // 尺码类型映射字典
    private static final Map<String, String> size_map_type_dict = Map.of(
            "missy", "MISSY",
            "plus", "PLUS",
            "petite", "PETITE",
            "junior", "JUNIOR"
            // 可以根据需要添加更多映射
    );
}