package com.looksky.agents.application.tryon.swapcloth.generation;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.template.FreemarkerService;
import com.looksky.agents.common.utils.ResourceUtils;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.SwapClothWorkflowReq;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class RunPodWorkflowService implements IRunWorkflow {

    @Resource
    private FreemarkerService freemarkerService;


    @Override
    public String run(String id, String imagePrompt, String faceImage, String modelImage) {
        log.info("{}, 执行工作流", id);
        WorkflowRecord workflowRecord = executeWorkflow(imagePrompt, faceImage, modelImage);
        log.info("{}, 发生工作流任务, PromptId:{}, ClientId:{}", id, workflowRecord.promptId(), workflowRecord.clientId());
        List<String> finalImages = pollingWorkflowResult(workflowRecord.promptId);
        log.info("{}, 工作流执行完毕, 工作流结果:{}", id, finalImages);
        return finalImages.getFirst();
    }


    @Override
    public boolean supports(WorkflowEnum workflowEnum) {
        return WorkflowEnum.RUN_POD == workflowEnum;
    }

    private List<String> pollingWorkflowResult(String promptId) {

        // 先睡 15s
        try {
            TimeUnit.SECONDS.sleep(15);
        } catch (InterruptedException e) {
            log.error("等待过程中被中断", e);
            Thread.currentThread().interrupt();
            return new ArrayList<>();
        }

        long startTime = System.currentTimeMillis();
        long timeout = TimeUnit.MINUTES.toMillis(5);
        long remainingTime = timeout - (System.currentTimeMillis() - startTime);
        while (remainingTime > 0) {
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                log.error("等待过程中被中断", e);
                Thread.currentThread().interrupt();
                return new ArrayList<>();
            }
            List<String> workflowResult = getWorkflowResult(promptId);
            if (!workflowResult.isEmpty()) {
                return workflowResult;
            }
            remainingTime = timeout - (System.currentTimeMillis() - startTime);
        }

        log.error("工作流执行超时");

        return new ArrayList<>();
    }


    private WorkflowRecord executeWorkflow(String imagePrompt, String faceImage, String modelImage) {
        String workflow = ResourceUtils.workflow("swap-cloth");
        String formattedWorkflow = freemarkerService.parseString(workflow, Map.of("imagePrompt", imagePrompt, "faceImage", faceImage, "modelImage", modelImage));
        log.info("格式化后的workflow: {}", formattedWorkflow);

        String clientId = IdUtil.getSnowflakeNextIdStr();
        SwapClothWorkflowReq workflowRequest = new SwapClothWorkflowReq(clientId, JSONUtil.parseObj(formattedWorkflow));

        String body;
        try (HttpResponse response = HttpRequest.post("https://prtog54rkijkse-8502.proxy.runpod.net/prompt").body(JSONUtil.toJsonStr(workflowRequest)).execute()) {
            if (!response.isOk()) {
                throw new RuntimeException("执行workflow失败");
            }
            body = response.body();
        }
        log.info("workflow 响应：{}", body);
        if (!body.contains("prompt_id")) {
            throw new RuntimeException("workflow 响应中缺少 prompt_id，请检查：1. ComfyUI服务是否运行 2. 工作流格式是否正确");
        }
        String promptId = JSONUtil.parseObj(body).getStr("prompt_id");

        return new WorkflowRecord(clientId, promptId);
    }


    private List<String> getWorkflowResult(String promptId) {
        String url = "https://prtog54rkijkse-8502.proxy.runpod.net/history/" + promptId;
        String body;
        try (HttpResponse response = HttpRequest.get(url).execute()) {
            if (!response.isOk()) {
                throw new RuntimeException("获取工作流结果失败");
            }
            body = response.body();
        }
        if (StrUtil.isBlankIfStr(body == null)) {
            return new ArrayList<>();
        }
        log.info("工作流结果：{}", body);
        JSONObject jsonResult = JSONUtil.parseObj(body);

        List<String> finalImages = new ArrayList<>();

        // 获取指定promptId的输出结果
        JSONObject promptResult = jsonResult.getJSONObject(promptId);
        if (promptResult != null && promptResult.containsKey("outputs")) {
            JSONObject outputs = promptResult.getJSONObject("outputs");
            // 检查节点158的输出
            if (outputs.containsKey("158")) {
                JSONObject node158 = outputs.getJSONObject("158");
                if (node158.containsKey("text")) {
                    List<String> resUrls = node158.getJSONArray("text").toList(String.class);
                    for (String resUrl : resUrls) {
                        finalImages.add(resUrl);
                        log.info("生成文件: {}", resUrl);
                    }
                }
            }
        }

        return finalImages;
    }


    /**
     * 记录工作流执行记录
     *
     * @param clientId 用来监控工作流是否执行完
     * @param promptId 用来获取结果
     */
    record WorkflowRecord(String clientId, String promptId) {
    }
}
