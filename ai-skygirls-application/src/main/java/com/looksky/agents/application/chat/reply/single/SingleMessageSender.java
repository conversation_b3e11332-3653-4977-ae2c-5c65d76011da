package com.looksky.agents.application.chat.reply.single;

import com.looksky.agents.application.chat.reply.AbstractMessageSender;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import java.util.Collections;
import java.util.HashMap;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Order(1000)
@Component
public class SingleMessageSender extends AbstractMessageSender {
    @Override
    protected boolean supports(PromptModel strategy) {
        return true;
    }

    
    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        String result = commonRequestService.commonExecuteStrategy(strategy, Collections.emptyMap());
        sendContent(initMessage(EventTypeEnum.TEXT.getType()), EventTypeEnum.TEXT.getType(), result);
        extraMessage(EventTypeEnum.TEXT.getType(), result, hashMap);
    }


}