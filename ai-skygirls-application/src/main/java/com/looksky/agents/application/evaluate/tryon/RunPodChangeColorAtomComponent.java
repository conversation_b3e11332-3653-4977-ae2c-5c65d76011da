package com.looksky.agents.application.evaluate.tryon;

import com.looksky.agents.application.tryon.swapcloth.changecolor.RunPodChangeClotheColorServiceV2;
import com.looksky.agents.application.tryon.swapcloth.changecolor.RunPodServerlessChangeClotheColorServiceV3;
import com.looksky.agents.sdk.optimize.dto.DatasetRecordDTO;
import com.looksky.agents.sdk.optimize.dto.OptimizePromptResultDTO;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.SwapClothChangeClothColorRequest;
import jakarta.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 1.1.8
 **/
@Slf4j
@Component
public class RunPodChangeColorAtomComponent {

    @Resource
    private RunPodServerlessChangeClotheColorServiceV3 runPodServerlessChangeClotheColorServiceV3;

    @Resource
    private RunPodChangeClotheColorServiceV2 runPodChangeClotheColorServiceV2;

    public OptimizePromptResultDTO run(DatasetRecordDTO datasetRecord, String name) {
        long start = System.currentTimeMillis();
        try {
            SwapClothChangeClothColorRequest param = new SwapClothChangeClothColorRequest();

            Map<String, Object> data = datasetRecord.getData();
            if (data != null) {
                Optional.ofNullable((String) data.get("imageUrl")).ifPresent(param::setImageUrl);
                Optional.ofNullable((String) data.get("color")).ifPresent(param::setColor);
            }
            String url;
            if ("runPodChangeColor".equals(name)) {
                url = runPodChangeClotheColorServiceV2.changeClothColor(param);
            } else {
                url = runPodServerlessChangeClotheColorServiceV3.changeClothColor(param);
            }
            long end = System.currentTimeMillis();
            return new OptimizePromptResultDTO(url, 0L, end - start);

        } catch (Exception e) {
            log.error("请求失败: {}", e.getMessage());
            long end = System.currentTimeMillis();
            return new OptimizePromptResultDTO(e.getMessage(), 0L, end - start);
        }

    }

}
