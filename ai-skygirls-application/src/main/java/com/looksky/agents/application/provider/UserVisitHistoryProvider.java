package com.looksky.agents.application.provider;

import cn.hutool.core.util.ObjectUtil;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.user.visit.dto.VisitHistoryDTO;
import com.looksky.agents.sdk.utils.date.DateUtils;
import com.looksky.agents.data.client.service.DataHubDataService;
import com.looksky.agents.infrastructure.context.Context;
import java.util.ArrayList;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UserVisitHistoryProvider implements DataProvider {
    private final DataHubDataService dataHubDataService;


    @Override
    public void getData() {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());
        String userId = requestInput.getUserId();
        VisitHistoryDTO visitHistory = dataHubDataService.visitHistory(userId);
        ArrayList<Long> timestampList = visitHistory.getTimestampList();
        if (ObjectUtil.isEmpty(timestampList) || timestampList.size() < 2) {
            // 如果没有历史访问记录，设置上一次访问时间为当前时间
            Context.put(Context.Name.USER_PREV_VISIT_DATE.getName(), requestInput.getDate());
        } else {
            Long date = timestampList.get(1);
            String firstVisitDate = DateUtils.getDateTimeBySeconds(date, requestInput.getZone());
            Context.put(Context.Name.USER_PREV_VISIT_DATE.getName(), firstVisitDate);
        }

    }

    @Override
    public boolean supports(String key) {
        return key.equals("prevVisitDate");
    }

} 