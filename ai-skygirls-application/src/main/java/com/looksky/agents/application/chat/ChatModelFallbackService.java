package com.looksky.agents.application.chat;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.exception.BusinessException;
import com.looksky.agents.infrastructure.trace.annotation.TraceMethod;
import com.looksky.agents.models.model.ModelEnum;
import com.looksky.agents.models.schema.AwsDeepSeekRequestBuilder;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import com.looksky.agents.models.schema.AzureJsonSchemaRequestBuilder;
import com.looksky.agents.models.schema.JsonSchemaRequestBuilder;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.azure.openai.AzureOpenAiChatModel;
import org.springframework.ai.bedrock.converse.BedrockProxyChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

@Slf4j
@Service
public class ChatModelFallbackService {

    @Resource
    private OpenAiChatModel openAiChatModel;

    @Resource
    private AzureOpenAiChatModel azureOpenAiChatModel;

    @Resource
    private BedrockProxyChatModel bedrockProxyChatModel;


    @CollectEvent(typeExpression = "#requestParam.modelName", extExpression = "#requestParam.promptName")
    public JsonSchemaRequestBuilder execute(RequestParamBO requestParam) {
        return new JsonSchemaRequestBuilder()
            .withChatModel(openAiChatModel)
            .withModel(requestParam.getModelName())
            .withSize(requestParam.getSize())
            .withJsonSchema(requestParam.getJsonSchema())
            .withOutputType(requestParam.getOutputType())
            .withImagesUserPrompt(requestParam.getUserPrompt(), requestParam.getImageUrl())
            .withSystemMessage(requestParam.getSystemPrompt())
            .withTemperature(requestParam.getTemperature())
            .execute();
    }
    @CollectEvent(typeExpression = "#requestParam.modelName", extExpression = "#requestParam.promptName")
    public AzureJsonSchemaRequestBuilder executeAzure(RequestParamBO requestParam) {
        return new AzureJsonSchemaRequestBuilder()
            .withChatModel(azureOpenAiChatModel)
            .withModel(requestParam.getModelName())
            .withSize(requestParam.getSize())
            .withJsonSchema(requestParam.getJsonSchema())
            .withOutputType(requestParam.getOutputType())
            .withImagesUserPrompt(requestParam.getUserPrompt(), requestParam.getImageUrl())
            .withSystemMessage(requestParam.getSystemPrompt())
            .withTemperature(requestParam.getTemperature())
            .execute();
    }

    /**
     * 使用故障转移机制执行非流式请求
     */
    @TraceMethod(description = "执行 Prompt")
    @CollectEvent(typeExpression = "#requestParam.modelName", extExpression = "#requestParam.promptName")
    public String executeWithFallback(RequestParamBO requestParam) {
        try {
            // 首先尝试使用OpenAI
            return new JsonSchemaRequestBuilder()
                .withChatModel(openAiChatModel)
                .withModel(requestParam.getModelName())
                .withSize(requestParam.getSize())
                .withJsonSchema(requestParam.getJsonSchema())
                .withOutputType(requestParam.getOutputType())
                .withImagesUserPrompt(requestParam.getUserPrompt(), requestParam.getImageUrl())
                .withSystemMessage(requestParam.getSystemPrompt())
                .withTemperature(requestParam.getTemperature())
                .execute()
                .getContent();
        } catch (Exception e) {
            log.warn("OpenAI请求失败，回退到Azure. Error: {}", e.getMessage());

            try {
                // 切换到Azure
                return new AzureJsonSchemaRequestBuilder()
                    .withChatModel(azureOpenAiChatModel)
                    .withModel(requestParam.getModelName())
                    .withSize(requestParam.getSize())
                    .withJsonSchema(requestParam.getJsonSchema())
                    .withOutputType(requestParam.getOutputType())
                    .withImagesUserPrompt(requestParam.getUserPrompt(), requestParam.getImageUrl())
                    .withSystemMessage(requestParam.getSystemPrompt())
                    .withTemperature(requestParam.getTemperature())
                    .execute()
                    .getContent();
            } catch (Exception azureEx) {
                String errorMessage = "OpenAI 和 Azure 都请求失败, 请检查请求入参. OpenAI error: %s, Azure error: %s, requestParam: %s".formatted( e.getMessage(), azureEx.getMessage(), JSONUtil.toJsonStr(requestParam));
                log.error(errorMessage);
                throw new BusinessException(errorMessage);
            }
        }
    }

    /**
     * 使用故障转移机制执行流式请求
     */
    @TraceMethod(description = "执行 Prompt")
    @CollectEvent(typeExpression = "#requestParam.modelName", extExpression = "#requestParam.promptName")
    public Flux<String> executeWithFallbackAsync(RequestParamBO requestParam) {

        if (ModelEnum.AWS_DEEP_SEEK.getModelName().equals(requestParam.getModelName())) {
            return new AwsDeepSeekRequestBuilder()
                .withChatModel(bedrockProxyChatModel)
                .withMaxTokens(31768)
                .withModel(requestParam.getModelName())
                .withSize(requestParam.getSize())
                .withOutputType(requestParam.getOutputType())
                .withJsonSchema(requestParam.getJsonSchema())
                .withImagesUserPrompt(requestParam.getUserPrompt(), requestParam.getImageUrl())
                .withSystemMessage(requestParam.getSystemPrompt())
                .withTemperature(requestParam.getTemperature())
                .executeAsync();
        }

        return new JsonSchemaRequestBuilder()
            .withChatModel(openAiChatModel)
            .withModel(requestParam.getModelName())
            .withSize(requestParam.getSize())
            .withOutputType(requestParam.getOutputType())
            .withJsonSchema(requestParam.getJsonSchema())
            .withImagesUserPrompt(requestParam.getUserPrompt(), requestParam.getImageUrl())
            .withSystemMessage(requestParam.getSystemPrompt())
            .withTemperature(requestParam.getTemperature())
            .executeAsync()
            .onErrorResume(e -> {
                log.warn("流式接口: OpenAI请求失败，回退到Azure. Error: {}", e.getMessage());

                // 切换到Azure的流式请求
                return new AzureJsonSchemaRequestBuilder()
                    .withChatModel(azureOpenAiChatModel)
                    .withModel(requestParam.getModelName())
                    .withSize(requestParam.getSize())
                    .withOutputType(requestParam.getOutputType())
                    .withJsonSchema(requestParam.getJsonSchema())
                    .withImagesUserPrompt(requestParam.getUserPrompt(), requestParam.getImageUrl())
                    .withSystemMessage(requestParam.getSystemPrompt())
                    .withTemperature(requestParam.getTemperature())
                    .executeAsync()
                    .onErrorResume(azureEx -> {
                        String errorMessage = "流式接口: OpenAI 和 Azure 都请求失败, 请检查请求入参. OpenAI error: %s, Azure error: %s, requestParam: %s".formatted( e.getMessage(), azureEx.getMessage(), JSONUtil.toJsonStr(requestParam));
                        log.error(errorMessage);
                        return Flux.error(new BusinessException(errorMessage));
                    });
            });
    }
} 