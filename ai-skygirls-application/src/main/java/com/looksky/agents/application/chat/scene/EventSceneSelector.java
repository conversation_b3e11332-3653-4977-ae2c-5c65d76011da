package com.looksky.agents.application.chat.scene;


import com.looksky.agents.application.prompt.PromptBusinessService;
import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.event.model.EventModel;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.infrastructure.context.Context;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Order(100)
@Component
@RequiredArgsConstructor
public class EventSceneSelector extends BaseSceneSelector {

    private final PromptBusinessService promptBusinessService;

    @Override
    public boolean support(String eventName, EnterPointEnum enterPoint, String page) {
        return true; // 作为默认策略,总是返回true
    }

    @Override
    protected SceneResult getSceneAndExternInfo(RequestInputDTO requestInput) {
        EventModel eventModel = Context.get(Context.Name.EVENT_MODEL.getName());
        // 只有一个策略，直接返回该策略

        PromptModel prompt;
        if ("girls_opening".equals(eventModel.getName())) {
            prompt = promptBusinessService.getPromptModelByNameWithMetaData(PromptNameEnum.getOpening(requestInput.getEventDict().getMessageType()));
        } else if ("search_dressing_opening".equals(eventModel.getName())) {
            // 这里是开场白改版, 以前是叫这个的, 现在要返回开场白和快捷问
            prompt = promptBusinessService.getPromptModelByNameWithMetaData("search_dressing_opening_and_ask");
        } else {
            prompt = promptBusinessService.getPromptModelByNameWithMetaData(eventModel.getPromptName());
        }

        String finalPrompt = prompt.getUserPrompt() + eventModel.getContent();
        prompt.setUserPrompt(finalPrompt);
        return new SceneResult(prompt, null);
    }

} 