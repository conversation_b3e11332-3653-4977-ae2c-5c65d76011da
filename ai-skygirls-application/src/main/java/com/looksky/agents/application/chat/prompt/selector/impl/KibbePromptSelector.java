package com.looksky.agents.application.chat.prompt.selector.impl;

import com.looksky.agents.application.chat.prompt.selector.PromptSelector;
import org.springframework.stereotype.Component;

/**
 * Kibbe体型相关问题选择器
 */
@Component
public class KibbePromptSelector implements PromptSelector {

    private static final String KIBBE_BODY_PROMPT = "kibbe_question_analysis_prompt";

    @Override
    public String support() {
        return "kibbe";
    }

    record KibbeQuestionInfo(String questionType, String kibbeStyleList){}
    

    @Override
    public String select() {


        // 执行 KIBBE_BODY_PROMPT todo
        KibbeQuestionInfo kibbeQuestionInfo = new KibbeQuestionInfo(null, null);
        String questionType = kibbeQuestionInfo.questionType();


        // 根据用户输入和历史记录判断具体的Kibbe体型相关子问题
        // TODO: 实现具体的选择逻辑
//        return "KIBBE_BODY_TYPE";
        return "small_talk_in_search";
    }
} 