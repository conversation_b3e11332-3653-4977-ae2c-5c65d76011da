package com.looksky.agents.application.tryon.swapcloth.changecolor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.SwapClothChangeClothColorRequest;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 1.1.8
 **/
@Slf4j
@Service
public class MeiTuChangeClotheColorService {
    @Value("${try-on.meiTu.apiKey}")
    private String apiKey;

    @Value("${try-on.meiTu.secretId}")
    private String secretId;

    @Value("${try-on.meiTu.appId}")
    private String appId;

    private static final String METHOD = "POST";

    /**
     * 换色
     *
     * @param imageUrl 图片地址
     * @param color    颜色值
     * @return 换色后的图片地址
     */
    @SneakyThrows
    @CollectEvent
    public String changeClothColor(SwapClothChangeClothColorRequest request) {
        String apiUrl = "https://openapi.mtlab.meitu.com/v1/change_cloth_color_model?api_key=" + apiKey + "&app_secret=" + secretId;
        log.debug("美图请求 Url: {}", apiUrl);

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put(Signer.HeaderHost, "openapi.mtlab.meitu.com");


        String requestBody = """
            {
                "parameter": {
                    "rsp_media_type": "jpg",
                    "color_str": "%s",
                    "cloth_category": "up",
                    "inpaint": 1
                },
                "media_info_list": [
                    {
                        "media_data": "%s",
                        "media_profiles": {
                            "media_data_type": "url"
                        }
                    }
                ]
            }
            """.formatted(request.getColor(), request.getImageUrl());

        Signer signer = new Signer(apiKey, secretId);

        Map<String, String> signedHeaders = signer.sign(apiUrl, METHOD, headers, requestBody);

        log.info("美图请求头: {}", signedHeaders);
        log.info("美图请求体: {}", requestBody);

        try (HttpClient client = HttpClient.newHttpClient()) {
            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .uri(URI.create(apiUrl))
                .POST(HttpRequest.BodyPublishers.ofString(requestBody));
            // 添加签名后的请求头
            for (Map.Entry<String, String> entry : signedHeaders.entrySet()) {
                // 跳过限制的头部名称
                if (!entry.getKey().equalsIgnoreCase("Host")) {
                    requestBuilder.header(entry.getKey(), entry.getValue());
                }
            }

            HttpResponse<String> response = client.send(
                requestBuilder.build(),
                HttpResponse.BodyHandlers.ofString()
            );
            String responseBody = response.body();
            log.info("美图返回响应体: {}", responseBody);
            if (response.statusCode() == 200) {
                ObjectMapper mapper = new ObjectMapper();
                // 使用驼峰转下划线配置
                mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
                ChangeClothColorResponse changeClothColorResponse = mapper.readValue(responseBody, ChangeClothColorResponse.class);
                return changeClothColorResponse.getMediaInfoList().getFirst().getMediaData();
            } else {
                log.error("美图返回错误: {}", responseBody);
                throw new RuntimeException("美图换装失败: " + responseBody);
            }
        }

    }


    @Data
    static class ChangeClothColorResponse {
        private List<MediaInfo> mediaInfoList;
        private Parameter parameter;

        @Data
        static class MediaInfo {
            private String mediaData;
            private MediaProfiles mediaProfiles;

            @Data
            public static class MediaProfiles {
                private String mediaDataType;
            }
        }

        @Data
        static class Parameter {
            private String rspMediaType;
            private String version;
        }
    }

}
