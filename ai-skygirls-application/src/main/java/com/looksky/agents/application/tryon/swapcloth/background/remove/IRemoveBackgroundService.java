package com.looksky.agents.application.tryon.swapcloth.background.remove;

import java.time.Duration;

/**
 * 抠图服务通用接口
 * 所有版本的实现都实现此接口
 *
 * <AUTHOR>
 */
public interface IRemoveBackgroundService {

    /**
     * 同步执行抠图
     * 
     * @param imageUrl 图片URL
     * @return 抠图结果的URL
     */
    String removeBackground(String imageUrl);
    
    /**
     * 获取服务版本
     * 默认使用实现类的简化类名
     * 
     * @return 服务版本
     */
    default String getVersion() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * 获取服务超时时间
     * 
     * @return 服务超时时间
     */
    default Duration getTimeout() {
        // 默认超时时间30秒
        return Duration.ofSeconds(10);
    }
} 