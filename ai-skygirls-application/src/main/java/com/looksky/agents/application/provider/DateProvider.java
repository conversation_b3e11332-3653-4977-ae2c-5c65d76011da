package com.looksky.agents.application.provider;

import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.utils.date.DateUtils;
import com.looksky.agents.infrastructure.context.Context;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class Date<PERSON>rovider implements DataProvider {


    @Override
    public void getData() {
        try {
            RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());
            if (requestInput == null) {
                Context.put(Context.Name.DATE_DATA.getName(), DateUtils.getNowDataTime());
                return;
            }

            if (requestInput.getDate() == null) {
                Context.put(Context.Name.DATE_DATA.getName(), DateUtils.getNowDataTime());
                return;
            }

            Context.put(Context.Name.DATE_DATA.getName(), requestInput.getDate());
        } catch (Exception e) {
            log.error("获取 requestInput 中的数据失败", e);
            Context.put(Context.Name.DATE_DATA.getName(), DateUtils.getNowDataTime());
        }

    }

    @Override
    public boolean supports(String key) {
        return key.equals("date");
    }

} 