package com.looksky.agents.application.chat.preference.extraction;

import cn.hutool.core.util.ObjectUtil;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.agent.common.enums.TagEnum;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TagExtractorFactory {
    private final Map<String, AbstractPreferenceExtractor<?>> extractors = new HashMap<>();

    @Resource
    private List<AbstractPreferenceExtractor<?>> allExtractors;

    @PostConstruct
    public void init() {
        allExtractors.forEach(extractor -> {
            String tagName = extractor.getTagName();
            extractors.put(tagName, extractor);
            log.info("注册标签抽取器: {} -> {}", tagName, extractor.getClass().getSimpleName());
        });
    }

    /**
     * 获取指定标签的抽取器
     * @param tagName 标签名称
     * @return 对应的抽取器，如果不存在返回null
     */
    public AbstractPreferenceExtractor<?> getExtractor(String tagName) {
        AbstractPreferenceExtractor<?> extractor = extractors.get(tagName);
        if (extractor == null) {
            log.warn("未找到标签[{}]对应的抽取器", tagName);
        }
        return extractor;
    }

    /**
     * 批量抽取标签偏好（并行处理）
     * @param tags 需要抽取的标签集合
     */
    @Async
    public void extractPreferences(Set<String> tags) {
        if (ObjectUtil.isEmpty(tags)) {
            log.info("没有需要抽取的标签");
            return;
        }

        // 如果 tags 里面包含 waist 标签, 那么往 tags 里面添加一个 waist_fit 标签 和 waist_line 标签, 并且删除 waist 标签
        if (tags.contains(TagEnum.WAIST.getName())) {
            tags.add(TagEnum.WAISTLINE.getName());
            tags.add(TagEnum.WAIST_FIT.getName());
            tags.remove(TagEnum.WAIST.getName());
        }

        if (tags.contains(TagEnum.NECKLINE.getName())) {
            tags.add(TagEnum.NECKLINE_SHAPE.getName());
            tags.add(TagEnum.NECKLINE_DETAIL.getName());
            tags.remove(TagEnum.NECKLINE.getName());
        }

        log.debug("开始并行抽取标签偏好: {}", tags);
        List<VirtualCompletableFuture<Void>> futures = new ArrayList<>();

        tags.forEach(tag -> {
            AbstractPreferenceExtractor<?> extractor = getExtractor(tag);
            if (extractor != null) {
                VirtualCompletableFuture<Void> future = VirtualCompletableFuture.runAsync(
                    () -> {
                        try {
                            extractor.extract();
                        } catch (Exception e) {
                            log.error("抽取标签[{}]偏好失败", tag, e);
                        }
                    }
                );
                futures.add(future);
            }
        });

        // 等待所有抽取任务完成
        VirtualCompletableFuture.allOf(futures.toArray(new VirtualCompletableFuture[0])).join();
        log.debug("完成并行抽取标签偏好");
    }

    /**
     * 检查是否支持指定标签的抽取
     * @param tagName 标签名称
     * @return 是否支持
     */
    public boolean supportsTag(String tagName) {
        return extractors.containsKey(tagName);
    }
} 