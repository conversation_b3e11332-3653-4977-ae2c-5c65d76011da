package com.looksky.agents.application.tryon.swapcloth;

import ai.fal.client.FalClient;
import ai.fal.client.SubscribeOptions;
import ai.fal.client.queue.QueueStatus;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.application.prompt.PromptBusinessService;
import com.looksky.agents.application.tryon.swapcloth.convertor.TryOnConvertor;
import com.looksky.agents.application.tryon.swapcloth.utils.BMIDescriptionsUtils;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.data.opensearch.OsProductMappingType;
import com.looksky.agents.data.opensearch.SkcSearchService;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import com.looksky.agents.sdk.product.ProductInfo;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothParam;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothStatus;
import com.looksky.agents.sdk.tryon.swapcloth.enums.SwapClothStatusEnum;
import com.skygirls.biz.user.model.enums.MeasureTypeEnum;
import com.skygirls.biz.user.tryon.dto.SearchTryOnStatusReq;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SwapClothServiceV3 {

    private final CommonRequestService commonRequestService;
    private final PromptBusinessService promptBusinessService;
    private final TryOnConvertor tryOnConvertor;
    private final GirlsClient girlsClient;
    private final FalClient falClient;
    private final SkcSearchService skcSearchService;


    private static final String ANALYZE_MODEL_IMAGE = "swap_cloth_analyze_model_image_simple";
    private static final String ANALYZE_USER_FULL_BODY_IMAGE = "swap_cloth_analyze_user_full_body_simple";
    private static final String GENERATE_IMAGE_PROMPT = "swap_cloth_combine_user_model_simple";

    private static final String CHECK_PRODUCT_CONTAIN_BODY = "check_product_img_contain_body";
    private static final String CATEGORY = "one-pieces";


    public Boolean asyncSwapCloth(SwapClothParam swapClothParam) {

        log.info("{}开始换装, 换装参数: skcId: {}", swapClothParam.getId(), swapClothParam.getSkcId());
        // todo 前置检测环境, 如果通过, 则开启异步任务, 否则都不通过


        VirtualCompletableFuture.runAsync(() -> {
            try {
                create(swapClothParam);
            } catch (Exception e) {
                log.error("换装失败", e);
                callback(swapClothParam, SwapClothStatusEnum.FAILED, null);
            }
        });
        return true;
    }


    public void create(SwapClothParam swapClothParam) {

        SwapClothStatus status = new SwapClothStatus();
        status.setTraceId(swapClothParam.getId());
        status.setCreateTime(LocalDateTime.now());
        status.setUpdateTime(LocalDateTime.now());
        status.setSwapClothParam(swapClothParam);

        var checkBodyFuture = VirtualCompletableFuture.supplyAsync(() -> checkProductBody(swapClothParam.getModelImage()));

        String analyze = analyze(status);

        status.setPromptContent(analyze);

        String workflowImage = fashnSwapFace(analyze, swapClothParam.getFaceImage());

        status.setWorkflowResultUrl(workflowImage);

        String category = getCategory(swapClothParam.getSkcId(), checkBodyFuture.join());

        String result = fashnSwapCloth(workflowImage, swapClothParam.getModelImage(), category);

        status.setResultImage(result);

        callback(status.getSwapClothParam(), SwapClothStatusEnum.COMPLETED, status.getResultImage());

    }

    private String getCategory(String skcId, CheckProductContainBody checkResult) {
        try {
            if (checkResult.getModelPresent()) {
                return CATEGORY;
            }

            ProductInfo osSkcModels = skcSearchService.searchSkc(skcId);
            String firstCategory = (String) osSkcModels.getTags().getJSONArray(OsProductMappingType.FIRST_CATEGORY).getFirst();
            return getFalCategory(firstCategory);
        } catch (Exception e) {
            log.error("获取商品一级品类失败, skcId: {}", skcId, e);
            return CATEGORY;
        }

    }


    private String getFalCategory(String category) {
        return switch (category) {
            case "one-piece", "set", "swimwear" -> CATEGORY;
            case "dresses", "jacket" -> "tops";
            default -> category;
        };
    }


    private String analyze(SwapClothStatus status) {
        log.info("开始分析, 换装参数: {}", JSONUtil.toJsonStr(status.getSwapClothParam()));
        SwapClothParam swapClothParam = status.getSwapClothParam();
        VirtualCompletableFuture<String> modelPrompt = getModelPrompt(swapClothParam.getModelImage());
        //CompletableFuture<String> userPrompt = getUserPrompt(swapClothParam.getFullBodyImage());
        VirtualCompletableFuture<String> userPrompt = getUserPrompt(swapClothParam.getFullBodyImage(), swapClothParam.getHeight(), swapClothParam.getWeight(), swapClothParam.getMeasureType());
        String result = getImagePrompt(userPrompt.join(), modelPrompt.join());
        log.info("分析结果: {}", result);
        return result;
    }


    public String fashnSwapFace(String prompt, String userFaceImage) {
        log.info("开始换脸, prompt: {}, userFaceImage: {}", prompt, userFaceImage);
        var input = Map.of(
            "prompt", prompt,
            "reference_image_url", userFaceImage,
            "image_size", Map.of("width", 768, "height", 1024)
        );
        var result = falClient.subscribe("fal-ai/flux-pulid",
            SubscribeOptions.<FashnResult>builder()
                .input(input)
                .logs(true)
                .resultType(FashnResult.class)
                .onQueueUpdate(update -> {
                    if (update instanceof QueueStatus.InProgress) {
                        log.info("换脸返回过程数据: {}", ((QueueStatus.InProgress) update).getLogs());
                    }
                })
                .build()
        );

        String requestId = result.getRequestId();
        log.info("换脸请求: requestId:{}, 请求体: {}", requestId, JSONUtil.toJsonStr(input));
        log.info("换脸结果: {}", JSONUtil.toJsonStr(result));

        return result.getData().getImages().getFirst().getUrl();

    }


    public String fashnSwapCloth(String workflowImage, String modelImage, String category) {
        log.info("开始换装, workflowImage: {}, modelImage: {}", workflowImage, modelImage);
        var input = Map.of(
            "model_image", workflowImage,
            "garment_image", modelImage,
            "category", category,
            "nsfw_filter", false
        );
        var result = falClient.subscribe("fashn/tryon",
            SubscribeOptions.<FashnResult>builder()
                .input(input)
                .logs(true)
                .resultType(FashnResult.class)
                .onQueueUpdate(update -> {
                    if (update instanceof QueueStatus.InProgress) {
                        log.info("换装返回过程数据: {}", ((QueueStatus.InProgress) update).getLogs());
                    }
                })
                .build()
        );

        String requestId = result.getRequestId();
        log.info("换装请求: requestId:{}, 请求体: {}", requestId, JSONUtil.toJsonStr(input));
        log.info("换装结果: {}", JSONUtil.toJsonStr(result));

        return result.getData().getImages().getFirst().getUrl();

    }


    @Data
    static class FashnResult {
        private List<FashnResultImage> images;

        @Data
        static class FashnResultImage {
            private String url;
            @JsonProperty("content_type")
            private String contentType;

            @JsonProperty("file_name")
            private String fileName;

            @JsonProperty("file_size")
            private long fileSize;

            private int width;
            private int height;
        }
    }


    private String getImagePrompt(String userPrompt, String modelPrompt) {
        return commonRequestService.commonExecuteStrategy(GENERATE_IMAGE_PROMPT, Map.of("userPrompt", userPrompt, "modelPrompt", modelPrompt));
    }


    private VirtualCompletableFuture<String> getModelPrompt(String modelImage) {
        return VirtualCompletableFuture.supplyAsync(() -> executeAnalyze(ANALYZE_MODEL_IMAGE, List.of(modelImage)));
    }


    private VirtualCompletableFuture<String> getUserPrompt(String userImage) {
        return VirtualCompletableFuture.supplyAsync(() -> executeAnalyze(ANALYZE_USER_FULL_BODY_IMAGE, List.of(userImage)));

    }

    private VirtualCompletableFuture<String> getUserPrompt(String userImage, String height, String weight, MeasureTypeEnum measureTypeEnum) {

        String userPrompt = BMIDescriptionsUtils.bmiDescription(measureTypeEnum, height, weight);


        return VirtualCompletableFuture.supplyAsync(() -> {
            PromptModel promptModel = promptBusinessService.getPromptModelByNameWithMetaData(ANALYZE_USER_FULL_BODY_IMAGE);
            RequestParamBO requestParam = promptBusinessService.convert(promptModel, MapUtil.empty());
            requestParam.setImageUrl(List.of(userImage));
            requestParam.setUserPrompt(userPrompt);
            return commonRequestService.commonExecuteStrategy(requestParam);

        });

    }

    private boolean checkUserPrompt(String prompt) {
        if (StrUtil.isBlankIfStr(prompt)) {
            return false;
        }

        if (prompt.contains("error") || prompt.contains("sorry")) {
            return false;
        }

        return true;
    }


    private String executeAnalyze(String promptName, List<String> modelImage) {
        PromptModel promptModel = promptBusinessService.getPromptModelByNameWithMetaData(promptName);
        RequestParamBO requestParam = promptBusinessService.convert(promptModel, MapUtil.empty());
        requestParam.setImageUrl(modelImage);
        return commonRequestService.commonExecuteStrategy(requestParam);
    }

    private void callback(SwapClothParam swapClothParam, SwapClothStatusEnum status, String resultImage) {
        SearchTryOnStatusReq callbackRequest = tryOnConvertor.toCallbackRequest(swapClothParam, status, resultImage);
        girlsClient.tryonCallback(callbackRequest);
    }

    private CheckProductContainBody checkProductBody(String productImage) {
        PromptModel promptModel = promptBusinessService.getPromptModelByNameWithMetaData(CHECK_PRODUCT_CONTAIN_BODY);
        RequestParamBO requestParam = promptBusinessService.convert(promptModel, MapUtil.empty());
        requestParam.setImageUrl(List.of(productImage));
        String result = commonRequestService.commonExecuteStrategy(requestParam);
        return JSONUtil.toBean(result, CheckProductContainBody.class);
    }

    @Data
    private static class CheckProductContainBody {
        @JsonProperty("model_present")
        private Boolean modelPresent;
        @JsonProperty("face_present")
        private Boolean faceComplete;
    }

}
