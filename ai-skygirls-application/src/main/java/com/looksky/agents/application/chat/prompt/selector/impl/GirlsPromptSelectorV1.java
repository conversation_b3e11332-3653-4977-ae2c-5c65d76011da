package com.looksky.agents.application.chat.prompt.selector.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.looksky.agents.application.chat.prompt.selector.PromptSelector;
import com.looksky.agents.common.utils.BooleanUtils;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.common.enums.TagEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.preference.CategoryAndTagPreference;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.preference.SubjectivePreference;
import com.looksky.agents.sdk.agent.product.enums.SearchQuestionType;
import com.skygirls.biz.im.dto.MessageRestDTO;
import com.skygirls.biz.report.IosUserInfoDto;
import com.skygirls.biz.user.dto.AllReportDto;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

/**
 * 服装搜索问题选择器
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ApiVersion(platform = ApiVersion.PlatformType.ALL, version = "1.1.0")
public class GirlsPromptSelectorV1 implements PromptSelector {

    private final TagSystemTableService tagSystemTableService;
    private final RedissonClient redissonClient;

    @Override
    public String support() {
        return "search_cloth";
    }

    @Override
    public String select() {

        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());

        String text = Optional.ofNullable(input.getEventDict()).map(MessageRestDTO.EventDict::getText).orElse(null);

        String adviceNextStep = checkIsAdviceQuestion(text);
        if (CharSequenceUtil.isNotBlank(adviceNextStep)) {
            log.info("用户输入了建议问题, 直接推荐商品, 执行 prompt: {}", adviceNextStep);
            return adviceNextStep;
        }

        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());

        List<String> allSubCategory = tagSystemTableService.getSubCategories();
        List<String> allFirstCategory = tagSystemTableService.getFirstCategories();
        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());

        // 没有反馈任何偏好, 需要咨询用户
        if (BeanUtil.isEmpty(userPreference)) {
            return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
        }

        // userPreference  不为空
        CategoryAndTagPreference categoryPreferences = userPreference.getCategoryAndTagPreference();
        SubjectivePreference subjectivePreference = userPreference.getSubjectivePreference();

        // 没有品类, 并且没有主观标签
        if (BeanUtil.isEmpty(categoryPreferences) && BeanUtil.isEmpty(subjectivePreference)) {
            // 细化
            return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
        }


        AtomicInteger conversationNumber = conversationStatus.getConversationNumber() == null ? new AtomicInteger(0) : conversationStatus.getConversationNumber();

        Set<String> currCategory = new HashSet<>(userPreference.getCurrentCategories());

        // 对话历史超过三轮 并且还没有推荐过商品
        if (conversationNumber.get() >= 2 && BooleanUtils.isFalse(conversationStatus.getRecommendProduct())) {

            // 获取当前品类
            // 判断是否表达了二级品类
            if (TagSystemTableService.isInnerTag(currCategory, new HashSet<>(allSubCategory))) {
                // 表达了二级品类, 推荐商品
                return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
            } else {
                // 没有表达二级品类品类, 推方案
                return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK_SOLUTION.getValue();
            }
        }


        // 如果当前只表达了一个品列, 且是 cloth, 但是没有主观标签
        if (currCategory.size() == 1 && currCategory.contains(CategoryEnum.CLOTH.getName()) && BeanUtil.isEmpty(subjectivePreference)) {
            if (ObjectUtil.isEmpty(categoryPreferences.getAllCategories().stream().filter(c -> CategoryEnum.CLOTH.getName().equals(c)))) {
                return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
            }
        }


        // 如果有品类标签但没有主观标签
        if (BeanUtil.isNotEmpty(categoryPreferences) && BeanUtil.isEmpty(subjectivePreference)) {


            int tagNumber = categoryPreferences.getTags().stream().filter(TagEnum::isClothTag).toList().size();


            // 如果包含二级品类
            if (TagSystemTableService.isInnerTag(currCategory, new HashSet<>(allSubCategory))) {
                if (tagNumber >= 2) {
                    // 推荐商品
                    return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
                } else {
                    // 如果还没有询问过 tag
                    if (BooleanUtils.isFalse(conversationStatus.getAskTag())) {
                        // 那么去询问 tag
                        // 如果是泳装, 那么走细化, 不推标签
                        if (isSwimwear(userPreference.getCurrentCategories())) {
                            return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
                        } else {
                            return SearchQuestionType.SUBCATEGORY_WITH_LESS_TWO_TAG_WITHOUT_ASK.getValue();
                        }
                    } else if (BooleanUtils.isFalse(conversationStatus.getRefiningRequirements())) {
                        // 如果还没有细化, 那么去细化
                        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
                    } else {
                        // 否则, 推荐商品
                        return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
                    }
                }
                // 如果不包含二级, 那么判断是否包含一级, 包含一级的话, 那么推荐品类选择器
            } else if (TagSystemTableService.isInnerTag(currCategory, new HashSet<>(allFirstCategory)) && tagNumber >= 3) {
                return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
            } else {
                // 不包含一级, 也不包含二级
                // 如果已经询问过品类
                if (BooleanUtils.isTrue(conversationStatus.getAskSubcategory())) {
                    // 并且也已经细化过
                    if (BooleanUtils.isTrue(conversationStatus.getRefiningRequirements())) {
                        // 那么推解决方案
                        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK_SOLUTION.getValue();
                    } else {
                        // 否则, 继续细化
                        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
                    }
                } else {
                    // 如果没有询问过二级品类, 那么去询问二级品类
                    return SearchQuestionType.FIRST_CATEGORY_WITHOUT_SUBCATEGORY_WITHOUT_ASK_SUBCATEGORY.getValue();
                }
            }
        }

        // 既有品类标签又有主观标签
        else if (BeanUtil.isNotEmpty(categoryPreferences) && BeanUtil.isNotEmpty(subjectivePreference)) {

            int tagNumber = categoryPreferences.getTags().size();

            // 判断当前表达的是二级品类
            if (TagSystemTableService.isInnerTag(currCategory, new HashSet<>(allSubCategory))) {
                if (tagNumber >= 2) {
                    // 如果标签数量大于等于2, 那么推荐商品
                    return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
                } else {
                    // 判断有没有细化过
                    if (BooleanUtils.isFalse(conversationStatus.getRefiningRequirements())) {
                        // 没有细化过, 去细化
                        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
                    } else {
                        // 细化过, 推荐商品
                        return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
                    }
                }
                // 判断当前表达的是一级品类
            } else if (TagSystemTableService.isInnerTag(currCategory, new HashSet<>(allFirstCategory)) && tagNumber >= 3) {
                // 推荐商品
                return SearchQuestionType.SUBCATEGORY_WITH_TWO_MORE_TAG.getValue();
            } else {
                // 既不是一级品类, 也不是二级品类
                if (BooleanUtils.isFalse(conversationStatus.getRefiningRequirements())) {
                    // 没有细化过, 去细化
                    return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
                } else {
                    // 判断品类是否改变
                    if (BooleanUtils.isTrue(conversationStatus.getCategoryChange())) {
                        // 如果改变, 那么去细化
                        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
                    } else {
                        // 如果没有改变, 推解决方案
                        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK_SOLUTION.getValue();
                    }
                }
            }
        }

        // 仅有主观词
        if (BeanUtil.isNotEmpty(subjectivePreference)) {
            // 还没有细化过
            if (BooleanUtils.isFalse(conversationStatus.getRefiningRequirements())) {
                // 去细化
                return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
            } else {
                // 细化过, 推解决方案
                return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK_SOLUTION.getValue();
            }
        }

        // 细化兜底
        return SearchQuestionType.SUBJECTIVE_WITHOUT_ASK.getValue();
    }


    private String checkIsAdviceQuestion(String userInput) {

        if (CharSequenceUtil.isBlank(userInput)) {
            return null;
        }
        // 需要去除 变量信息, 在进行 md5 的计算
        IosUserInfoDto userInfo = Context.get(Context.Name.USER_INFO.getName());
        String originText = getOriginText(userInput, userInfo);


        String md5Key = DigestUtil.md5Hex(originText);

        RBucket<String> bucket = redissonClient.getBucket(RedisKeyConstants.adviceQuestionNextStep(md5Key));

        // 如果不存在, 那么直接返回
        if (bucket.isExists()) {
            return bucket.get();
        }

        return null;

    }

    @NotNull
    private static String getOriginText(String userInput, IosUserInfoDto userInfo) {
        AllReportDto appUser = userInfo.getAppUser();
        String kibbeType = appUser.getKibbeType().getCode().replace("_", "");
        String bodyShape = appUser.getBodyShape().getCode().replace("_", "");
        String heightType = appUser.getHeightType().getCode();
        String colorSeason = appUser.getColorSeason().getName().replace("_", "");


        return userInput.replace(kibbeType, "{kibbeType}")
            .replace(bodyShape, "{bodyType}")
            .replace(heightType, "{heightType}")
            .replace(colorSeason, "{colorSeason}");
    }


    public boolean isSwimwear(Set<String> categorySet) {
        if (categorySet == null || categorySet.isEmpty()) {
            return false;
        }

        List<String> categoryList = new ArrayList<>(tagSystemTableService.getSubCategoryByFirstCategory(CategoryEnum.SWIMWEAR.getName()));
        categoryList.add(CategoryEnum.SWIMWEAR.getName());

        return categorySet.stream().anyMatch(categoryList::contains);

    }


} 