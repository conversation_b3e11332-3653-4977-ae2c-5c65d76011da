package com.looksky.agents.application.chat.reply.stream;

import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import java.util.HashMap;
import org.springframework.core.annotation.Order;


/**
 * 细化用户需求
 *
 * @since  1.1.11
 * <AUTHOR>
 **/
@Order(99)
//@Component
@Deprecated(since = "1.1.11", forRemoval = true)
public class RefiningRequirementMessageSender extends StreamMessageSender {
    @Override
    protected boolean supports(PromptModel strategy) {
        return PromptNameEnum.REFINING_REQUIREMENTS.getName().equals(strategy.getName());
    }

    @Override
    protected void afterSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        super.afterSend(strategy, hashMap);
        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());
        conversationStatus.setRefiningRequirements(true);
    }

}
