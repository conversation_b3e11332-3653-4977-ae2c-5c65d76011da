package com.looksky.agents.application.chat.reply.multiEvent;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.preference.enums.PreferenceTypeEnum;
import com.looksky.agents.sdk.agent.product.enums.SearchQuestionType;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.search.dto.CategoryFeedBackDTO;
import com.looksky.agents.sdk.agent.search.dto.CategorySelectorDTO;
import java.util.Collections;
import java.util.HashMap;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * Search 场景, 通用的选择器, 选择器样式复用品类选择器的
 *
 * @since  1.1.12
 * <AUTHOR>
 **/
@Order(1)
@Component
public class CommonSelectorMessageSender extends MultiEventMessageSender {
    @Override
    protected boolean supports(PromptModel strategy) {
        return SearchQuestionType.SELECTOR.getValue().equals(strategy.getName());
    }

    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {

        String result = commonRequestService.commonExecuteStrategy(strategy, Collections.emptyMap());
        CategorySelectorDTO categorySelector = JSONUtil.toBean(result, CategorySelectorDTO.class);
        CategoryFeedBackDTO like = CategoryFeedBackDTO.builder().direction(PreferenceTypeEnum.LIKE.getValue()).category(categorySelector.getCategory()).build();

        sendContent(initMessage(EventTypeEnum.TEXT.getType()), EventTypeEnum.TEXT.getType(), categorySelector.getText());
        extraMessage(EventTypeEnum.TEXT.getType(), categorySelector.getText(), hashMap);

        sendContent(initMessage(EventTypeEnum.CONFIRM_CATEGORY.getType()), EventTypeEnum.CONFIRM_CATEGORY.getType(), like);
        extraMessage(EventTypeEnum.CONFIRM_CATEGORY.getType(), "Pushed the category selector to the user: " + JSONUtil.toJsonStr(like), hashMap);

    }

    @Override
    protected void afterSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        super.afterSend(strategy, hashMap);
        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());
        conversationStatus.setAskSubcategory(true);
    }
}
