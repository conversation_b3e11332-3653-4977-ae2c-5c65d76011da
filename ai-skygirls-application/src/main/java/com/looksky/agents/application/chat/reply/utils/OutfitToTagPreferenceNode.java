package com.looksky.agents.application.chat.reply.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.infrastructure.exception.BusinessException;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.infrastructure.trace.annotation.TraceMethod;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.search.dto.OutfitProductPlans;
import com.looksky.agents.sdk.agent.search.dto.PlansDetail;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 方案转换为标签偏好节点
 *
 * <AUTHOR>
 * @since 1.1.14
 **/
@Component
@RequiredArgsConstructor
public class OutfitToTagPreferenceNode {
    private final CommonRequestService commonRequestService;
    private final ObjectMapper objectMapper;

    @TraceMethod(description = "将 OutfitProductPlans 转换为标签偏好")
    public VirtualCompletableFuture<Void> extract(OutfitProductPlans outfitProductPlans) {
        return VirtualCompletableFuture.runAsync(() -> {
            String tagPreferenceStr = commonRequestService.commonExecuteStrategy("extract_user_tag_preference", Map.of("input", JSONUtil.toJsonStr(outfitProductPlans)));
            Preference preference = null;
            try {
                preference = objectMapper.readValue(tagPreferenceStr, Preference.class);
            } catch (JsonProcessingException e) {
                throw new BusinessException("解析标签偏好JSON失败", e);
            }
            convert(outfitProductPlans, preference);
        });

    }

    private void convert(OutfitProductPlans outfitProductPlans, Preference preference) {
        if (preference == null) {
            return;
        }

        List<TagPreference> tagPreferencesList = preference.preference();
        if (CollUtil.isEmpty(tagPreferencesList)) {
            return;
        }

        List<PlansDetail> plans = outfitProductPlans.getPlans();

        for (PlansDetail plansDetail : plans) {
            String plansDetailTitle = plansDetail.getTitle();
            if (plansDetailTitle == null) {
                continue;
            }

            // 在推荐方案中查找标题匹配的项
            for (TagPreference tagPreference : tagPreferencesList) {
                String tagPreferenceTitle = tagPreference.title();
                // 如果方案和抽词的标题一样
                if (plansDetailTitle.equals(tagPreferenceTitle)) {
                    // 找到匹配项，复制属性
                    plansDetail.setTagPreference(tagPreference.tagPreference());
                    break; // 已找到匹配项，退出内层循环
                }
            }
        }
    }

    private record Preference(List<TagPreference> preference) {
    }

    private record TagPreference(String title, ExtractedEntityObject tagPreference) {
    }

}
