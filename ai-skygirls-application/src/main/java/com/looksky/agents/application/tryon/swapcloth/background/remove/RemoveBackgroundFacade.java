package com.looksky.agents.application.tryon.swapcloth.background.remove;

import com.looksky.agents.data.redis.config.RedisCodecFactory;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.exception.BusinessException;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.infrastructure.thread.VirtualThreadExecutor;
import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

/**
 * 抠图服务门面，提供自动容错功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RemoveBackgroundFacade {

    private static final String SERVICE_STATUS_KEY = "tryon:removebg:service:status";
    // 服务保底机制：重置所有服务的标记
    private static final String RESET_ALL_FLAG_KEY = "tryon:removebg:reset:all:flag";
    // 保底机制重置冷却时间
    private static final Duration RESET_ALL_COOLDOWN = Duration.ofMinutes(5);
    // 整个方法的全局超时时间（60秒）
    private static final Duration GLOBAL_TIMEOUT = Duration.ofSeconds(60);
    // 默认优先级（如果从服务实现获取失败）
    private static final int DEFAULT_PRIORITY = Integer.MAX_VALUE;
    // 失败阈值
    private static final int FAILURE_THRESHOLD = 3;

    private final RedissonClient redissonClient;
    private final RedisCodecFactory redisCodecFactory;
    
    // 所有可用的服务实现
    private final List<IRemoveBackgroundService> serviceImpls;
    
    // 优先级排序的服务实现映射
    private Map<String, IRemoveBackgroundService> versionToServiceMap;

    @PostConstruct
    public void init() {
        // 创建版本到服务的映射
        versionToServiceMap = serviceImpls.stream().collect(Collectors.toMap(IRemoveBackgroundService::getVersion, service -> service));
        
        // 初始化服务状态
        initServiceStatus();
    }

    /**
     * 获取服务的优先级
     */
    private int getServicePriority(IRemoveBackgroundService service) {
        Order order = AnnotationUtils.findAnnotation(service.getClass(), Order.class);
        return order != null ? order.value() : DEFAULT_PRIORITY;
    }
    
    /**
     * 获取状态映射
     */
    private RMap<String, RemoveBackgroundServiceStatus> getStatusMap() {
        return redissonClient.getMap(SERVICE_STATUS_KEY, redisCodecFactory.createMapCodec(String.class, RemoveBackgroundServiceStatus.class));
    }

    /**
     * 初始化服务状态
     */
    private void initServiceStatus() {
        RMap<String, RemoveBackgroundServiceStatus> statusMap = getStatusMap();
        
        versionToServiceMap.forEach((version, service) -> {
            if (!statusMap.containsKey(version)) {
                RemoveBackgroundServiceStatus status = new RemoveBackgroundServiceStatus();
                // 从服务实现类获取优先级
                status.updatePriority(getServicePriority(service));
                // 从服务实现类获取超时时间
                status.updateTimeout(service.getTimeout());
                statusMap.put(version, status);
                log.info("初始化服务 {} 状态: 优先级={}, 超时={}ms", version, status.getPriority(), status.getTimeoutMillis());
            }
        });
    }

    /**
     * 重置所有服务的状态为可用
     */
    private boolean resetAllServicesIfNeeded() {
        // 获取上次重置的时间戳
        RBucket<Long> resetTimeBucket = redissonClient.getBucket(RESET_ALL_FLAG_KEY);
        Long lastResetTime = resetTimeBucket.get();
        long currentTime = System.currentTimeMillis();
        
        // 如果上次重置时间为空，或者已经超过冷却时间，则执行重置
        if (lastResetTime == null || (currentTime - lastResetTime) > RESET_ALL_COOLDOWN.toMillis()) {
            RMap<String, RemoveBackgroundServiceStatus> statusMap = getStatusMap();
            
            // 记录所有服务状态
            log.warn("执行全部服务重置保底机制：所有抠图服务不可用，将尝试重置所有服务状态");
            
            // 重置所有服务状态
            for (String version : versionToServiceMap.keySet()) {
                RemoveBackgroundServiceStatus status = statusMap.get(version);
                if (status != null) {
                    status.markAsAvailable();
                    // 设置一个较低的失败计数阈值，一次失败就再次标记为不可用
                    status.setFailureCount(FAILURE_THRESHOLD - 1);
                    statusMap.put(version, status);
                    log.info("重置服务 {} 为可用状态", version);
                }
            }
            
            // 更新重置时间戳
            resetTimeBucket.set(currentTime);
            return true;
        }
        
        // 在冷却期内，无法再次重置
        log.warn("所有服务都不可用，但上次全局重置时间距今不足 {} 分钟，暂不执行全局重置", RESET_ALL_COOLDOWN.toMinutes());
        return false;
    }
    
    /**
     * 对外提供的抠图服务入口，自动容错，并有60秒全局超时限制
     */
    @CollectEvent
    public String removeBackground(String imageUrl) {
        return CompletableFuture.supplyAsync(() -> {
                try {
                    return executeWithAutoFailover(imageUrl);
                } catch (Exception e) {
                    log.error("执行抠图过程中发生异常", e);
                    throw new BusinessException("执行抠图失败: " + e.getMessage());
                }
            }, VirtualThreadExecutor.get())
            .orTimeout(GLOBAL_TIMEOUT.toMillis(), TimeUnit.MILLISECONDS)
            .exceptionally(throwable -> {
                Throwable cause = throwable instanceof ExecutionException ? throwable.getCause() : throwable;

                if (cause instanceof TimeoutException) {
                    log.error("抠图全局执行超时，已超过{}秒", GLOBAL_TIMEOUT.getSeconds());
                    throw new BusinessException("抠图请求处理超时，请稍后重试");
                } else {
                    log.error("抠图执行过程中发生未预期异常", cause);
                    throw new BusinessException("抠图处理失败: " + cause.getMessage());
                }
            })
            .join();
    }

    private List<String> getAvailableVersionsTryResetting(RMap<String, RemoveBackgroundServiceStatus> statusMap) {
        // 获取所有可用服务版本，按优先级排序
        List<String> availableVersions = getAvailableVersions(statusMap);

        // 如果没有可用服务，尝试重置所有服务
        if (availableVersions.isEmpty()) {
            boolean reset = resetAllServicesIfNeeded();
            if (reset) {
                // 重新获取可用服务
                availableVersions = getAvailableVersions(statusMap);
            }

            // 如果重置后仍然没有可用服务，返回失败
            if (availableVersions.isEmpty()) {
                log.error("所有抠图服务都不可用，且无法执行全局重置");
                throw new BusinessException("所有抠图服务都不可用");
            }
        }
        return availableVersions;
    }
    
    /**
     * 执行抠图的核心逻辑，带自动容错功能
     */
    private String executeWithAutoFailover(String imageUrl) {
        // 获取服务状态
        RMap<String, RemoveBackgroundServiceStatus> statusMap = getStatusMap();
        
        // 获取所有可用服务版本并尝试重置，按优先级排序
        List<String> availableVersions = getAvailableVersionsTryResetting(statusMap);
        
        log.info("尝试按优先级调用服务: {}", String.join("->", availableVersions));
        
        // 依次尝试调用服务，直到成功或全部失败
        for (String version : availableVersions) {
            IRemoveBackgroundService service = versionToServiceMap.get(version);
            RemoveBackgroundServiceStatus status = statusMap.get(version);
            
            if (service == null || status == null) {
                continue;
            }

            try {
                // 使用异步调用，设置超时
                String result = executeWithTimeout(service, imageUrl, status.getTimeoutMillis());

                if (result != null && !imageUrl.equals(result)) {
                    // 调用成功，重置失败计数
                    if (status.getFailureCount() > 0) {
                        status.resetFailureCount();
                        statusMap.put(version, status);
                    }
                    log.info("服务 {} 调用成功", version);
                    return result;
                }

                // 返回空结果或原始图片，视为失败
                handleServiceFailure(statusMap, version, status);
                log.warn("服务 {} 返回原始图片或空结果，尝试下一个服务", version);

            } catch (Exception e) {
                // 处理异常，标记失败
                handleServiceFailure(statusMap, version, status);
                if (e.getCause() instanceof TimeoutException) {
                    log.error("服务 {} 调用超时 ({}ms)，尝试下一个服务", version, status.getTimeoutMillis());
                } else {
                    log.error("服务 {} 调用异常: {}，尝试下一个服务", version, e.getMessage());
                }
            }
        }
        
        log.error("所有可用服务调用失败，返回原始图片");
        return imageUrl; // 所有服务失败，返回原始图片
    }
    
    /**
     * 获取所有可用的服务版本列表，按优先级排序
     */
    private List<String> getAvailableVersions(RMap<String, RemoveBackgroundServiceStatus> statusMap) {
        return versionToServiceMap.keySet().stream()
            .filter(version -> Optional.ofNullable(statusMap.get(version))
                .map(RemoveBackgroundServiceStatus::isAvailable)
                .orElse(false))
            .sorted(Comparator.comparingInt(v -> Optional.ofNullable(statusMap.get(v))
                .map(RemoveBackgroundServiceStatus::getPriority)
                .orElse(DEFAULT_PRIORITY)))
            .collect(Collectors.toCollection(ArrayList::new));
    }

    /**
     * 在限定时间内执行服务调用
     */
    private String executeWithTimeout(IRemoveBackgroundService service, String imageUrl, long timeoutMillis) {
        VirtualCompletableFuture<String> future = VirtualCompletableFuture.supplyAsync(() -> service.removeBackground(imageUrl));
        // 使用服务自己的异步实现
        return future.orTimeout(timeoutMillis, TimeUnit.MILLISECONDS)
                .join();
    }
    
    /**
     * 处理服务调用失败
     */
    private void handleServiceFailure(RMap<String, RemoveBackgroundServiceStatus> statusMap, 
                                     String version, 
                                     RemoveBackgroundServiceStatus status) {
        // 记录失败
        status.recordFailure();
        
        // 如果达到失败阈值，标记为不可用
        if (status.getFailureCount() >= FAILURE_THRESHOLD) {
            status.markAsUnavailable();
            log.warn("服务 {} 已达到失败阈值 {}，标记为不可用", version, FAILURE_THRESHOLD);
        }
        
        // 更新状态
        statusMap.put(version, status);
    }
} 