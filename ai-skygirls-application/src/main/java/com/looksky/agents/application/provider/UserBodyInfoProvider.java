package com.looksky.agents.application.provider;

import cn.hutool.core.lang.Assert;
import com.graecove.common.ApiResp;
import com.looksky.agents.common.utils.SignUtils;
import com.looksky.agents.data.client.business.LookSkyClient;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.exception.BusinessException;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.skygirls.biz.agent.dto.BodyMeasurementResp;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UserBodyInfoProvider implements DataProvider {
    private final LookSkyClient lookSkyClient;

    @Value("${looksky.signature}")
    private String signature;

    @Override
    public void getData() {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());

        String userId = requestInput.getUserId();

        String sing = SignUtils.generateExpectedSign(userId, signature);

        ApiResp<BodyMeasurementResp> response = lookSkyClient.getBodyMeasurement(userId, sing, userId);

        Assert.isTrue(response.getSuccess(), () -> new BusinessException("获取用户信息失败"));

        // todo 合并个人中心数据
        BodyMeasurementResp bodyMeasurementResp = response.getData();

        if (bodyMeasurementResp == null) {
            bodyMeasurementResp = new BodyMeasurementResp();
        }

        Context.put(Context.Name.EXTRACTED_BODY_DATA.getName(), bodyMeasurementResp);

    }

    @Override
    public boolean supports(String key) {
        return key.equals(Context.Name.EXTRACTED_BODY_DATA.getName());
    }



} 