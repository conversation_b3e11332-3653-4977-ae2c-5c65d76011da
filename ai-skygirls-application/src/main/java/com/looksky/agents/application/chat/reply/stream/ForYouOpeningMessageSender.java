package com.looksky.agents.application.chat.reply.stream;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.looksky.agents.application.chat.reply.AbstractMessageSender;
import com.looksky.agents.data.client.business.DataHubClient;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.common.enums.EventNameEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.common.enums.PageEnum;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.datahub.message.dto.MsgReq;
import com.looksky.agents.sdk.datahub.message.enums.DirectionEnum;
import com.skygirls.biz.im.model.MessageModel;
import com.skygirls.biz.report.IosUserInfoDto;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.client.codec.StringCodec;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Order(1)
@Component
public class ForYouOpeningMessageSender extends AbstractMessageSender {

    @Resource
    private DataHubClient dataHubClient;

    private static final String DATE_FORMAT = "yyyy-MM-dd";

    private static final String FOR_YOU_PREFERENCE_SETTINGS = "Complete forYou preference settings";


    @Override
    protected boolean supports(PromptModel strategy) {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());
        return PageEnum.FOR_YOU.getName().equals(requestInput.getPage()) && EnterPointEnum.HOME == requestInput.getEnterPointEnum();
    }


    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        AgentMessageResp message = initMessage(EventTypeEnum.TEXT.getType());

        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());
        // 检查并获取是否是刚填写完偏好, 如果是的话, 那么返回需要回复的内容
        String completeSettings = checkIsCompleteSettings(input);
        // 如果获取到了内容, 那直接返回即可
        if (CharSequenceUtil.isNotBlank(completeSettings)) {
            sendContent(message, EventTypeEnum.TEXT.getType(), completeSettings);
            return;
        }

        IosUserInfoDto userInfo = Context.get(Context.Name.USER_INFO.getName());

        DateTime parse = DateUtil.parse(input.getDate());
        String format = DateUtil.format(parse, DATE_FORMAT);

        int days = 0;
        try {
            days = calculateDays(userInfo, 0, format);
        } catch (Exception e) {
            log.error("获取用户历史消息失败", e);
        }


        String redisKey;

        if (days <= 1) {
            // 返回第一天的文案
            redisKey = RedisKeyConstants.FOR_YOU_FIRST_DAY_OPENING;
        } else {
            // 返回第二天的文案
             redisKey = RedisKeyConstants.FOR_YOU_SECOND_DAY_OPENING;
        }

        RBucket<String> bucket = redissonClient.getBucket(redisKey, StringCodec.INSTANCE);

        String replyStr = bucket.get();


        replyStr = fillVariable(replyStr, userInfo);

        sendContent(message, EventTypeEnum.TEXT.getType(), replyStr);

    }

    private String checkIsCompleteSettings(RequestInputDTO input) {
        String content = input.getEvent().getContent();
        if (CharSequenceUtil.isEmpty(content) || !FOR_YOU_PREFERENCE_SETTINGS.equals(content)) {
            return null;
        }

        RBucket<String> bucket = redissonClient.getBucket(RedisKeyConstants.FOR_YOU_COMPLETE_SETTINGS, StringCodec.INSTANCE);
        return Optional.ofNullable(bucket).map(RBucket::get).filter(StrUtil::isNotBlank).orElse(null);
    }


    private int calculateDays(IosUserInfoDto userInfo, int pageNo, String date) {
        Page<MessageModel> messageModelPage = getMessageModelPage(userInfo, pageNo);

        if (messageModelPage == null || messageModelPage.getTotal() == 0) {
            return 0;
        }

        if (messageModelPage.getTotal() == 1) {
            return 1;
        }

        List<MessageModel> messageModels = messageModelPage.getRecords();
        Set<String> days = new HashSet<>();
        days.add(date);

        for (MessageModel messageModel : messageModels) {
            String format = DateUtil.format(messageModel.getCreateTime(), DATE_FORMAT);
            days.add(format);
        }

        // 如果 size 大于 1, 说明不是第一天, 直接返回
        if (days.size() > 1) {
            return days.size();
        }

        // 如果是 1, 并且 total 等于 size, 说明已经是全部数据, 那么直接返回
        if (messageModelPage.getTotal() == messageModelPage.getSize()) {
            return days.size();
        }

        long pages = messageModelPage.getPages();

        if (++pageNo <= pages) {
            return calculateDays(userInfo, pageNo, date);
        }

        return days.size();
    }



    private Page<MessageModel> getMessageModelPage(IosUserInfoDto userInfo, int pageNo) {
        MsgReq msgReq = new MsgReq()
            .setDirection(DirectionEnum.SEND.getName())
            .setUserId(userInfo.getAppUser().getUserId())
            .setEventName(EventNameEnum.OPENING.getValue())
            .setPage(PageEnum.FOR_YOU.getName())
            .setPageNo(pageNo)
            .setEnterPoint(EnterPointEnum.HOME.getValue())
            .setPageSize(10);
        return dataHubClient.messageHistory(msgReq).getData();
    }



    /**
     * 填充配置中的变量
     *
     * @param replyStr
     * @param userInfo
     * @return
     */
    private String fillVariable(String replyStr, IosUserInfoDto userInfo) {
        PromptTemplate template = new PromptTemplate(replyStr);
        Prompt prompt = template.create(Map.of("user_name", userInfo.getBasicUser().getUsername()));
        return prompt.getContents();
    }



}