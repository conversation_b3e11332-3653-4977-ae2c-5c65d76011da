package com.looksky.agents.application.userSimilar;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.sdk.common.dto.CommonModelRequestDTO;
import com.looksky.agents.sdk.recommend.star.dto.UserStarBodySimilarDTO;
import com.looksky.agents.sdk.recommend.star.dto.UserStarBodySimilarParam;
import com.looksky.agents.sdk.recommend.star.dto.UserStarBodySimilarResultDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @ClassName UserStarSimilarService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/15 上午10:33
 * @Version 1.0
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class UserStarSimilarService {

    private final CommonRequestService commonRequestService;
    private final ObjectMapper objectMapper;

    private static final String USER_STAR_BODY_SIMILAR_PROMPT = "Body_Type_Similarity";

    public UserStarBodySimilarResultDTO userStarBodySimilar(UserStarBodySimilarParam similarReq) {
        log.info("用户和明星身形相似度计算, 请求参数: {}", JSONUtil.toJsonStr(similarReq));
        List<CompletableFuture<UserStarBodySimilarResultDTO.SimilarResult>> futures = new ArrayList<>();
        UserStarBodySimilarParam.BodyComparisonData userBodyComparisonData = similarReq.getUserBodyComparisonData();

        for (UserStarBodySimilarParam.BodyComparisonData starBodyComparisonData : similarReq.getStarBodyComparisonData()) {
            CompletableFuture<UserStarBodySimilarResultDTO.SimilarResult> future = CompletableFuture.supplyAsync(() -> {
                        if (starBodyComparisonData.getImage() == null) {
                            return null;
                        }

                        if (userBodyComparisonData.getImage() == null) {
                            return null;
                        }

                        try {
                            return commonRequestService.commonRequestModelWithText(
                                    CommonModelRequestDTO.builder()
                                            .userId(starBodyComparisonData.getUserId())
                                            .strategyName(USER_STAR_BODY_SIMILAR_PROMPT)
                                            .variable(BeanUtil.beanToMap(UserStarBodySimilarDTO.builder()
                                                    .userBodyComparisonData(userBodyComparisonData)
                                                    .starBodyComparisonData(starBodyComparisonData)
                                                    .build()))
                                            .images(List.of(userBodyComparisonData.getImage(), starBodyComparisonData.getImage()))
                                            .build()
                            );
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }).handle((result, throwable) -> extractSimilarityPercentage(result, starBodyComparisonData, throwable));

            futures.add(future);
        }

        // 等待所有任务完成，不会因为单个任务失败而中断
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).exceptionally(throwable -> {
            log.error("求用户与明星相似度的并行处理过程中发生错误", throwable);
            return null;
        }).join();

        // 收集所有结果
        List<UserStarBodySimilarResultDTO.SimilarResult> resultList = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

        return UserStarBodySimilarResultDTO.builder()
                .similarResults(resultList)
                .build();
    }

    private UserStarBodySimilarResultDTO.SimilarResult extractSimilarityPercentage(String result, UserStarBodySimilarParam.BodyComparisonData starBodyComparisonData, Throwable throwable) {
        if (throwable != null || result == null) {
            log.error("处理用户 {} 的相似度请求失败", starBodyComparisonData.getUserId(), throwable);
            return UserStarBodySimilarResultDTO.SimilarResult.builder()
                    .userId(starBodyComparisonData.getUserId())
                    .similarityPercentage(0)
                    .build();
        }

        JsonNode jsonNode;
        int similarityPercentage = 0;
        try {
            jsonNode = objectMapper.readTree(result);
            similarityPercentage = jsonNode.get("similarityPercentage").asInt();
        } catch (JsonProcessingException e) {
            log.error("提取相似度百分比失败, 模型响应结果: {}, 出错明星: {}", result, JSONUtil.toJsonStr(starBodyComparisonData), e);
        }
        return UserStarBodySimilarResultDTO.SimilarResult.builder().userId(starBodyComparisonData.getUserId()).similarityPercentage(similarityPercentage).build();
    }
}