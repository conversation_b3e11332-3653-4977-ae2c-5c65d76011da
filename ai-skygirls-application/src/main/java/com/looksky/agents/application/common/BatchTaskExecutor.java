package com.looksky.agents.application.common;

import com.looksky.agents.infrastructure.thread.VirtualThreadExecutor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class BatchTaskExecutor {
    private final CommonRequestService commonRequestService;

    public record Task<T>(
            String name,
            Callable<T> callable
    ) implements Callable<T> {
        @Override
        public T call() throws Exception {
            return callable.call();
        }
    }

    public record Result<T>(
            String name,
            T result
    ) {}

    public Map<String, String> batchExecuteStrategy(List<String> strategyList) {
        List<Task<String>> tasks = strategyList.stream()
                .map(strategy -> new Task<>(
                        strategy,
                        () -> commonRequestService.commonExecuteStrategy(strategy)
                ))
                .toList();

        try {
            return execute(tasks);
        } catch (Exception e) {
            throw new RuntimeException("执行策略失败", e);
        }
    }

    public Map<String, String> execute(List<Task<String>> tasks) {
        try {
            List<Result<String>> results = VirtualThreadExecutor.executeWithResults(
                    tasks.stream()
                            .map(task -> (Callable<Result<String>>) () ->
                                    new Result<>(task.name(), task.call()))
                            .toList()
            );
            return results.stream()
                    .collect(Collectors.toMap(
                            Result::name,
                            Result::result
                    ));
        } catch (Exception e) {
            throw new RuntimeException("执行任务失败", e);
        }
    }
} 