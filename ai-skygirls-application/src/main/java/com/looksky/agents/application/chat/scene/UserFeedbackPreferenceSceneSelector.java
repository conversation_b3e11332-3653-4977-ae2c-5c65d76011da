package com.looksky.agents.application.chat.scene;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.looksky.agents.application.chat.prompt.selector.NextPromptSelector;
import com.looksky.agents.application.prompt.PromptBusinessService;
import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.common.enums.EventNameEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.common.enums.IntentTypeEnum;
import com.looksky.agents.sdk.agent.conversation.ExternInfo;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.search.dto.CategoryFeedBackDTO;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.preference.CategoryAndTagPreference;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.infrastructure.context.Context;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Order(1)
@Component
@RequiredArgsConstructor
public class UserFeedbackPreferenceSceneSelector extends BaseSceneSelector {

    private final NextPromptSelector nextPromptSelector;
    private final PromptBusinessService promptBusinessService;
    private final TagSystemTableService tagSystemTableService;

    @Override
    public boolean support(String eventName, EnterPointEnum enterPoint, String page) {
        return EventNameEnum.FEEDBACK_PREFERENCE.getValue().equals(eventName);
    }

    @Override
    protected SceneResult getSceneAndExternInfo(RequestInputDTO requestInput) {
        String feedbackType = requestInput.getEventDict().getFeedbackType();
        if (EventTypeEnum.CONFIRM_CATEGORY.getType().equals(feedbackType)) {
            Object feedbackPreference = requestInput.getEventDict().getFeedbackPreference();
            if (feedbackPreference != null) {

                CategoryFeedBackDTO categorySelectorResp = JSONObject.parseObject(JSONObject.toJSONString(feedbackPreference), CategoryFeedBackDTO.class);
                List<String> category = categorySelectorResp.getCategory();
                // 获取偏好数据
                ExtractedEntityObject preference = Context.get(Context.Name.PREFERENCE.getName());
                // 设置当前品类为用户反馈的品类
                preference.setCurrentCategories(new HashSet<>(category));

                if (category.getFirst() != null) {
                    String firstCategory = tagSystemTableService.getFirstCategoryBySubCategory(category.getFirst());
                    CategoryAndTagPreference categoryAndTagPreference = preference.getCategoryAndTagPreference();
                    if (categoryAndTagPreference != null && categoryAndTagPreference.getCategories() != null) {
                        categoryAndTagPreference.getCategories().stream().filter( c -> c.getCategory().equals(firstCategory)).findFirst().ifPresent(
                                c -> c.setCategory(category.getFirst())
                        );
                    }
                }
                Context.put(Context.Name.PREFERENCE.getName(), preference);
            }
        } else if (EventTypeEnum.CONFIRM_TAG.getType().equals(feedbackType)) {
            Object feedbackPreference = requestInput.getEventDict().getFeedbackPreference();
            if (feedbackPreference != null) {
                CategoryFeedBackDTO categorySelectorResp = JSONObject.parseObject(JSONObject.toJSONString(feedbackPreference), CategoryFeedBackDTO.class);
                // 获取偏好数据
                ExtractedEntityObject preference = Context.get(Context.Name.PREFERENCE.getName());

                Map<String, List<String>> tag = categorySelectorResp.getTag();

                Set<String> tags = tag.keySet();
                Set<String> currentCategories = preference.getCurrentCategories();

                if (!ObjectUtil.isEmpty(tags) && !ObjectUtil.isEmpty(currentCategories)) {
                    preference.getCategoryAndTagPreference().getCategories().stream().filter(c -> currentCategories.contains(c.getCategory())).forEach(c -> {
                        HashSet<String> allTags = new HashSet<>(c.getLabelTypeList());
                        allTags.addAll(tags);
                        c.setLabelTypeList(allTags);
                    });
                    Context.put(Context.Name.PREFERENCE.getName(), preference);
                }
            }
        }
        String promptName = nextPromptSelector.select(IntentTypeEnum.SEARCH_CLOTH);
        PromptModel promptModel = promptBusinessService.getPromptModelByNameWithMetaData(promptName);
        return new SceneResult(promptModel, new ExternInfo());

    }
}