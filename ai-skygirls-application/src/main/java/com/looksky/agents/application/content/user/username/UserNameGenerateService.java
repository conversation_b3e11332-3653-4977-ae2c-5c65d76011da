package com.looksky.agents.application.content.user.username;

import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.exception.BusinessException;
import jakarta.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.SetUtils;
import org.redisson.api.RLock;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

/**
 * 用户名生成服务
 *
 * <AUTHOR>
 * @since 1.1.14
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class UserNameGenerateService {
    private final CommonRequestService commonRequestService;
    private final RedissonClient redissonClient;

    private final static String PROMPT = "generate_user_name";
    private final static String USER_NAME_KEY = "content:username:pool";
    private final static int MIN_NAME_POOL_SIZE = 100;
    private final static String USER_NAME_USED_KEY = "content:username:used";
    private final static String USER_NAME_LOCK_KEY = "content:username:lock";
    private final static long LOCK_TIME = 60;

    // 将RSet对象作为类属性
    private RSet<String> namePool;
    private RSet<String> usedNames;

    @PostConstruct
    public void init() {
        namePool = redissonClient.getSet(USER_NAME_KEY);
        usedNames = redissonClient.getSet(USER_NAME_USED_KEY);
        checkNamePool();
    }

    /**
     * 生成用户名
     *
     * @return username list
     */
    public Set<String> generateUserName() {
        String result = commonRequestService.commonExecuteStrategy(PROMPT);
        return Arrays.stream(result.split("\\R")).map(String::trim).collect(Collectors.toCollection(HashSet::new));
    }

    public Set<String> generateUserNameToPool() {
        Set<String> newNames = generateUserName();
        // 创建临时集合用于差集操作
        Set<String> unusedNewNames = new HashSet<>(newNames);
        // 移除所有已使用的名称（差集操作）
        unusedNewNames.removeAll(usedNames.readAll());

        // 批量添加未使用的新名称到名称池
        if (!unusedNewNames.isEmpty()) {
            namePool.addAll(unusedNewNames);
            log.info("成功添加{}个新用户名到池中", unusedNewNames.size());
            return unusedNewNames;
        }
        return SetUtils.emptySet();
    }

    /**
     * 随机获取一个 name
     *
     * @return name
     */
    @CollectEvent
    public String randomUserName() {
        checkNamePool();

        // 随机获取一个名称
        String randomName = namePool.random();

        // 从池中删除该名称，并添加到已使用集合中
        namePool.remove(randomName);
        usedNames.add(randomName);

        return randomName;
    }


    /**
     * 检查 name 池是否有足够的 name
     */
    public void checkNamePool() {
        // 当名称池小于100个时，获取锁并生成新的用户名
        if (namePool.size() < MIN_NAME_POOL_SIZE) {
            RLock lock = redissonClient.getLock(USER_NAME_LOCK_KEY);
            boolean locked = false;
            try {
                // 尝试获取锁，最多等待3秒，10秒后自动释放
                locked = lock.tryLock(LOCK_TIME, LOCK_TIME, TimeUnit.SECONDS);
                if (locked) {
                    // 再次检查大小，因为可能在等待锁的过程中已有其他线程填充了池
                    if (namePool.size() < MIN_NAME_POOL_SIZE) {
                        log.info("名称池数量不足，开始生成新用户名");
                        generateUserNameToPool();
                    }
                } else {
                    log.warn("未能获取到名称池锁，继续使用当前名称池");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("获取名称池锁时被中断", e);
            } catch (Exception e) {
                log.error("填充名称池过程中发生异常", e);
            } finally {
                // 仅在成功获取锁的情况下解锁
                if (locked) {
                    lock.unlock();
                }
            }
        }

        // 如果池中仍然没有名称，抛出异常
        if (namePool.isEmpty()) {
            throw new BusinessException("userName Poll is Empty");
        }
    }
}
