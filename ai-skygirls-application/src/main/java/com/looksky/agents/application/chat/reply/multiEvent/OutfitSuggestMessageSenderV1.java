package com.looksky.agents.application.chat.reply.multiEvent;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.application.chat.recommend.PreferenceToSearchConverter;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.data.grpc.SearchGrpcClient;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import com.looksky.agents.sdk.agent.common.dto.CurrentCategoryTagDTO;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.search.bo.OutfitSuggestionList;
import com.looksky.agents.sdk.agent.search.bo.RecommendOutfitBO;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import com.looksky.agents.sdk.recommend.search.dto.response.SearchResponseDTO;
import jakarta.annotation.Resource;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @ClassName CategorySelectorMessageSender
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 下午10:29
 * @Version 1.0
 **/
@Slf4j
@Order(1)
@Component
@ApiVersion(platform = ApiVersion.PlatformType.ANDROID, version = "1.1.0")
public class OutfitSuggestMessageSenderV1 extends MultiEventMessageSender {

    @Resource
    private SearchGrpcClient girlsAgentSearchGrpcClient;

    @Resource
    private TagSystemTableService tagSystemTableService;

    private static final String RETURN_MESSAGE = "The system has pushed the product, the following are the product details:";


    @Override
    protected boolean supports(PromptModel strategy) {
        // 生成搜索过程
        return PromptNameEnum.INQUIRE_USER_SOLUTION.getName().equals(strategy.getName());
    }

    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        @SuppressWarnings("unchecked")
        ArrayDeque<String> returnEvents = (ArrayDeque<String>) hashMap.get("returnEvents");

        ArrayDeque<Object> returnContents = generateReturnContents(strategy);

        if (returnContents.isEmpty()) {
            log.warn("推荐方案为空, returnContents: {}", JSONUtil.toJsonStr(returnContents));
            // 执行兜底回复
            String finalMessageStr = sendStreamMessage("query_no_products", MapUtil.empty()).block();
            extraMessage(EventTypeEnum.TEXT.getType(), finalMessageStr, hashMap);
            return;
        }

        while (!returnEvents.isEmpty()) {
            String returnEvent = returnEvents.poll();
            Object returnContent = returnContents.poll();
            AgentMessageResp agentMessageResp = initMessage(returnEvent);
            extraMessage(returnEvent, RETURN_MESSAGE + returnContent, hashMap);
            sendContent(agentMessageResp, returnEvent, returnContent);
        }
    }

    @Override
    protected ArrayDeque<Object> generateReturnContents(PromptModel strategy) {
        sendContent(initMessage(EventTypeEnum.WAIT_SOLUTION.getType()), EventTypeEnum.WAIT_SOLUTION.getType(), "");

        OutfitSuggestionList outfitSuggestionList = buildOutfitTitleAndTag();
        filterOutfitSuggestionList(outfitSuggestionList);
        List<RecommendOutfitDTO> recommendOutfitDTOS = generatePositiveWords(outfitSuggestionList);

        try {
            recommendOutfitDTOS = recommendOutfitProduct(recommendOutfitDTOS);
        } catch (Exception e) {
            log.error("推荐方案失败", e);
            return new ArrayDeque<>();
        }


        if (ObjectUtil.isEmpty(recommendOutfitDTOS) || ObjectUtil.isEmpty(recommendOutfitDTOS.getFirst().getSkcIds())) {
            return new ArrayDeque<>();
        }

        ArrayDeque<Object> deque = new ArrayDeque<>();
        deque.offer(recommendOutfitDTOS);


        ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());
        conversationStatus.setRecommendProduct(true);

        return deque;
    }

    /**
     * 限制最多 3 条
     * @param outfitSuggestionList
     */
    private void filterOutfitSuggestionList(OutfitSuggestionList outfitSuggestionList) {
        List<RecommendOutfitBO> outfitSuggestions = outfitSuggestionList.getOutfitSuggestions();
        if (ObjectUtil.isNotEmpty(outfitSuggestions) && outfitSuggestions.size() > 3) {
            outfitSuggestions.subList(3, outfitSuggestions.size()).clear();
        }
    }

    private List<RecommendOutfitDTO> recommendOutfitProduct(List<RecommendOutfitDTO> recommendOutfits) {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());

        List<SearchRequestDTO.GirlsStrategyTerm> searchTerms = recommendOutfits.stream().map(this::convertToVectorQuery).toList();

        SearchRequestDTO searchRequestDTO = SearchRequestDTO.builder()
                .userId(requestInput.getUserId())
                .season(requestInput.getSeason())
                .strategyTerms(searchTerms)
                .build();

        SearchResponseDTO searchResult = girlsAgentSearchGrpcClient.search(searchRequestDTO);

        if (ObjectUtil.isEmpty(searchResult) || ObjectUtil.isEmpty(searchResult.getStrategyResponses())) {
            return recommendOutfits;
        }

        List<SearchResponseDTO.StrategyResponse> strategyResponses = searchResult.getStrategyResponses().stream().toList();

        recommendOutfits.forEach(recommendOutfitDTO -> {
            String uuid = recommendOutfitDTO.getUuid();
            strategyResponses.stream().filter(strategyResponse -> strategyResponse.getSearchStrategy().equals(uuid)).findFirst().ifPresent(strategyResponse -> {
                recommendOutfitDTO.setSkcIds(strategyResponse.getItems().stream().map(ItemDTO::getItemId).toList());
            });
        });

        // 过滤掉没有 skcId 的推荐
        return recommendOutfits.stream().filter(recommendOutfitDTO -> !ObjectUtil.isEmpty(recommendOutfitDTO.getSkcIds())).toList();


    }

    private SearchRequestDTO.GirlsStrategyTerm convertToVectorQuery(RecommendOutfitDTO recommendOutfitDTO) {
        Set<String> category = tagSystemTableService.convertToSubCategory(Set.of(recommendOutfitDTO.getCategory()));
        SearchRequestDTO.GirlsStrategyTerm.GirlsStrategyTermBuilder builder =  SearchRequestDTO.GirlsStrategyTerm.builder();

        ArrayList<SearchRequestDTO.VectorQuery> step1 = new ArrayList<>();
        ArrayList<SearchRequestDTO.VectorQuery> step2 = new ArrayList<>();

        Set<String> subCategorySet = tagSystemTableService.convertToSubCategory(category);

        SearchRequestDTO.SearchTerm searchTerm =
            SearchRequestDTO.SearchTerm.builder()
                .categories(new ArrayList<>(subCategorySet))
                .searchStrategy(recommendOutfitDTO.getUuid())
                .build();

        setTagValue(searchTerm);

        builder.searchTerm(searchTerm);


        // 构建 step1
        step1.add(SearchRequestDTO.VectorQuery.builder()
            .text(recommendOutfitDTO.getTitle())
            .weight(1)
            .build());

        builder.step1VectorQueries(step1);


        // 构建 step2. 使用 第三步的结果
        if (CharSequenceUtil.isNotBlank(recommendOutfitDTO.getOutfitSuggestion())) {
            step2.add(SearchRequestDTO.VectorQuery.builder()
                .text(recommendOutfitDTO.getTitle() + ", " + recommendOutfitDTO.getOutfitSuggestion())
                .weight(1)
                .build());
        }

        if (ObjectUtil.isNotEmpty(step2)) {
            builder.step2VectorQueries(step2);
        }

        return builder.build();
    }

    private void setTagValue(SearchRequestDTO.SearchTerm vectorQueries) {
        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());
        PreferenceToSearchConverter.convertToSearchTerm(vectorQueries, userPreference);
    }



    public OutfitSuggestionList buildOutfitTitleAndTag() {
        ExtractedEntityObject preference = Context.get(Context.Name.PREFERENCE.getName());
        Set<String> categories = preference.getCurrentCategories();
        CurrentCategoryTagDTO currentCategoryTag = CurrentCategoryTagDTO.builder().build();
        if (ObjectUtil.isEmpty(categories)) {
            currentCategoryTag.setCategory(CategoryEnum.CLOTH.getName());
        } else {
            currentCategoryTag.setCategory(categories.stream().findFirst().get());
        }

        String result = commonRequestService.commonExecuteStrategy(PromptNameEnum.OUTFIT_SUGGESTION_NEW.getName(), Map.of(Context.Name.CURRENT.name(), currentCategoryTag));

        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(result, OutfitSuggestionList.class);
        } catch (Exception e) {
            e.printStackTrace();

        }
        return new OutfitSuggestionList();
    }


    /**
     * 生成正向词
     * @param outfitSuggestionList
     * @return
     */
    private List<RecommendOutfitDTO> generatePositiveWords(OutfitSuggestionList outfitSuggestionList) {
        List<RecommendOutfitDTO> categoryAndVectorQueries = new ArrayList<>();
        for (RecommendOutfitBO suggestion : outfitSuggestionList.getOutfitSuggestions()) {
            categoryAndVectorQueries.add(RecommendOutfitDTO.builder()
                    .category(suggestion.getCategory())
                    .title(suggestion.getTitle())
                    .outfitSuggestion(suggestion.getOutfitSuggestion())
                    .uuid(IdUtil.getSnowflakeNextIdStr())
                    .build());
        }
        return categoryAndVectorQueries;
    }


    @Data
    @Builder
    static class RecommendOutfitDTO {
        private String category;
        private String positiveQuery;
        private String negativeQuery;
        private String title;
        @JsonProperty("product_id_list")
        private List<String> skcIds;
        private String uuid;
        private String outfitSuggestion;
    }

}