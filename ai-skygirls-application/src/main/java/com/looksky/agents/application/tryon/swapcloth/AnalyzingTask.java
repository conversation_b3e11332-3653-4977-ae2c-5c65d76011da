package com.looksky.agents.application.tryon.swapcloth;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.application.prompt.PromptBusinessService;
import com.looksky.agents.application.tryon.swapcloth.convertor.TryOnConvertor;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.data.redis.config.RedisCodecFactory;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.common.bo.RequestParamBO;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothParam;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothStatus;
import com.looksky.agents.sdk.tryon.swapcloth.enums.SwapClothStatusEnum;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AnalyzingTask extends SwapClothBaseService {


    @Resource
    private PromptBusinessService promptBusinessService;

    @Resource
    private CommonRequestService commonRequestService;


    private Thread analysisThread;
    private static final String ANALYZE_MODEL_IMAGE = "swap_cloth_analyze_model_image";
    private static final String ANALYZE_USER_FULL_BODY_IMAGE = "swap_cloth_analyze_user_full_body_image";
    private static final String ANALYZE_USER_FACE_IMAGE = "swap_cloth_analyze_user_face_image";

    public AnalyzingTask(RedissonClient redissonClient, RedisCodecFactory factory, TryOnConvertor tryOnConvertor, GirlsClient girlsClient) {
        super(redissonClient, factory, tryOnConvertor, girlsClient);
    }

    @PostConstruct
    public void start() {
        //获取当前环境, 如果为 dev 环境, 则不启动
        if ("dev".equals(env)) {
            log.warn("当前环境为 dev, 不启动消费者线程");
            return;
        }
        startAnalysisConsumer();
    }


    private void startAnalysisConsumer() {
        analysisThread = Thread.ofVirtual().name("分析线程").unstarted(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                SwapClothStatus status = getQueueTask(SwapClothStatusEnum.QUEUED_FOR_ANALYSIS);
                if (status != null) {
                    VirtualCompletableFuture.runAsync(() -> {
                        try {
                            ensureQueue(status, SwapClothStatusEnum.ANALYZING);
                            SwapClothStatus copy = tryOnConvertor.copy(status);
                            String imagePrompt = analyze(status);
                            removeFromQueue(copy, SwapClothStatusEnum.ANALYZING);
                            status.setPromptContent(imagePrompt);
                            ensureQueue(status, SwapClothStatusEnum.QUEUED_FOR_GENERATION);
                        } catch (Exception e) {
                            removeFromQueue(status, SwapClothStatusEnum.ANALYZING);
                            log.error("分析失败", e);
                            status.setErrorMessage(e.getMessage());
                            ensureQueue(status, SwapClothStatusEnum.FAILED);
                        }
                    }).orTimeout(5, TimeUnit.MINUTES)
                    .exceptionally(throwable -> {
                        removeFromQueue(status, SwapClothStatusEnum.ANALYZING);
                        log.error("分析任务超时, 任务:{}", JSONUtil.toJsonStr(status));
                        status.setErrorMessage("分析任务超时，执行时间超过5分钟");
                        ensureQueue(status, SwapClothStatusEnum.FAILED);
                        return null;
                    });
                } else {
                    try {
                        TimeUnit.SECONDS.sleep(2);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }

            }
        });
        analysisThread.setDaemon(true);
        analysisThread.start();
    }

    private String analyze(SwapClothStatus status) {
        SwapClothParam swapClothParam = status.getSwapClothParam();

        String height = "";
        String weight = "";
        if (CharSequenceUtil.isNotBlank(swapClothParam.getHeight())) {
            height = swapClothParam.getHeight();
        }
        if (CharSequenceUtil.isNotBlank(swapClothParam.getWeight())) {
            weight = swapClothParam.getWeight();
        }

        VirtualCompletableFuture<ModelOutput> modelPrompt = getModelPrompt(swapClothParam.getModelImage());
        VirtualCompletableFuture<String> userBodyPrompt = getUserBodyPrompt(swapClothParam.getFullBodyImage(), height, weight);
        VirtualCompletableFuture<String> userFacePrompt = getUserFacePrompt(swapClothParam.getFaceImage());
        return getImagePrompt(userBodyPrompt.join(), modelPrompt.join(), userFacePrompt.join());
    }


    private String getImagePrompt(String userBodyPrompt, ModelOutput modelPrompt, String userFacePrompt) {
        return userFacePrompt + " " + userBodyPrompt + " " + modelPrompt.getFull_description();
    }


    private VirtualCompletableFuture<ModelOutput> getModelPrompt(String modelImage) {
        return VirtualCompletableFuture.supplyAsync(() -> {
            String result = executeAnalyze(ANALYZE_MODEL_IMAGE, List.of(modelImage), Collections.emptyMap());
            return JSONUtil.toBean(result, ModelOutput.class);
        });
    }


    private VirtualCompletableFuture<String> getUserBodyPrompt(String userBodyImage, String height, String weight) {
        return VirtualCompletableFuture.supplyAsync(() -> {
            String result = executeAnalyze(ANALYZE_USER_FULL_BODY_IMAGE, List.of(userBodyImage), Map.of("height", height, "weight", weight));
            BodyOutput bean = JSONUtil.toBean(result, BodyOutput.class);

            PromptTemplate template = new PromptTemplate("(({weighttype} build 1.3)),((Body shape: {bodyshape} 1.2)) ,  with {feature}");
            Prompt prompt = template.create(BeanUtil.beanToMap(bean));
            return prompt.getContents();
        });
    }

    private VirtualCompletableFuture<String> getUserFacePrompt(String faceImage) {
        return VirtualCompletableFuture.supplyAsync(() -> {
            String result = executeAnalyze(ANALYZE_USER_FACE_IMAGE, List.of(faceImage), Collections.emptyMap());
            FaceOutput bean = JSONUtil.toBean(result, FaceOutput.class);
            PromptTemplate template = new PromptTemplate("She had {skin_tone} skin, and {face_shape} face ,with {hair} ");
            Prompt prompt = template.create(BeanUtil.beanToMap(bean));
            return prompt.getContents();
        });

    }

    private String executeAnalyze(String promptName, List<String> modelImage, Map<String, Object> map) {
        PromptModel promptModel = promptBusinessService.getPromptModelByNameWithMetaData(promptName);
        RequestParamBO requestParam = promptBusinessService.convert(promptModel, map);
        requestParam.setImageUrl(modelImage);
        return commonRequestService.commonExecuteStrategy(requestParam);
    }

    @Data
    static class ModelOutput {
        private String full_description;
        private String design_complexity;
        private String pose_complexity;
    }

    @Data
    static class BodyOutput {
        private String bodyshape;
        private String weighttype;
        private String feature;
    }

    @Data
    static class FaceOutput {
        private String skin_tone;
        private String hair;
        private String face_shape;
    }
}
