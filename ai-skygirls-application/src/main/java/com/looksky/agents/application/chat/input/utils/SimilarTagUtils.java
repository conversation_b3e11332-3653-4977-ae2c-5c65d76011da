package com.looksky.agents.application.chat.input.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * @ClassName SimilarTagUtils
 * @Description 相似标签处理
 * <AUTHOR>
 * @Date 2024/11/30 下午5:08
 * @Version 1.0
 **/
@Slf4j
public class SimilarTagUtils {
    private static final Map<String, List<String>> MAP_LABEL = new HashMap<>() {{
        put("neckline_detail", Arrays.asList("neckline_detail", "neckline_shape", "collar"));
        put("neckline_shape", List.of("neckline_detail"));
        put("collar", Arrays.asList("neckline_detail", "neckline_shape", "collar"));
        put("design", Arrays.asList("design", "trims", "other_design"));
        put("trims", Arrays.asList("design", "trims", "other_design"));
        put("other_design", Arrays.asList("design", "trims", "other_design"));
    }};

    private static final List<String> SKIRT_CATEGORIES = Arrays.asList(
            "skirt", "skort", "short_dresses", "midi_dresses",
            "maxi_dresses", "dresses", "romper", "jumpsuit", "skirt_set"
    );

    private static final List<String> PANTS_CATEGORIES = Arrays.asList(
            "pants", "shorts", "shorts_set", "pants_set"
    );

    public static List<String> normSimilarTag(String category, List<String> labelTypeList) {
        log.info("人工处理相似标签,处理前---> category: {}, labelTypeList: {}", category, labelTypeList);
        Set<String> normLabelTypeList = new HashSet<>();

        for (String labelType : labelTypeList) {
            if ("rise".equals(labelType) || "waist_fit".equals(labelType)) {
                if (SKIRT_CATEGORIES.contains(category)) {
                    normLabelTypeList.add("waist_fit");
                } else if (PANTS_CATEGORIES.contains(category)) {
                    normLabelTypeList.add("rise");
                } else {
                    normLabelTypeList.add(labelType);
                }
            } else if (MAP_LABEL.containsKey(labelType)) {
                normLabelTypeList.addAll(MAP_LABEL.get(labelType));
            } else {
                normLabelTypeList.add(labelType);
            }
        }

        log.info("人工处理相似标签,处理后---> category: {}, labelTypeList: {}", category, normLabelTypeList);

        return normLabelTypeList.stream().toList();
    }
}
