package com.looksky.agents.application.chat.reply.stream;

import com.looksky.agents.application.chat.reply.AbstractMessageSender;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import java.util.HashMap;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Order(100)
@Component
public class StreamMessageSender extends AbstractMessageSender {
    @Override
    protected boolean supports(PromptModel strategy) {
        return true;
    }

    @Override
    protected void doSend(PromptModel prompt, HashMap<String, Object> hashMap) {
        String messageStr = sendStreamMessage(prompt).block();
        extraMessage(EventTypeEnum.TEXT.getType(), messageStr, hashMap);
    }

}