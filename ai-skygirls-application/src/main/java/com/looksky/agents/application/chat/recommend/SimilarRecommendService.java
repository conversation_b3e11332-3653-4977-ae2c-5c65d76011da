package com.looksky.agents.application.chat.recommend;

import cn.hutool.core.util.ObjectUtil;
import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import com.looksky.agents.sdk.recommend.similar.dto.request.SimilarRequestDTO;
import com.looksky.agents.sdk.recommend.similar.dto.response.SimilarResponseDTO;
import com.looksky.agents.data.grpc.SimilarGrpcClient;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * @ClassName SimilarRecommendService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/10 下午4:24
 * @Version 1.0
 **/
@Component
@RequiredArgsConstructor
public class SimilarRecommendService {
    private final SimilarGrpcClient girlsAgentSimilarGrpcClient;


    public SimilarResponseDTO similarRecommend(SimilarRequestDTO similarRequestDTO) {
        return girlsAgentSimilarGrpcClient.search(similarRequestDTO);
    }

    public List<ItemDTO> similarRecommend(String skcId, String query, String userId, List<String> categories) {
        var builder = SimilarRequestDTO
                .builder()
                .userId(userId)
                .itemId(skcId);

        if (StringUtils.hasText(query)) {
            var searchTermBuilder =
                SearchRequestDTO.SearchTerm.builder()
                    .vectorQueries(List.of(SearchRequestDTO.VectorQuery.builder()
                        .weight(1)
                        .text(query)
                        .build()));

            if (ObjectUtil.isNotEmpty(categories)) {
                searchTermBuilder.categories(categories);
            }
            builder.searchTerm(searchTermBuilder.build());

        }

        SimilarResponseDTO similarResponseDTO = similarRecommend(builder.build());
        return similarResponseDTO.getItems();
    }
}
