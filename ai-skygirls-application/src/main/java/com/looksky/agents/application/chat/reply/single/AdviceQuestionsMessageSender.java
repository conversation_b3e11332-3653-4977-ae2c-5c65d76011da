package com.looksky.agents.application.chat.reply.single;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.reply.AbstractMessageSender;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.dto.AgentAdviceQuestions;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EventNameEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import java.util.Collections;
import java.util.HashMap;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Order(1)
@Component
public class AdviceQuestionsMessageSender extends AbstractMessageSender {

    @Override
    protected boolean supports(PromptModel strategy) {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());
        String promptName = requestInput.getPage() + "_" + requestInput.getEnterPointEnum().getValue() + "_" + EventNameEnum.ADVICE_QUESTIONS.getValue();
        return strategy.getName().equals(promptName);
    }


    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        String result = commonRequestService.commonExecuteStrategy(strategy, Collections.emptyMap());
        AgentAdviceQuestions questions = JSONUtil.toBean(result, AgentAdviceQuestions.class);
        sendContent(initMessage(EventTypeEnum.ADVICE_QUESTIONS.getType()), EventTypeEnum.ADVICE_QUESTIONS.getType(), questions.getAdviceQuestions());
    }


}