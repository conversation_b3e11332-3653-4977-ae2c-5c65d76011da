package com.looksky.agents.application.chat.preference.extraction.tag;

import com.looksky.agents.application.chat.preference.extraction.AbstractPreferenceExtractor;
import com.looksky.agents.application.chat.preference.extraction.SingleTagExtractService;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.data.redis.conversation.PreferenceDataService;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.preference.SetPreferenceValue;
import org.springframework.stereotype.Component;

/**
 * 品牌抽取
 *
 * @since  1.1.8
 * <AUTHOR>
 **/
@Component
public class BrandExtractor extends AbstractPreferenceExtractor<SetPreferenceValue> {

    public BrandExtractor(SingleTagExtractService singleTagExtractService, PreferenceDataService preferenceDataService, TagSystemTableService tagSystemTableService) {
        super(singleTagExtractService, preferenceDataService, tagSystemTableService);
    }

    @Override
    protected String getTagName() {
        return CategoryEnum.BRAND.getName();
    }

    @Override
    protected SetPreferenceValue parseExtractedValue(String result) {
        return SetPreferenceValue.parseJson(result);
    }

    @Override
    protected SetPreferenceValue getCurrentPreference(ExtractedEntityObject userPreference) {
        return userPreference.getBrandPreference();
    }

    @Override
    protected void updatePreference(ExtractedEntityObject userPreference, SetPreferenceValue newValue) {
        userPreference.setBrandPreference(newValue);
    }

    @Override
    protected SetPreferenceValue mergePreference(SetPreferenceValue newValue, SetPreferenceValue currentValue) {
        return SetPreferenceValue.merge(newValue, currentValue);
    }
} 