package com.looksky.agents.application.template;

import cn.hutool.core.util.ObjectUtil;
import com.looksky.agents.application.provider.DataProvider;
import com.looksky.agents.data.mysql.service.IPromptMacrosService;
import com.looksky.agents.infrastructure.context.Context;
import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import java.io.IOException;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class FreemarkerService {
    private final Configuration configuration;
    private final List<DataProvider> dataProviders;
    private static final Pattern NAMESPACE_PATTERN = Pattern.compile("\\$\\{([\\w]+)\\..*?}");
    private final StringTemplateLoader stringLoader;
    private final IPromptMacrosService promptMacrosService;
    private static final Pattern MACRO_USAGE_PATTERN = Pattern.compile("<@([\\w]+)\\s*(?:[^>]*?)/?>");
    private final Object templateLock = new Object();


    public FreemarkerService(Configuration configuration,
                             List<DataProvider> dataProviders,
                             IPromptMacrosService promptMacrosService
    ) {
        this.configuration = configuration;
        this.dataProviders = dataProviders;
        this.promptMacrosService = promptMacrosService;
        this.stringLoader = new StringTemplateLoader();

        // 设置模板加载器
        configuration.setTemplateLoader(this.stringLoader);
    }


    @SneakyThrows
    public String parseString(String templateContent, Map<String, Object> params) {
        if (!StringUtils.hasText(templateContent)) {
            return null;
        }
        Template template = getTemplate(templateContent);
        StringWriter writer = new StringWriter();
        template.process(params, writer);
        return writer.toString();
    }

    public String parseTemplate(String templateContent, Map<String, Object> params) throws TemplateException, IOException {

        if (!StringUtils.hasText(templateContent)) {
            return null;
        }

        // 1. 解析并处理所有宏定义
        Set<String> usedMacros = new HashSet<>();
        Matcher macroMatcher = MACRO_USAGE_PATTERN.matcher(templateContent);
        while (macroMatcher.find()) {
            usedMacros.add(macroMatcher.group(1));
        }

        // 3. 处理宏定义和数据提供
        StringBuilder macroDefinitions = new StringBuilder();
        for (String macroName : usedMacros) {
            // 处理宏定义
            String macroDefinition = promptMacrosService.getMacrosContent(macroName);
            if (macroDefinition != null) {
                macroDefinitions.append(macroDefinition).append("\n");
            }

            // 处理宏的数据
            processProviderData(macroName, params);
        }

        // 5. 组装完整模板并处理
        String fullTemplate = macroDefinitions + templateContent;

        // 2. 解析所有命名空间
        Set<String> namespaces = new HashSet<>();
        Matcher namespaceMatcher = NAMESPACE_PATTERN.matcher(fullTemplate);
        while (namespaceMatcher.find()) {
            namespaces.add(namespaceMatcher.group(1));
        }


        // 4. 处理命名空间数据
        for (String namespace : namespaces) {
            processProviderData(namespace, params);
        }

        HashMap<String, Object> finalData = new HashMap<>(Context.get());
        finalData.putAll(params);

        Template template = getTemplate(fullTemplate);
        StringWriter writer = new StringWriter();
        template.process(finalData, writer);
        return writer.toString();

    }

    private void processProviderData(String name, Map<String, Object> params) {
        if (!ObjectUtil.isEmpty(params.get(name)) || !ObjectUtil.isEmpty(Context.get(name))) {
            return;
        }

        for (DataProvider provider : dataProviders) {
            if (provider.supports(name)) {
                provider.getData();
                break;
            }
        }
    }

    private Template getTemplate(String templateContent) throws IOException {
        // 使用模板内容的哈希作为模板名，避免重复
        String templateName = String.valueOf(templateContent.hashCode());
        synchronized (templateLock) {
            stringLoader.putTemplate(templateName, templateContent);
            return configuration.getTemplate(templateName);
        }

    }
} 