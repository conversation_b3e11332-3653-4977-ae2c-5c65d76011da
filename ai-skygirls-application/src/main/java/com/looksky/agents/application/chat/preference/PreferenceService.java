package com.looksky.agents.application.chat.preference;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.preference.extraction.TagExtractorFactory;
import com.looksky.agents.common.utils.BeanMergeUtils;
import com.looksky.agents.data.client.service.TagSystemTableService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.trace.annotation.TraceMethod;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.preference.CategoryAndTagPreference;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.preference.SubjectivePreference;
import com.looksky.agents.sdk.agent.search.bo.EcommercePreference;
import java.util.HashSet;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName UserPreferenceService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/12 下午3:23
 * @Version 1.0
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PreferenceService {

    private final TagSystemTableService tagSystemTableService;
    private final TagExtractorFactory tagExtractorFactory;


    /**
     * 抽取用户标签偏好
     * @param currCate 当前品类和标签偏好
     */
    private void extractTagPreferences(CategoryAndTagPreference currCate) {
        if (currCate == null || ObjectUtil.isEmpty(currCate.getTags())) {
            log.info("没有需要抽取的标签偏好");
            return;
        }

        // 使用工厂统一处理所有标签的抽取
        tagExtractorFactory.extractPreferences(currCate.getTags());
    }

    @TraceMethod(description = "处理用户偏好")
    public void handleUserPreference(SubjectivePreference currSub, EcommercePreference currEco,
                                     CategoryAndTagPreference currCate) {
        // 合并偏好
        ExtractedEntityObject prevPreference = Context.get(Context.Name.PREFERENCE.getName());

        // 如果之前没有偏好，则直接将当前偏好放入上下文
        if (prevPreference == null) {
            ExtractedEntityObject mergedPreference = ExtractedEntityObject.builder()
                .subjectivePreference(currSub)
                .ecommercePreference(currEco)
                .categoryAndTagPreference(currCate)
                .currentCategories(currCate.getAllCategories())
                .build();

            Context.put(Context.Name.PREFERENCE.getName(), mergedPreference);
            extractTagPreferences(currCate);
            return;
        }

        // 如果之前有偏好，则合并当前偏好和之前偏好
        SubjectivePreference mergedSub = BeanMergeUtils.merge(new SubjectivePreference(),
            prevPreference.getSubjectivePreference(), currSub);
        EcommercePreference mergedEco =
            BeanMergeUtils.merge(new EcommercePreference(), prevPreference.getEcommercePreference(),
                currEco);

        // 处理品类
        Set<String> currentCategory = handleCurrentCategory(currCate.getAllCategories(), prevPreference.getCurrentCategories());

        CategoryAndTagPreference mergedCate =
            mergeCateTagPref(currCate, prevPreference.getCategoryAndTagPreference());

        ExtractedEntityObject mergedPreference = ExtractedEntityObject.builder()
            .subjectivePreference(mergedSub)
            .ecommercePreference(mergedEco)
            .categoryAndTagPreference(mergedCate)
            .currentCategories(currentCategory)
            .pricePreference(prevPreference.getPricePreference())
            .build();
        log.info("合并后的偏好: {}", JSONUtil.toJsonStr(mergedPreference));
        Context.put(Context.Name.PREFERENCE.getName(), mergedPreference);

        handleCategoryChange(mergedPreference.getCurrentCategories(),
            prevPreference.getCurrentCategories());

        extractTagPreferences(currCate);
    }

    private Set<String> handleCurrentCategory(Set<String> curr,
                                              Set<String> prev) {

        if (ObjectUtil.isEmpty(prev)) {
            return curr;
        }

        if (ObjectUtil.isEmpty(curr)) {
            return prev;
        }

        if (curr.contains(CategoryEnum.CLOTH.getName())) {
            return prev;
        }

        HashSet<String> newCategories = new HashSet<>();

        // 选判断 prev 中是否包含具体的二级

        // 如果 curr 中包含一级, 如果包含一级, 那么就先去 prev 中尝试找二级, 如果找到了, 就把二级添加到 newCategories 中
        // 如果没有找到二级, 那么就尝试去找一级, 如果找到了, 就把一级添加到 newCategories 中
        curr.stream().filter(tagSystemTableService::isFirstCategory).forEach(firstCategory -> {

            HashSet<String> subCategories = new HashSet<>();

            tagSystemTableService.getSubCategoryByFirstCategory(firstCategory).stream()
                .filter(curr::contains)
                .findFirst()
                .ifPresent(subCategories::add);

            // 如果没有找到二级, 那么就直接添加当前的一级
            if (subCategories.isEmpty()) {
                newCategories.add(firstCategory);
            } else {
                // 找到了二级, 那么就使用二级
                newCategories.addAll(subCategories);
            }
        });

        // 如果 curr 中包含二级, 那么直接添加到 newCategories 中
        curr.stream().filter(tagSystemTableService::isSubCategory).forEach(newCategories::add);

        // 判断 prev 中是否包含 curr 的相同的

        return newCategories;
    }

    private CategoryAndTagPreference mergeCateTagPref(CategoryAndTagPreference curr,
                                                      CategoryAndTagPreference prev) {
        if (prev == null) {
            return curr;
        }

        // 创建新的对象来存储合并结果
        CategoryAndTagPreference merged = new CategoryAndTagPreference();

        // 初始化categories集合
        Set<CategoryAndTagPreference.CategoryAndTags> mergedCategories = new HashSet<>();

        // 处理当前偏好中的每个品类
        if (curr != null && curr.getCategories() != null) {
            for (CategoryAndTagPreference.CategoryAndTags currCat : curr.getCategories()) {
                // 在prev中查找匹配的品类
                CategoryAndTagPreference.CategoryAndTags matchingPrevCat =
                    prev.getCategories() != null ?
                        prev.getCategories().stream()
                            .filter(prevCat -> isRelatedCategory(prevCat.getCategory(),
                                currCat.getCategory()))
                            .findFirst()
                            .orElse(
                                prev.getCategories().stream()
                                    .filter(prevCat -> CategoryEnum.CLOTH.getName().equals(prevCat.getCategory()))
                                    .findFirst()
                                    .orElse(null)
                            ) : null;
                // 判断是否有匹配的品类, 如果没有的话, 应该还要去找一下 cloth
                // 合并品类标签
                CategoryAndTagPreference.CategoryAndTags mergedCat =
                    mergeCategoryAndTags(currCat, matchingPrevCat);
                mergedCategories.add(mergedCat);
            }
        }

        // 添加prev中未处理的品类
        if (prev.getCategories() != null) {
            for (CategoryAndTagPreference.CategoryAndTags prevCat : prev.getCategories()) {
                boolean hasBeenMerged = curr != null && curr.getCategories() != null &&
                    curr.getCategories().stream()
                        .anyMatch(currCat -> isRelatedCategory(currCat.getCategory(),
                            prevCat.getCategory()));

                if (!hasBeenMerged) {
                    mergedCategories.add(prevCat);
                }
            }
        }

        merged.setCategories(mergedCategories);

        // 处理clothIndex，只使用当前的值
        merged.setClothIndex(
            curr != null && curr.getClothIndex() != null ? curr.getClothIndex() : 0);

        return merged;
    }

    private boolean isRelatedCategory(String category1, String category2) {
        if (category1 == null || category2 == null) {
            return false;
        }

        if (category1.equals(category2)) {
            return true;
        }

        // 获取父级品类
        String firstCategory1 = tagSystemTableService.getFirstCategoryBySubCategory(category1);
        String firstCategory2 = tagSystemTableService.getFirstCategoryBySubCategory(category2);

        return (category1.equals(firstCategory2) || // category1 是 category2 的父类
            category2.equals(firstCategory1) || // category2 是 category1 的父类
            (firstCategory1 != null && firstCategory1.equals(firstCategory2))); // 共同父类
    }

    private CategoryAndTagPreference.CategoryAndTags mergeCategoryAndTags(
        CategoryAndTagPreference.CategoryAndTags curr,
        CategoryAndTagPreference.CategoryAndTags prev) {
        if (prev == null) {
            return curr;
        }

        boolean sameCategory = curr.getCategory().equals(prev.getCategory());
        boolean haveCloth = curr.getCategory().equals(CategoryEnum.CLOTH.getName()) ||
            prev.getCategory().equals(CategoryEnum.CLOTH.getName());

        // 如果品类相同, 那么直接合并表达的标签
        if (sameCategory) {
            Set<String> mergedLabelTypeList = new HashSet<>(curr.getLabelTypeList());
            mergedLabelTypeList.addAll(prev.getLabelTypeList());
            CategoryAndTagPreference.CategoryAndTags result =
                new CategoryAndTagPreference.CategoryAndTags();
            result.setCategory(curr.getCategory());
            result.setLabelTypeList(mergedLabelTypeList);
            return result;
        } else if (haveCloth) {
            // 存在 cloth 的品类，合并标签并使用非cloth的品类
            Set<String> mergedLabelTypeList = new HashSet<>(curr.getLabelTypeList());
            mergedLabelTypeList.addAll(prev.getLabelTypeList());
            CategoryAndTagPreference.CategoryAndTags result =
                new CategoryAndTagPreference.CategoryAndTags();
            // 如果当前是cloth，使用prev的品类，否则使用curr的品类
            result.setCategory(
                curr.getCategory().equals(CategoryEnum.CLOTH.getName()) ? prev.getCategory() :
                    curr.getCategory());
            result.setLabelTypeList(mergedLabelTypeList);
            return result;
        }

        // 判断当前品类是否为 prev 的子品类
        // 先获取他们的父级品类 -> 有可能为 null
        String currFirstCategory =
            tagSystemTableService.getFirstCategoryBySubCategory(curr.getCategory());
        String prevFirstCategory =
            tagSystemTableService.getFirstCategoryBySubCategory(prev.getCategory());

        // 判断 currFirstCategory prevFirstCategory curr.getCategory() prev.getCategory() 是否有父子关系
        if (curr.getCategory().equals(prevFirstCategory) || // curr 是 prev 的父类
            prev.getCategory().equals(currFirstCategory) || // prev 是 curr 的父类
            (currFirstCategory != null && currFirstCategory.equals(prevFirstCategory))) { // 共同父类

            // 合并标签
            Set<String> mergedLabelTypeList = new HashSet<>(curr.getLabelTypeList());
            mergedLabelTypeList.addAll(prev.getLabelTypeList());

            // 优先使用更具体的子类别
            String finalCategory =
                currFirstCategory != null && currFirstCategory.equals(prevFirstCategory)
                    ? curr.getCategory()
                    : (curr.getCategory().equals(prevFirstCategory) ? prev.getCategory() :
                    curr.getCategory());

            CategoryAndTagPreference.CategoryAndTags result =
                new CategoryAndTagPreference.CategoryAndTags();
            result.setCategory(finalCategory);
            result.setLabelTypeList(mergedLabelTypeList);
            return result;
        }

        // 如果没有父子关系，返回当前类别
        return curr;
    }

    //public void handleUserPreference(SubjectivePreference subjectivePreference,
    //                                 EcommercePreference ecommercePreference,
    //                                 TagPreferences categoryTagPreferences) {
    //    Map<String, List<String>> categoryDimension =
    //        tagDimensionExtract.extract(categoryTagPreferences);
    //
    //    // 抽取具体维度值
    //    CategoryPreference userPreferences = tagValueExtract.extract(categoryDimension);
    //
    //    // 用户当前的全部偏好
    //    ExtractedEntityObject currentPreference = ExtractedEntityObject.builder()
    //        .subjectivePreference(subjectivePreference)
    //        .ecommercePreference(ecommercePreference)
    //        .categoryPreferences(userPreferences)
    //        .currentCategories(new HashSet<>(categoryDimension.keySet()))
    //        .build();
    //
    //
    //    ExtractedEntityObject prevPreference = Context.get(Context.Name.PREFERENCE.getName());
    //
    //
    //    ExtractedEntityObject resultUserPreference =
    //        PreferenceMergeUtils.mergePreference(currentPreference, prevPreference);
    //
    //
    //    Context.put(Context.Name.PREFERENCE.getName(), resultUserPreference);
    //
    //    handleCategoryChange(currentPreference.getCurrentCategories(),
    //        prevPreference.getCurrentCategories());
    //
    //
    //}

    private void handleCategoryChange(Set<String> curr, Set<String> prev) {
        ConversationStatus status;
        if (isCategoryChange(curr, prev)) {
            status = new ConversationStatus();
            status.setCategoryChange(true);
        } else {
            status = Context.get(Context.Name.STATUS.getName());
            if (status == null) {
                status = new ConversationStatus();
            }
            status.setCategoryChange(false);
        }
        // todo 排查问题
        //Context.put(Context.Name.STATUS.getName(), status);
    }

    private boolean isCategoryChange(Set<String> currCate, Set<String> prevCate) {
        if (ObjectUtil.isEmpty(prevCate) || ObjectUtil.isEmpty(currCate)) {
            return false;
        }

        if (prevCate.size() == 1 && prevCate.contains(CategoryEnum.CLOTH.getName())) {
            return false;
        }

        if (currCate.size() == 1 && currCate.contains(CategoryEnum.CLOTH.getName())) {
            return false;
        }

        for (String category : currCate) {
            if (prevCate.contains(category)) {
                continue;
            }

            String firstCategory = tagSystemTableService.getFirstCategoryBySubCategory(category);

            if (prevCate.contains(firstCategory)) {
                continue;
            }

            return true;
        }

        return false;
    }
}

