package com.looksky.agents.application.tryon.swapcloth;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.tryon.swapcloth.convertor.TryOnConvertor;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.data.redis.config.RedisCodecFactory;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothParam;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothStatus;
import com.looksky.agents.sdk.tryon.swapcloth.enums.SwapClothStatusEnum;
import com.skygirls.biz.user.tryon.dto.SearchTryOnStatusReq;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SwapClothBaseService {

    private final RedissonClient redissonClient;

    private final RedisCodecFactory factory;
    protected final TryOnConvertor tryOnConvertor;
    private final GirlsClient girlsClient;

    private static final String QUEUE_NAME = "swap_cloth_queue:";

    @Value("${spring.profiles.active}")
    protected String env;

    public String asyncSwapCloth(SwapClothParam swapClothParam) {
        VirtualCompletableFuture.runAsync(() -> create(swapClothParam));
        return "success";
    }

    private void create(SwapClothParam swapClothParam) {
        SwapClothStatus status = new SwapClothStatus();
        status.setTraceId(swapClothParam.getId());
        status.setCreateTime(LocalDateTime.now());
        status.setUpdateTime(LocalDateTime.now());
        status.setSwapClothParam(swapClothParam);
        ensureQueue(status, SwapClothStatusEnum.QUEUED_FOR_ANALYSIS);
    }


    protected void ensureQueue(SwapClothStatus status, SwapClothStatusEnum statusEnum) {
        status.setStatus(statusEnum);
        status.setCreateTime(status.getUpdateTime());
        status.setUpdateTime(LocalDateTime.now());
        // 添加到队列里面
        RBlockingQueue<SwapClothStatus> queue = redissonClient.getBlockingQueue(QUEUE_NAME + statusEnum, factory.createCodec(SwapClothStatus.class));
        log.info("添加到 {} 队列: {}", statusEnum.getDescription(), JSONUtil.toJsonStr(status));
        queue.add(status);
        SearchTryOnStatusReq callbackRequest = tryOnConvertor.toCallbackRequest(status.getSwapClothParam(), statusEnum, status.getResultImage());
        girlsClient.tryonCallback(callbackRequest);
    }

    @SneakyThrows
    protected SwapClothStatus getQueueTask(SwapClothStatusEnum statusEnum) {
        RBlockingQueue<SwapClothStatus> queue = redissonClient.getBlockingQueue(QUEUE_NAME + statusEnum, factory.createCodec(SwapClothStatus.class));
        SwapClothStatus poll = queue.poll(1, TimeUnit.SECONDS);
        if (poll == null) {
            return null;
        }
        log.info("获取 {} 队列中获取到任务: {}", statusEnum.getDescription(), JSONUtil.toJsonStr(poll));
        return poll;
    }

    protected void removeFromQueue(SwapClothStatus status, SwapClothStatusEnum statusEnum) {
        RBlockingQueue<SwapClothStatus> queue = redissonClient.getBlockingQueue(QUEUE_NAME + statusEnum, factory.createCodec(SwapClothStatus.class));
        boolean remove = queue.remove(status);
        if (remove) {
            log.info("从 {} 队列中删除任务: {}", statusEnum.getDescription(), JSONUtil.toJsonStr(status));
        } else {
            log.info("从 {} 队列中删除任务失败: {}", statusEnum.getDescription(), JSONUtil.toJsonStr(status));
        }
    }


}
