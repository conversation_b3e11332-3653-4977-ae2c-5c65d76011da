package com.looksky.agents.application.chat.scene;

import com.looksky.agents.application.prompt.PromptBusinessService;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.common.enums.EventNameEnum;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @ClassName GirlsSceneSelector
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/3 上午10:16
 * @Version 1.0
 **/
@Slf4j
@Order(1)
@Component
@RequiredArgsConstructor
public class StyleHacksSceneSelector extends BaseSceneSelector {

    private final PromptBusinessService promptBusinessService;


    @Override
    public boolean support(String eventName, EnterPointEnum enterPoint, String page) {
        return EventNameEnum.USER_SPEAK.getValue().equals(eventName) && EnterPointEnum.STYLE_HACKS == enterPoint;
    }

    @Override
    protected SceneResult getSceneAndExternInfo(RequestInputDTO requestInput) {

        String promptName = "small_talk_in_style_hacks";

        PromptModel promptModel = promptBusinessService.getPromptModelByNameWithMetaData(promptName);

        // 请求推荐
        return new SceneResult(promptModel, null);
    }

}
