package com.looksky.agents.application.content.user.avatar;

import ai.fal.client.ClientConfig;
import ai.fal.client.CredentialsResolver;
import ai.fal.client.FalClient;
import ai.fal.client.SubscribeOptions;
import ai.fal.client.queue.QueueStatus;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.common.model.tryOn.TryOnFalKeyConfig;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.sdk.content.user.avatar.UserAvatarParam;
import java.util.List;
import java.util.Map;
import java.util.Random;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 头像文本转图片服务
 *
 * <AUTHOR>
 * @since 1.1.14
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class Text2ImageService {
    private final Random random = new Random();
    private final TryOnFalKeyConfig tryOnFalKeyConfig;

    @CollectEvent(typeExpression = "#param.id", extExpression = "#param.url")
    public String text2Image(UserAvatarParam param) {
        List<String> apiKeyList = tryOnFalKeyConfig.getApiKeyList();
        String apiKey = apiKeyList.get(random.nextInt(apiKeyList.size()));

        FalClient falClient = FalClient.withConfig(ClientConfig.withCredentials(CredentialsResolver.fromApiKey(apiKey)));

        var input = Map.of(
            "prompt", param.getPrompt(),
            "image_size", Map.of("width", 180, "height", 180),
            "num_inference_steps", 9,
            "enable_safety_checker", false
        );

        log.info("文本转图片请求参数: {}", input);
        var result = falClient.subscribe("fal-ai/flux/dev",
            SubscribeOptions.<Text2ImageResponse>builder()
                .input(input)
                .logs(true)
                .resultType(Text2ImageResponse.class)
                .onQueueUpdate(update -> {
                    if (update instanceof QueueStatus.InProgress status) {
                        log.info("文本转图片过程数据: {}", status.getLogs());
                    }
                })
                .build()
        );


        log.info("文本转图片结果: {}", JSONUtil.toJsonStr(result));

        return result.getData().getImages().getFirst().getUrl();
    }


    @Data
    static class Text2ImageResponse {
        private List<Text2ImageResponse.Images> images;

        private Long seed;
        private List<Boolean> has_nsfw_concepts;
        private String prompt;

        @Data
        static class Images {
            private String url;
            private String content_type;
        }
    }
}
