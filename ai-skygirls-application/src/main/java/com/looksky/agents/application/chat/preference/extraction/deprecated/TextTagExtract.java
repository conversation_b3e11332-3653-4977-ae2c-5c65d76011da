package com.looksky.agents.application.chat.preference.extraction.deprecated;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.application.chat.preference.extraction.SingleTagExtractService;
import com.looksky.agents.data.redis.conversation.PreferenceDataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.sdk.agent.common.enums.TagEnum;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.preference.SetPreferenceValue;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;

@Slf4j
//@Component
@RequiredArgsConstructor
public class TextTagExtract {
    private final SingleTagExtractService tagValueExtract;
    private final PreferenceDataService preferenceDataService;

    /**
     * 文本标签抽取
     * 维度:
     *    "function",   --> 功能
     *    "fabric",  --> 面料
     *    "care_instruction",  --> 洗护
     *    "process",  --> 工艺
     */
    @Async
    public void extract(Set<String> tags) {

        // 如果不包含文本类的标签, 则直接返回
        if (!TagEnum.containTextTag(tags)) {
            return;
        }

        Set<String> relevantTags = new HashSet<>(tags);
        relevantTags.retainAll(Set.of(TagEnum.FUNCTION.getName(), TagEnum.CARE_INSTRUCTION.getName(), TagEnum.FABRIC_TYPE.getName(), TagEnum.PROCESS.getName()));


        ExtractedEntityObject userPreference = Context.get(Context.Name.PREFERENCE.getName());


        Optional<String> category = userPreference.getCurrentCategories().stream().findFirst();

        category.ifPresent(s -> {
            List<VirtualCompletableFuture<Void>> tasks = new ArrayList<>();
            relevantTags.forEach(tag -> tasks.add(processTag(s, tag, userPreference)));
            VirtualCompletableFuture.allOf(tasks.toArray(new VirtualCompletableFuture[0])).join();
            preferenceDataService.savePreference(userPreference);
        });

    }

    private VirtualCompletableFuture<Void> processTag(String category, String tag, ExtractedEntityObject userPreference) {
        return VirtualCompletableFuture.runAsync(() -> {
            String result = tagValueExtract.extractSingleTagPreference(category, tag);
            SetPreferenceValue curr = JSONUtil.toBean(result, SetPreferenceValue.class);

            if (TagEnum.FUNCTION.getName().equals(tag)) {
                userPreference.setFunctionPreference(SetPreferenceValue.merge(curr, userPreference.getFunctionPreference()));
            } else if (TagEnum.FABRIC_TYPE.getName().equals(tag)) {
                userPreference.setFabricPreference(SetPreferenceValue.merge(curr, userPreference.getFabricPreference()));
            } else if (TagEnum.CARE_INSTRUCTION.getName().equals(tag)) {
                userPreference.setCareInstructionPreference(SetPreferenceValue.merge(curr, userPreference.getCareInstructionPreference()));
            } else if (TagEnum.PROCESS.getName().equals(tag)) {
                userPreference.setProcessPreference(SetPreferenceValue.merge(curr, userPreference.getProcessPreference()));
            }
        });
    }

}
