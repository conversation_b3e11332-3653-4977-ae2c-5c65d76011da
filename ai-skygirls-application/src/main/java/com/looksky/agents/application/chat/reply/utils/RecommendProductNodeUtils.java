package com.looksky.agents.application.chat.reply.utils;

import cn.hutool.core.util.ObjectUtil;
import com.looksky.agents.sdk.agent.search.bo.OutfitSuggestionList;
import com.looksky.agents.sdk.agent.search.bo.RecommendOutfitBO;
import com.looksky.agents.sdk.agent.search.dto.OutfitProductPlans;
import com.looksky.agents.sdk.agent.search.dto.PlansDetail;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 推商品节点的工具类
 *
 * <AUTHOR>
 * @since 1.1.14
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RecommendProductNodeUtils {

    /**
     * 将穿搭推荐方案合并到穿搭方案
     *
     * @param outfitProductPlans Deep Seek 给的 穿搭方案
     * @param outfitSuggestionList 抽词后的 穿搭搜索词
     * @return 合并后的穿搭方案
     */
    public static OutfitProductPlans mergeOutfitSuggestToPlan(OutfitProductPlans outfitProductPlans, OutfitSuggestionList outfitSuggestionList) {
        if (outfitProductPlans == null || outfitSuggestionList == null) {
            return outfitProductPlans;
        }

        // 获取所有的推荐方案
        List<RecommendOutfitBO> recommendOutfits = outfitSuggestionList.getOutfitSuggestions();
        if (ObjectUtil.isEmpty(recommendOutfits)) {
            return outfitProductPlans;
        }

        // 获取所有的计划详情
        List<PlansDetail> plansDetails = outfitProductPlans.getPlans();
        if (ObjectUtil.isEmpty(plansDetails)) {
            return outfitProductPlans;
        }

        // 遍历所有计划详情，查找匹配的推荐方案
        for (PlansDetail plansDetail : plansDetails) {
            String plansDetailTitle = plansDetail.getTitle();
            if (plansDetailTitle == null) {
                continue;
            }

            // 在推荐方案中查找标题匹配的项
            for (RecommendOutfitBO recommendOutfit : recommendOutfits) {
                String recommendOutfitTitle = recommendOutfit.getTitle();
                if (recommendOutfitTitle != null && recommendOutfitTitle.equals(plansDetailTitle)) {
                    // 找到匹配项，复制属性
                    plansDetail.setCategory(recommendOutfit.getCategory());
                    plansDetail.setPositivePreferences(recommendOutfit.getPositivePreferences());
                    plansDetail.setNegativePreferences(recommendOutfit.getNegativePreferences());
                    plansDetail.setTagPreference(recommendOutfit.getTagPreference());
                    break; // 已找到匹配项，退出内层循环
                }
            }
        }

        return outfitProductPlans;
    }

}
