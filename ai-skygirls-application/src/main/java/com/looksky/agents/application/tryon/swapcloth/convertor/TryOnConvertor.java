package com.looksky.agents.application.tryon.swapcloth.convertor;

import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothParam;
import com.looksky.agents.sdk.tryon.swapcloth.dto.SwapClothStatus;
import com.looksky.agents.sdk.tryon.swapcloth.enums.SwapClothStatusEnum;
import com.skygirls.biz.user.tryon.dto.SearchTryOnStatusReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface TryOnConvertor {
    SwapClothStatus copy(SwapClothStatus status);

    @Mapping(target = "swapClothStatus", source = "statusEnum")
    @Mapping(target = "resultImage", source = "resultImage")
    SearchTryOnStatusReq toCallbackRequest(SwapClothParam param, SwapClothStatusEnum statusEnum, String resultImage);
}
