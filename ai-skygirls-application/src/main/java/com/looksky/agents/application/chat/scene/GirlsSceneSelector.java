package com.looksky.agents.application.chat.scene;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.application.chat.preference.PreferenceService;
import com.looksky.agents.application.chat.prompt.selector.NextPromptSelector;
import com.looksky.agents.application.common.BatchTaskExecutor;
import com.looksky.agents.application.common.CommonRequestService;
import com.looksky.agents.application.prompt.PromptBusinessService;
import com.looksky.agents.common.utils.ReflectUtils;
import com.looksky.agents.data.redis.conversation.StatusDataService;
import com.looksky.agents.infrastructure.trace.annotation.TraceMethod;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.common.enums.EventNameEnum;
import com.looksky.agents.sdk.agent.common.enums.IntentTypeEnum;
import com.looksky.agents.sdk.agent.preference.CategoryAndTagPreference;
import com.looksky.agents.sdk.agent.preference.SubjectivePreference;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.search.bo.EcommercePreference;
import com.looksky.agents.sdk.agent.search.bo.IntentRecognitionBO;
import com.looksky.agents.sdk.agent.search.bo.NewConversation;
import com.looksky.agents.sdk.common.enums.PromptNameEnum;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @ClassName GirlsSceneSelector
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/3 上午10:16
 * @Version 1.0
 **/
@Slf4j
@Order(1)
@Component
@RequiredArgsConstructor
public class GirlsSceneSelector extends BaseSceneSelector {

    private final ObjectMapper objectMapper;
    private final BatchTaskExecutor batchExecute;
    private final PromptBusinessService promptBusinessService;
    private final NextPromptSelector nextPromptSelector;
    private final StatusDataService statusDataService;
    private final PreferenceService preferenceService;
    private final CommonRequestService commonRequestService;

    @Lazy
    @Resource
    private GirlsSceneSelector self;

    @Override
    public boolean support(String eventName, EnterPointEnum enterPoint, String page) {
        return EventNameEnum.USER_SPEAK.getValue().equals(eventName) &&  (EnterPointEnum.SEARCH == enterPoint || EnterPointEnum.DRESSING == enterPoint);
    }


    public PromptModel getScene(RequestInputDTO requestInput) {
        SceneResult result = self.getSceneAndExternInfo(requestInput);
        saveUserExternInfo(requestInput, result.externInfo());
        return result.strategy();
    }

    @SneakyThrows
    @Override
    @TraceMethod(description = "进入到 search 场景")
    protected SceneResult getSceneAndExternInfo(RequestInputDTO requestInput) {


        List<String> strategyNameList = ReflectUtils.getClassStaticFinalStr(GirlsStrategyNames.class);


        String result = commonRequestService.commonExecuteStrategy(PromptNameEnum.INTENT_RECOGNITION.getName());
        IntentRecognitionBO intentRecognitionBO = objectMapper.readValue(result, IntentRecognitionBO.class);


        // 批量执行策略
        Map<String, String> batchResult = batchExecute.batchExecuteStrategy(strategyNameList);

        // 解析策略执行结果
        GirlsParsedResults results = parseStrategyResults(batchResult);


        statusDataService.newConversation(results.newConversation().isNewConversation());

        // 抽取品类
        //extractCategories(results.categoryAndTagPreference());


        preferenceService.handleUserPreference(results.subjectivePreference(), results.ecommercePreference(), results.categoryAndTagPreference());
        IntentTypeEnum intentType = intentRecognitionBO.getIntentType();

        if (IntentTypeEnum.inquireProduct(intentType)) {
            intentType = IntentTypeEnum.SEARCH_CLOTH;
        }


        String promptName = nextPromptSelector.select(intentType);
        if (CharSequenceUtil.isBlank(promptName)) {
            return new SceneResult(null, null);
        }
        PromptModel promptModel = promptBusinessService.getPromptModelByNameWithMetaData(promptName);

        // 请求推荐
        return new SceneResult(promptModel, null);
    }


    // 扩展策略名称
    public static class GirlsStrategyNames extends SceneModels.Names {
        public static final String INTENT_RECOGNITION = PromptNameEnum.INTENT_RECOGNITION.getName();
        public static final String EXTRACT_USER_PREFERENCE_TYPE = PromptNameEnum.EXTRACT_USER_PREFERENCE_TYPE.getName();
        public static final String USER_SUBJECTIVE_PREFERENCE = PromptNameEnum.EXTRACT_USER_SUBJECTIVE_PREFERENCE.getName();
        public static final String ECOMMERCE_PREFERENCE = PromptNameEnum.EXTRACT_USER_ECOMMERCE_PREFERENCE.getName();
        public static final String DETERMINE_NEW_CONVERSATION = PromptNameEnum.IS_NEW_CONVERSATION.getName();
    }

    // 扩展解析结果
    private record GirlsParsedResults(
            IntentRecognitionBO intentRecognition,
            NewConversation newConversation,
            SubjectivePreference subjectivePreference,
            EcommercePreference ecommercePreference,
            CategoryAndTagPreference categoryAndTagPreference
    ) implements SceneModels.ParsedResults {
    }


    private GirlsParsedResults parseStrategyResults(Map<String, String> batchResult) {

        try {

            return new GirlsParsedResults(
                    objectMapper.readValue(batchResult.get(GirlsStrategyNames.INTENT_RECOGNITION), IntentRecognitionBO.class),
                    objectMapper.readValue(batchResult.get(GirlsStrategyNames.DETERMINE_NEW_CONVERSATION), NewConversation.class),
                    SubjectivePreference.parse(batchResult.get(GirlsStrategyNames.USER_SUBJECTIVE_PREFERENCE)),
                    objectMapper.readValue(batchResult.get(GirlsStrategyNames.ECOMMERCE_PREFERENCE), EcommercePreference.class),
                    objectMapper.readValue(batchResult.get(GirlsStrategyNames.EXTRACT_USER_PREFERENCE_TYPE), CategoryAndTagPreference.class)
            );
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to parse strategy results", e);
        }
    }

}
