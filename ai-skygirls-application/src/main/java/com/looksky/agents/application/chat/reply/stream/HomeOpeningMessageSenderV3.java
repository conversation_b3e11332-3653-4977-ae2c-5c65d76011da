package com.looksky.agents.application.chat.reply.stream;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.graecove.common.ApiResp;
import com.looksky.agents.application.chat.reply.AbstractMessageSender;
import com.looksky.agents.data.client.business.DataHubClient;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.versioncompat.annotation.ApiVersion;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.common.enums.EventNameEnum;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.common.enums.PageEnum;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import com.looksky.agents.sdk.agent.tryon.bo.ColorSeasonCardBO;
import com.looksky.agents.sdk.datahub.message.dto.MsgReq;
import com.looksky.agents.sdk.datahub.message.enums.DirectionEnum;
import com.skygirls.biz.im.model.MessageModel;
import com.skygirls.biz.report.IosUserInfoDto;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.client.codec.StringCodec;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Order(1)
@Component
@ApiVersion(platform = ApiVersion.PlatformType.ALL, version = "1.1.8")
public class HomeOpeningMessageSenderV3 extends AbstractMessageSender {

    @Resource
    private DataHubClient dataHubClient;

    private static final String LOADING_MESSAGE = "Analyzing your color season...";


    @Override
    protected boolean supports(PromptModel strategy) {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());
        return PageEnum.HOME.getName().equals(requestInput.getPage()) && EnterPointEnum.HOME == requestInput.getEnterPointEnum();
    }


    @Override
    protected void doSend(PromptModel strategy, HashMap<String, Object> hashMap) {
        AgentMessageResp message = initMessage(EventTypeEnum.TEXT.getType());
        IosUserInfoDto userInfo = Context.get(Context.Name.USER_INFO.getName());


        String redisKey;

        // 判断用户解锁状态

        // 已解锁
        if (userInfo != null && userInfo.getBasicUser() != null && userInfo.getBasicUser().getProductExpiredDateStamp() != null && userInfo.getBasicUser().getProductExpiredDateStamp() > 0) {
            redisKey = RedisKeyConstants.HOME_UNLOCK_OPENING;
        } else {
            // 未解锁
            // 判断第几次进入
            long total = getTotal(userInfo);
            if (total <= 1) {
                redisKey = RedisKeyConstants.HOME_FIRST_OPENING;
            } else {
                redisKey = RedisKeyConstants.HOME_SECOND_OPENING;
            }
        }

        RBucket<String> bucket = redissonClient.getBucket(redisKey, StringCodec.INSTANCE);

        String replyStr = bucket.get();


        replyStr = fillVariable(replyStr, userInfo);

        sendContent(message, EventTypeEnum.TEXT.getType(), replyStr);
        sendLoading(LOADING_MESSAGE);
        sendColorSeasonCardMessage(userInfo);

    }

    private long getTotal(IosUserInfoDto userInfo) {
        long total = 0;
        try {
            MsgReq msgReq = new MsgReq().setDirection(DirectionEnum.SEND.getName()).setUserId(userInfo.getAppUser().getUserId()).setEventName(EventNameEnum.OPENING.getValue())
                .setPage(PageEnum.HOME.getName())
                .setEnterPoint(EnterPointEnum.HOME.getValue());
            ApiResp<Page<MessageModel>> pageApiResp = dataHubClient.messageHistory(msgReq);
            total = pageApiResp.getData().getTotal();
        } catch (Exception e) {
            log.error("获取 home 打招呼次数失败, 默认返回 0", e);
        }

        return total;
    }

    /**
     * 填充配置中的变量
     */
    private String fillVariable(String replyStr, IosUserInfoDto userInfo) {
        if (userInfo == null) {
            return replyStr;
        }
        PromptTemplate template = new PromptTemplate(replyStr);
        Prompt prompt = template.create(Map.of("user_name", userInfo.getBasicUser().getUsername()));
        return prompt.getContents();
    }


    private void sendColorSeasonCardMessage(IosUserInfoDto userInfo) {
        if (userInfo == null) {
            return;
        }
        ColorSeasonCardBO colorSeasonCardBO = new ColorSeasonCardBO();
        colorSeasonCardBO.setBestColorCards(userInfo.getAppUser().getBestColorCards());
        colorSeasonCardBO.setBestColors(userInfo.getAppUser().getBestColorNumbers());
        colorSeasonCardBO.setColorSeason(userInfo.getAppUser().getColorSeason().getName());
        AgentMessageResp adviceMessage = initMessage(EventTypeEnum.COLOR_SEASON_CARD.getType());

        sendContent(adviceMessage, EventTypeEnum.COLOR_SEASON_CARD.getType(), colorSeasonCardBO);
    }
}