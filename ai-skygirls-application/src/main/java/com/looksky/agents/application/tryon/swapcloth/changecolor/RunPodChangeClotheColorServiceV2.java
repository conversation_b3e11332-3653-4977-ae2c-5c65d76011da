package com.looksky.agents.application.tryon.swapcloth.changecolor;

import com.fasterxml.jackson.databind.JsonNode;
import com.looksky.agents.data.client.business.TryOnClient;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.SwapClothChangeClothColorRequest;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.changecolor.RunPodChangeColorRequest;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 1.1.8
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class RunPodChangeClotheColorServiceV2 {

    private final TryOnClient tryOnClient;

    /**
     * 换色
     * @return 换色后的图片地址
     */
    @CollectEvent
    public String changeClothColor(SwapClothChangeClothColorRequest request) {
        RunPodChangeColorRequest runPodChangeColorRequest = new RunPodChangeColorRequest();
        runPodChangeColorRequest.setColorHex(request.getColor());
        runPodChangeColorRequest.setImageUrl(request.getImageUrl());
        JsonNode response = tryOnClient.runPodChangeColor(runPodChangeColorRequest);
        return Optional.ofNullable(response).map(r -> r.get("data").asText()).orElseThrow(() -> new RuntimeException("换装失败"));
    }

}
