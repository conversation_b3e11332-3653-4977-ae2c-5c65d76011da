package com.looksky.agents.sdk.recommend.daily100.dto.request;


import com.looksky.agents.sdk.recommend.colorseason.request.ColorSeasonRequestDTO;
import com.looksky.agents.sdk.recommend.common.dto.VectorRecallModelDTO;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Daily100TestRequestDTO {

    private String userId;
    private Integer limit;
    private String season;
    @Deprecated
    private List<GirlsDaily100TestRecallVector> vectorRecall;

    private List<GirlsDaily100TestRecallVector> categoryVectorRecall;
    private List<GirlsDaily100TestRecallVector> totalVectorRecall;


    @Data
    @Builder
    public static class GirlsDaily100TestRecallVector {

        //召回策略
        private String recallStrategy;
        //向量文本
        private List<GirlsDaily100TestVectorQuery> vectorQuery;
        //召回权重
        private Double recallWeight;
        private List<String> mustSubCategory;
        private List<String> mustNotSubCategory;


        public static GirlsDaily100TestRecallVector fromVectorRecall(VectorRecallModelDTO vectorRecall) {
            return GirlsDaily100TestRecallVector.builder()
                    .recallStrategy(vectorRecall.getRecallStrategy())
                    .vectorQuery(vectorRecall.getVectorQuery().stream()
                            .map(GirlsDaily100TestVectorQuery::fromVectorQuery)
                            .toList())
                    .mustSubCategory(List.of(vectorRecall.getRecallStrategy()))
                    .build();
        }

    }

    @Data
    @Builder
    public static class GirlsDaily100TestVectorQuery {
        private String text;
        Integer weight;

        public static GirlsDaily100TestVectorQuery fromVectorQuery(SearchRequestDTO.VectorQuery vectorQuery) {
            return GirlsDaily100TestVectorQuery.builder()
                    .text(vectorQuery.getText())
                    .weight(vectorQuery.getWeight())
                    .build();
        }

    }


    public static Daily100TestRequestDTO fromColorSeason(ColorSeasonRequestDTO colorSeasonRequestDTO) {
        return Daily100TestRequestDTO.builder()
                .userId(colorSeasonRequestDTO.getUserId())
                .categoryVectorRecall(colorSeasonRequestDTO.getVectorRecall().stream()
                        .map(GirlsDaily100TestRecallVector::fromVectorRecall)
                        .toList())
                .build();
    }



}