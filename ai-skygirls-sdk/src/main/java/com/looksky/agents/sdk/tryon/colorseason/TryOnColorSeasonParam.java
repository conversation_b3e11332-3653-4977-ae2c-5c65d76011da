package com.looksky.agents.sdk.tryon.colorseason;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * colorSeason 试色参数
 *
 * @since  1.1.6
 * <AUTHOR>
 **/
@Data
public class TryOnColorSeasonParam {
    @NotBlank(message = "用户 ID 不能为空")
    @Schema(description = "用户上传的半身体照, 最好使用原图, 而不是裁剪过后的")
    private String userImage;

    @Deprecated(since = "1.1.8", forRemoval = true)
    @Schema(description = "领形")
    private String neckline;

    @NotBlank(message = "衣服颜色不能为空")
    @Schema(description = "衣服颜色")
    private String color;

    @NotBlank(message = "体重类型不能为空")
    @Schema(description = "体重类型")
    private String weightType;

    @NotBlank(message = "发色不能为空")
    @Schema(description = "头发颜色")
    private String hairColor;

    @NotBlank(message = "正负向不能为空")
    @Schema(description = "正向传 true, 负向传 false")
    private Boolean positive;

    @Deprecated(since = "1.1.8", forRemoval = true)
    @Schema(description = "种子, 同一用户需要固定, 保证效果")
    private String seed;

    @NotBlank(message = "是否戴眼镜不能为空")
    @Schema(description = "用户是否戴眼镜, true 戴眼镜, false 不戴眼镜")
    private Boolean glasses;

    @NotBlank(message = "肤色不能为空")
    @Schema(description = "肤色")
    private String skinTone;

    @NotBlank(message = "肤色深度不能为空")
    @Schema(description = "肤色深度")
    private String skinDepth;
}
