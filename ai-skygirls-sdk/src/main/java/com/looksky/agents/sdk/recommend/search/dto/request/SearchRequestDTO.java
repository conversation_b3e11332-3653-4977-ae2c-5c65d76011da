package com.looksky.agents.sdk.recommend.search.dto.request;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
public class SearchRequestDTO {
    private String userId;
    private String season;
    private List<GirlsStrategyTerm> strategyTerms;

    @Data
    @Builder
    public static class GirlsStrategyTerm {
        private SearchTerm searchTerm;
        private List<VectorQuery> step1VectorQueries;
        private List<VectorQuery> step2VectorQueries;
        private List<VectorQuery> step3VectorQueries;
        private List<VectorQuery> tagsVectorQuery;

    }

    @Data
    @Builder
    public static class SearchTerm {
        private String searchStrategy;
        private List<String> categories;
        private List<String> brands;
        private PriceRange priceRange;
        private List<UserPreference> userPreferences;
        private List<CombinationPreference> combinationPreferenceMust;
        private List<CombinationPreference> combinationPreferenceMustNot;
        private List<CombinationPreference> combinationPreferenceShould;
        private List<String> positiveElements;
        private List<String> negativeElements;
        private Boolean isFreePostage;
        private Boolean isCanReturn;
        private Boolean isDiscount;
        private List<String> clothesSizes;
        private List<String> sizeTypes;
        private List<String> dislikeBrands;
        private List<String> dislikeCategories;
        private List<String> shouldElements;
        private List<VectorQuery> vectorQueries;

    }

    @Data
    @Builder
    public static class PriceRange {
        private Double minPrice;
        private Double maxPrice;
        private Double normPrice;

    }

    @Data
    @Builder
    public static class UserPreference {
        private String tagType;
        private List<String> like;
        private List<String> disLike;
        private List<String> recommend;

        public boolean isEmpty() {
            return (like == null || like.isEmpty()) && (disLike == null || disLike.isEmpty());
        }
    }

    @Data
    @Builder
    public static class CombinationPreference {
        private String parentTagType;
        private List<CombinationPreferenceNode> preferenceNodes;

    }

    @Data
    @Builder
    public static class CombinationPreferenceNode {
        private String tagType;
        private List<String> tagValues;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VectorQuery {
        private String text;
        private Integer weight;
    }
}