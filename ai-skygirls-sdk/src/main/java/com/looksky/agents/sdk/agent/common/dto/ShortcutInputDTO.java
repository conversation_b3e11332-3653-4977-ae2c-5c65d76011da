package com.looksky.agents.sdk.agent.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 快捷输入
 *
 * <AUTHOR>
 * @since  1.1.12
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShortcutInputDTO {
    @Schema(description = "建议问题列表")
    private List<String> tips;

    @Schema(description = "快捷输入的标题")
    private String title;

    @Schema(description = "模型回复的内容, 内部使用字段")
    private String response;
}
