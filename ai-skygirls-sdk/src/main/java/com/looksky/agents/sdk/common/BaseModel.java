package com.looksky.agents.sdk.common;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * MySql 基础实体类
 *
 * <AUTHOR>
 * @since 1.0
 **/
@Data
public abstract class BaseModel implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(value = "pk_id", type = IdType.ASSIGN_ID)
    protected String id;

    @Schema(description = "创建时间")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    protected LocalDateTime createdTime;

    @Schema(description = "更新时间")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    protected LocalDateTime updatedTime;

    @Schema(description = "创建人")
    protected String createdBy;

    @Schema(description = "更新人")
    protected String updatedBy;

    @Schema(description = "删除标记: 0 启用 1 不启用")
    protected Boolean deleted;
}
