package com.looksky.agents.sdk.agent.tryon.bo;

import com.looksky.agents.sdk.utils.string.StringUtilsExt;
import java.util.List;
import lombok.Data;

/**
 * <p>
 * 用于返回 季节颜色卡片
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class ColorSeasonCardBO {
    private String title = "Your Color Season is";
    private String tryOnResultUrl;
    private List<String> bestColorCards;
    private List<String> bestColors;
    private String jumpContent = "Try On More Colors";
    private String colorSeason;

    public String getColorSeason() {
        return StringUtilsExt.capitalizeWords(this.colorSeason.replace("_", " "));
    }
}
