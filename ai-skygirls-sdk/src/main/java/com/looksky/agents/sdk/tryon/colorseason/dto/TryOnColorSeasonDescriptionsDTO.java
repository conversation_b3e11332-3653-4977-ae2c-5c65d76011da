package com.looksky.agents.sdk.tryon.colorseason.dto;

import java.util.Map;
import lombok.Data;

/**
 * <p>
 * 用于存储颜色季节换装字段的描述信息
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class TryOnColorSeasonDescriptionsDTO {
    private Map<String, String> glassesDescriptions;
    private Map<String, String> necklinesDescriptions;
    private Map<String, String> positiveAndNegativeDescriptions;
    private Map<String, String> hairColorDescriptions;
    private Map<String, String> weightTypeDescriptions;
    private Map<String, Integer> necklinesConfig;
    private Map<String, String> colorDescriptions;
}
