package com.looksky.agents.sdk.tryon.colorseason;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * colorSeason 试色参数, 四步方案, 第四步
 *
 * @since  1.1.8
 * <AUTHOR>
 **/
@Data
public class TryOnColorSeasonParamV1 {

    @Schema(description = "追踪 ID, 唯一")
    private String id;

    @NotBlank(message = "用户 ID 不能为空")
    @Schema(description = "用户 ID")
    private String userId;

    @NotBlank(message = "用户 ID 图片")
    @Schema(description = "用户上传的半身体照, 最好使用原图, 而不是裁剪过后的")
    private String userImage;

    @Schema(description = "衣服的图片")
    private String clothImage = "https://cdn.lookskyai.com/upload/agent/92530df70c0888fc195505c1b095b9b7.png";
}
