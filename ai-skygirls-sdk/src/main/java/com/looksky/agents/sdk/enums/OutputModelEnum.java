package com.looksky.agents.sdk.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Getter
public enum OutputModelEnum {
    TEXT(1, "text"),
    FUNCTION_CALL(2, "function_call"),
    JSON_SCHEMA(3, "json_schema"),
    NO_STRICT_JSON_SCHEMA(4, "json_schema");

    @EnumValue
    private final Integer code;

    @JsonValue
    private final String name;

    OutputModelEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }


//    public static OutputModelEnum fromString(String text) {
//        for (OutputModelEnum model : OutputModelEnum.values()) {
//            if (model.value.equalsIgnoreCase(text)) {
//                return model;
//            }
//        }
//        return TEXT; // 默认返回TEXT
//    }
} 