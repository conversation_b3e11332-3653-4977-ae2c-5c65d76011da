package com.looksky.agents.sdk.agent.prompt.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-21 14:14:29
 */
@Getter
@Setter
@TableName("t_prompt_macros")
@Schema(description = "PromptMacros对象")
public class PromptMacrosModel implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键 id")
    @TableId(value = "pk_id", type = IdType.ASSIGN_ID)
    private String pkId;

    @Schema(description = "宏的名字")
    private String name;

    @Schema(description = "宏的内容")
    private String content;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "创建时间")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @Schema(description = "更新时间")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "更新人")
    private String updatedBy;

    @Schema(description = "删除标记: 0 启用 1 不启用")
    @TableLogic
    private Boolean deleted;
}
