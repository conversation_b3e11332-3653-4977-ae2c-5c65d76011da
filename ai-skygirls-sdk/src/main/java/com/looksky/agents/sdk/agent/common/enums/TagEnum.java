package com.looksky.agents.sdk.agent.common.enums;

import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
/**
 * 标签名枚举
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Getter
@AllArgsConstructor
public enum TagEnum {
    CATEGORY("category", "类别"),
    PRICE("price", "价格"),
    SIZE("size", "尺码"),
    MATERIAL("material", "材质"),
    FABRIC_TYPE("fabric_type", "面料"),
    FABRIC("fabric", "面料"),
    @Deprecated(since = "细分面料(special_fabric)合并到了面料(fabric_type)")
    SPECIAL_FABRIC("special_fabric", "细分面料"),
    FUNCTION("function", "功能"),
    CARE_INSTRUCTION("care_instruction", "洗护方式"),
    PROCESS("process", "工艺"),
    BRAND("brand", "品牌"),


    SATURATION("saturation", "饱和度"),
    BRIGHTNESS("brightness", "亮度"),
    SLEEVE_LENGTH("sleeve_length", "袖长"),
    LENGTH("length", "长度"),
    FIT("fit", "合身度"),
    WAIST("waist", "腰部的标签, 实际上, 他本不应该作为一个枚举出现在这里, 但是因为抽取维度的时候, 会抽取为 waist, 所以先暂时放到这里"),
    WAISTLINE("waistline", "腰线"),
    WAIST_FIT("waist_fit", "腰部合适度"),
    RISE("rise", "裤子的腰线"),
    NECKLINE("neckline", "领口"),
    NECKLINE_SHAPE("neckline_shape", "领口形状"),
    NECKLINE_DETAIL("neckline_detail", "领口细节"),
    PATTERN_TYPE("pattern_type", "图案类型"),
    ELASTICITY("elasticity", "弹性"),
    STYLE("clothing_styles", "风格"),





    ;

    private final String name;
    private final String description;

    public static boolean isPriceOrSize(String name) {
        return PRICE.name.equals(name) || SIZE.name.equals(name);
    }


    /**
     * 是否包含文本类的标签
     * @param tags 待判断的标签集合
     * @return 布尔值, 是否包含文本类的标签
     */
    public static boolean containTextTag(Set<String> tags) {
        return tags.contains(FUNCTION.getName()) || tags.contains(CARE_INSTRUCTION.getName()) || tags.contains(SPECIAL_FABRIC.getName()) || tags.contains(FABRIC_TYPE.getName()) || tags.contains(PROCESS.getName());
    }

    /**
     * 是否是通用标签
     * @param tag 标签名称
     * @return 布尔值, 是否是通用标签
     */
    public static boolean isCommonTag(String tag) {
        return TagEnum.PRICE.getName().equals(tag) || TagEnum.BRAND.getName().equals(tag) || TagEnum.FABRIC.getName().equals(tag);
    }

    /**
     * 是否是服装标签
     * @param tag 待判断的 tag
     * @return 布尔值, 是否是服装标签
     */
    public static boolean isClothTag(String tag) {
        return !PRICE.getName().equals(tag) || !BRAND.getName().equals(tag) || !SIZE.getName().equals(tag);
    }
}
