package com.looksky.agents.sdk.agent.common.dto;


import com.looksky.agents.sdk.agent.common.enums.EnterPointEnum;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.conversation.Event;
import com.looksky.agents.sdk.product.Product;
import com.skygirls.biz.im.dto.MessageRestDTO;
import com.skygirls.biz.im.dto.MessageTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;


/**
 * chat 的输入参数 内部使用
 *
 * <AUTHOR>
 * @since 1.0
 **/
@Data
@Builder
public class RequestInputDTO {
    @Schema(description = "入口点")
    private EnterPointEnum enterPointEnum;
    @Schema(description = "消息id")
    private String messageId;
    @Schema(description = "会话id")
    private String conversationId;
    @Schema(description = "用户id")
    private String userId;
    @Schema(description = "请求id")
    private String requestId;
    @Schema(description = "连接id")
    private String connectionId;
    @Schema(description = "事件对象")
    private Event event;
    @Schema(description = "产品对象")
    private Product product;
    @Schema(description = "产品列表")
    private List<Product> products;
    @Schema(description = "品牌")
    private String brand;
    @Schema(description = "用户所在时区")
    private String zone;
    @Schema(description = "发送请求所在的页面")
    private String page;

    @Deprecated
    @Schema(description = "是否重试")
    private Boolean isRetry;
    @Schema(description = "策略名")
    private String strategyName;

    // 这个应该删掉, 因为都已经转为了 Event
    @Schema(description = "事件字典")
    private MessageRestDTO.EventDict eventDict;

    @Schema(description = "会话状态")
    private ConversationStatus status;

    @Schema(description = "消息类型")
    private MessageTypeEnum messageType;

    @Schema(description = "季节")
    private String season;
    @Schema(description = "城市")
    private String city;
    @Schema(description = "日期 yyyy-MM-dd HH:mm:ss 格式")
    private String date;

}