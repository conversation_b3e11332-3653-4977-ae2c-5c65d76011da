package com.looksky.agents.sdk.perplex.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class PerplexSearchResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @JsonProperty("llm_response")
    private String llmResponse;
    private List<PerplexSource> sources;
    private List<Object> images;
    @JsonProperty("response_time")
    private Double responseTime;
    private String error;

    private String searchContent;


    @Data
    public static class PerplexSource {
        private String title;
        private String link;
        private String snippet;
    }
} 