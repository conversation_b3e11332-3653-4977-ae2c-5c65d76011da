package com.looksky.agents.sdk.recommend.star.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
public class UserStarBodySimilarResultDTO {

    private List<SimilarResult> similarResults;

    @Data
    @Builder
    public static class SimilarResult {
        private String userId;
        private Integer similarityPercentage;
    }
}
