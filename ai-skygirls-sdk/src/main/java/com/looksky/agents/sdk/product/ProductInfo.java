package com.looksky.agents.sdk.product;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 商品信息
 *
 * @since  1.1.12
 * <AUTHOR>
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProductInfo {
    @Schema(description = "SPU ID")
    @JsonProperty("product_id")
    private String productId;

    @Schema(description = "商品描述")
    private String desc;

    @Schema(description = "商品标题")
    private String title;

    @Schema(description = "品牌名")
    @JsonProperty("merchant_name")
    private String merchantName;


    @Schema(description = "SKC ID")
    @JsonProperty("skc_id")
    private String skcId;

    @Schema(description = "SKC 链接")
    @JsonProperty("skc_link")
    private String link;

    @Schema(description = "SKC 图片")
    @JsonProperty("skc_imgs")
    private List<String> images;

    @Schema(description = "折扣, 1 为原价, 0.9 为 9 折扣")
    @JsonProperty("skc_discounts")
    private Double discounts;

    @Schema(description = "价格")
    @JsonProperty("skc_price")
    private Double price;

    @Schema(description = "最高价格")
    @JsonProperty("skc_highest_price")
    private Double highestPrice;

    @Schema(description = "原价, 不可依赖, 有可能为 0")
    @JsonProperty("skc_original_price")
    private Double originalPrice;

    @Schema(description = "最高原价, 不可依赖, 有可能为 0")
    @JsonProperty("skc_highest_original_price")
    private Double highestOriginalPrice;

    @Schema(description = "发布时间")
    @JsonProperty("skc_publish_time")
    private Date publishTime;

    @Schema(description = "销售尺码")
    @JsonProperty("sale_size")
    private List<String> saleSize;

    @Schema(description = "商品标签")
    private JSONObject tags;
}