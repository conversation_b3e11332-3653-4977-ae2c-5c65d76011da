package com.looksky.agents.sdk.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "通用请求策略")
public class CommonModelRequestDTO {
    @NotBlank
    @Schema(description = "策略名")
    private String strategyName;

    @Schema(description = "用户 ID")
    private String userId;

    @Schema(description = "SKC ID")
    private String skcId;

    @Schema(description = "用户输入")
    private String input;

    @Schema(description = "变量消息")
    private Map<String, Object> variable;

    @Schema(description = "图片地址")
    private List<String> images;

    @Schema(description = "会话历史")
    private String conversationHistory;

    @Schema(description = "输出结果")
    private String output;
}
