package com.looksky.agents.sdk.perplex.dto.request;

import com.looksky.agents.sdk.utils.date.DateUtils;
import lombok.Data;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class PerplexSearchRequest {
    private String query;
    private String dateContext = DateUtils.getNowDate(null);
    private String location = "us";
    private Boolean proMode = false;
    private String responseLanguage = "en";
    private String answerType = "text";
    private String searchType = "general";
    private Boolean verboseMode = true;
    private Boolean returnCitations = false;
    private Boolean returnSources = true;
    private Boolean returnImages = false;
} 