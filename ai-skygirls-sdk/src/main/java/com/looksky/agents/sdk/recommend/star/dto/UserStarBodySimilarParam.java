package com.looksky.agents.sdk.recommend.star.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class UserStarBodySimilarParam {
    private BodyComparisonData userBodyComparisonData;
    private List<BodyComparisonData> starBodyComparisonData;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BodyComparisonData {
        private String userId;
        private String bodyShape;
        private String heightType;
        private String weightType;
        private String proportions;
        private String image;

        // 三围信息
        private String bust;
        private String waist;
        private String hip;

        // 身高和体重信息
        private String weight;
        private String height;
    }
}