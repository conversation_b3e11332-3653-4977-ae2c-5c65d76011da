package com.looksky.agents.sdk.feishu.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class SpreadsheetDataDTO {

    /**
     * 工作表的版本号。从 0 开始计数，更新一次版本号加一。
     */
    private Integer revision;
    /**
     * 表格的 token
     */
    private String spreadsheetToken;
    /**
     * 读取的单元格总数
     */
    private Integer totalCells;
    /**
     * 读取的值与范围
     */
    private List<ValueRange> valueRanges;

    @Data
    public static class ValueRange{

        private String majorDimension;
        private String range;
        private Integer revision;
        private List<List<Object>> values;

    }

    @Data
    public static class SegmentNode{

        private String text;
        private String type;

    }

}
