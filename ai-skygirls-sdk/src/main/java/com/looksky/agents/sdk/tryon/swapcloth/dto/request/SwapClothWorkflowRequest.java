package com.looksky.agents.sdk.tryon.swapcloth.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


/**
 * <p>
 * 换衣, 请求 comfy ui 工作流 的请求参数
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class SwapClothWorkflowRequest {
    @JsonProperty("new_face_image")
    private String newFaceImage;
    @JsonProperty("new_cloth_image")
    private String newClothImage;
    @JsonProperty("new_prompt_text")
    private String newPromptText;
    private String id;
}
