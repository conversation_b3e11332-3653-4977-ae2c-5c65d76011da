package com.looksky.agents.sdk.product;

import com.graecove.common.Pair;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.skygirls.biz.merchant.dto.MerchantDTO;
import com.skygirls.biz.product.dto.PriceTable;
import com.skygirls.biz.product.dto.RecommendReasonDataResp;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Deprecated(since = "1.1.12", forRemoval = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductInfoResp {
    private List<ProductCard> skcs;


    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ProductCard {
        private String skcId;

        private String productId;

        private List<String> imgs;

        private String link;

        private String discount;

        private String title;

        private String color;

        private BigDecimal price;

        private BigDecimal originalPrice;

        @Schema(description = "标签")
        private List<String> tags = new ArrayList<>();

        @Schema(description = "标签以及标签值")
        private Map<String, List<String>> tagMap;

        private String cardFeature;

        @Schema(description = "关联的分类")
        private CategoryDTO category;

        private String strategy;

        @Schema(description = "品牌")
        private MerchantDTO merchant;


        @Data
        @Accessors(chain = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class CategoryDTO {
            private String id;
            private String name;
        }

        
        
        @Schema(description = "是否被添加到喜欢")
        private Boolean isWished;

        @Schema(description = "推荐那边给过来的类型，3是skc,4是库外商品")
        private Long itemType;

        @Schema(description = "是否选中了不喜欢")
        private Boolean isDislike;

        @Schema(description = "商品介绍")
        private String desc;

        private String recommendReason;

        @Schema(description = "其他颜色的")
        private List<String> moreColor;

        @Schema(description = "价格表")
        private List<PriceTable> priceTable;

        @Schema(description = "商品特点")
        private List<Pair<String, String>> properties;


        @Schema(description = "推荐理由 v2")
        private RecommendReasonDataResp recommendReasonV2;
    }
}
