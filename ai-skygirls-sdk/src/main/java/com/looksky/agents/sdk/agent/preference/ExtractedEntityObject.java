package com.looksky.agents.sdk.agent.preference;

import com.looksky.agents.sdk.agent.search.bo.EcommercePreference;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtractedEntityObject implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private SubjectivePreference subjectivePreference;
    private EcommercePreference ecommercePreference;
    private CategoryAndTagPreference categoryAndTagPreference;

    @Deprecated
    private CategoryPreference categoryPreferences;
    private Set<String> currentCategories;

    // 现有的偏好
    private PricePreference pricePreference;
    private SetPreferenceValue brandPreference;
    private SetPreferenceValue fabricPreference;

    // 新增的偏好
    private SetPreferenceValue saturationPreference;    // 饱和度
    private SetPreferenceValue brightnessPreference;    // 亮度
    private SetPreferenceValue sleeveLengthPreference;  // 袖长
    private SetPreferenceValue lengthPreference;        // 长度
    private SetPreferenceValue fitPreference;           // 合身度
    private SetPreferenceValue waistlinePreference;     // 腰线
    private SetPreferenceValue waistFitPreference;      // 腰部合适度
    private SetPreferenceValue risePreference;          // 裤子的腰线

    private SetPreferenceValue functionPreference;      // 功能
    private SetPreferenceValue careInstructionPreference;// 洗护
    private SetPreferenceValue processPreference;       // 工艺

    private SetPreferenceValue necklineShapePreference;         // 领口形状
    private SetPreferenceValue necklineDetailPreference;         // 领口细节

    private SetPreferenceValue materialPreference;           // 材质
    private SetPreferenceValue stylePreference;            // 风格
    private SetPreferenceValue elasticityPreference;            // 弹性
    private SetPreferenceValue sizePreference;            // 尺码


}