package com.looksky.agents.sdk.agent.product.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Getter
@AllArgsConstructor
public enum BodyMeasure {
    HEIGHT("height"),
    WEIGHT("weight"),
    CHEST("chest"),
    WAIST("waist"),
    HIP("hip"),
    SHOULDER_WIDTH("shoulder_width"),
    BODY_SHAPE("body_shape");

    private final String value;
}