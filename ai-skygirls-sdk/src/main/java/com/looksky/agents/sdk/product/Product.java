package com.looksky.agents.sdk.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import java.util.List;
import java.util.Map;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
public class Product {
    private String skcId;
    private String spuId;
    private String title;
    private String firstCategory;
    private String subCategory;
    private String description;
    private String features;
    private String brand;
    
    @JsonProperty("color_value_list")
    private List<String> colorValueList;
    
    @JsonProperty("color_list")
    private List<String> colorList;
    
    @JsonProperty("size_list")
    private List<String> sizeList;
    
    @JsonProperty("item_tag_dict")
    private Map<String, String> itemTagDict;
    
    private String priceType;
    private String price;
    
    @JsonProperty("highest_price")
    private String highestPrice;
    
    @JsonProperty("origin_price")
    private String originPrice;
    
    @JsonProperty("highest_original_price")
    private String highestOriginalPrice;
    
    @JsonProperty("body_size_list")
    private List<BodySize> bodySizeList;
    
    @JsonProperty("size_type")
    private String sizeType;
} 