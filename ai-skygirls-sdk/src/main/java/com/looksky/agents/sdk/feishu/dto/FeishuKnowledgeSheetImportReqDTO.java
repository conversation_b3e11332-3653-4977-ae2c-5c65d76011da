package com.looksky.agents.sdk.feishu.dto;

import java.util.List;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class FeishuKnowledgeSheetImportReqDTO {

    /**
     * 映射的redis的Key（获取知识表的可以）
     */
    private String redisKey;
    /**
     * 飞书表格token（从飞书表格链接中获取）
     */
    private String spreadsheetToken;
    /**
     * 飞书表格Id（从飞书表格链接中获取）
     */
    private String sheetId;
    /**
     * 从第几行开始（去除表头-文字部分）
     */
    private Integer headRowNumber = 1;
    /**
     * 如果有需要满足多条件，这里填写 “满足数量” 那一列的索引
     * 从0开始算
     */
    private Integer quantityConditionColumnIndex;
    /**
     * 每一列的定义
     */
    private List<KnowledgeSheetColumn> sheetColumnList;

    /**
     * 知识表列的映射
     */
    @Data
    private static class KnowledgeSheetColumn{

        /**
         * 列对应的索引 从0开始算
         */
        private Integer columnIndex;
        /**
         * 列对应的key
         */
        private String columnKey;
        /**
         * 处理字符串的格式
         */
        private ProcessString isProcessString = ProcessString.none;

    }

    private enum ProcessString{
        /**
         * 不处理
         */
        none,
        /**
         * 小写
         */
        toLowerCase,
        /**
         * 小写并将空格替换成下划线
         */
        replace
    }

    /**
     * 把飞书表格的数据转化为指定的json格式
     */
//    public JSONArray convertSheetDate2Json(Map<Integer, Map<Integer, String>> sheetDataMap, MergeRange[] mergeRanges){
//
//        Map<Integer,String> multiFlgMap = new HashMap<>();
//        AtomicInteger flgInt = new AtomicInteger(1);
//        if(this.quantityConditionColumnIndex != null && ArrayUtil.isNotEmpty(mergeRanges)){
//            Arrays.stream(mergeRanges).filter(cellExtra -> cellExtra.getStartColumnIndex().equals(this.quantityConditionColumnIndex) && cellExtra.getEndColumnIndex().equals(quantityConditionColumnIndex))
//                    .forEach(cellExtra -> {
//                        Integer firstRowIndex = cellExtra.getStartRowIndex();
//                        Integer lastRowIndex = cellExtra.getEndRowIndex();
//                        String multiFlg = "multiIdentifier" + flgInt.getAndIncrement();
//                        for (int i = firstRowIndex; i <= lastRowIndex; i++) {
//                            multiFlgMap.put(i, multiFlg);
//                        }
//                    });
//        }
//
//        JSONArray jsonArray = new JSONArray();
//        for (int i = 0; i < sheetDataMap.size(); i++) {
//
//            if(i < headRowNumber){
//                continue;
//            }
//
//            Map<Integer, String> rowDataMap = sheetDataMap.get(i);
//            JSONObject rowJson = new JSONObject();
//
//            sheetColumnList.forEach(column -> {
//
//                String columnData = rowDataMap.get(column.getColumnIndex());
//                if(StrUtil.isBlank(columnData)){
//                    return;
//                }
//                switch (column.getIsProcessString()){
//                    case replace:
////                        columnData = StrUtil.manipulateString(columnData);
//                        break;
//                    case toLowerCase:
//                        columnData = columnData.toLowerCase();
//                        break;
//                    default:
//                        break;
//                }
//
//
//                rowJson.put(column.getColumnKey(),columnData);
//            });
//
//            if(CollUtil.isNotEmpty(multiFlgMap)){
//                if(multiFlgMap.containsKey(i)){
//                    rowJson.put("multiIdentifier",multiFlgMap.get(i));
//                }else {
//                    rowJson.put("multiIdentifier","multiIdentifier" + flgInt.getAndIncrement());
//                }
//            }
//            jsonArray.add(rowJson);
//        }
//
//        return jsonArray;
//    }


}
