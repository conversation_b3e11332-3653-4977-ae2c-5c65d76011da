package com.looksky.agents.sdk.agent.preference;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class SubjectivePreference implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String occasion;
    private String season;
    private String style;
    @JsonProperty("wearing_effect")
    private String wearingEffect;
    @JsonProperty("extra_subjective_tags")
    private String extraSubjectiveTags;

    public static SubjectivePreference parse(String result) {
        SubjectivePreference subjective = JSONUtil.toBean(result, SubjectivePreference.class);
        if (subjective == null) {
            return null;
        }

        if (StrUtil.isBlankIfStr(subjective.getOccasion())) {
            subjective.setOccasion(null);
        }

        if (StrUtil.isBlankIfStr(subjective.getSeason()) || "none".equals(subjective.getSeason())) {
            subjective.setSeason(null);
        }

        if (StrUtil.isBlankIfStr(subjective.getStyle())) {
            subjective.setStyle(null);
        }

        if (StrUtil.isBlankIfStr(subjective.getWearingEffect())) {
            subjective.setWearingEffect(null);
        }

        if (StrUtil.isBlankIfStr(subjective.getExtraSubjectiveTags()) || "none".equals(subjective.getExtraSubjectiveTags()) || "null".equals(subjective.getExtraSubjectiveTags())) {
            subjective.setExtraSubjectiveTags(null);
        }

        if (subjective.getOccasion() == null && subjective.getSeason() == null && subjective.getStyle() == null && subjective.getWearingEffect() == null && subjective.getExtraSubjectiveTags() == null) {
            return null;
        }

        return subjective;

    }
}