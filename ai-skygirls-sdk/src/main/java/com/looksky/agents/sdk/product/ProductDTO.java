package com.looksky.agents.sdk.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class ProductDTO implements Serializable {
    private List<Tag> tags;
    
    @JsonProperty("1st_cat")
    private List<Tag> firstCategory;
    
    @JsonProperty("sub_cat")
    private List<Tag> subCategory;
    
    private List<Tag> topics;
    private Info info;
} 