package com.looksky.agents.sdk.utils.user;

import com.graecove.common.BusinessException;
import com.skygirls.biz.agent.enums.HeightTypeEnum;
import com.skygirls.biz.faceplusplus.enums.WeightTypeEnum;
import com.skygirls.biz.user.model.enums.MeasureTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

public class BMIUtils {

    @Data
    @AllArgsConstructor
    public static class BmiResult {
        private HeightTypeEnum heightType;
        private WeightTypeEnum bmiCategory;
        private double bmi;
    }

    public static BmiResult calculateBmiAndHeightType(MeasureTypeEnum measureType, String height, String weight) {

        try {
            double bmi;
            HeightTypeEnum heightType;
            WeightTypeEnum bmiCategory;

            if (measureType == MeasureTypeEnum.cmkg) {
                double heightCm = parseHeightInCm(height);
                double weightKg = parseWeightInKg(weight);
                bmi = weightKg / Math.pow(heightCm / 100, 2);
                heightType = HeightTypeEnum.calculateHeightTypeCm(heightCm);
            } else if (measureType == MeasureTypeEnum.inlbs) {
                double heightInches = parseHeightInInches(height);
                double weightLbs = parseWeightInLbs(weight);
                bmi = (weightLbs / Math.pow(heightInches, 2)) * 703;
                heightType = HeightTypeEnum.calculateHeightTypeInches(heightInches);
            } else {
                throw new IllegalArgumentException("Error calculating bmi and height type");
            }
            bmiCategory = WeightTypeEnum.fromBmiValue(bmi);
            return new BmiResult(heightType, bmiCategory, bmi);
        } catch (Exception e) {
            throw new BusinessException("Error calculating bmi and height type");
        }
    }

    private static double parseHeightInInches(String height) {
        String[] parts = height.split(" ");
        int feet = Integer.parseInt(parts[0].replace("'", ""));
        int inches = Integer.parseInt(parts[1].replace("\"", ""));
        return feet * 12 + inches;
    }

    private static double parseWeightInLbs(String weight) {
        return Double.parseDouble(weight.replace(" lbs", ""));
    }

    private static double parseHeightInCm(String height) {
        return Double.parseDouble(height.replace(" cm", ""));
    }

    private static double parseWeightInKg(String weight) {
        return Double.parseDouble(weight.replace(" kg", ""));
    }
}
