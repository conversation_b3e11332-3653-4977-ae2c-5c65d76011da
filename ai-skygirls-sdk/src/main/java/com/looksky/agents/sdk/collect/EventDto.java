package com.looksky.agents.sdk.collect;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 事件数据传输对象
 *
 * @since  1.1.11
 * <AUTHOR>
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventDto {
    @Schema(description = "用户ID")
    private String uid;

    private String customerId;
    private String appid = "SkyGirls";
    @Schema(description = "操作系统")
    private String os = "Linux";
    @Schema(description = "操作系统版本")
    private String osVer;
    @Schema(description = "设备")
    private String device;
    @Schema(description = "平台")
    private String platform = "";
    @Schema(description = "应用版本")
    private String appVer;
    @Schema(description = "网络")
    private String network;
    @Schema(description = "事件ID")
    private String eid;
    @Schema(description = "时间戳")
    private Long ts;
    @Schema(description = "是否为测试环境, 1 为是, 0 为否")
    private Integer test;
    @Schema(description = "国家")
    private String country;
    @Schema(description = "地区")
    private String region;

    // 特定事件类型的扩展字段
    @Schema(description = "页面类型")
    @JsonProperty("page_type")
    private String pageType;
    @Schema(description = "url")
    private String ext1;
    @Schema(description = "请求ID")
    private String ext2;
    @Schema(description = "采样率")
    private String ext3 = "1";
    @Schema(description = "执行状态, 1 为成功, 2 为失败")
    private String ext4;
    @Schema(description = "响应码")
    private String ext5;
    @Schema(description = "响应信息, success or errorMessage")
    private String ext6;
    @Schema(description = "运行时间")
    private String ext7;
    @Schema(description = "type")
    private String ext8;
    @Schema(description = "其他参数")
    private String ext9;
}
