package com.looksky.agents.sdk.agent.common.enums;

import lombok.Getter;

/**
 * 事件名枚举
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Getter
public enum EventNameEnum {
    USER_SPEAK("user_speak"),
    AGENT_SPEAK("fashion_shopper_speak"),
    USER_FEEDBACK("user_feedback"),
    USER_CLICK("user_click"),
    FEEDBACK_PREFERENCE("feedback_preference"),
    OPENING("opening"),
    ADVICE_QUESTIONS("advice_questions"),

    // 商详页打招呼
    HOME_NEW_USER_ENTER_FEED("home_new_user_enter_feed"),
    PROACTIVE_MARKETING("proactive_marketing"),
    QUERY_AFTER_RECOMMEND("query_after_recommend"),
    QUERY_SUGGESTION_RECOMMEND("query_suggestion_recommend"),


    // 找衣服, 三个快捷按钮
    SEASON_STYLE("season_style"),
    OCCASION_OUTFIT("occasion_outfit"),
    FIND_BY_CATEGORY("find_by_category")
    ;

    private final String value;

    EventNameEnum(String value) {
        this.value = value;
    }

    public static EventNameEnum fromValue(String value) {
        for (EventNameEnum eventNameEnum : EventNameEnum.values()) {
            if (eventNameEnum.getValue().equals(value)) {
                return eventNameEnum;
            }
        }
        return null;
    }

    public static boolean detailEvent(String value) {
        return HOME_NEW_USER_ENTER_FEED.getValue().equals(value) || PROACTIVE_MARKETING.getValue().equals(value) || QUERY_AFTER_RECOMMEND.getValue().equals(value) || QUERY_SUGGESTION_RECOMMEND.getValue().equals(value);
    }


    public static boolean isClickButton(String value) {
        return SEASON_STYLE.getValue().equals(value) || OCCASION_OUTFIT.getValue().equals(value) || FIND_BY_CATEGORY.getValue().equals(value);
    }
}
