package com.looksky.agents.sdk.agent.conversation;

import com.looksky.agents.sdk.agent.common.enums.RoleTypeEnum;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 用于存储会话历史
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Event implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String eventName;

    private String messageId;
    private RoleTypeEnum role;
    private String content;
    private LocalDateTime time;
} 