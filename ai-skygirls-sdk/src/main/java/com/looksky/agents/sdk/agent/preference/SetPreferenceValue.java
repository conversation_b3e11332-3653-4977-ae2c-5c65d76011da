package com.looksky.agents.sdk.agent.preference;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.looksky.agents.sdk.agent.common.enums.TagValueEnum;
import java.io.Serializable;
import java.util.HashSet;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SetPreferenceValue implements Serializable {
    private HashSet<String> like;
    private HashSet<String> dislike;


    public void setLike(HashSet<String> like) {
        if (like != null && !like.isEmpty()) {
            like.remove(TagValueEnum.NULL.getName());
            like.remove(TagValueEnum.NONE.getName());
        }
        this.like = like;
    }

    public void setDislike(HashSet<String> dislike) {
        if (dislike != null && !dislike.isEmpty()) {
            dislike.remove(TagValueEnum.NULL.getName());
            dislike.remove(TagValueEnum.NONE.getName());
        }
        this.dislike = dislike;
    }

    public static SetPreferenceValue parseJson(String json) {
        SetPreferenceValue preferenceValue = JSONUtil.toBean(json, SetPreferenceValue.class);
        if (preferenceValue == null || preferenceValue.isEmpty()) {
            return null;
        } else {
            // 判断里面是否包含 null, 如果包含空, 那么就设置为 null
            if (preferenceValue.getLike() != null &&
                containsNullOrNone(preferenceValue.getLike())) {
                preferenceValue.setLike(null);
            }
            if (preferenceValue.getDislike() != null &&
                containsNullOrNone(preferenceValue.getDislike())) {
                preferenceValue.setDislike(null);
            }

            if (ObjectUtil.isNotEmpty(preferenceValue.getLike()) || ObjectUtil.isNotEmpty(preferenceValue.getDislike())) {
                return preferenceValue;
            }
            return null;
        }
    }

    private static boolean containsNullOrNone(HashSet<String> set) {
        return set.contains(TagValueEnum.NULL.getName()) ||
            set.contains(TagValueEnum.NONE.getName());
    }

    public boolean isEmpty() {
        return (like == null || like.isEmpty()) && (dislike == null || dislike.isEmpty());
    }

    public static SetPreferenceValue merge(SetPreferenceValue curr, SetPreferenceValue prev) {
        if (curr == null) {
            return prev;
        }
        if (prev == null) {
            return curr;
        }

        SetPreferenceValue result = new SetPreferenceValue();
        HashSet<String> resultLike = new HashSet<>();
        HashSet<String> resultDislike = new HashSet<>();

        // 处理当前轮次的偏好
        if (curr.getLike() != null) {
            resultLike.addAll(curr.getLike());
        }
        if (curr.getDislike() != null) {
            resultDislike.addAll(curr.getDislike());
        }

        // 处理前一轮的偏好
        if (prev.getLike() != null) {
            // 添加前一轮喜欢的，但是当前轮没有明确表示不喜欢的值
            prev.getLike().stream()
                .filter(v -> curr.getDislike() == null || !curr.getDislike().contains(v))
                .forEach(resultLike::add);
        }
        if (prev.getDislike() != null) {
            // 添加前一轮不喜欢的，但是当前轮没有明确表示喜欢的值
            prev.getDislike().stream()
                .filter(v -> curr.getLike() == null || !curr.getLike().contains(v))
                .forEach(resultDislike::add);
        }

        // 处理冲突：如果一个值同时出现在喜欢和不喜欢中，以最新的偏好为准
        if (curr.getDislike() != null) {
            resultLike.removeAll(curr.getDislike());  // 移除当前轮明确表示不喜欢的值
        }
        if (curr.getLike() != null) {
            resultDislike.removeAll(curr.getLike());  // 移除当前轮明确表示喜欢的值
        }

        result.setLike(resultLike);
        result.setDislike(resultDislike);
        return result;

    }
}
