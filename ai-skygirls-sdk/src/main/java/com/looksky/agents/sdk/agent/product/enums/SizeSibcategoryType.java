package com.looksky.agents.sdk.agent.product.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Getter
@AllArgsConstructor
public enum SizeSibcategoryType {
    ONLY_ONE_SIZE("only_one_size"),
    NO_BODY_SIZE("no_body_size"), 
    NO_BODY_SIZE_WITHOUT_WEIGHT_HEIGHT("no_body_size_without_weight_height"),
    THREE_MEASURES_IN_ONE_SIZE("three_measures_in_one_size"),
    THREE_MEASURES_IN_ONE_SIZE_WITHOUT_BODY_SHAPE("three_measures_in_one_size_without_body_shape"),
    THREE_MEASURES_IN_TWO_SIZE("three_measures_in_two_size"),
    THREE_MEASURES_IN_THREE_SIZE("three_measures_in_three_size"),
    SIZE_TOO_LARGE("size_too_large"),
    SIZE_TOO_SMALL("size_too_small"),
    TWO_MEASURES_IN_ONE_SIZE("two_measures_in_one_size"),
    TWO_MEASURES_IN_TWO_SIZE("two_measures_in_two_size"),
    SIZE_BY_WEIGHT_HEIGHT("size_by_weight_height"),
    INQUIRE_USER_BODY_MEASURE("inquire_user_body_measure");

    private final String value;
}