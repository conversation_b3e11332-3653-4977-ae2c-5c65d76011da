package com.looksky.agents.sdk.agent.search.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryFeedBackDTO {
    private String direction;
    private List<String> category;
    private Map<String, List<String>> tag;
}
