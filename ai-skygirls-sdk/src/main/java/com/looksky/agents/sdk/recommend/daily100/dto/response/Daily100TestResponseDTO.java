package com.looksky.agents.sdk.recommend.daily100.dto.response;

import java.util.ArrayList;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class Daily100TestResponseDTO {

    //最后推荐的商品id
    private List<GirlsDaily100TestItem> totalItemList;

    //单路召回策略 - 召回的商品
    // k 召回策略， v 召回商品列表
    private Map<String, List<GirlsDaily100TestItem>> recallItemMap;


    //商品
    @Data
    public static class GirlsDaily100TestItem {

        //商品skcId
        private String itemId;
        //分数排序
        private Integer index;
        //商品url
        private String url;
        //商品排序打分
        private Double rankScore;
        //商品加权类型 kibbe:0.5*20 = 10;color:30;
        private String rankWeightedType;
        //商品标题
        private String title;
        //商品价格 - 现价
        private Double price;
        //商品价格 - 原价
        private Double originalPrice;
        //商品链接
        private String skcLink;
        //品牌名
        private String merchantName;

    }


    public List<String> getItemList() {
        ArrayList<String> itemList = new ArrayList<>();
        totalItemList.forEach(item -> itemList.add(item.getItemId()));
        recallItemMap.forEach((k, v) -> v.forEach(item -> itemList.add(item.getItemId())));
        return itemList;
    }


}