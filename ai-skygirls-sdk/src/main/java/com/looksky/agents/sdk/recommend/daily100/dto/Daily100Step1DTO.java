package com.looksky.agents.sdk.recommend.daily100.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "daily100 缓存的推荐数据")
public class Daily100Step1DTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "推荐内容")
    private String content;
    @Schema(description = "生成的时间")
    private Date generateDate;
    @Schema(description = "用户ID")
    private String userId;
    @Schema(description = "城市")
    private String city;
    @Schema(description = "季节")
    private String season;
    @Schema(description = "区域")
    private String zone;
}
