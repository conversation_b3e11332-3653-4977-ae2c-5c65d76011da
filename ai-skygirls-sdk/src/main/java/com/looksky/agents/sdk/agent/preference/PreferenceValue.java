package com.looksky.agents.sdk.agent.preference;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PreferenceValue {
    private String textValue;

    /**
     * 为了满足多层级标签的存储需求，使用 Map 存储
     */
    private Map<String, String> mapValue;
    
    // 静态工厂方法
    public static PreferenceValue ofText(String text) {
        PreferenceValue value = new PreferenceValue();
        value.textValue = text;
        return value;
    }
    
    public static PreferenceValue ofMap(Map<String, String> map) {
        PreferenceValue value = new PreferenceValue();
        value.mapValue = map;
        return value;
    }
    
    // 判断值类型的方法
    public boolean isText() {
        return textValue != null;
    }
    
    public boolean isMap() {
        return mapValue != null;
    }

    public static PreferenceValue parseJson(Object json) {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.valueToTree(json);
        PreferenceValue value = new PreferenceValue();
        if (jsonNode.isObject()) {
            Map<String, String> mapValue = objectMapper.convertValue(jsonNode, new TypeReference<>() {});

            // 过滤 map 中的元素
            mapValue = mapValue.entrySet().stream()
                    .filter(entry -> entry.getValue() != null && !"null".equals(entry.getValue()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            value.setMapValue(mapValue);
        } else {
            String text = jsonNode.asText();
            if (text != null && !"null".equals(text)) {
                value.setTextValue(text);
            }
        }
        return value;
    }

    public boolean isNotEmpty() {
        return StringUtils.hasText(textValue) || !ObjectUtil.isEmpty(mapValue);
    }
}