package com.looksky.agents.sdk.agent.search.dto;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class SearchProcessDTO {
    private List<Reason> reasons;
    private boolean isSearch;

    @Data
    public static class Reason {
        private String title;
        // 过滤后的商品数量
        @JsonProperty("filter_numbers")
        private int filterNumbers;

        // 过滤前的商品数量
        @JsonProperty("candidates_numbers")
        private int candidatesNumbers;
        @JsonProperty("reason_detail")
        private String reasonDetail;
        private List<LinkItem> links;


        @Data
        public static class LinkItem {
            private String url;
            private String title;
        }
    }

    // 把他转为json 字符串, 供转正负向量词使用
    public static String toPreferenceData(SearchProcessDTO searchProcessDTO) {
        SearchProcessDTO result = new SearchProcessDTO();
        // 深拷贝
        List<Reason> reasons = searchProcessDTO.getReasons().stream()
            .map(reason -> {
                Reason newReason = new Reason();
                newReason.setTitle(reason.getTitle());
                newReason.setFilterNumbers(reason.getFilterNumbers());
                newReason.setCandidatesNumbers(reason.getCandidatesNumbers());
                newReason.setReasonDetail(reason.getReasonDetail());
                return newReason;
            }).toList();
        result.setReasons(reasons);
        return JSONUtil.toJsonStr(result);
    }

}
