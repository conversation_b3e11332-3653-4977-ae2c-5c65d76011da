package com.looksky.agents.sdk.agent.prompt.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-21 14:14:56
 */
@Getter
@Setter
@Builder
@TableName("t_prompt_metadata")
@Schema( description = "PromptMetadata对象")
public class PromptMetadataModel implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键 id")
    @TableId(value = "pk_id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "关联 t_prompt 的 pk_id")
    private String promptId;

    @Schema(description = "元数据的 key")
    private String metadataKey;

    @Schema(description = "元数据的 value")
    private String metadataValue;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "创建时间")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @Schema(description = "更新时间")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "更新人")
    private String updatedBy;

    @Schema(description = "删除标记: 0 启用 1 不启用")
    @TableLogic
    private Boolean deleted;


    public static PromptMetadataModel tagValueMetaModel(String value) {
        return PromptMetadataModel.builder()
                .metadataKey("tag")
                .metadataValue(value)
                .build();
    }



    public PromptMetadataModel copy() {
        PromptMetadataModel copy = PromptMetadataModel.builder().build();

        // 复制基本属性
        copy.setId(this.getId());
        copy.setPromptId(this.getPromptId());
        copy.setMetadataKey(this.getMetadataKey());
        copy.setMetadataValue(this.getMetadataValue());
        copy.setDescription(this.getDescription());

        // 复制时间和操作者信息
        copy.setCreatedTime(this.getCreatedTime());
        copy.setUpdatedTime(this.getUpdatedTime());
        copy.setCreatedBy(this.getCreatedBy());
        copy.setUpdatedBy(this.getUpdatedBy());
        copy.setDeleted(this.getDeleted());

        return copy;
    }

}
