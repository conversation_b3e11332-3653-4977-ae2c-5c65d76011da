package com.looksky.agents.sdk.recommend.foryou.dto;

import com.looksky.agents.sdk.recommend.common.dto.VectorRecallModelDTO;
import com.looksky.agents.sdk.recommend.foryou.enums.RecommendScenEnum;
import java.util.List;
import lombok.Data;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class PartitionRecomModelDTO {
    private RecommendScenEnum partitionRecomScenes;
    private String hashtag;
    private String title;
    private List<VectorRecallModelDTO> recallVectors;
    private String clientDayTime;
}
