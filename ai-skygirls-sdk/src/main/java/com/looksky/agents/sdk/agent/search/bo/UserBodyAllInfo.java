package com.looksky.agents.sdk.agent.search.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Deprecated
public class UserBodyAllInfo {

    @JsonProperty("chest_circumference")
    private Float chestCircumference;

    @JsonProperty("waist_circumference")
    private Float waistCircumference;

    @JsonProperty("hip_circumference")
    private Float hipCircumference;

    private String unit;
    private Integer age;
    private Weight weight;
    private Height height;

    @JsonProperty("shoulder_width")
    private Float shoulderWidth;

    @JsonProperty("upper_to_lower_body_ratio")
    private String upperToLowerBodyRatio; // enum: "short_body_long_leg", "long_body_short_leg", "balanced", "none"

    @JsonProperty("skin_undertone")
    private String skinUndertone; // enum: "cool", "warm", "neutral", "none"

    @JsonProperty("hair_color")
    private String hairColor;

    @JsonProperty("eye_color")
    private String eyeColor; // enum: "blue", "green", "brown", "gray", "hazel", "mixed", "amber", "none"

    @JsonProperty("body_shape")
    private String bodyShape;

    @JsonProperty("shoulder_shape")
    private String shoulderShape; // enum: "sloping", "square", "rounded", "none"

    @JsonProperty("face_shape")
    private String faceShape; // enum: "round", "square", "diamond", "oval", "heart", "triangle", "none"

    @JsonProperty("leg_shape")
    private String legShape; // enum: "straight", "bow", "knock-kneed", "diamond", "curvy", "none"

    @JsonProperty("shoulder_type")
    private String shoulderType; // enum: "narrow", "wide", "standard", "none"

    @JsonProperty("tops_size")
    private String topsSize;

    @JsonProperty("jacket_size")
    private String jacketSize;

    @JsonProperty("dresses_size")
    private String dressesSize;

    @JsonProperty("set_size")
    private String setSize;

    @JsonProperty("one_piece_size")
    private String onePieceSize;

    @JsonProperty("bottoms_size")
    private String bottomsSize;

    @JsonProperty("other_body_data")
    private String otherBodyData;

    @JsonProperty("kibbe_type")
    private String kibbeType; // enum: "dramatic", "soft_dramatic", "flamboyant_natural", ...

    @JsonProperty("shoulder_to_hip_ratio")
    private String shoulderToHipRatio; // enum: "broad_shoulders/narrow_hips", "narrow_shoulders/wide_hips", "balanced_shoulders/hips", "none"

    @JsonProperty("body_areas_to_emphasize_or_downplay")
    private String bodyAreasToEmphasizeOrDownplay;

    @JsonProperty("expressed_body_shape")
    private String expressedBodyShape;

    @JsonProperty("expressed_skin_color")
    private String expressedSkinColor;

    @JsonProperty("expressed_face_shape")
    private String expressedFaceShape;

    @Data
    public static class Weight {
        private Float weight;
        private String unit;
    }

    @Data
    public static class Height {
        private Float height;
        private String unit;
    }

}
