package com.looksky.agents.sdk.tryon.swapcloth.dto;


import com.skygirls.biz.user.model.enums.MeasureTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class SwapClothParam {
    @Schema(description = "模特照片")
    private String modelImage;
    @Schema(description = "用户的全身照照片")
    private String fullBodyImage;
    @Schema(description = "用户的脸部照片")
    private String faceImage;
    private String userId;
    private String id;
    private String skcId;
    private String skuId;
    private String weight;
    private String height;
    private MeasureTypeEnum measureType;
    private String category = "one-pieces";
    private String type = "auto";
}
