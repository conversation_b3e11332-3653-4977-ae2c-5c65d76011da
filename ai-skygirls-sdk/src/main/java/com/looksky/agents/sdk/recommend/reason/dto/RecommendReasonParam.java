package com.looksky.agents.sdk.recommend.reason.dto;

import com.looksky.agents.sdk.recommend.foryou.enums.RecommendScenEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 推荐理由接收请求参数对象
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class RecommendReasonParam {
    @Schema(description = "用户")
    private String userId;

    @Schema(description = "季节")
    private String season;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "商品id")
    private List<String> itemIds;

    @Schema(description = "多少商品一批次")
    private Integer batchSize;

    @Schema(description = "推荐场景")
    private RecommendScenEnum scene;

}