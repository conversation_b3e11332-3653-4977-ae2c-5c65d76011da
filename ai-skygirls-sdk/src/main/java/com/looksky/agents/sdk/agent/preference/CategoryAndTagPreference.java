package com.looksky.agents.sdk.agent.preference;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class CategoryAndTagPreference implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @JsonProperty("userpreference_list")
    private Set<CategoryAndTags> categories;
    @JsonProperty("cloth_index")
    private Integer clothIndex;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CategoryAndTags {
        private String category;
        @JsonProperty("label_type_list")
        private Set<String> labelTypeList;
    }

    public Set<String> getAllCategories() {
        return categories.stream().map(CategoryAndTags::getCategory).collect(Collectors.toCollection(HashSet::new));
    }


    public Set<String> getTags() {
        return categories.stream().flatMap(categoryAndTags -> categoryAndTags.getLabelTypeList().stream()).collect(Collectors.toCollection(HashSet::new));
    }
}
