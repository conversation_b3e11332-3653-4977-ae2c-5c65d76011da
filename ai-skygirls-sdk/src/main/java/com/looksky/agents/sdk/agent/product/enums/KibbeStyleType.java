package com.looksky.agents.sdk.agent.product.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Getter
@AllArgsConstructor
public enum KibbeStyleType {
    DRAMATIC("dramatic"),
    SOFT_DRAMATIC("soft_dramatic"),
    FLAMBOYANT_NATURAL("flamboyant_natural"),
    NATURAL("natural"),
    SOFT_NATURAL("soft_natural"),
    DRAMATIC_CLASSIC("dramatic_classic"),
    CLASSIC("classic"),
    SOFT_CLASSIC("soft_classic"),
    FLAMBOYANT_GAMINE("flamboyant_gamine"),
    GAMINE("gamine"),
    SOFT_GAMINE("soft_gamine"),
    THEATRICAL_ROMANTIC("theatrical_romantic"),
    ROMANTIC("romantic");

    private final String value;
}