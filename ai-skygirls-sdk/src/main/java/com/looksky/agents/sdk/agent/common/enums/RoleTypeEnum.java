package com.looksky.agents.sdk.agent.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 角色类型
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Getter
public enum RoleTypeEnum {
    USER("User"),
    AGENT("Skylar"),
    SYSTEM("system")
    ;

    @JsonValue
    private final String value;

    RoleTypeEnum(String value) {
        this.value = value;
    }

    public static String buildUserMessage(String message) {
        return RoleTypeEnum.USER.value + ": " + message;
    }

    public static String buildAgentMessage(String message) {
        return RoleTypeEnum.AGENT.value + ": " + message;
    }

    public static String buildSystemMessage(String message) {
        return RoleTypeEnum.SYSTEM.value + ": " + message;
    }

}