package com.looksky.agents.sdk.tryon.swapcloth.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SwapClothWorkflowReq {
    @JsonProperty("client_id")
    private String clientId;
    private Object prompt;
}
