package com.looksky.agents.sdk.agent.search.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <p>
 * 定义过滤步骤的类，用于存储每轮的过滤前后数量
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FilterStepDTO {
    private int round;           // 当前轮次
    private int beforeCount;     // 过滤前的商品数量
    private int afterCount;      // 过滤后的商品数量


    @Override
    public String toString() {
        return "第 " + round + " 轮: 过滤前 = " + beforeCount + ", 过滤后 = " + afterCount;
    }
}