package com.looksky.agents.sdk.agent.common.enums;

import cn.hutool.core.text.CharSequenceUtil;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 标签值枚举
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Getter
@AllArgsConstructor
public enum TagValueEnum {
    OTHER("other", "其他"),
    NULL("null", "空"),
    NONE("none", "无"),
    OTHER_PATTERN("other_patterns", "其他图案"),

    ;

    private final String name;
    private final String description;

    public static boolean isNotNull(String name) {
        return CharSequenceUtil.isNotBlank(name) && !NULL.getName().equals(name) && !NONE.getName().equals(name);
    }

    public static List<String> getBadValues() {
        return List.of(OTHER.getName(), NULL.getName(), NONE.getName(), OTHER_PATTERN.getName());
    }

}
