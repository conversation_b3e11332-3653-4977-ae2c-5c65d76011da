package com.looksky.agents.sdk.agent.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 用于临时存储当前的类别和标签, 在抽词时, 会根据对象的品类和标签名进行动态替换
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
public class CurrentCategoryTagDTO {
    @Schema(description = "品类")
    private String category;

    @Schema(description = "标签名")
    private String tagName;
}
