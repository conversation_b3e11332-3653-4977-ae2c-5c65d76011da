package com.looksky.agents.sdk.agent.product.enums;

import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Getter
public enum SearchQuestionType {
    /**
     * 场景：有主观标签，且还没细化需求
     * 策略：进一步细化用户需求
     */
//    SUBJECTIVE_WITHOUT_ASK("subjective_without_ask"),
    SUBJECTIVE_WITHOUT_ASK("search_cloth_with_subjective_preference"),

    /**
     * 场景：大模型认为应该追问，且明确了要追问的内容
     * 策略：进一步细化用户需求
     */
//    INQUIRE_USER_INFO("inquire_user_info"),
    @Deprecated
    INQUIRE_USER_INFO("inquire_user_personal_info"),

    /**
     * 场景：有一级品类，没有二级品类，没有咨询过用户二级品类
     * 策略：咨询二级品类
     */
//    FIRST_CATEGORY_WITHOUT_SUBCATEGORY_WITHOUT_ASK_SUBCATEGORY("firstcategory_without_subcategory_without_ask"),
    FIRST_CATEGORY_WITHOUT_SUBCATEGORY_WITHOUT_ASK_SUBCATEGORY("inquire_user_subcategory_preference"),

    /**
     * 场景：有二级品类，不够两个标签，没有主观词,没有咨询过标签
     * 策略：咨询标签
     */
//    SUBCATEGORY_WITH_LESS_TWO_TAG_WITHOUT_ASK("subcategory_with_two_less_tag_without_ask"),
    SUBCATEGORY_WITH_LESS_TWO_TAG_WITHOUT_ASK("inquire_user_tag_preference"),

    /**
     * 场景：有二级品类 + 两个及以上客观标签，直接推荐衣服
     * 策略：根据标签，直接推荐衣服
     */
//    SUBCATEGORY_WITH_TWO_MORE_TAG("subcategory_with_two_more_tag"),
//    SUBCATEGORY_WITH_TWO_MORE_TAG("recommend_product_with_enough_condition"),
    SUBCATEGORY_WITH_TWO_MORE_TAG("search_process_first_reply"),

    /**
     * 场景：仅有主观词,还没有咨询过穿衣方案
     * 策略：出解决方案
     */
//    SUBJECTIVE_WITHOUT_ASK_SOLUTION("subjective_without_ask_solution");
    SUBJECTIVE_WITHOUT_ASK_SOLUTION("inquire_user_solution"),


    /**
     * 发送选择器
     */
    SELECTOR("search_send_selector");

    private final String value;

    SearchQuestionType(String value) {
        this.value = value;
    }

}