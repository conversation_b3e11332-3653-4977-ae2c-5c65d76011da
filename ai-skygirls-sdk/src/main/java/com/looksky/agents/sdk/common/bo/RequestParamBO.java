package com.looksky.agents.sdk.common.bo;

import com.looksky.agents.sdk.enums.OutputModelEnum;
import lombok.Data;

import java.util.List;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class RequestParamBO {
    private String promptName;
    private String description;
    private String systemPrompt;
    private String userPrompt;
    private List<String> imageUrl;
    private String modelName;
    private Double temperature;
    private String jsonSchema;
    /**
     * 输出的是什么对象
     */
    private OutputModelEnum outputType;
    private boolean streaming;
    private int size;

    private List<String> parameter;
    private Object data;
}
