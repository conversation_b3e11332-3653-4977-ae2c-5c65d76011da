package com.looksky.agents.sdk.agent.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 品类枚举
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Getter
@AllArgsConstructor
public enum CategoryEnum {
    CLOTH("cloth", "服装"),
    DRESSES("dresses", "连衣裙"),
    PRICE("price", "价格"),
    BRAND("brand", "品牌"),
    SWIMWEAR("swimwear", "泳衣"),
    SET("set", "套装")
    ;

    private final String name;
    private final String description;
}
