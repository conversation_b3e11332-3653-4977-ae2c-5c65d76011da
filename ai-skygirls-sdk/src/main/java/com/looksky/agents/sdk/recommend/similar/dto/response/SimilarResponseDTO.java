package com.looksky.agents.sdk.recommend.similar.dto.response;

import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimilarResponseDTO {
    private List<ItemDTO> items;
    private long size;
    private String requestId;

    public static List<String> getItemList(List<ItemDTO> items) {
        return items.stream()
                .map(ItemDTO::getItemId)
                .toList();
    }
}