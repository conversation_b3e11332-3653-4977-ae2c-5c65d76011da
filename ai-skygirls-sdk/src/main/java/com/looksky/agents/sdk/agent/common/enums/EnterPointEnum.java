package com.looksky.agents.sdk.agent.common.enums;

import com.skygirls.biz.im.dto.MessageTypeEnum;
import lombok.Getter;

/**
 * 进入点枚举
 *
 * <AUTHOR>
 * @since 1.0
 **/
@Getter
public enum EnterPointEnum {
    SEARCH_AFTER_RECOMMEND("search_after_recommand"),
    DETAIL("detail"),
    SEARCH("search"),
    SEARCH_BEFORE_RECOMMEND("search_before_recommand"),
    DRESSING("dressing"),
    MAKEUP("makeup"),
    HAIRSTY<PERSON>("hairstyle"),
    HOME("home"),
    STYLE_HACKS("style_hacks"),
    VIBE_SCAN("vibe_scan"),
    CLOSET("closet"),
    PREFECT_MATCH_PANEL("prefect_match_panel"),
    BOLD_EXPLORATION_PANEL("bold_exploration_panel"),
    FOLLOW_BRANDS_PANEL("follow_brands_panel"),
    OCCASIONS_PANEL("occasions_panel"),
    VERSATILE_PANEL("versatile_panel"),
    TIDE_PANEL("tide_panel"),
    DIS_COUNT_PANEL("discount_panel"),
    TRY_ON_UPLOAD_PHOTO("tryOn_uploadPhoto"),
    TRY_ON_PROCESSING("tryOn_processing"),
    TRY_ON_DETAIL("tryOn_detail"),


    ;

    private final String value;

    EnterPointEnum(String value) {
        this.value = value;
    }

    public static EnterPointEnum fromValue(String value, MessageTypeEnum messageType) {
        if (messageType != null) {
            if (MessageTypeEnum.HAIRSTYLE == messageType) {
                return HAIRSTYLE;
            }
            if (MessageTypeEnum.DRESSING == messageType) {
                return DRESSING;
            }
            if (MessageTypeEnum.MAKEUP == messageType) {
                return MAKEUP;
            }
        }
        for (EnterPointEnum enterPointEnum : EnterPointEnum.values()) {
            if (enterPointEnum.getValue().equals(value)) {
                return enterPointEnum;
            }
        }
        return null;
    }


    public static boolean isRecommend(EnterPointEnum enterPointEnum) {
        return SEARCH_AFTER_RECOMMEND == enterPointEnum || SEARCH_BEFORE_RECOMMEND == enterPointEnum;
    }
}