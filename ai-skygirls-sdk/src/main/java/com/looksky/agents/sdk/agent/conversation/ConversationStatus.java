package com.looksky.agents.sdk.agent.conversation;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * 用于存储会话状态
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConversationStatus implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 这个字段存储的是总共的会话轮次
     */
    private AtomicInteger conversationNumber;
    private AtomicInteger inquireUserInfoNumber;
    private Boolean askSubcategory;
    /**
     * 是否是询问标签
     */
    private Boolean askTag;
    /**
     * 是否需要细化需求
     */
    private Boolean refiningRequirements;
    private Boolean recommendProduct;
    private Boolean newConversation;
    private Boolean categoryChange;
    private Boolean showSearchProcess;

    /**
     * 这个自带存储的是会话轮次的变化, 例如需要加一轮, 那么就传 1
     */
    private Integer conversationNumberOption;
}
