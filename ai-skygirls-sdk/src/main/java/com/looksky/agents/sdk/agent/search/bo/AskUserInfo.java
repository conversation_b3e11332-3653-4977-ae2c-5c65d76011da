package com.looksky.agents.sdk.agent.search.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class AskUserInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    
    private String thought;
    
    @JsonProperty("is_ask_user")
    private boolean isAskUser;
    
    @JsonProperty("info_to_ask")
    private List<String> infoToAsk;

}
