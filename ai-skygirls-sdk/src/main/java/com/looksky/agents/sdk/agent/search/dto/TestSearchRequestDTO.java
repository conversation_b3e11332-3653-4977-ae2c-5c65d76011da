package com.looksky.agents.sdk.agent.search.dto;

import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import java.util.List;
import lombok.Data;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class TestSearchRequestDTO {

    private String userId;
    private String season;
    private List<String> subCategoryList;
    private List<SearchRequestDTO.VectorQuery> step1VectorQuery;
    private List<SearchRequestDTO.VectorQuery> step2VectorQuery;
    private List<SearchRequestDTO.VectorQuery> step3VectorQuery;


    public static TestSearchRequestDTO fromGirlsAgentSearchRequestDTO(SearchRequestDTO requestDTO) {
        TestSearchRequestDTO testSearchRequestDTO = new TestSearchRequestDTO();
        testSearchRequestDTO.setUserId(requestDTO.getUserId());
        testSearchRequestDTO.setSeason(requestDTO.getSeason());
        testSearchRequestDTO.setSubCategoryList(requestDTO.getStrategyTerms().getFirst().getSearchTerm().getCategories());
        testSearchRequestDTO.setStep1VectorQuery(requestDTO.getStrategyTerms().getFirst().getStep1VectorQueries());
        testSearchRequestDTO.setStep2VectorQuery(requestDTO.getStrategyTerms().getFirst().getStep2VectorQueries());
        testSearchRequestDTO.setStep3VectorQuery(requestDTO.getStrategyTerms().getFirst().getStep3VectorQueries());
        return testSearchRequestDTO;
    }
}