package com.looksky.agents.sdk.tryon.swapcloth.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <p>
 * 换衣, 查询 comfy ui 工作流 状态 的请求参数
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class SwapClothStatusRequest {
    private String status;
    @JsonProperty("prompt_id")
    private String promptId;
    @JsonProperty("client_id")
    private String clientId;
}
