package com.looksky.agents.sdk.agent.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 页面枚举
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Getter
@AllArgsConstructor
public enum PageEnum {
    // 商详页, daily100 直接对话
    CHAT_BOX("chat_box"),
    // ai stylist
    SEARCH("search"),
    // fou you
    FOR_YOU("for_you"),
    // try on
    TRY_ON("try_on"),
    // home
    HOME("home"),

    VIBE_SCAN("vibe_scan"),

    // 闲聊页面
    STYLE_HACKS("styleHacks"),

    // 衣橱
    CLOSET("closet"),
    ;
    private final String name;
}
