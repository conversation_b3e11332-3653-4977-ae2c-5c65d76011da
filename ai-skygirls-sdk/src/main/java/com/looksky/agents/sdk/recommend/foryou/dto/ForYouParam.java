package com.looksky.agents.sdk.recommend.foryou.dto;

import java.util.List;
import lombok.Data;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class ForYouParam {
    private String userId;
    //private String date;
    private String clientDayTime;
    private List<PartitionRecomModelDTO> partitionRecomModels;

    public List<String> getPartitionRecomScenes() {
        return partitionRecomModels.stream()
                .map(p -> p.getPartitionRecomScenes().getName())
                .toList();
    }
}