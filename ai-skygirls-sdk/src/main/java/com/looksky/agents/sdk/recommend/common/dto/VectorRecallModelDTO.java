package com.looksky.agents.sdk.recommend.common.dto;

import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VectorRecallModelDTO {
    private List<SearchRequestDTO.VectorQuery> vectorQuery;
    //private List<SearchRequestDTO.VectorQuery> vectorQueries;
    private String recallStrategy;
    private List<String> mustSubCategory;
    private List<String> mustNotSubCategory;

    //
    //public List<SearchRequestDTO.VectorQuery> getVectorQueries() {
    //    return this.vectorQuery;
    //}
    //
    //public List<SearchRequestDTO.VectorQuery> getVectorQuery() {
    //    if (this.vectorQuery == null) {
    //        return this.vectorQueries;
    //    }
    //    return this.vectorQuery;
    //}

}