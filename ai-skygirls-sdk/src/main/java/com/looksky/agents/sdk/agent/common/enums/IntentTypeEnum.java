package com.looksky.agents.sdk.agent.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 意图类型枚举
 *
 * <AUTHOR>
 * @since  1.0
 **/
public enum IntentTypeEnum {
    KIBBE("kibbe", "Kibbe类型相关"),
    APP_INTRODUCE("app_introduce", "应用介绍"),
    SMALL_TALK_IN_SEARCH("small_talk_in_search", "搜索中的闲聊"),
    SEARCH_CLOTH("search_cloth", "搜索服装"),
    SEARCH_SIMILAR_CLOTH("search_similar_cloth", "搜索相似服装"),
    SEARCH_NON_CLOTH("search_non_cloth", "搜索非服装物品"),
    INQUIRE_CLOTHING_DETAIL("inquire_clothing_detail", "询问服装详情"),
    FEEDBACK_TOO_EXPENSIVE("feedback_too_expensive", "反馈价格太贵"),
    RECOMMEND_PRODUCT("recommend_product", "推荐商品"),
    SIZE("size", "询问尺码"),



    ;

    private final String type;

    @Getter
    private final String description;

    IntentTypeEnum(String type, String description) {
        this.type = type;
        this.description = description;
    }

    public static IntentTypeEnum getByType(String code) {
        for (IntentTypeEnum value : values()) {
            if (value.getType().equals(code)) {
                return value;
            }
        }
        return null;
    }

    @JsonValue
    public String getType() {
        return type;
    }

    /**
     * 是否是搜索相关的意图
     * @param name
     * @return
     */
    public static boolean isSearch(IntentTypeEnum name) {
        return SEARCH_CLOTH == name || SEARCH_SIMILAR_CLOTH == name || RECOMMEND_PRODUCT == name;
    }

    public static boolean inquireProduct(IntentTypeEnum name) {
        return INQUIRE_CLOTHING_DETAIL == name;
    }

}
