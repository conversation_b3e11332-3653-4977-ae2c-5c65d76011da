package com.looksky.agents.sdk.utils.string;

/**
 * 字符串工具类扩展，提供额外的字符串处理功能
 */
public class StringUtilsExt {
    /**
     * 将字符串中每个单词的首字母转为大写
     *
     * @param str 需要转换的字符串
     * @return 转换后的字符串
     */
    public static String capitalizeWords(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        char[] chars = str.toCharArray();
        boolean capitalizeNext = true;

        for (int i = 0; i < chars.length; i++) {
            if (Character.isWhitespace(chars[i])) {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                chars[i] = Character.toUpperCase(chars[i]);
                capitalizeNext = false;
            }
        }

        return new String(chars);
    }

    /**
     * 将字符串的第一个字母转为大写
     *
     * @param str 需要转换的字符串
     * @return 转换后的字符串
     */
    public static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }
}
