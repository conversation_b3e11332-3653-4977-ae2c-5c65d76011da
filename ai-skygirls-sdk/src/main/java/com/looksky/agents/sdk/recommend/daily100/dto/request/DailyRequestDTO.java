package com.looksky.agents.sdk.recommend.daily100.dto.request;

import com.looksky.agents.sdk.recommend.common.dto.VectorRecallModelDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DailyRequestDTO {
    private String userId;
    private String date;
    private String versionCode;
    private Integer offset;
    private Integer limit;
    private List<VectorRecallModelDTO> categoryVectorRecall;
    private List<VectorRecallModelDTO> totalVectorRecall;
    private String vectorQueryVersion;
}