package com.looksky.agents.sdk.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import java.util.List;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
public class BodySize {
    private String label;
    private String value;
    
    @JsonProperty("size_type")
    private String sizeType;
    
    @JsonProperty("size_list")
    private List<String> sizeList;
    
    private String unit;
} 