package com.looksky.agents.sdk.recommend.similar.dto.request;

import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SimilarRequestDTO {
    private String userId;
    private String itemId;
    private String season;
    private SearchRequestDTO.SearchTerm searchTerm;
}