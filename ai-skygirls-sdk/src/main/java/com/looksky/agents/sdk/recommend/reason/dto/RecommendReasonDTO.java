package com.looksky.agents.sdk.recommend.reason.dto;

import com.looksky.agents.sdk.recommend.foryou.enums.RecommendScenEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 *  解析推荐理由 返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-20 21:48:15
 */
@Getter
@Setter
@Schema(description = "girls版本推荐理由对象")
public class RecommendReasonDTO {

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "商品id")
    private String itemId;

    @Schema(description = "title")
    private String prologue;

    @Schema(description = "推荐理由")
    private String reason;

    @Schema(description = "推荐场景")
    private RecommendScenEnum scene;

}
