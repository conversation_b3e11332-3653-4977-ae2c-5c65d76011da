package com.looksky.agents.sdk.recommend.colorseason.request;

import com.looksky.agents.sdk.recommend.common.dto.VectorRecallModelDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ColorSeasonRequestDTO {
    private String userId;
    private List<VectorRecallModelDTO> vectorRecall;
    private String vectorQueryVersion;
    // 这里跟推荐的请求不一样，推荐的请求是date，这里是clientDayTime
    //private String clientDayTime;
    private String date;

}