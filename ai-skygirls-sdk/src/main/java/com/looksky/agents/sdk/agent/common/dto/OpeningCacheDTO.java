package com.looksky.agents.sdk.agent.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用于缓存开场白的DTO对象
 *
 * <AUTHOR>
 * @since  2025-3-15 10:22:00
 **/
@Data
public class OpeningCacheDTO {
    @Schema(description = "有邀请码但是没有解锁")
    private String haveRegisterCodeLock;
    @Schema(description = "没有邀请码也没有解锁")
    private String noRegisterCodeLock;
    @Schema(description = "解锁了")
    private String unlock;

    @Schema(description = "未上传照片")
    private String notUploaded;
    @Schema(description = "已经上传照片")
    private String uploaded;
    @Schema(description = "分析中")
    private String analysis;
}
