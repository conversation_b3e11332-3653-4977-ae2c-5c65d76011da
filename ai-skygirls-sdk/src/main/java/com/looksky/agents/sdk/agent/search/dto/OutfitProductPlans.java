package com.looksky.agents.sdk.agent.search.dto;

import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutfitProductPlans {
    private String summary;
    private List<PlansDetail> plans;
    private String conclusion;

    public List<ItemDTO> getRecommendItem() {
        if (plans == null) {
            return Collections.emptyList();
        }
        return plans.stream().filter(Objects::nonNull).map(PlansDetail::getProductIdList).filter(Objects::nonNull).flatMap(List::stream).toList();
    }
}