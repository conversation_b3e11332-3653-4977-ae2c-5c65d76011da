package com.looksky.agents.sdk.agent.ext;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.skygirls.biz.im.dto.MessageRestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RequestVo extends MessageRestDTO {
    private boolean isRetry;
    private String strategyName;
    private String taskId;
}
