package com.looksky.agents.sdk.agent.ext;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.skygirls.biz.im.dto.AgentMessageRespV3;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Optional;

//@Builder
//@AllArgsConstructor
//@NoArgsConstructor
public class AgentMessageResp extends AgentMessageRespV3 {

    
    @Override
    @JsonIgnore  // 忽略父类的 time 字段
    public Date getTime() {
        return super.getTime();
    }
    
    @Override
    @JsonIgnore  // 忽略父类的 time 字段
    public AgentMessageRespV3 setTime(Date time) {
        super.setTime(time);
        return this;
    }
    
    // 新增 timestamp 字段
    @JsonProperty("time")  // 序列化时使用 "time" 作为字段名
    public long getTimestamp() {
        return Optional.ofNullable(super.getTime())
                .map(Date::getTime)
                .orElseGet(() -> LocalDateTime.now()
                        .atZone(ZoneId.systemDefault())
                        .toInstant()
                        .toEpochMilli());
    }

    public void setLocalDateTime(LocalDateTime localDateTime) {
        super.setTime(Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()));
    }
}