package com.looksky.agents.sdk.optimize.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Map;
import lombok.Data;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class DatasetRecordDTO {
    
    @Schema(description = "所属数据集ID", example = "1864647886066765824")
    private String datasetId;
    
    @Schema(description = "记录索引，用于排序", example = "1")
    private Integer recordIndex;
    
    @Schema(description = "记录数据内容")
    private Map<String, Object> data;
} 