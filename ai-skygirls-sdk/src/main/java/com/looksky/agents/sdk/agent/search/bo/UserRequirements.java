package com.looksky.agents.sdk.agent.search.bo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserRequirements {
    @Deprecated(since = "2025-01-03 更换新的抽取正负向量词的prompt", forRemoval = true)
    @JsonProperty("positive_preferences")
    private String positivePreferences;
    @Deprecated(since = "2025-01-03 更换新的抽取正负向量词的prompt", forRemoval = true)
    @JsonProperty("negative_preferences")
    private String negativePreferences;


    private String category;
    @JsonProperty("basic_needs")
    private PreferenceStep basicNeeds;
    @JsonProperty("personalized_refinement")
    private List<PreferenceStep> personalizedRefinement;


    @Data
    public static class PreferenceStep {
        @JsonProperty("category_description")
        private String categoryDescription;
        @JsonProperty("positive_preferences")
        private String positivePreferences;
        @JsonProperty("negative_preferences")
        private String negativePreferences;
        @JsonProperty("user_intent")
        private String userIntent;
        @JsonProperty("title")
        private String title;
    }

}