package com.looksky.agents.sdk.recommend.daily100.dto.response;

import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DailyResponseDTO {
    private List<ItemDTO> items;
    private long size;
    private String requestId;
    private long lastIndex;
    private boolean newDay;
}