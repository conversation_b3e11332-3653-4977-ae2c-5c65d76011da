package com.looksky.agents.sdk.agent.preference;

import cn.hutool.json.JSONUtil;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class PricePreference implements Serializable {
    private Double high;
    private Double norm;
    private Double low;

    public static PricePreference parseJson(String json) {
        PricePreference bean = JSONUtil.toBean(json, PricePreference.class);
        if (bean.getHigh() == null || bean.getHigh() <= 0) {
            bean.setHigh(null);
        }
        if (bean.getNorm() == null || bean.getNorm() <= 0) {
            bean.setNorm(null);
        }
        if (bean.getLow() == null || bean.getLow() <= 0) {
            bean.setLow(null);
        }
        return bean;
    }

    public boolean isEmpty() {
        return (high == null || high <= 0) &&
               (norm == null || norm <= 0) &&
               (low == null || low <= 0);
    }
}
