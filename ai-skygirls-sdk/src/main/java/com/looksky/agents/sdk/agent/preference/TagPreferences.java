package com.looksky.agents.sdk.agent.preference;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class TagPreferences {
    private Map<String, LikePreference> tagsPreferences;

    //          tagName   attribute   value list
    private Map<String, Map<String, LikePreference>> specialTagsPreferences;


    public static TagPreferences parseFeedback(Map<String, List<String>> feedbackTag) {

        HashMap<String, LikePreference> map = new HashMap<>();

        feedbackTag.forEach((tag, feedback) -> {
            if (ObjectUtil.isEmpty(feedback)) {
                return;
            }
            LikePreference likePreference = new LikePreference();
            Set<PreferenceValue> like = feedback.stream().map(PreferenceValue::ofText).collect(Collectors.toSet());
            likePreference.setLike(like);
            map.put(tag, likePreference);
        });

        TagPreferences tagPreferences = new TagPreferences();
        tagPreferences.setTagsPreferences(map);
        return tagPreferences;
    }



    public boolean isEmpty() {
        return ObjectUtil.isEmpty(this.tagsPreferences) && ObjectUtil.isEmpty(this.specialTagsPreferences);
    }


    public static TagPreferences parseSpecialJson(String json) {
        ObjectMapper objectMapper = new ObjectMapper();
        //  tagName   attribute   value list
        Map<String, Map<String, List<Object>> > map;

        try {
            map =  objectMapper.readValue(json, new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        TagPreferences tagPreferences = new TagPreferences();
        for (Map.Entry<String, Map<String, List<Object>>> tag : map.entrySet()) {
            String tagName = tag.getKey();
            HashMap<String, LikePreference> resultAttributeMap = new HashMap<>();
            for (Map.Entry<String, List<Object>> attribute : tag.getValue().entrySet()) {
                String attributeName = attribute.getKey();
                List<Object> attributeValue = attribute.getValue();
                Set<PreferenceValue> like = attributeValue.stream().map(PreferenceValue::parseJson).filter(PreferenceValue::isNotEmpty).collect(Collectors.toSet());
                LikePreference likePreference = new LikePreference();
                likePreference.setLike(like);
                resultAttributeMap.put(attributeName, likePreference);

            }
            tagPreferences.setSpecialTagsPreferences(Map.of(tagName, resultAttributeMap));
        }

        return tagPreferences;
    }


    public static TagPreferences parseJson(String json) {
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Map<String, List<Object>>> map;

        try {
             map =  objectMapper.readValue(json, new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        TagPreferences tagPreferences = new TagPreferences();
        HashMap<String, LikePreference> likePreferenceHashMap = new HashMap<>();

        for (Map.Entry<String, Map<String, List<Object>>> entry : map.entrySet()) {
            String tagName = entry.getKey();
            Set<PreferenceValue> like = entry.getValue().get("like").stream().map(PreferenceValue::parseJson).filter(PreferenceValue::isNotEmpty).collect(Collectors.toSet());
            Set<PreferenceValue> dislike = entry.getValue().get("dislike").stream().map(PreferenceValue::parseJson).filter(PreferenceValue::isNotEmpty).collect(Collectors.toSet());


            if (ObjectUtil.isEmpty(like) && ObjectUtil.isEmpty(dislike)) {
                continue;
            }

            LikePreference likePreference = new LikePreference();
            likePreference.setLike(like);
            likePreference.setDislike(dislike);

            likePreferenceHashMap.put(tagName, likePreference);
        }

        tagPreferences.setTagsPreferences(likePreferenceHashMap);

        return tagPreferences;
    }

    @Deprecated
    private static TagPreferences _parseJson(String json) {
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Map<String, List<Object>>> map;

        try {
            map =  objectMapper.readValue(json, new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        TagPreferences tagPreferences = new TagPreferences();


        String tagName = map.keySet().stream().findFirst().orElse(null);
        if (tagName == null) {
            return null;
        }

        Map<String, List<Object>> likeAndDisLike = map.get(tagName);
        Set<PreferenceValue> like = likeAndDisLike.get("like").stream().map(PreferenceValue::parseJson).collect(Collectors.toSet());
        Set<PreferenceValue> dislike = likeAndDisLike.get("dislike").stream().map(PreferenceValue::parseJson).collect(Collectors.toSet());

        LikePreference likePreference = new LikePreference();
        likePreference.setLike(like);
        likePreference.setDislike(dislike);

        tagPreferences.setTagsPreferences(Map.of(tagName, likePreference));
        return tagPreferences;
    }


}
