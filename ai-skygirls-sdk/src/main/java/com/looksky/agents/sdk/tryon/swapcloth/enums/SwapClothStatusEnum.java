package com.looksky.agents.sdk.tryon.swapcloth.enums;


import lombok.Getter;

@Getter
public enum SwapClothStatusEnum {
    CREATED("已创建"),
    
    QUEUED_FOR_ANALYSIS("等待分析"),
    ANALYZING("分析中"),
    
    QUEUED_FOR_GENERATION("等待生成"),
    GENERATING("生成中"),
    
    QUEUED_FOR_FACE_SWAP("等待换脸"),
    FACE_SWAPPING("换脸中"),
    
    COMPLETED("已完成"),
    FAILED("失败");

    private final String description;
    private final boolean isQueueState;

    SwapClothStatusEnum(String description) {
        this.description = description;
        this.isQueueState = name().startsWith("QUEUED_");
    }
}