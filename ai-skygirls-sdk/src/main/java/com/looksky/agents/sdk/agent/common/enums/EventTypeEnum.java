package com.looksky.agents.sdk.agent.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;


/**
 * 事件类型枚举
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Getter
public enum EventTypeEnum {
    TEXT("text"),
    PRODUCT_ID_LIST("product_id_list"),
    ANSWER("answer"),
    PRODUCT_ID_GROUP_LIST("product_id_group_list"),
    FEEDBACK("feedback"),
    SINGLE_OPTION("single_option"),
    RANGE("range"),
    WAIT_SOLUTION("wait_solution"),
    CONFIRM_SOLUTION("confirm_solution"),
    CONFIRM_TAG("confirm_tag"),
    CONFIRM_CATEGORY("confirm_category"),
    WAIT_PRODUCT("wait_product"),
    TO_SEARCH("to_search"),
    SEARCH_SIMILAR("search_similar"),
    USER_TAG("user_tag"),
    USER_PREFERENCE("user_preference"),
    BRAND("brand"),
    TAGS("tags"),
    USER("user"),
    SEARCH_PROCESS("search_process"),
    ADVICE_QUESTIONS("advice_questions"),
    LOADING("loading"),
    COLOR_SEASON_CARD("color_season_card"),
    TRY_ON_UPLOAD_IMAGE_CARD("try_on_upload_image_card"),
    TRY_ON_SKU_INFO("try_on_sku_info"),
    THINK("think"),
    SHORTCUT_INPUT("shortcut_input"),
    PLANS("plans"),

    REASONING_PLANS("reasoning_plans"),
    REASONING_CONTENT("reasoning_content")
    ;

    @JsonValue
    private final String type;

    EventTypeEnum(String type) {
        this.type = type;
    }
}