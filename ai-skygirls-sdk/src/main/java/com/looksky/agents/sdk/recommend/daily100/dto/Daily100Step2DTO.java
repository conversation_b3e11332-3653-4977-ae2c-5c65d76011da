package com.looksky.agents.sdk.recommend.daily100.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Daily100Step2DTO {
    private String generateDate;
    private Daily100QueryDTO daily100QueryDTO;
    private String version;
    private String zone;


    public String getRecommendVersion() {
        return generateDate + "-" + version;
    }

    public boolean isSameVersion(String date, String version) {
        return this.getRecommendVersion().equals(date + "-" + version);
    }
}
 