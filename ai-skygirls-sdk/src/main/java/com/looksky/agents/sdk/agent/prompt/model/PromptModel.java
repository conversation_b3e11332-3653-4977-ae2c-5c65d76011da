package com.looksky.agents.sdk.agent.prompt.model;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.looksky.agents.sdk.enums.OutputModelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-20 21:48:15
 */
@Getter
@Setter
@TableName("t_prompt")
@Schema(description = "Prompt对象")
public class PromptModel implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键 id")
    @TableId(value = "pk_id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "prompt 名")
    @TableField("name")
    private String name;

    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "输出类型 1:text, 2:function_call 3:json_schema")
    private OutputModelEnum outputType;

    @Schema(description = "用户提示词")
    private String userPrompt;

    @Schema(description = "系统提示词")
    private String systemPrompt;

    @Schema(description = "使用的模型名")
    private String modelName;

    @Schema(description = "温度值")
    private Double temperature;

    @Schema(description = "json 结构")
    private String jsonSchema;

    @Schema(description = "创建时间")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @Schema(description = "更新时间")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "更新人")
    private String updatedBy;

    @Schema(description = "删除标记: 0 启用 1 不启用")
    @TableLogic
    private Boolean deleted;

    @Schema(description = "元数据列表")
    @TableField(exist = false)
    private List<PromptMetadataModel> metadataList;


    public PromptModel copy() {
        PromptModel copy = new PromptModel();

        // 复制基本属性
        copy.setId(this.getId());
        copy.setName(this.getName());
        copy.setDescription(this.getDescription());
        copy.setOutputType(this.getOutputType());
        copy.setUserPrompt(this.getUserPrompt());
        copy.setSystemPrompt(this.getSystemPrompt());
        copy.setModelName(this.getModelName());
        copy.setTemperature(this.getTemperature());
        copy.setJsonSchema(this.getJsonSchema());

        // 复制时间和操作者信息
        copy.setCreatedTime(this.getCreatedTime());
        copy.setUpdatedTime(this.getUpdatedTime());
        copy.setCreatedBy(this.getCreatedBy());
        copy.setUpdatedBy(this.getUpdatedBy());
        copy.setDeleted(this.getDeleted());

        // 复制元数据列表
        if (this.getMetadataList() != null) {
            List<PromptMetadataModel> metadataCopy = new ArrayList<>();
            for (PromptMetadataModel metadata : this.getMetadataList()) {
                metadataCopy.add(metadata.copy());
            }
            copy.setMetadataList(metadataCopy);
        }

        return copy;
    }


    public int getImageSize() {
        String size = getMetadataValue("size");
        if (CharSequenceUtil.isNotBlank(size)) {
            return Integer.parseInt(size);
        }
        return 0;
    }

    private String getMetadataValue(String key) {
        if (metadataList == null) {
            return null;
        }

        return metadataList.stream()
            .filter(metadata -> metadata.getMetadataKey().equals(key))
            .findFirst()
            .map(PromptMetadataModel::getMetadataValue)
            .orElse(null);

    }

}
