package com.looksky.agents.sdk.recommend.foryou.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Random;
import lombok.AllArgsConstructor;
import lombok.Getter;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Getter
@AllArgsConstructor
public enum RecommendScenEnum {
    GIRLS_PARTITION_MATCH("girlsPartitionMatch", "最佳匹配", "Perfect 5"),
    GIRLS_PARTITION_TRY("girlsPartitionTry", "大胆尝试", "Bold Play"),
    GIRLS_PARTITION_BRAND("girlsPartitionBrand", "品牌推荐", "Brand Crush"),
    GIRLS_PARTITION_FESTIVAL("girlsPartitionFestival", "节日", "Event Edit"),
    GIRLS_PARTITION_BASIC("girlsPartitionBasic", "基础款", "Essentials"),
    GIRLS_PARTITION_TREND("girlsPartitionTrend", "流行趋势", "It-List"),
    GIRLS_PARTITION_DISCOUNT("girlsPartitionDiscount", "折扣", "Steals & Deals (30)"),
    ;

    @JsonValue
    private final String name;
    private final String description;
    private final String showName;

    public static RecommendScenEnum randomScen() {
        RecommendScenEnum[] values = RecommendScenEnum.values();
        return values[new Random().nextInt(values.length)];
    }


    public static RecommendScenEnum getByName(String nameStr) {
        for (RecommendScenEnum value : RecommendScenEnum.values()) {
            if (value.getName().equals(nameStr)) {
                return value;
            }
        }
        throw new IllegalArgumentException("No enum constant " + RecommendScenEnum.class.getCanonicalName() + "." + nameStr);
    }

    public static boolean isExist(String nameStr) {
        for (RecommendScenEnum value : RecommendScenEnum.values()) {
            if (value.getName().equals(nameStr)) {
                return true;
            }
        }
        return false;
    }

}