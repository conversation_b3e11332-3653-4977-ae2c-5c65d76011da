package com.looksky.agents.sdk.tryon.swapcloth.dto;

import com.looksky.agents.sdk.tryon.swapcloth.enums.SwapClothStatusEnum;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class SwapClothStatus {
    private String traceId;
    private SwapClothStatusEnum status;
    @Deprecated
    private String resultUrl;
    private String errorMessage;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private SwapClothParam swapClothParam;
    private String promptContent;
    private String workflowResultUrl;
    private String resultImage;
}