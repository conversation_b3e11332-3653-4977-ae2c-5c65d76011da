package com.looksky.agents.sdk.utils.date;

import cn.hutool.core.util.StrUtil;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
public class DateUtils {

    public static final String ZONE = "America/Los_Angeles";

    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String DATE_PATTERN = "yyyy-MM-dd";



    /**
     * 获取明天某点的剩余时间
     * @return 明天某点的剩余时间
     */
    public static Duration getTomorrowTimeAt(long hour) {
        // 获取当前时间
        ZonedDateTime now = getNow();
        // 计算到美国东部24点的剩余时间
        ZonedDateTime nextMidnight = now.truncatedTo(ChronoUnit.DAYS).plusDays(1).plusHours(hour);
        return Duration.between(now, nextMidnight);
    }

    /**
     * 获取当天剩余时间
     * @return 当天剩余时间
     */
    public static Duration getTheRestOfTheDay() {
        // 获取当前时间
        ZonedDateTime now = getNow();
        // 计算到美国东部24点的剩余时间
        ZonedDateTime nextMidnight = now.truncatedTo(ChronoUnit.DAYS).plusDays(1);
        return Duration.between(now, nextMidnight);
    }


    /**
     * 获取当前月份
     * @return 当前月份 例如: JANUARY
     */
    public static String getMonth() {
        return getNow().getMonth().name();
    }

    /**
     * 获取下一个月份
     * @return 下一个月份 例如: FEBRUARY
     */
    public static String getNextMonth() {
        return getNow().plusMonths(1).getMonth().name();
    }

    /**
     * 获取下下个月份
     * @return 下下个月份 例如: MARCH
     */
    public static String getNextNextMonth() {
        return getNow().plusMonths(2).getMonth().name();
    }

    /**
     * 获取当前季节
     * @return 当前季节 (spring / summer / fall / winter)
     */
    public static String getCurrentSeason() {
        int month = getNow().getMonthValue();
        return switch (month) {
            case 3, 4, 5 -> "spring";
            case 6, 7, 8 -> "summer";
            case 9, 10, 11 -> "fall";
            case 12, 1, 2 -> "winter";
            default -> throw new IllegalStateException("Unexpected month value: " + month);
        };
    }

    public static ZonedDateTime getNow() {
        return ZonedDateTime.now(ZoneId.of(ZONE));
    }

    public static String getNowDataTime() {
        return getNow().format(DateTimeFormatter.ofPattern(PATTERN));
    }

    /**
     * 根据毫秒数获取时间
     * @param time 毫秒数, 例如: 1736478789000
     * @param zone 时区
     * @return 时间
     */
    public static String getDateTimeByMilliSeconds(String time, String zone) {
        long epochMilli = Long.parseLong(time);
        ZoneId zoneId = ZoneId.of(zone);
        ZonedDateTime zonedDateTime = Instant.ofEpochMilli(epochMilli).atZone(zoneId);
        return zonedDateTime.format(DateTimeFormatter.ofPattern(PATTERN));
    }


    /**
     * 根据毫秒数获取时间
     * @param time 毫秒数, 例如: 1736478789000
     * @param zone 时区
     * @return 时间
     */
    public static String getDateByMilliSeconds(String time, String zone) {
        long epochMilli = Long.parseLong(time);
        ZoneId zoneId = ZoneId.of(zone);
        ZonedDateTime zonedDateTime = Instant.ofEpochMilli(epochMilli).atZone(zoneId);
        return zonedDateTime.format(DateTimeFormatter.ofPattern(DATE_PATTERN));
    }


    /**
     * 根据秒数获取时间
     * @param time 秒数, 例如: 1736478789
     * @param zone 时区
     * @return 时间
     */
    public static String getDateTimeBySeconds(Long time, String zone) {
        ZoneId zoneId = ZoneId.of(zone);
        ZonedDateTime zonedDateTime = Instant.ofEpochSecond(time).atZone(zoneId);
        return zonedDateTime.format(DateTimeFormatter.ofPattern(PATTERN));
    }


    /**
     * 获取当地的时间 以 yyyy-MM-dd 的格式返回
     * @param zone 当地的时区
     * @return 返回用户的当地的时间
     */
    public static String getNowDate(String zone) {
        if (StrUtil.isBlankIfStr(zone)) {
            zone = ZONE;
        }
        ZonedDateTime zonedDateTime = ZonedDateTime.now(ZoneId.of(zone));
        return zonedDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }
}
