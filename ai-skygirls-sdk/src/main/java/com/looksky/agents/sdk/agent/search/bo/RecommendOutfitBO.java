package com.looksky.agents.sdk.agent.search.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecommendOutfitBO {
    private String category;
    @JsonProperty("positive_preferences")
    private String positivePreferences;
    @JsonProperty("negative_preferences")
    private String negativePreferences;
    private String title;
    @JsonProperty("product_id_list")
    private List<ItemDTO> skcIds;
    private String uuid;

    @Deprecated
    @JsonProperty("outfit_suggestion")
    private String outfitSuggestion;


    @Schema(description = "正向的标签, 包括风格, 材质, 合适度, 长度")
    @JsonProperty("positive_style_material_fit_length_labels")
    private String positiveLabels;
    @Schema(description = "负向的标签, 包括风格, 材质, 合适度, 长度")
    @JsonProperty("negative_style_material_fit_length_labels")
    private String negativeLabels;

    private ExtractedEntityObject tagPreference;


    public List<String> getAllSkcIds() {
        return skcIds.stream().map(ItemDTO::getItemId).toList();
    }
}