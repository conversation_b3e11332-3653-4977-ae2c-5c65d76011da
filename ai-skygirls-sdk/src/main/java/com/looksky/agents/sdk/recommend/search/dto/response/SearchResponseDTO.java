package com.looksky.agents.sdk.recommend.search.dto.response;

import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 1.0
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SearchResponseDTO {
    private List<StrategyResponse> strategyResponses;
    private String requestId;
    private boolean isDefault;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class StrategyResponse {
        private String searchStrategy;
        private List<ItemDTO> items;
        private long size;
    }

    public List<ItemDTO> getItems() {
        if (strategyResponses == null) {
            return Collections.emptyList();
        }
        return strategyResponses.stream()
            .flatMap(sr -> sr.getItems().stream())
            .collect(Collectors.toList());
    }
}