package com.looksky.agents.sdk.common.enums;

import com.skygirls.biz.im.dto.MessageTypeEnum;
import lombok.Getter;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Getter
public enum PromptNameEnum {


    EXTRACT_USER_PREFERENCE_TYPE("extract_user_preference_type", "提取用户偏好类型"),


    INTENT_RECOGNITION("question_type_search_before_recommend", "意图识别"),
    IS_NEW_CONVERSATION("determine_new_conversation", "判断是否为新会话"),
    SMALL_TALK_IN_SEARCH("small_talk_in_search", "闲聊"),
    CATEGORY_PREFERENCE("category_preference", "提取用户表达的品类偏好"),
    EXTRACT_LABEL_DIMENSION("extract_label_dimension", "提取用户表达的品类的标签维度"),
    GET_CLOTH_INDEX("get_cloth_index", "推荐商品后, 获取用户表达的喜欢第几件"),

    EXTRACT_USER_SUBJECTIVE_PREFERENCE("extract_user_subjective_preference", "提取用户主观词"),
    EXTRACT_USER_ECOMMERCE_PREFERENCE("extract_user_ecommerce_preference", "提取用户电商词"),
    USER_BODY_ALL_INFO("user_body_all_info", "提取用户身体信息"),
    DETERMINE_ASK_USER_INFO("determine_ask_user_info", "判断是否需要追问"),

    EXTRACT_SINGLE_USER_PREFERENCE("extract_single_user_preference", "提取单个标签的偏好"),
    RECOMMEND_PRODUCT_WITH_ENOUGH_CONDITION("recommend_product_with_enough_condition", "已经有了二级品类, 推荐商品"),
    INQUIRE_USER_SOLUTION("inquire_user_solution", "推方案"),


    OUTFIT_SUGGESTION_NEW("outfit_suggestion_new", "生成推荐方案的标题和 tag"),

    CONVERT_OUTFIT_SUGGESTION_TO_TAG("convert_outfit_suggestion_to_cloth_tag", "将推荐方案转换为标签"),

    ENW_QUERY_AND_IS_SEARCH("new_query_and_is_search", "生成新 query 和是否搜索"),
    BUILD_SEARCH_PROCESS("search_process_first_reply", "生成搜索过程"),
    BUILD_POSITIVE_NEGATIVE_QUERY("build_positive_negative_query", "生成正负向 query"),

    GIRLS_OPENING_DRESSING("girls_opening_dressing", "dressing页面的开场白"),
    GIRLS_OPENING_MAKEUP("girls_opening_makeup", "makeup页面的开场白"),
    GIRLS_OPENING_HAIRSTYLE("girls_opening_hairstyle", "hairstyle页面的开场白"),

    INQUIRE_USER_SUBCATEGORY_PREFERENCE("inquire_user_subcategory_preference", "询问用户二级品类偏好"),
    INQUIRE_USER_TAG_PREFERENCE("inquire_user_tag_preference", "询问用户标签, 推荐标签选择器"),
    REFINING_REQUIREMENTS("search_cloth_with_subjective_preference", "细化需求"),
    SIMILAR_SEARCH_IN_SEARCH_PAGE_WITH_INDEX("similar_search_in_search_page_with_index", "推荐商品后, 用户反馈编号, 找与该商品相似"),
    SIMILAR_NEW_QUERY("similar_new_query", "推荐商品后, 用户反馈编号, 找与该商品相似, 生成新 query"),


    APP_INTRODUCE("app_introduce", "应用介绍"),


    ;

    private final String name;
    private final String description;

    PromptNameEnum(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public static String getOpening(MessageTypeEnum messageType) {
        return switch (messageType) {
            case DRESSING -> GIRLS_OPENING_DRESSING.name;
            case MAKEUP -> GIRLS_OPENING_MAKEUP.name;
            case HAIRSTYLE -> GIRLS_OPENING_HAIRSTYLE.name;
        };
    }

    public static boolean isOpening(String promptName) {
        return GIRLS_OPENING_DRESSING.name.equals(promptName) || GIRLS_OPENING_MAKEUP.name.equals(promptName) || GIRLS_OPENING_HAIRSTYLE.name.equals(promptName);
    }
}
