package com.looksky.agents.sdk.agent.search.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class SearchProcessPart2DTO {
    private String demandConfirmation;
    private String summary;
    private SearchProcessDTO.Reason reason;
    private List<SearchProcessDTO.Reason.LinkItem> links;


    public SearchProcessDTO.Reason toReason() {
        SearchProcessDTO.Reason reason = new SearchProcessDTO.Reason();
        reason.setTitle(this.reason.getTitle());
        reason.setFilterNumbers(this.reason.getFilterNumbers());
        reason.setCandidatesNumbers(this.reason.getCandidatesNumbers());
        reason.setReasonDetail(this.reason.getReasonDetail());
        reason.setLinks(this.links);
        return reason;
    }

}
