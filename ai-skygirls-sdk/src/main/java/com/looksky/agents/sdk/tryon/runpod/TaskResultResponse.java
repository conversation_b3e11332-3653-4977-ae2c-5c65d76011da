package com.looksky.agents.sdk.tryon.runpod;

import lombok.Data;

/**
 * RunPod 任务结果响应
 *
 * <AUTHOR>
 * @since 1.1.8
 **/
@Data
public class TaskResultResponse {
    /**
     * 延迟时间，以毫秒为单位
     */
    private Long delayTime;

    /**
     * 执行时间，以毫秒为单位
     */
    private Long executionTime;

    /**
     * 任务ID
     */
    private String id;

    /**
     * 任务输出结果
     */
    private Output output;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 执行任务的工作节点ID
     */
    private String workerId;

    /**
     * 任务输出内容
     */
    @Data
    public static class Output {
        /**
         * 处理状态
         */
        private String status;

        /**
         * 结果图片URL
         */
        private String url;

        /**
         * 换白 T 的 base 64
         */
        private String data;
        /**
         * 处理 Code
         */
        private Integer code;
        /**
         * 错误消息
         */
        private String message;
        /**
         * 是否成功
         */
        private Boolean success;
    }
}