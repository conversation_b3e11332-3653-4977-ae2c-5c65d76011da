package com.looksky.agents.sdk.content.user.avatar;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 生成用户头像请求参数
 *
 * <AUTHOR>
 * @since 1.1.14
 **/
@Data
public class UserAvatarParam {
    @Schema(description = "唯一 ID")
    private String id;
    @Schema(description = "图像文本描述")
    private String prompt;
    @Schema(description = "用户名")
    private String userName;
    @Schema(description = "传入的 url 图片, 参照的头像")
    private String originalUrl;
    @Schema(description = "用户头像, 这是结果")
    private String url;
    @Schema(description = "身材类型")
    private String weightType;
}
