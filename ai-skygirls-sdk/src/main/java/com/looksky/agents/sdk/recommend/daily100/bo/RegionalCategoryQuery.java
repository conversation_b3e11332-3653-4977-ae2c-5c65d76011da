package com.looksky.agents.sdk.recommend.daily100.bo;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.looksky.agents.sdk.recommend.common.dto.VectorRecallModelDTO;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class RegionalCategoryQuery {
    @JsonAlias({"categoryQueries", "regionCategories"})
    private List<CategoryPositiveAndNegativeQuery> regionCategories;
    private GeneralCategoryQuery generalCategory;


    @Data
    public static class CategoryPositiveAndNegativeQuery {
        private String category;

        // 标签体系内的二级品类
        private String filterCategory;

        private String positiveQuery;

        private String negativeQuery;

    }


    @Data
    public static class GeneralCategoryQuery {
        private String positiveQuery;
        private String negativeQuery;

    }

    public List<VectorRecallModelDTO> toCategoryVectorRecall() {
        List<VectorRecallModelDTO> categoryList = new ArrayList<>();
        if (regionCategories != null) {
            categoryList.addAll(regionCategories.stream()
                    .map(category -> VectorRecallModelDTO.builder()
                            .recallStrategy(Optional.ofNullable(category.getFilterCategory()).orElse(category.getCategory()))
                            .mustSubCategory(List.of((Optional.ofNullable(category.getFilterCategory()).orElse(category.getCategory()))))
                            .vectorQuery(Stream.of(
                                    SearchRequestDTO.VectorQuery.builder()
                                            .text(category.getPositiveQuery())
                                            .weight(1)
                                            .build(),
                                    SearchRequestDTO.VectorQuery.builder()
                                            .text(category.getNegativeQuery())
                                            .weight(-1)
                                            .build()
                            ).filter(query -> StrUtil.isNotBlank(query.getText())).toList())
                            .build())
                    .toList());
        }


        return categoryList;
    }

    public List<VectorRecallModelDTO> toTotalVectorRecall() {
        List<VectorRecallModelDTO> categoryList = new ArrayList<>();

        // 处理通用品类
        if (generalCategory != null) {
            categoryList.add(VectorRecallModelDTO.builder()
                    .recallStrategy("GeneralCategory")
                    .vectorQuery(Stream.of(
                            SearchRequestDTO.VectorQuery.builder()
                                    .text(generalCategory.getPositiveQuery())
                                    .weight(1)
                                    .build(),
                            SearchRequestDTO.VectorQuery.builder()
                                    .text(generalCategory.getNegativeQuery())
                                    .weight(-1)
                                    .build()
                    ).filter(query -> StrUtil.isNotBlank(query.getText())).toList())
                    .build());
        }

        return categoryList;
    }


}
