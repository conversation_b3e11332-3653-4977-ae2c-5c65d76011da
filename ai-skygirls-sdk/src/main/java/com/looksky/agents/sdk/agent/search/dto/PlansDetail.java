package com.looksky.agents.sdk.agent.search.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlansDetail {
    private String title;
    private String reason;
    @JsonProperty("product_id_list")
    private List<ItemDTO> productIdList;
    private String category;
    private String positivePreferences;
    private String negativePreferences;
    private ExtractedEntityObject tagPreference;
}