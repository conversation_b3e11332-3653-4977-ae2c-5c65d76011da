package com.looksky.agents.sdk.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class SpuInfo implements Serializable {
    private String id;
    
    @JsonProperty("skc_id")
    private String skcId;
    
    private String currency;
    
    @JsonProperty("category_id")
    private String categoryId;
    
    private String title;
    private String tags;
    private String desc;
    private Integer status;
    
    @JsonProperty("copy_status")
    private Integer copyStatus;
    
    @JsonProperty("color_count")
    private Integer colorCount;
    
    @JsonProperty("image_count")
    private Integer imageCount;
    
    @JsonProperty("video_count")
    private Integer videoCount;
    
    private String spu;
    
    @JsonProperty("default_color")
    private String defaultColor;
    
    private Double price;
    
    @JsonProperty("original_price")
    private Double originalPrice;
    
    private String ext;
    private String features;
    
    @JsonProperty("size_type")
    private String sizeType;
    
    @JsonProperty("create_time")
    private String createTime;
    
    @JsonProperty("publish_time")
    private String publishTime;
    
    @JsonProperty("update_time")
    private String updateTime;
    
    @JsonProperty("delete_time")
    private String deleteTime;
    
    private String link;
    
    @JsonProperty("color_list")
    private List<String> colorList;
    
    @JsonProperty("color_value_list")
    private List<String> colorValueList;
    
    @JsonProperty("size_list")
    private List<String> sizeList;
    
    @JsonProperty("skc_title_list")
    private List<String> skcTitleList;
    
    @JsonProperty("skc_link_list")
    private List<String> skcLinkList;
    
    @JsonProperty("skc_price_list")
    private List<Double> skcPriceList;
    
    @JsonProperty("skc_highest_price_list")
    private List<Double> skcHighestPriceList;
    
    @JsonProperty("skc_original_price_list")
    private List<Double> skcOriginalPriceList;
    
    @JsonProperty("skc_highest_original_price_list")
    private List<Double> skcHighestOriginalPriceList;
    
    @JsonProperty("size_map")
    private String sizeMap;
    
    @JsonProperty("product_size_map")
    private List<ProductSize> productSizeMap;
} 