package com.looksky.agents.sdk.agent.search.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class EcommercePreference implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @JsonProperty("is_free_postage")
    private Boolean isFreePostage;
    @JsonProperty("is_can_return")
    private Boolean isCanReturn;
    @JsonProperty("is_discount")
    private Boolean isDiscount;
}
