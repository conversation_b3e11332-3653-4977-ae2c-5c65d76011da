package com.looksky.agents.sdk.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 **/
@Data
public class SkcInfo implements Serializable {
    @JsonProperty("product_id")
    private String productId;
    
    private String id;
    
    @JsonProperty("system_category_id")
    private String systemCategoryId;
    
    private String title;
    private String link;
    private Double price;
    
    @JsonProperty("highest_price")
    private Double highestPrice;
    
    @JsonProperty("original_price")
    private Double originalPrice;
    
    @JsonProperty("highest_original_price")
    private Double highestOriginalPrice;
    
    private Integer status;
    
    @JsonProperty("copy_status")
    private Integer copyStatus;
    
    @JsonProperty("size_stock")
    private Map<String, String> sizeStock;
} 