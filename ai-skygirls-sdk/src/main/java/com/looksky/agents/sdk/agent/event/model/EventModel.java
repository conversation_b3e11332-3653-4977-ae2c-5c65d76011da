package com.looksky.agents.sdk.agent.event.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.looksky.agents.sdk.common.BaseModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22 14:04:33
 */
@Getter
@Setter
@TableName("t_event")
@Schema(name = "Event")
public class EventModel extends BaseModel {
    @Schema(description = "事件名")
    private String name;

    @Schema(description = "事件的描述")
    private String description;

    @Schema(description = "事件内容")
    private String content;

    @Schema(description = "事件需要使用的 prompt")
    private String promptId;

    @Schema(description = "事件需要使用的 prompt 名称")
    private String promptName;
}
