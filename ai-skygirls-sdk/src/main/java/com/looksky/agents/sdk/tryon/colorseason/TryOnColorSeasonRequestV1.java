package com.looksky.agents.sdk.tryon.colorseason;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * colorSeason 试色请求参数, 四步方案, 第三步 请求 runPod 进行换色
 * <AUTHOR>
 * @since 1.1.8
 **/
@Data
public class TryOnColorSeasonRequestV1 {
    private String prompt = "crew neck t-shirt, short sleeves";
    @JsonProperty("model_image_url")
    private String modelImageUrl;
    @JsonProperty("garment_image_url")
    private String garmentImageUrl;
    private Integer seed;
}
