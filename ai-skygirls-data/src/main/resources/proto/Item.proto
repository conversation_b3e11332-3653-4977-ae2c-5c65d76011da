syntax = "proto3";
package com.westyle.recm;

message Item {

  //物品id
  string itemId = 1;

  // 物品类型：1：视频；2：图集；3：商品（skc）;4：集合；5：信息采集页;6：推荐页
  int64 itemType = 2;

  // 策略字符串（透传）
  string strategy = 3;

  Extend extend = 4;

  ItemGroup itemGroup = 5;

  int64 index = 6;

  string spuId = 7;

}

message Extend{
  //推荐页的类型：1：主题集合列表；2：视频列表；
  int64 recmPageType = 1;
  string video_id = 2;
}

message ItemGroup {

  repeated Item items = 1;

}