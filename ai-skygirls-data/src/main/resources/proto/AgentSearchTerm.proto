syntax = "proto3";
package com.westyle.recm;
import "google/protobuf/wrappers.proto";

// 入参
message SingleAgentSearchTerm {

  //模糊匹配 用户选择的搜索策略
  string searchStrategy = 1;

  //二级品类列表
  repeated string category = 2;

  //品牌列表
  repeated string brand = 3;

  //价格范围
  PriceRangeModel priceRange = 4;

  //用户偏好标签 （精准匹配与全文匹配的标签都在这，推荐会判断是那一种）
  repeated UserPreferenceModel userPreference = 5;

  //必须满足的组合标签 （示例：图片 大玫瑰）
  repeated combinationPreference combinationPreferenceMust = 6;
  //不能有的组合标签 （示例：图片 大玫瑰）
  repeated combinationPreference combinationPreferenceMustNot = 7;
  //不能有的组合标签 （示例：图片 大玫瑰）
  repeated combinationPreference combinationPreferenceShould = 8;

  //正向模糊匹配关键词 (在title，desc中查询)
  repeated string positiveElement = 9;

  //负向模糊匹配关键词 (在title，desc中查询)
  repeated string negativeElement = 10;

  //是否包邮
  google.protobuf.BoolValue isFreePostage = 11;

  //是否支持退货
  google.protobuf.BoolValue isCanReturn = 12;

  //有无折扣
  google.protobuf.BoolValue isDiscount = 13;

  //衣服尺码
  repeated string clothesSize = 14;
  //衣服尺寸的大小码，当clothesSize有值的时候sizeType无效
  //值： big / medium / small
  repeated string sizeType = 15;
  //用户不喜欢的品牌
  repeated string dislikeBrand = 16;
  //用户不喜欢的二级品类列表
  repeated string dislikeCategory = 17;

  //加分项模糊匹配关键词 (在title，desc中查询)
  repeated string shouldElement = 18;

  //生成向量的query查询
  repeated VectorQueryModel vectorQuery = 19;
}

// 查询向量语句
message VectorQueryModel {
  string text = 1;
  int32 weight = 2;
}


// 用户偏好模型
message UserPreferenceModel {

  //标签类型
  string tagType = 1;

  //必须有的标签值
  repeated string like = 2;
  //不能有的标签值
  repeated string disLike = 3;
  //非必需的标签值
  repeated string recommend = 4;

}

//价格范围模型
message PriceRangeModel {

  // 最低价格 默认为0
  google.protobuf.DoubleValue minPrice = 1;
  // 最高价格 默认为0
  google.protobuf.DoubleValue maxPrice = 2;
  // 标准价格 默认为0
  google.protobuf.DoubleValue normPrice = 3;
}

//组合条件用户偏好
//组合偏好标签 示例：图片 大玫瑰 + 图片 小狗
// [
//    {
//        "parentTagType": "pattern",
//        "preferenceNode": [
//            {
//                "tagType": "specific_pattern_type",
//                "tagValue": "rose print"
//            },
//            {
//                "tagType": "specific_pattern_scale",
//                "tagValue": "large"
//            }
//        ]
//    },
//    {
//        "parentTagType": "pattern",
//        "preferenceNode": [
//            {
//                "tagType": "specific_pattern_type",
//                "tagValue": "dogs"
//            },
//            {
//                "tagType": "specific_pattern_scale",
//                "tagValue": "small"
//            }
//        ]
//    }
//]
message combinationPreference {

    string parentTagType = 1;
    repeated combinationPreferenceNode preferenceNode = 2;

}

// 组合条件节点
message combinationPreferenceNode {

  //标签类型
  string tagType = 1;

  //标签值
  repeated string tagValue = 2;

}

message TouristFeature {

  //身高 cm
  double height = 1;
  //体重 kg
  double weight = 2;
  //肩型
  string shoulders = 3;
  //胸型
  string bustShape = 4;
  //上下身比例
  string proportion = 5;
  //身型
  string bodyShape = 6;

}