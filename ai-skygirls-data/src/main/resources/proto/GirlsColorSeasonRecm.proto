syntax = "proto3";
package com.westyle.recm;
import "Item.proto";
import "VectorRecall.proto";
// app的每日100风格推荐服务
service GirlsColorSeasonRecommendService {
  rpc colorSeasonRecommend (GirlsColorSeasonRequest) returns (GirlsColorSeasonResponse);
}

// 入参
message GirlsColorSeasonRequest {
  string userId = 1;
  // 6个品类的向量召回
  repeated VectorRecallModel vectorRecall = 2;
  // 向量词的版本
  string vectorQueryVersion = 3;
  //客户端时间
  string clientDayTime = 4;
}

// 返回
message GirlsColorSeasonResponse {
  repeated Item items = 1;
  int64 size = 2;
  string requestId = 3;
  bool newDay = 4;
}
