syntax = "proto3";
package com.westyle.recm;
import "Item.proto";
import "AgentSearchTerm.proto";
// 首页推荐服务
service GirlsAgentSearchRecommendService {
  rpc girlsAgentSearchRecommend (GirlsAgentSearchRequest) returns (GirlsAgentSearchResponse);
}

// 入参
message GirlsAgentSearchRequest {
  //用户id，没有传空字符串
  string userId = 1;
  //用户的季节（spring / summer / fall / winter ）
  string season = 2;
  //搜索条件
  repeated GirlsSingleStrategyTerm strategyTerm = 3;

}

message GirlsSingleStrategyTerm{

  //过滤条件
  SingleAgentSearchTerm searchTerm = 1;
  //第一步的向量词
  repeated VectorQueryModel step1VectorQuery = 2;
  //第二步的向量词
  repeated VectorQueryModel step2VectorQuery = 3;
  // 第三步的向量词
  repeated VectorQueryModel step3VectorQuery = 4;
  // 风格/标签特征向量词
  repeated VectorQueryModel tagsVectorQuery = 5;
}

// 返回
message GirlsAgentSearchResponse {

  repeated GirlsSingleStrategyResponse strategyV2Responses = 1;
  // 请求id（透传）
  string requestId = 2;
  //是否是兜底推荐
  bool isDefault = 3;
}

//每一个策略对应一个商品列表
message GirlsSingleStrategyResponse{

  //模糊匹配 用户选择的搜索策略
  string searchStrategy = 1;
  repeated Item items = 2;
  int64 size = 3;
}



