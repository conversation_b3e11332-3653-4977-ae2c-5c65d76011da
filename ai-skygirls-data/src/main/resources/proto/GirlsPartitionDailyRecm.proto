syntax = "proto3";
package com.westyle.recm;
import "Item.proto";
import "VectorRecall.proto";
// girl版本的的每日100 分区 推荐服务
service GirlsPartitionDailyRecommendService {
  rpc partitionDailyRecommend (GirlsPartitionDailyRequest) returns (GirlsPartitionDailyResponse);
}

// 入参
message GirlsPartitionDailyRequest {
  //用户id
  string userId = 1;
  //客户端时间
  string clientDayTime = 2;
  //每一个分区的条件
  repeated PartitionRecomModel partitionRecomModels = 3;
}

//每个分区的向量词
message PartitionRecomModel {
  // 分区推荐场景
  string partitionRecomScenes = 1;
  //每一个分区的每一路召回条件
  repeated VectorRecallModel recallVectors = 2;
}

// 返回
message GirlsPartitionDailyResponse {
  map<string,PartitionResponse> partitionResponses = 1;
  bool newDay = 5;
}

message PartitionResponse {
  repeated Item items = 1;
  int64 size = 2;
}
