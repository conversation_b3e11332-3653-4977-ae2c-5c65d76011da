syntax = "proto3";
package com.westyle.recm;
import "Item.proto";
import "VectorRecall.proto";

// app的每日100风格推荐服务
service GirlsDailyRecommendService {
  rpc appDailyRecommend (GirlsDailyRequest) returns (GirlsDailyResponse);
}

// 入参
message GirlsDailyRequest {
  string userId = 1;
  string versionCode = 2;
  int32 offset = 3;
  int32 limit = 4;
  // 6个品类的向量召回
  repeated VectorRecallModel categoryVectorRecall = 5;
  // 除6个品类以外的向量召回
  repeated VectorRecallModel totalVectorRecall = 6;
  // 向量词的版本
  string vectorQueryVersion = 7;
  //客户端时间
  string clientDayTime = 8;
}

// 返回
message GirlsDailyResponse {
  repeated Item items = 1;
  int64 size = 2;
  string requestId = 3;
  int64 lastIndex = 4;
  bool newDay = 5;
}
