syntax = "proto3";
package com.westyle.recm;
import "Item.proto";
import "AgentSearchTerm.proto";

// 首页推荐服务
service GirlsAgentSimilarRecommendService {
  rpc girlsAgentSimilarRecommend (GirlsAgentSimilarRequest) returns (GirlsAgentSimilarResponse);
}

// 入参
message GirlsAgentSimilarRequest {
  //用户id，没有传空字符串
  string userId = 1;
  //商品id，必传（如果searchTerm中的vectorQuery有值，那么则以这个值为主，否则以itemId对应的向量为搜索条件）
  string itemId = 2;
  //用户的季节（spring / summer / fall / winter ）
  string season = 3;
  //搜索条件
  SingleAgentSearchTerm searchTerm = 4;
}

// 返回
message GirlsAgentSimilarResponse {
  repeated Item items = 1;
  int64 size = 2;
  // 请求id（透传）
  string requestId = 3;
}

