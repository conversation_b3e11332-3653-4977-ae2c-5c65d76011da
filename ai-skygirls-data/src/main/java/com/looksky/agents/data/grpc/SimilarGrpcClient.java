package com.looksky.agents.data.grpc;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.data.grpc.convertor.SimilarConvertor;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.interceptor.GrpcClientHeaderInterceptor;
import com.looksky.agents.infrastructure.interceptor.GrpcResponseHeaderInterceptor;
import com.looksky.agents.sdk.recommend.similar.dto.request.SimilarRequestDTO;
import com.looksky.agents.sdk.recommend.similar.dto.response.SimilarResponseDTO;
import com.westyle.recm.GirlsAgentSimilarRecom;
import com.westyle.recm.GirlsAgentSimilarRecommendServiceGrpc;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.client.inject.GrpcClient;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SimilarGrpcClient {

    @GrpcClient("girlsAgentSimilarRecommendService")
    private GirlsAgentSimilarRecommendServiceGrpc.GirlsAgentSimilarRecommendServiceBlockingStub stub;

    @Resource
    private SimilarConvertor grpcConvertor;

    private static final long DEADLINES = 15;

    @PostConstruct
    public void init() {
        // 在初始化时添加拦截器
        stub = stub.withInterceptors(new GrpcClientHeaderInterceptor(), new GrpcResponseHeaderInterceptor());
    }

    private GirlsAgentSimilarRecom.GirlsAgentSimilarResponse request(GirlsAgentSimilarRecom.GirlsAgentSimilarRequest request) {
        try {
            log.info("xxxxxxxxxxxx, {}", request);
            return stub.withDeadlineAfter(DEADLINES, TimeUnit.SECONDS)
                    .girlsAgentSimilarRecommend(request);
        } catch (Exception e) {
            log.error("similar search GRPC 调用发生异常: {}", e.getMessage());
            throw e;
        }
    }

    @Retryable(
            retryFor = Exception.class,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    @CollectEvent
    public SimilarResponseDTO search(SimilarRequestDTO request) {
        try {
            log.info("请求 similar search GRPC 接口 -----------> : {}", JSONUtil.toJsonStr(request));
            GirlsAgentSimilarRecom.GirlsAgentSimilarResponse protoResponse = request(grpcConvertor.toGrpcSimilarRequest(request));
            SimilarResponseDTO recommendSearchResponseDTO = grpcConvertor.toSimilarResponseDTO(protoResponse);
            log.info("similar search GRPC 接口返回 <------------ : {}", JSONUtil.toJsonStr(recommendSearchResponseDTO));
            return recommendSearchResponseDTO;
        } catch (Exception e) {
            log.error("similar search grpc 接口请求失败", e);
            throw e;
        }
    }

    @Recover
    public SimilarResponseDTO recover(Exception e, SimilarRequestDTO request) {
        log.error("similar search GRPC 调用失败，重试 {} 次后仍然失败", 2, e);
        throw new RuntimeException("调用 similar search GRPC 服务失败", e);
    }
}