package com.looksky.agents.data.grpc.convertor;

import com.google.protobuf.BoolValue;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * Boolean 与 BoolValue 之间的转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface BoolValueMapper {

    /**
     * 将 Boolean 转换为 BoolValue
     * @param value Boolean 值
     * @return BoolValue 对象
     */
    default BoolValue map(Boolean value) {
        if (value == null) {
            return null;
        }
        return BoolValue.of(value);
    }

    /**
     * 将 BoolValue 转换为 Boolean
     * @param value BoolValue 对象
     * @return Boolean 值
     */
    default Boolean map(BoolValue value) {
        if (value == null) {
            return null;
        }
        return value.getValue();
    }
}