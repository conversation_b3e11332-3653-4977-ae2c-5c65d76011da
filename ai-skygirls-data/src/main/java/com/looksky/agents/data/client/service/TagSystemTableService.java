package com.looksky.agents.data.client.service;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.graecove.common.ApiResp;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.sdk.agent.common.enums.CategoryEnum;
import com.looksky.agents.sdk.agent.common.enums.TagEnum;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * @ClassName TagSystemTableService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/12 下午3:32
 * @Version 1.0
 **/
@Service
@RequiredArgsConstructor
public class TagSystemTableService {


    private static final Logger log = LoggerFactory.getLogger(TagSystemTableService.class);
    private final ObjectMapper objectMapper;
    private final GirlsClient girlsClient;
    private final TagSystemTableParser tagSystemParser;

    @PostConstruct
    private void init() {
        ApiResp<JsonNode> tagSystemTable = girlsClient.getTagSystemTable();
        tagSystemParser.parser(tagSystemTable.getData());
    }



    /**
     * 判断某个标签是否为该品类下的标签
     * @param category 品类
     * @param tag 标签
     * @return 是否为该品类下的标签
     */
    @Cacheable(value = "tagSystem:isCategoryTag", key = "#category + #tag")
    public boolean isCategoryTag(String category, String tag) {
        // 判断 tag 是否为通用标签, 如果为通用标签, 那么直接返回 true
        if (TagEnum.isCommonTag(tag)) {
            return true;
        } else if (CategoryEnum.CLOTH.getName().equals(category)) {
            // 判断品类是否为 cloth, 如果是, 那么直接返回 true
            return true;
        } else if (getFirstCategories().contains(category)) {
            // 判断品类是否为一级
            return tagSystemParser.getFirstCategoryTagValueMap().get(category).containsKey(tag);
        } else {
            // 判断品类是否为二级
            return tagSystemParser.getSubCategoryTagValueMap().get(category).containsKey(tag);
        }
    }



    /**
     * 尝试将用户表达的品类转换为二级品类, 如果转换失败, 返回原品类
     * @param categories 用户表达的品类
     * @return 转换后的品类列表
     */
    @Cacheable(value = "tagSystem:cTSub", key = "#categories.hashCode()")
    public Set<String> convertToSubCategory(Set<String> categories) {
        Set<String> result = new HashSet<>();
        for (String category : categories) {
            if (getFirstCategories().contains(category)) {
                result.addAll(getSubCategoryByFirstCategory(category));
            } else {
                result.add(category);
            }
        }
        return result;

    }


    /**
     * 获取某个标签下的所有标签值, 不分品类
     * @param tag 需要获取的标签
     * @return 这个标签的所有标签值
     */
    @Cacheable(value = "tagSystem:tagValues", key = "#tag")
    public List<String> getTagValues(String tag) {
        return tagSystemParser.getAllTagAndTagValueMap().get(tag).stream().sorted().toList();
    }

    /**
     * 获取所有的一级品类
     * @return 一级品类列表
     */
    @Cacheable(value = "tagSystem:firstCategories")
    public List<String> getFirstCategories() {
        return tagSystemParser.getFirstCategoryList().stream().sorted().toList();
    }

    /**
     * 获取所有的二级品类
     * @return 二级品类列表
     */
    @Cacheable(value = "tagSystem:subCategories")
    public List<String> getSubCategories() {
        return tagSystemParser.getSubCategoryList().stream().sorted().toList();
    }

    /**
     * 判断某个品类是否为二级品类
     * @param category 品类
     * @return 是否为二级品类
     */
    @Cacheable(value = "tagSystem:isSubCategory", key = "#category")
    public boolean isSubCategory(String category) {
        return getSubCategories().contains(category);
    }

    /**
     * 判断某个品类是否为一级品类
     * @param category 品类
     * @return 是否为一级品类
     */
    @Cacheable(value = "tagSystem:isFirstCategory", key = "#category")
    public boolean isFirstCategory(String category) {
        return getFirstCategories().contains(category);
    }

    /**
     * 根据一级品类获取二级品类
     * @param firstCategory 一级品类
     * @return 二级品类
     */
    @Cacheable(value = "tagSystem:subCategoryByFirstCategory", key = "#firstCategory")
    public List<String> getSubCategoryByFirstCategory(String firstCategory) {
        return tagSystemParser.getFirstSubCategoryMap().get(firstCategory).stream().sorted().toList();
    }


    /**
     * 根据二级品类获取一级品类
     * @param subCategory 二级品类
     * @return 一级品类
     */
    @Cacheable(value = "tagSystem:firstCategoryBySubCategory", key = "#subCategory")
    public String getFirstCategoryBySubCategory(String subCategory) {
        return tagSystemParser.getSubCategoryToFirstCategoryMap().get(subCategory);
    }


    /**
     * 检查currTag列表中是否有任何标签存在于allTag列表中
     * @param currTag 待检查的标签列表
     * @param allTag 所有标签列表
     * @return 如果currTag中有任何标签存在于allTag中则返回true，否则返回false
     */
    public static boolean isInnerTag(Set<String> currTag, Set<String> allTag) {
        return currTag.stream().anyMatch(allTag::contains);
    }

    /**
     * 判断checkList中是否包含有subCategory
     * @param checkList 待检查的列表
     * @return  如果checkList中有任何subCategory则返回true，否则返回false
     */
    @Cacheable(value = "tagSystem:containsSubCategory", key = "#checkList.hashCode()")
    public boolean containsSubCategory(Set<String> checkList) {
        if (ObjectUtil.isEmpty(checkList)) {
            return false;
        }
        return getSubCategories().stream().anyMatch(checkList::contains);
    }


    /**
     * 判断checkList中是否包含有firstCategory
     * @param checkList 待检查的列表
     * @return  如果checkList中有任何firstCategory则返回true，否则返回false
     */
    @Cacheable(value = "tagSystem:containsFirstCategory", key = "#checkList.hashCode()")
    public boolean containsFirstCategory(Set<String> checkList) {
        if (ObjectUtil.isEmpty(checkList)) {
            return false;
        }
        return getFirstCategories().stream().anyMatch(checkList::contains);
    }


    /**
     * 获取所有的品类信息, 包括一级和二级类目, 抽取用户表达的品类时使用
     * @return 所有的品类信息 包括一级和二级类目和CLOTH, 以json字符串的形式返回
     */
    @Cacheable(value = "tagSystem:categoryEnum")
    public List<String> getCategoryEnum() {
        ArrayList<String> result = new ArrayList<>(List.of("null", CategoryEnum.CLOTH.getName()));
        // 添加所有一级品类
        result.addAll(getFirstCategories());
        // 添加所有二级品类
        result.addAll(getSubCategories());
        return result.stream().sorted().toList();
    }


    /**
     * 根据品类获取内部 tag, 用于抽取用户表达的标签维度
     * @param category 品类, 可能是一级, 二级, cloth
     * @return 需要抽取的 tag 维度
     */
    @Cacheable(value = "tagSystem:innerTagEnumByCategory", key = "#category")
    public String getInnerTagEnumByCategory(String category) {
        log.debug("获取{}品类的父级tag", category);
        Map<String, String> tagToParentTagMap = tagSystemParser.getTagToParentTagMap();

        Set<String> resultSet = new HashSet<>(Set.of("price", "brand", "null"));
        // 如果是 cloth, 那么返回所有维度
        if (!StringUtils.hasText(category) || CategoryEnum.CLOTH.getName().equals(category)) {
            tagToParentTagMap.forEach((key, value) -> resultSet.add(value));

        } else if (getFirstCategories().contains(category)){
            // 如果是一级品类, 那么获取这个品类的所有 tag
            Set<String> firstCategoryTag = tagSystemParser.getFirstCategoryTagValueMap().get(category).keySet();
            // 然后找到父级, 添加到 resultSet中
            firstCategoryTag.forEach(t -> resultSet.add(tagToParentTagMap.get(t)));
        } else {
            // 传入的是二级品类
            Set<String> subCategoryTag = tagSystemParser.getSubCategoryTagValueMap().get(category).keySet();
            subCategoryTag.forEach(t -> resultSet.add(tagToParentTagMap.get(t)));
        }

        try {
            return objectMapper.writeValueAsString(resultSet.stream().sorted().toList());
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }



    /**
     * 获取某个标签的标签值
     * @param category 品类, 可能是一级类目或者二级类目, 也有可能是 CLOTH
     * @param tag 标签
     * @return 标签值列表
     */
    @Cacheable(value = "tagSystem:enumValues", key = "#category + #tag")
    public String getEnumValues(String category, String tag) {
        log.debug("获取{}品类的{}标签值", category, tag);

        List<String> result = new ArrayList<>(List.of("null"));
        List<String> strings = getTagAndTagValueByCategory(category).get(tag);
        if (ObjectUtil.isNotEmpty(strings)) {
            result.addAll(strings);
        }

        try {
            return objectMapper.writeValueAsString(result.stream().sorted().toList());
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 获取某个品类下的所有标签和标签值
     * @param category 品类, 可能是一级类目或者二级类目, 也有可能是 CLOTH
     * @return 标签和标签值的映射
     */
    @Cacheable(value = "tagSystem:tagAndTagValueByCategory", key = "#category")
    public Map<String, List<String>> getTagAndTagValueByCategory(String category) {
        // 如果 category 是一级类目, 找到 一级品类为 category, 并且 tag name 为 tag 的所有标签值
        if (getFirstCategories().contains(category)) {
            // 如果 category 是一级类目, 找到 一级品类为 category, 并且 tag name 为 tag 的所有标签值
            return tagSystemParser.getFirstCategoryTagValueMap().get(category);
        } else if (getSubCategories().contains(category)) {
            // 如果 category 是二级类目, 那么返回该二级类目下的所有标签值
            return tagSystemParser.getSubCategoryTagValueMap().get(category);
        } else {
            // 判断 category 是否为 CLOTH 或者为空, 也就是部分品类, 那么直接返回该 tag 的所有标签值
            return tagSystemParser.getAllTagAndTagValueMap();
        }
    }


    /**
     * 获取列表中的最小的品类 -> 值的是二级品类, 如果没有二级品类, 那么返回一级品类, 如果都没有, 那么返回 CLOTH
     * @param categories 品类列表
     * @return 最小的品类
     */
    @Cacheable(value = "tagSystem:smallestCategory", key = "#categories.hashCode()")
    public String obtainTheSmallestCategory(List<String> categories) {
        if (ObjectUtil.isEmpty(categories)) {
            return CategoryEnum.CLOTH.getName();
        }
    
        // 先找二级品类, 如果有, 那么返回二级品类
        for (String category : categories) {
            if (getSubCategories().contains(category)) {
                return category;
            }
        }
    
        // 如果没有二级品类, 再去一级品类里面找, 如果有, 那么返回一级品类
        for (String category : categories) {
            if (getFirstCategories().contains(category)) {
                return category;
            }
        }
    
        // 如果都没有, 那么返回 CLOTH
        return CategoryEnum.CLOTH.getName();
    }

    /**
     * 获取内部的父级标签
     * @param tag 标签体系里面的标签
     * @return 内部系统的标签, 对应标签体系的第二层级
     */
    @Cacheable(value = "tagSystem:innerParentTag", key = "#tag")
    public String getInnerParentTag(String tag) {
        return tagSystemParser.getTagToParentTagMap().get(tag);
    }

}
