package com.looksky.agents.data.redis;

import com.looksky.agents.sdk.recommend.foryou.enums.RecommendScenEnum;

/**
 * Redis key 常量类
 * 集中管理所有 Redis 的 key
 */
public class RedisKeyConstants {


    /**
     * 首页第一次打招呼
     */
    public static final String HOME_FIRST_OPENING = "opening:home:first";
    /**
     * 首页第二次打招呼
     */
    public static final String HOME_SECOND_OPENING = "opening:home:second";

    /**
     * 首页解锁了打招呼
     */
    public static final String HOME_UNLOCK_OPENING = "opening:home:unlock";

    /**
     * forYou 第一天打招呼
     */
    public static final String FOR_YOU_FIRST_DAY_OPENING = "opening:forYou:firstDays";
    /**
     * forYou 第二天打招呼
     */
    public static final String FOR_YOU_SECOND_DAY_OPENING = "opening:forYou:secondDays";

    /**
     * forYou 完成偏好设置需要回复的内容
     */
    public static final String FOR_YOU_COMPLETE_SETTINGS = "opening:forYou:completeSettings";

    
    /**
     * 商品相关的 key 前缀
     */
    public static final String PRODUCT_PRODUCT_INFO = "all:spu:{}";
    public static final String PRODUCT_SKC_INFO = "all:skc:{}";
    public static final String PRODUCT_SKC_ELASTICITY = "skc_elasticity:{}";

    /**
     * 会话相关的 key
     */
    public static final String CONVERSATION_HISTORY = "agent:%s:%s:history";

    /**
     * 消息相关的 key
     */
    private static final String CONVERSATION_INFO = "agent:%s:%s:status";

    private static final String CONVERSATION_USER_PREFERENCE = "agent:%s:%s:preference";

    private static final String RECOMMEND_PRODUCT = "agent:%s:%s:recommend";
    
    /**
     * 用户是否首次发送消息key（用户级维度）
     */
    private static final String USER_FIRST_SEND_MESSAGE_STATUS = "agent:user:%s:firstSendMessage";

    /**
     * tryOn ColorSeason 新进入 app 用户
     */
    public static final String TRY_ON_COLOR_SEASON_NOTIFY = "tryon:colorseason:notify";


    /**
     * 会话过程中的一些状态信息
     * @param userId
     * @param conversationId
     * @return
     */
    public static String statusKey(String userId, String conversationId) {
        return String.format(CONVERSATION_INFO, userId, conversationId);
    }

    /**
     * 会话中用户活跃状态的key（用户级维度）
     * @param userId 用户 id
     * @return 用户是否首次发送消息key
     */
    public static String userFirstSendMessageKey(String userId) {
        return String.format(USER_FIRST_SEND_MESSAGE_STATUS, userId);
    }

    /**
     * 会话中存储的用户偏好
     * @param userId
     * @param conversationId
     * @return
     */
    public static String preferenceKey(String userId, String conversationId) {
        return String.format(CONVERSATION_USER_PREFERENCE, userId, conversationId);
    }

    /**
     * 推荐返回的商品
     * @param userId
     * @return
     */
    public static String recommendProductKey(String userId, String conversationId) {
        return String.format(RECOMMEND_PRODUCT, userId, conversationId);
    }

    public static String historyKey(String userId, String conversationId) {
        return String.format(CONVERSATION_HISTORY, userId, conversationId);
    }


    public static String daily100Key(String userId) {
        return String.format("daily100:%s:step2", userId);
    }

    public static String daily100CategoryRecommendKey(String userId) {
        return String.format("daily100:%s:step1", userId);
    }

    public static String daily100CategoryRecommendGeneratingKey(String userId) {
        return String.format("daily100:category:recommend:generating:%s", userId);
    }

    public static String daily100CategoryRecommendLockKey(String userId) {
        return String.format("daily100:%s:lock", userId);
    }

    @Deprecated
    private static final String KB_KEY = "agent:cache:kibbe";

    @Deprecated
    public static String kbKey() {
        return KB_KEY;
    }

    @Deprecated
    private static final String COLOR_SEASON = "agent:cache:colorSeason";
    @Deprecated
    public static String colorSeasonKey() {
        return COLOR_SEASON;
    }

    @Deprecated
    private static final String FACE2HAIR = "agent:cache:face2hair";
    @Deprecated
    public static String face2HairKey() {
        return FACE2HAIR;
    }

    public static String girlsRecomReasonKey(String userId, String itemId) {
        return String.format("recomReason:girls:%s:%s", userId, itemId);
    }

    public static String girlsRecomReasonLockKey(String userId, String itemId) {
        return String.format("lock:recomReason:girls:%s:%s", userId, itemId);
    }

    public static String forYouRecomReasonLockKey(String userId, String itemId) {
        return String.format("lock:recomReason:foryou:%s:%s", userId, itemId);
    }

    /**
     * OpenAI监控相关的键名
     */
    private static final String OPENAI_MONITORING_LATENCY = "monitoring:openai:latency:%s";

    /**
     * 获取OpenAI监控耗时的键名，按日期存储
     * @param date 日期，格式为yyyyMMdd
     * @return Redis键名
     */
    public static String openaiMonitoringLatencyKey(String date) {
        return String.format(OPENAI_MONITORING_LATENCY, date);
    }


    public static String swapClothKey(String id) {
        return String.format("swapCloth:%s", id);
    }


    /**
     * colorSeason 试穿相关的键名
     */
    public static final String TRY_ON_COLOR_SEASON_DESCRIPTION = "tryon:colorseason:descriptions";

    /**
     * 存储 foryou 分区数据
     * @param userId
     * @param scene
     * @return
     */
    public static String forYouKey(String userId, String scene) {
        return String.format("forYou:%s:%s", userId, scene);
    }

    /**
     *  for you 板块的锁
     * @param userId
     * @return
     */
    public static String forYouLock(String userId) {
        return String.format("forYou:lock:%s", userId);
    }

    /**
     * 最佳匹配第一次访问的主题数据
     * @return
     */
    public static String forYouMatchThemesFirstKey() {
        return "forYou:themes:" + RecommendScenEnum.GIRLS_PARTITION_MATCH.getName() + ":first";
    }

    /**
     * 最佳匹配每日访问的主题数据
     * @return
     */
    public static String forYouMatchThemesDailyKey() {
        return "forYou:themes:" + RecommendScenEnum.GIRLS_PARTITION_MATCH.getName() + ":daily";
    }

    public static String adviceQuestionKey(String page, String enterPoint, String event) {
        return "advice_questions:%s:%s:%s".formatted(page, enterPoint, event);
    }

    /**
     * 用户点击了建议的问题, 获取他的下一步应该执行什么流程
     * @param md5
     * @return
     */
    public static String adviceQuestionNextStep(String md5) {
        return "advice_questions:next_step:%s".formatted(md5);
    }


}