package com.looksky.agents.data.redis.daily100;

import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.sdk.recommend.common.dto.ColorSeasonCacheDataDTO;
import com.looksky.agents.sdk.recommend.common.dto.Face2HairCacheDataDTO;
import com.looksky.agents.sdk.recommend.common.dto.UserPageKibbeCacheDataDTO;
import com.looksky.agents.sdk.recommend.common.dto.VectorRecallModelDTO;
import com.looksky.agents.sdk.recommend.daily100.dto.Daily100Step1DTO;
import com.looksky.agents.sdk.recommend.daily100.dto.Daily100Step2DTO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RList;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class Daily100DataService {

    private final RedissonClient redissonClient;
    private final Daily100LocalCache daily100LocalCache;

    /**
     * 缓存category recommend结果
     */
    public void cacheDaily100CategoryRecommend(String userId, Daily100Step1DTO daily100Step1DTO) {
        RBucket<Daily100Step1DTO> bucket = redissonClient.getBucket(
            RedisKeyConstants.daily100CategoryRecommendKey(userId),
            new TypedJsonJacksonCodec(Daily100Step1DTO.class)
        );
        bucket.setAsync(daily100Step1DTO);
    }

    /**
     * 获取用户的category recommend
     */
    public Daily100Step1DTO getDaily100CategoryRecommend(String userId) {
        RBucket<Daily100Step1DTO> bucket = redissonClient.getBucket(
            RedisKeyConstants.daily100CategoryRecommendKey(userId),
            new TypedJsonJacksonCodec(Daily100Step1DTO.class)
        );
        if (bucket.isExists()) {
            log.info("从缓存中获取到用户: {} 的 daily100 category recommend 数据", userId);
            return bucket.get();
        } else {
            log.info("缓存中没有获取到用户: {} 的 daily100 category recommend 数据", userId);
            return null;
        }
    }

    /**
     * 获取分布式锁
     */
    public RLock getDaily100CategoryRecommendLock(String userId) {
        return redissonClient.getLock(RedisKeyConstants.daily100CategoryRecommendLockKey(userId));
    }

    /**
     * 获取缓存的Daily100查询对象
     */
    public Daily100Step2DTO getDaily100Step2Cache(String userId) {
       RList<Daily100Step2DTO> list = redissonClient.getList(
           RedisKeyConstants.daily100Key(userId),
           new TypedJsonJacksonCodec(Daily100Step2DTO.class)
       );
       if (list.isExists() && !list.isEmpty()) {
            log.info("从redis中获取到用户: {} 的 daily100Step2 缓存数据", userId);
            return list.getLast();
        } else {
            log.info("Redis 中没有获取到用户: {} 的 daily100Step2 缓存数据", userId);
            return null;
        }
    }

    /**
     * 缓存Daily100查询对象
     */
    public void cacheDaily100Step2(String userId, Daily100Step2DTO daily100Step2DTO) {
        RList<Daily100Step2DTO> list = redissonClient.getList(
            RedisKeyConstants.daily100Key(userId),
            new TypedJsonJacksonCodec(Daily100Step2DTO.class)
        );
        // 保留最近三天数据
        if (list.size() >= 3) {
            list.removeFirst();
        }

        list.addAsync(daily100Step2DTO);
    }

    @Deprecated
    public List<VectorRecallModelDTO> getCacheQuery(String userId) {
        return null;
    }

    @Deprecated
    public void cacheQuery(String userId, String md5, List<VectorRecallModelDTO> data) {
    }

    @SneakyThrows
    @Cacheable(value = "kbType", key = "#kbType")
    public UserPageKibbeCacheDataDTO getKbType(String kbType) {
        return daily100LocalCache.getKbType(kbType);
    }

    @SneakyThrows
    @Cacheable(value = "colorSeason", key = "#colorSeason")
    public ColorSeasonCacheDataDTO getColorSeason(String colorSeason) {
        return daily100LocalCache.getColorSeason(colorSeason);
    }

    @SneakyThrows
    @Cacheable(value = "face2Hair", key = "#faceType")
    public Face2HairCacheDataDTO getFace2Hair(String faceType) {
        return daily100LocalCache.getFace2Hair(faceType);
    }

    /**
     * 预生成每日100推荐
     * 扫描所有用户的 step1 缓存，检查生成日期是否超过7天
     * 
     * @return 超过7天的用户ID列表
     */
    @SuppressWarnings("deprecation")
    public List<String> getExpiredUserIds() {
        List<String> expiredUserIds = new ArrayList<>();
        
        // 获取所有符合模式的键
        String keyPattern = "daily100:*:step1";
        Iterable<String> keys = redissonClient.getKeys().getKeysByPattern(keyPattern);
        
        for (String key : keys) {
            try {
                RBucket<Daily100Step1DTO> bucket = redissonClient.getBucket(key, new TypedJsonJacksonCodec(Daily100Step1DTO.class));
                if (bucket.isExists()) {
                    Daily100Step1DTO step1DTO = bucket.get();
                    Date generateDate = step1DTO.getGenerateDate();
                    Date now = new Date();
                    
                    // 计算时间差
                    long diffInMillies = now.getTime() - generateDate.getTime();
                    long diffInDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
                    
                    // 如果超过7天，从键中提取用户ID并添加到列表
                    if (diffInDays > 7) {
                        expiredUserIds.add(step1DTO.getUserId());
                        log.info("找到过期缓存，用户ID: {}, 生成时间: {}, 已过期天数: {}", step1DTO.getUserId(), generateDate, diffInDays);
                    }
                }
            } catch (Exception e) {
                log.error("处理缓存键 {} 时发生错误", key, e);
            }
        }
        
        log.info("共找到 {} 个过期缓存", expiredUserIds.size());
        return expiredUserIds;
    }
}
