package com.looksky.agents.data.client.service;


import com.graecove.common.ApiResp;
import com.looksky.agents.sdk.datahub.version.response.UserClientUaResponse;
import com.looksky.agents.sdk.user.visit.dto.VisitHistoryDTO;
import com.looksky.agents.data.client.business.DataHubClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName UserDataService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/12 下午2:18
 * @Version 1.0
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class DataHubDataService {
    private final DataHubClient dataHubClient;

    public VisitHistoryDTO visitHistory(String userId) {
        try {
            ApiResp<VisitHistoryDTO> response = dataHubClient.appVisitHistory(userId);
            return response.getData();
        } catch (Exception e) {
            log.error("获取用户访问历史失败, userId: {}", userId, e);
            return new VisitHistoryDTO();
        }
    }

    public String getUserAppVersion(String userId) {
        ApiResp<UserClientUaResponse> userClientUaResponseApiResp = dataHubClient.clientUa(userId);
        return userClientUaResponseApiResp.getData().getClientUa();
    }
}
