package com.looksky.agents.data.opensearch;

/**
 * @Author：ch
 * @Date：2024/8/20 15:45
 * @Description
 */
public class OsProductMappingType {

    //index
    public static final String PRODUCT_SKC_INDEX = "products_skc";
    public static final String BRAND_INDEX = "brand-all-index";

    //inner-hit
    public static final String TOP_HITS_BY_MERCHANT = "top_hits_by_merchant";

    //skc
    public static final String SKC_ID = "skc_id";
    public static final String PRODUCT_ID = "product_id";
    public static final String PRICE_FLAG = "price_flag";
    public static final String REBATE_FLAG = "rebate_flag";
    public static final String BRAND = "merchant_name";
    public static final String TITLE = "title";
    public static final String DESC = "desc";
    public static final String SKC_PRICE = "skc_price";
    public static final String SKC_ORIGINAL_PRICE = "skc_original_price";
    public static final String KIBBE_STYLES = "kibbe_styles";
    public static final String SKC_LINK = "skc_link";
    public static final String SALE_SIZE = "sale_size";
    public static final String SALE_OVER_NUM = "sale_over_num";
    public static final String SKC_STATUS = "skc_status";
    public static final String SKC_IMGS = "skc_imgs";
    public static final String BRAND_STATUS = "brand_status";
    public static final String SKC_PUBLISH_TIME = "skc_publish_time";
    public static final String SKYGIRLS_BRAND = "skygirls_brand";
    public static final String SKC_DISCOUNT = "skc_discounts";
    public static final String SKC_TAG = "tags";

    //copy-to
    public static final String FULL_DESC = "full_desc";
    public static final String COMBINED_FABRIC = "combined_fabric";

    //skc_tag
    public static final String KIBBE_STYLE = "kibbe_style";
    public static final String FIRST_CATEGORY = "first_category";
    public static final String SECOND_CATEGORY = "sub_category";

    //tag_pattern
    public static final String PATTERN_TYPE = "pattern_type";
    public static final String SPECIFIC_PATTERN_TYPE = "specific_pattern_type";
    public static final String SPECIFIC_PATTERN_SCALE = "specific_pattern_scale";
    public static final String SPECIFIC_PATTERN_ARRANGEMENT = "specific_pattern_arrangement";

    //tag_Trims
    public static final String TRIMS_TYPE = "trims_type";
    public static final String TRIMS_POSITION = "trims_position";

    //tag_Design
    public static final String DESIGN_DETAIL_TYPE = "design_detail_type";
    public static final String DESIGN_DETAIL_POSITION = "design_detail_position";
    public static final String OTHER_DESIGN = "other_design";

    //tag_Closure
    public static final String CLOSURE_TYPE = "closure_type";
    public static final String CLOSURE_POSITION = "closure_position";

    //tag_pocket
    public static final String POCKET_TYPE = "pocket_type";
    public static final String POCKET_POSITION = "pocket_position";
    public static final String POCKET_SHAPE = "pocket_shape";
    public static final String POCKET_AMT = "pocket_amt";

    //向量
    public static final String JINA_VECTOR = "jina_vector";
    public static final String MARGO_VECTOR = "margo_vector";
    public static final String MERGE_VECTOR = "merge_vector";
    public static final String MERGE_VECTOR_2 = "merge_vector_2";
    public static final String JINA_TEXT = "jina_text";
    public static final String MARQO_ECOMMERCE = "marqo_ecommerce";
    public static final String JINA_MATERIAL_TEXT = "jina_material_text";
    public static final String JINA_SAM = "jina_sam";

    //非系统标签
    public static final String NON_VISUAL = "non_visual";
    public static final String HIGHLIGHT = "highlight";

    //public static final OsProductVectorFieldEnums QUERY_VECTOR = OsProductVectorFieldEnums.margo_vector;

    public static final String TAG_PARENT_VALUE = "value";




}
