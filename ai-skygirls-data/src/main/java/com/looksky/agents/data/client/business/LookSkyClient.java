package com.looksky.agents.data.client.business;


import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.graecove.common.ApiResp;
import com.looksky.agents.sdk.agent.search.dto.TestSearchRequestDTO;
import com.looksky.agents.sdk.recommend.daily100.dto.request.Daily100TestRequestDTO;
import com.looksky.agents.infrastructure.httpapi.ApiServiceEnum;
import com.looksky.agents.infrastructure.httpapi.HttpApi;
import com.skygirls.biz.agent.dto.BodyMeasurementReq;
import com.skygirls.biz.agent.dto.BodyMeasurementResp;
import com.skygirls.biz.im.dto.AgentMessageRespV3;
import com.skygirls.biz.user.dto.VisitHistoryDTO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.PostExchange;

@HttpApi(service = ApiServiceEnum.LOOKSKY_API)
public interface LookSkyClient {
    ///**
    // * 获取用户个人中心保存的数据
    // * @param mainPageDTO 请求对象, 里面只有一个 userId
    // * @return 个人中心保存的数据
    // */
    //@Deprecated
    //@PostExchange("/user/infoV2/mainPage1")
    //ApiResp<UserMainPageDTO> getUser(@RequestBody MainPageDTO mainPageDTO);
    //
    //
    //@Deprecated
    //@PostExchange("/user/infoV2/mainPage")
    //ApiResp<UserPersonalCenterData> getUserPersonalCenterData(@RequestBody MainPageDTO mainPageDTO);



    /**
     * 获取用户在对话过程中累加的消息
     * @param userid 放在请求头中的用户id
     * @param sign 放在请求头中的签名
     * @param userId 需要获取哪个用户的数据
     * @return BodyMeasurementResp 用户的对话中累加的消息
     */
    //@Deprecated
    @GetExchange("/sign/agent/getById")
    ApiResp<BodyMeasurementResp> getBodyMeasurement(@RequestHeader String userid, @RequestHeader String sign, @RequestParam String userId);

    /**
     * 更新用户的对话消息
     * @param bodyMeasurementReq 请求对象
     * @return 累加结果
     */
    //@Deprecated
    @PostExchange("/sign/agent/bodyMeasurement")
    ApiResp<String> updateUserBodyMeasurement(@RequestBody BodyMeasurementReq bodyMeasurementReq);


    @PostExchange("/chat/receiveAgentMsgV2")
    ApiResp<Void> sendAgentMessage(@RequestBody AgentMessageRespV3 agentMessageRespV3);


    @PostExchange("/recsys/recom/girlsDaily100Test")
    JSONObject daily100Test(@RequestBody Daily100TestRequestDTO daily100TestReqModel);


    @Deprecated(since = "1.1.9", forRemoval = true)
    @GetExchange("/category/getTagsConfigV2?publicKey=225a44b332f0492bbbc04b4ab5378f42")
    ApiResp<JsonNode> getTagSystemTable();


    @Deprecated
    @GetExchange("/datahub/user/appVisitHistory")
    ApiResp<VisitHistoryDTO> appVisitHistory(@RequestParam String userId);


    @PostExchange("/recsys/recom/girlsSearchTestEvaluation")
    JSONObject girlsSearchTestEvaluation(@RequestBody TestSearchRequestDTO testSearchRequestDTO);



}