package com.looksky.agents.data.grpc;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.data.grpc.convertor.ColorSeasonConvertor;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.interceptor.GrpcClientHeaderInterceptor;
import com.looksky.agents.infrastructure.interceptor.GrpcResponseHeaderInterceptor;
import com.looksky.agents.sdk.recommend.colorseason.request.ColorSeasonRequestDTO;
import com.looksky.agents.sdk.recommend.colorseason.response.ColorSeasonResponseDTO;
import com.looksky.agents.sdk.recommend.similar.dto.request.SimilarRequestDTO;
import com.looksky.agents.sdk.recommend.similar.dto.response.SimilarResponseDTO;
import com.westyle.recm.GirlsColorSeasonRecm;
import com.westyle.recm.GirlsColorSeasonRecommendServiceGrpc;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.client.inject.GrpcClient;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ColorSeasonGrpcClient {

    @GrpcClient("girlsColorSeasonRecommendService")
    private GirlsColorSeasonRecommendServiceGrpc.GirlsColorSeasonRecommendServiceBlockingStub stub;

    @Resource
    private ColorSeasonConvertor grpcConvertor;

    private static final long DEADLINES = 15;

    @PostConstruct
    public void init() {
        // 在初始化时添加拦截器
        stub = stub.withInterceptors(new GrpcClientHeaderInterceptor(), new GrpcResponseHeaderInterceptor());
    }

    private GirlsColorSeasonRecm.GirlsColorSeasonResponse request(GirlsColorSeasonRecm.GirlsColorSeasonRequest request) {
        try {
            log.info("xxxxxxx, {}", request);
            return stub.withDeadlineAfter(DEADLINES, TimeUnit.SECONDS).colorSeasonRecommend(request);
        } catch (Exception e) {
            log.error("similar search GRPC 调用发生异常: {}", e.getMessage());
            throw e;
        }
    }

    @Retryable(
            retryFor = Exception.class,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    @CollectEvent
    public ColorSeasonResponseDTO colorSeason(ColorSeasonRequestDTO request) {
        try {
            log.info("请求 color season GRPC 接口 -----------> : {}", JSONUtil.toJsonStr(request));
            GirlsColorSeasonRecm.GirlsColorSeasonResponse protoResponse = request(grpcConvertor.toGrpcColorSeasonRequest(request));
            ColorSeasonResponseDTO colorSeasonResponseDTO = grpcConvertor.toColorSeasonResponseDTO(protoResponse);
            log.info("color season GRPC 接口返回 <------------ : {}", JSONUtil.toJsonStr(colorSeasonResponseDTO));
            return colorSeasonResponseDTO;
        } catch (Exception e) {
            log.error("color season grpc 接口请求失败", e);
            throw e;
        }
    }

    @Recover
    public SimilarResponseDTO recover(Exception e, SimilarRequestDTO request) {
        log.error("color season GRPC 调用失败，重试 {} 次后仍然失败", 2, e);
        throw new RuntimeException("调用 color season GRPC 服务失败", e);
    }
}