package com.looksky.agents.data.grpc.convertor;

import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import com.looksky.agents.sdk.recommend.common.dto.VectorRecallModelDTO;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import com.westyle.recm.AgentSearchTerm;
import com.westyle.recm.ItemOuterClass;
import com.westyle.recm.VectorRecall;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * 通用转换器，包含可以在多个转换器之间共享的方法
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface CommonConvertor {
    
    CommonConvertor INSTANCE = Mappers.getMapper(CommonConvertor.class);
    
    /**
     * 将VectorRecallModelDTO转换为VectorRecallModel
     * @param vectorRecallModelDTO VectorRecallModelDTO对象
     * @return VectorRecallModel对象
     */
    @Named("toGrpcVectorRecallModel")
    default VectorRecall.VectorRecallModel toGrpcVectorRecallModel(VectorRecallModelDTO vectorRecallModelDTO) {
        if (vectorRecallModelDTO == null) {
            return null;
        }
        
        VectorRecall.VectorRecallModel.Builder builder = VectorRecall.VectorRecallModel.newBuilder();
        
        // 设置召回策略
        if (vectorRecallModelDTO.getRecallStrategy() != null) {
            builder.setRecallStrategy(vectorRecallModelDTO.getRecallStrategy());
        }
        
        // 处理vectorQuery列表
        if (vectorRecallModelDTO.getVectorQuery() != null && !vectorRecallModelDTO.getVectorQuery().isEmpty()) {
            for (SearchRequestDTO.VectorQuery query : vectorRecallModelDTO.getVectorQuery()) {
                AgentSearchTerm.VectorQueryModel vectorQueryModel = toGrpcVectorQueryModel(query);
                if (vectorQueryModel != null) {
                    builder.addVectorQuery(vectorQueryModel);
                }
            }
        }
        
        // 处理mustSubCategory列表
        if (vectorRecallModelDTO.getMustSubCategory() != null && !vectorRecallModelDTO.getMustSubCategory().isEmpty()) {
            builder.addAllMustSubCategory(vectorRecallModelDTO.getMustSubCategory());
        }
        
        // 处理mustNotSubCategory列表
        if (vectorRecallModelDTO.getMustNotSubCategory() != null && !vectorRecallModelDTO.getMustNotSubCategory().isEmpty()) {
            builder.addAllMustNotSubCategory(vectorRecallModelDTO.getMustNotSubCategory());
        }
        
        return builder.build();
    }

    /**
     * 将VectorQuery转换为VectorQueryModel
     * @param vectorQuery VectorQuery对象
     * @return VectorQueryModel对象
     */
    @Named("toGrpcVectorQueryModel")
    default AgentSearchTerm.VectorQueryModel toGrpcVectorQueryModel(SearchRequestDTO.VectorQuery vectorQuery) {
        if (vectorQuery == null) {
            return null;
        }
        
        AgentSearchTerm.VectorQueryModel.Builder builder = AgentSearchTerm.VectorQueryModel.newBuilder();
        
        if (vectorQuery.getText() != null) {
            builder.setText(vectorQuery.getText());
        }
        
        if (vectorQuery.getWeight() != null) {
            builder.setWeight(vectorQuery.getWeight());
        }
        
        return builder.build();
    }
    
    /**
     * 将GRPC Item对象转换为ItemDTO
     * @param item GRPC Item对象
     * @return ItemDTO对象
     */
    @Named("toItemDTO")
    default ItemDTO toItemDTO(ItemOuterClass.Item item) {
        if (item == null) {
            return null;
        }
        
        return ItemDTO.builder()
            .itemId(item.getItemId())
            .itemType(item.getItemType())
            .strategy(item.getStrategy())
            .index(item.getIndex())
            .spuId(item.getSpuId())
            .build();
    }
} 