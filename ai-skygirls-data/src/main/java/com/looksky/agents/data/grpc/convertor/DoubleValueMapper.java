package com.looksky.agents.data.grpc.convertor;

import com.google.protobuf.DoubleValue;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * Double 与 DoubleValue 之间的转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface DoubleValueMapper {

    /**
     * 将 Double 转换为 DoubleValue
     * @param value Double 值
     * @return DoubleValue 对象
     */
    default DoubleValue map(Double value) {
        if (value == null) {
            return null;
        }
        return DoubleValue.of(value);
    }

    /**
     * 将 DoubleValue 转换为 Double
     * @param value DoubleValue 对象
     * @return Double 值
     */
    default Double map(DoubleValue value) {
        if (value == null) {
            return null;
        }
        return value.getValue();
    }
}