package com.looksky.agents.data.grpc;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.data.grpc.convertor.DailyConvertor;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.config.RetryConfig;
import com.looksky.agents.infrastructure.interceptor.GrpcClientHeaderInterceptor;
import com.looksky.agents.infrastructure.interceptor.GrpcResponseHeaderInterceptor;
import com.looksky.agents.infrastructure.retry.DynamicRetry;
import com.looksky.agents.sdk.recommend.daily100.dto.request.DailyRequestDTO;
import com.looksky.agents.sdk.recommend.daily100.dto.response.DailyResponseDTO;
import com.westyle.recm.GirlsDailyRecm;
import com.westyle.recm.GirlsDailyRecommendServiceGrpc;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.client.inject.GrpcClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class Daily100GrpcClient {

    @GrpcClient("girlsDailyRecommendService")
    private GirlsDailyRecommendServiceGrpc.GirlsDailyRecommendServiceBlockingStub stub;

    @Resource
    private RetryConfig retryConfig;

    @Resource
    private DailyConvertor grpcConvertor;

    @PostConstruct
    public void init() {
        // 在初始化时添加拦截器
        stub = stub.withInterceptors(new GrpcClientHeaderInterceptor(), new GrpcResponseHeaderInterceptor());
    }

    private GirlsDailyRecm.GirlsDailyResponse request(GirlsDailyRecm.GirlsDailyRequest request) {
        try {
            return stub.withDeadlineAfter(retryConfig.getDaily100ExpectedDuration(), TimeUnit.MILLISECONDS)
                    .appDailyRecommend(request);
        } catch (Exception e) {
            log.error("daily 100 GRPC 调用发生异常: {}", e.getMessage());
            throw e;
        }
    }

    @DynamicRetry(
        timeout = 28000,
        maxAttempts = 3
    )
    @CollectEvent
    public DailyResponseDTO search(DailyRequestDTO request) {
        try {
            log.info("请求 daily 100 GRPC 接口 -----------> : {}", JSONUtil.toJsonStr(request));
            GirlsDailyRecm.GirlsDailyResponse protoResponse = request(grpcConvertor.toGrpcDailyRequest(request));
            DailyResponseDTO recommendSearchResponseDTO = grpcConvertor.toDailyResponseDTO(protoResponse);
            log.info("daily 100 GRPC 接口返回 <------------ : {}", JSONUtil.toJsonStr(recommendSearchResponseDTO));
            if (CollectionUtils.isEmpty(recommendSearchResponseDTO.getItems())) {
                log.error("daily 100 接口返回数据为空");
                throw new RuntimeException("daily 100 接口返回数据为空");
            }

            return recommendSearchResponseDTO;
        } catch (Exception e) {
            log.error("daily 100 grpc 接口请求失败", e);
            throw e;
        }
    }
}