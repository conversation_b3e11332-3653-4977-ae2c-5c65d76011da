package com.looksky.agents.data.mysql.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.looksky.agents.sdk.agent.prompt.model.PromptMetadataModel;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-21 14:14:56
 */
public interface IPromptMetadataService extends IService<PromptMetadataModel> {
    List<PromptMetadataModel> getMetaDataByPromptId(String name);

    /**
     * 根据元数据查询对应的prompt
     * @param tag
     * @return
     */
    PromptMetadataModel findMetadata(PromptMetadataModel tag);
}
