package com.looksky.agents.data.opensearch;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.opensearch.client.opensearch.core.search.Hit;

/**
 * @Author：ch
 * @Date：2024/8/20 17:47
 * @Description
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class OsSkcModel {

    //spu属性
    private String product_id;
    private String desc;
    private String title;
    private String spu;
    private String merchant_name;
    private Integer skygirls_brand;

    //skc属性
    private String skc_id;
    private String skc;
    private String skc_link;
    private Double skc_price;
    private Double skc_highest_price;
    private Double skc_original_price;
    private Double skc_highest_original_price;
    private Integer skc_status;
    private Date skc_update_time;
    private Date skc_publish_time;
    private Integer sale_over_num;
    private Integer price_flag;
    private Integer rebate_flag;
    private List<String> skc_imgs;

    private List<String> sale_size;

    private JSONObject tags;

    //获取扁平化的标签
    private Map<String,List<String>> flatTags;
    //查询排序的数据
    private List<String> searchAfterSort;
    //分数
    private Double score;
    //原始分数
    private Double originalScore;

    public static OsSkcModel initByHit(Hit<OsSkcModel> hit){

        if(hit == null || hit.source() == null){
            return null;
        }

        OsSkcModel source = hit.source();
        source.searchAfterSort = hit.sort();
        source.score = hit.score() == null ? 0 : hit.score() % 1;
        source.originalScore = hit.score();
        return source;
    }



}
