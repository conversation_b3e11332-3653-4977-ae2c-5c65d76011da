package com.looksky.agents.data.client.business;


import cn.hutool.json.JSONObject;
import com.looksky.agents.sdk.recommend.daily100.dto.request.Daily100TestRequestDTO;
import com.looksky.agents.infrastructure.httpapi.ApiServiceEnum;
import com.looksky.agents.infrastructure.httpapi.HttpApi;
import com.looksky.agents.sdk.recommend.foryou.dto.ForYouParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;

@HttpApi(service = ApiServiceEnum.LOCALHOST)
public interface LookSkyClientLocal {


    @PostExchange("/recsys/recom/girlsDaily100Test")
    JSONObject daily100Test(@RequestBody Daily100TestRequestDTO daily100TestReqModel);


    @PostExchange("/recsys/recom/girlsColorSeasonTest")
    JSONObject girlsColorSeasonTest(@RequestBody Daily100TestRequestDTO daily100TestReqModel);


    @PostExchange("/recsys/recom/partitionDailyTest")
    JSONObject partitionDailyTest(@RequestBody ForYouParam forYouTestRequestDTO);


}