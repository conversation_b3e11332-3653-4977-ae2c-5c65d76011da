package com.looksky.agents.data.opensearch;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import com.looksky.agents.sdk.product.ProductInfo;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.FieldValue;
import org.opensearch.client.opensearch._types.query_dsl.Query;
import org.opensearch.client.opensearch._types.query_dsl.TermsQueryField;
import org.opensearch.client.opensearch.core.SearchRequest;
import org.opensearch.client.opensearch.core.SearchResponse;
import org.opensearch.client.opensearch.core.search.Collector;
import org.opensearch.client.opensearch.core.search.Hit;
import org.opensearch.client.opensearch.core.search.Profile;
import org.springframework.stereotype.Service;

/**
 * @Author：ch
 * @Date：2024/8/26 10:11
 * @Description
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SkcSearchService {

    private final OpenSearchClient openSearchClient;

    /**
     * 获取所有品牌名称
     * @return 品牌列表
     */
    public List<String> findAllBrand() {

        try {
            SearchRequest searchRequest = new SearchRequest.Builder()
                .index(OsProductMappingType.BRAND_INDEX)
                .query(Query.of(q -> q.matchAll(m -> m)))
                .source(s -> s.filter(f -> f.includes(ListUtil.of("name"))))
                .size(10000)
                .requestCache(true)
                .build();

            long startTime = System.currentTimeMillis();

            SearchResponse<BrandModel> searchResponse = openSearchClient.search(searchRequest, BrandModel.class);

            log.info("opensearch 查询品牌列表 结束: {}ms", System.currentTimeMillis() - startTime);

            return searchResponse.hits().hits().stream().map(Hit::source).filter(Objects::nonNull).map(BrandModel::getName).collect(Collectors.toCollection(ArrayList::new));
        } catch (Exception e) {
            log.error("opensearch 查询所有品牌失败 失败:{}", e.getMessage(), e);
        }

        return Collections.emptyList();
    }

    public ProductInfo searchSkc(String skcId) throws IOException {

        FieldValue fieldValue = FieldValue.of(skcId);

        SearchRequest.Builder builder = new SearchRequest.Builder()
            .index(OsProductMappingType.PRODUCT_SKC_INDEX)
            .query(Query.of(q -> q.terms(t -> t.field(OsProductMappingType.SKC_ID).terms(TermsQueryField.of(tm -> tm.value(List.of(fieldValue)))))))
            .from(0).size(1);

        return searchSkcByDsl(builder.build()).getFirst();
    }


    /**
     * 查询商品详情
     */
    public List<ProductInfo> searchSkc(List<String> skcIds) throws IOException {

        List<FieldValue> fieldValues = skcIds.stream().map(FieldValue::of).collect(Collectors.toList());
        SearchRequest.Builder builder = new SearchRequest.Builder()
            .index(OsProductMappingType.PRODUCT_SKC_INDEX)
            .query(Query.of(q -> q.terms(t -> t.field(OsProductMappingType.SKC_ID).terms(TermsQueryField.of(tm -> tm.value(fieldValues))))))
            .from(0).size(skcIds.size());

        return searchSkcByDsl(builder.build());
    }


    public List<ProductInfo> searchSkcByDsl(SearchRequest searchRequest) throws IOException {

        log.info("opensearch 查询商品列表 开始");

        long startTime = System.currentTimeMillis();

        SearchResponse<ProductInfo> searchResponse = openSearchClient.search(searchRequest, ProductInfo.class);

        log.info("opensearch 查询商品列表 结束: {}ms,查询数量为:{}", System.currentTimeMillis() - startTime, searchResponse.hits().hits().size());

        Profile profile = searchResponse.profile();
        if (profile != null) {
            long sum = profile.shards().stream().mapToLong(shard -> shard.searches().stream().mapToLong(searchProfile -> searchProfile.collector().stream().mapToLong(Collector::timeInNanos).sum()).sum()).sum();

            log.info("opensearch 查询商品列表 profile 耗时: {} 秒", NumberUtil.div(sum,1000000000));
        }

        if (searchRequest.collapse() != null && !searchRequest.collapse().innerHits().isEmpty()) {
            return searchResponse.hits().hits().stream()
                .map(hit -> hit.innerHits().get(OsProductMappingType.TOP_HITS_BY_MERCHANT).hits().hits().stream().map(Hit::source).filter(Objects::nonNull).collect(Collectors.toList()))
                .flatMap(List::stream)
                .map(os -> os.to(ProductInfo.class))
                .collect(Collectors.toCollection(ArrayList::new));
        }

        return searchResponse.hits().hits().stream().map(Hit::source).collect(Collectors.toCollection(ArrayList::new));
    }

}
