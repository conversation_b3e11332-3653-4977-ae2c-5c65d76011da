package com.looksky.agents.data.redis.conversation;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.data.redis.conversation.utils.MessageUtils;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.trace.annotation.TraceMethod;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.conversation.ConversationStatus;
import com.looksky.agents.sdk.agent.conversation.Event;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import java.time.Duration;
import java.util.ArrayList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBatch;
import org.redisson.api.RBucket;
import org.redisson.api.RListAsync;
import org.redisson.api.RedissonClient;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.stereotype.Service;

/**
 * @ClassName ConversationStatusService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/30 下午3:28
 * @Version 1.0
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class StatusDataService {

    private final RedissonClient redissonClient;

    public void cleanStatus() {
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());
        String userId = input.getUserId();
        String conversationId = input.getConversationId();

        // 使用批量操作提升性能
        RBatch batch = redissonClient.createBatch();

        // 删除对话历史
        RListAsync<Event> historyList = batch.getList(RedisKeyConstants.historyKey(userId, conversationId));
        historyList.deleteAsync();
        // 回填当前用户发送的消息
        historyList.addAsync(MessageUtils.userSendMessage());

        // 删除对话状态
        batch.getBucket(RedisKeyConstants.statusKey(userId, conversationId)).deleteAsync();

        // 清空用户偏好
        batch.getBucket(RedisKeyConstants.preferenceKey(userId, conversationId)).deleteAsync();

        // 清空推荐商品
        batch.getBucket(RedisKeyConstants.recommendProductKey(userId, conversationId)).deleteAsync();

        // 执行批量操作
        batch.execute();

        // 回填用户当轮发送的消息
    }

    public void updateStatus(ConversationStatus conversationStatus) {
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());
        String userId = input.getUserId();
        String conversationId = input.getConversationId();
        RBucket<ConversationStatus> bucket = redissonClient.getBucket(RedisKeyConstants.statusKey(userId, conversationId), new TypedJsonJacksonCodec(ConversationStatus.class));
        bucket.set(conversationStatus);
        bucket.expire(Duration.ofDays(1));
    }

    public ConversationStatus getStatus() {
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());
        String userId = input.getUserId();
        String conversationId = input.getConversationId();
        RBucket<ConversationStatus> bucket = redissonClient.getBucket(RedisKeyConstants.statusKey(userId, conversationId), new TypedJsonJacksonCodec(ConversationStatus.class));
        ConversationStatus status = bucket.get();
        if (status == null) {
            status = new ConversationStatus();
        }
        Context.put(Context.Name.STATUS.getName(), status);
        log.info("获取对话状态, userId: {}, conversationId: {}, 状态:{}", userId, conversationId, JSONUtil.toJsonStr(status));
        return status;
    }

    /**
     * 判断是否是新对话
     *
     * @param isNewConversation
     */
    @TraceMethod(description = "判断是否是新对话")
    public void newConversation(boolean isNewConversation) {

        Context.put(Context.Name.NEW_CONVERSATION_STATUS.getName(), isNewConversation);

        // 如果是新对话, 则需清除数据
        if (isNewConversation) {
            log.debug("agent 判断当前会话为新会话, 即将清楚历史消息以及偏好信息");
            cleanStatus();
            // Context 中的消息
            Context.put(Context.Name.PREFERENCE.getName(), new ExtractedEntityObject());
            Context.put(Context.Name.EVENT_LIST.getName(), new ArrayList<>());
            Context.put(Context.Name.STATUS.getName(), new ConversationStatus());
        } else {
            log.debug("agent 判断当前会话 不是 新会话");
            ConversationStatus conversationStatus = Context.get(Context.Name.STATUS.getName());
            conversationStatus.setNewConversation(false);
            Context.put(Context.Name.STATUS.getName(), conversationStatus);
        }
    }

    /**
     * 保存用户活跃状态（用户级维度）
     * 
     * @param userId 用户ID
     */
    public void saveFirstUserSendMessageFlag(String userId) {
        RBucket<Boolean> bucket = redissonClient.getBucket(RedisKeyConstants.userFirstSendMessageKey(userId));
        bucket.set(true);
    }

    /**
     * 获取用户活跃状态（用户级维度）
     * 
     * @param userId 用户ID
     * @return 活跃状态，如果不存在则返回null
     */
    public Boolean getUserFirstSendMessageFlag(String userId) {
        RBucket<Boolean> bucket = redissonClient.getBucket(RedisKeyConstants.userFirstSendMessageKey(userId));
        if (bucket.isExists()) {
            return bucket.get();
        }
        return false;
    }

}
