package com.looksky.agents.data.redis.conversation;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSONObject;
import com.looksky.agents.data.mysql.service.IEventService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.enums.EventNameEnum;
import com.looksky.agents.sdk.agent.common.enums.RoleTypeEnum;
import com.looksky.agents.sdk.agent.conversation.Event;
import com.looksky.agents.sdk.agent.event.model.EventModel;
import com.looksky.agents.sdk.agent.preference.enums.PreferenceTypeEnum;
import com.looksky.agents.sdk.agent.search.dto.CategoryFeedBackDTO;
import com.skygirls.biz.im.dto.MessageRestDTO;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventConvertService {

    private final IEventService eventService;

    public Event convert(String eventName, MessageRestDTO.EventDict eventDict, String messageId, String page, String enterPoint) {

        log.debug("转换事件, 当前事件类型:{}, 事件字典:{}, 消息ID:{}", eventName, eventDict, messageId);

        // 用户说话, 或者点击
        if (eventName.equals(EventNameEnum.USER_SPEAK.getValue()) ||  eventName.equals(EventNameEnum.USER_CLICK.getValue())) {

            return Event.builder().messageId(messageId).role(RoleTypeEnum.USER).eventName(eventName).content(eventDict.getText()).time(LocalDateTime.now()).build();
        // 用户反馈偏好
        } else if (eventName.equals(EventNameEnum.FEEDBACK_PREFERENCE.getValue())) {

            // 先判断反馈类型
            String feedbackType = eventDict.getFeedbackType();

            CategoryFeedBackDTO categorySelectorResp = JSONObject.parseObject(JSONObject.toJSONString(eventDict.getFeedbackPreference()), CategoryFeedBackDTO.class);

            String direction = categorySelectorResp.getDirection();

            // 如果是确认品类
            if ("confirm_category".equals(feedbackType)) {
                if (direction.equals(PreferenceTypeEnum.LIKE.getValue())) {
                    // 获取用户选择的品类
                    List<String> categoryList = categorySelectorResp.getCategory();

                    // 将用户选择的品类拼接成一段话
                    String content = String.format( "user select these categories : %s",  String.join(",", categoryList) );
                    return Event.builder().messageId(messageId).role(RoleTypeEnum.SYSTEM).eventName(eventName).content(content).time(LocalDateTime.now()).build();
                }

            // 如果是确认标签
            } else if ("confirm_tag".equals(feedbackType)) {
                if (direction.equals(PreferenceTypeEnum.LIKE.getValue())) {
                    Map<String, List<String>> tagDict = categorySelectorResp.getTag();
                    List<String> tagList = new ArrayList<>();

                    for (Map.Entry<String, List<String>> entry : tagDict.entrySet()) {
                        if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                            tagList.add(String.format("user express like these %s : %s",entry.getKey(),String.join(",", entry.getValue())));
                        }
                    }
                    String content = String.join("\n", tagList);
                    return Event.builder().messageId(messageId).role(RoleTypeEnum.SYSTEM).eventName(eventName).content(content).time(LocalDateTime.now()).build();

                }
            }
        } else if (eventName.equals(EventNameEnum.OPENING.getValue()) || eventName.equals(EventNameEnum.ADVICE_QUESTIONS.getValue())) {
            String eventNewName = page + "_" + enterPoint + "_" + eventName;
            EventModel eventModel = eventService.getEventByName(eventNewName);
            Context.put(Context.Name.EVENT_MODEL.getName(), eventModel);
            return Event.builder().messageId(messageId).role(RoleTypeEnum.USER).eventName(eventName).content(eventDict.getText()).time(LocalDateTime.now()).build();
        }

        try {

            EventModel eventModel = eventService.getEventByName(eventName);

            if (eventModel == null) {
                return Event.builder().messageId(messageId).role(RoleTypeEnum.USER).eventName(EventNameEnum.USER_SPEAK.getValue()).content(eventDict.getText()).time(LocalDateTime.now()).build();
            }
            //String prompt = eventModel.getContent();
            //String actionContent;
            //
            //if (eventDict != null && !ObjectUtils.isEmpty(eventDict)) {
            //    try {
            //        // 替换 content 中的模板
            //        actionContent = CustomStringUtils.replaceTemplate(prompt, BeanUtil.beanToMap(eventDict));
            //    } catch (Exception e) {
            //        throw new RuntimeException(String.format("event content 处理失败, event:%s, event_dict:%s", JSONUtil.toJsonStr(eventModel), JSONUtil.toJsonStr(eventDict)));
            //    }
            //} else {
            //    actionContent = prompt;
            //}

            Context.put(Context.Name.EVENT_MODEL.getName(), eventModel);

            return Event.builder().messageId(messageId).role(RoleTypeEnum.USER).eventName(eventName).content(eventDict.getText()).time(LocalDateTime.now()).build();

        } catch (Exception e) {
            log.error("无法获取事件，event:{}", eventName);
            var build = Event.builder().messageId(messageId).role(RoleTypeEnum.USER).eventName(EventNameEnum.USER_SPEAK.getValue()).time(LocalDateTime.now());
            if (eventDict != null && CharSequenceUtil.isNotBlank(eventDict.getText())) {
                build.content(eventDict.getText());
            }
            return build.build();
        }
    }

} 