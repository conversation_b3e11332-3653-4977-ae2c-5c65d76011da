package com.looksky.agents.data.grpc.convertor;

import com.looksky.agents.sdk.recommend.common.dto.VectorRecallModelDTO;
import com.looksky.agents.sdk.recommend.daily100.dto.request.DailyRequestDTO;
import com.looksky.agents.sdk.recommend.daily100.dto.response.DailyResponseDTO;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import com.westyle.recm.AgentSearchTerm;
import com.westyle.recm.GirlsDailyRecm;
import com.westyle.recm.VectorRecall;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = MappingConstants.ComponentModel.SPRING,
    uses = {BoolValueMapper.class, DoubleValueMapper.class},
    collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface DailyConvertor {

    /**
     * 将 DailyRequestDTO 转换为 GRPC 对象
     * @param request DailyRequestDTO 对象
     * @return GRPC 对象
     */
    @Mapping(target = "userId", source = "userId")
    @Mapping(target = "versionCode", source = "versionCode")
    @Mapping(target = "offset", source = "offset")
    @Mapping(target = "limit", source = "limit")
    @Mapping(target = "vectorQueryVersion", source = "vectorQueryVersion")
    GirlsDailyRecm.GirlsDailyRequest toGrpcDailyRequest(DailyRequestDTO request);

    /**
     * 在映射完成后处理DailyRequestDTO中的列表字段
     * @param builder 目标GirlsDailyRequest.Builder对象
     * @param request 源DailyRequestDTO对象
     */
    @AfterMapping
    default void handleDailyRequestLists(@MappingTarget GirlsDailyRecm.GirlsDailyRequest.Builder builder, DailyRequestDTO request) {
        // 处理categoryVectorRecall列表
        if (request.getCategoryVectorRecall() != null && !request.getCategoryVectorRecall().isEmpty()) {
            for (VectorRecallModelDTO dto : request.getCategoryVectorRecall()) {
                VectorRecall.VectorRecallModel model = toGrpcVectorRecallModel(dto);
                if (model != null) {
                    builder.addCategoryVectorRecall(model);
                }
            }
        }

        // 处理totalVectorRecall列表
        if (request.getTotalVectorRecall() != null && !request.getTotalVectorRecall().isEmpty()) {
            for (VectorRecallModelDTO dto : request.getTotalVectorRecall()) {
                VectorRecall.VectorRecallModel model = toGrpcVectorRecallModel(dto);
                if (model != null) {
                    builder.addTotalVectorRecall(model);
                }
            }
        }
    }

    /**
     * 将VectorRecallModelDTO转换为VectorRecallModel
     * @param vectorRecallModelDTO VectorRecallModelDTO对象
     * @return VectorRecallModel对象
     */
    @Named("toGrpcVectorRecallModel")
    @Mapping(target = "recallStrategy", source = "recallStrategy")
    VectorRecall.VectorRecallModel toGrpcVectorRecallModel(VectorRecallModelDTO vectorRecallModelDTO);

    /**
     * 在映射完成后处理Protocol Buffers列表类型
     * @param model 目标VectorRecallModel对象
     * @param dto 源VectorRecallModelDTO对象
     */
    @AfterMapping
    default void handleProtobufLists(@MappingTarget VectorRecall.VectorRecallModel.Builder model, VectorRecallModelDTO dto) {
        // 处理vectorQuery列表
        if (dto.getVectorQuery() != null && !dto.getVectorQuery().isEmpty()) {
            for (SearchRequestDTO.VectorQuery query : dto.getVectorQuery()) {
                AgentSearchTerm.VectorQueryModel vectorQueryModel = toGrpcVectorQueryModel(query);
                if (vectorQueryModel != null) {
                    model.addVectorQuery(vectorQueryModel);
                }
            }
        }

        // 处理mustSubCategory列表
        if (dto.getMustSubCategory() != null && !dto.getMustSubCategory().isEmpty()) {
            model.addAllMustSubCategory(dto.getMustSubCategory());
        }

        // 处理mustNotSubCategory列表
        if (dto.getMustNotSubCategory() != null && !dto.getMustNotSubCategory().isEmpty()) {
            model.addAllMustNotSubCategory(dto.getMustNotSubCategory());
        }
    }

    /**
     * 将VectorQuery转换为VectorQueryModel
     * @param vectorQuery VectorQuery对象
     * @return VectorQueryModel对象
     */
    @Named("toGrpcVectorQueryModel")
    AgentSearchTerm.VectorQueryModel toGrpcVectorQueryModel(SearchRequestDTO.VectorQuery vectorQuery);

    /**
     * 将VectorRecallModelDTO列表转换为VectorRecallModel列表
     * @param list VectorRecallModelDTO列表
     * @return VectorRecallModel列表
     */
    @Named("vectorRecallModelDTOListToVectorRecallModelList")
    List<VectorRecall.VectorRecallModel> vectorRecallModelDTOListToVectorRecallModelList(List<VectorRecallModelDTO> list);


    /**
     * 将 GRPC 对象转换为 DailyResponseDTO 对象
     * @param response GRPC 对象
     * @return DailyResponseDTO 对象
     */
    @Mapping(target = "items", source = "itemsList")
    DailyResponseDTO toDailyResponseDTO(GirlsDailyRecm.GirlsDailyResponse response);

}
