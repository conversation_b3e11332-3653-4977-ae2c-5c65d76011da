package com.looksky.agents.data.redis.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.stereotype.Component;

@Component
public class RedisCodecFactory {
    
    private final ObjectMapper redisObjectMapper;
    
    public RedisCodecFactory(ObjectMapper redisObjectMapper) {
        this.redisObjectMapper = redisObjectMapper;
    }
    
    public TypedJsonJacksonCodec createCodec(Class<?> type) {
        return new TypedJsonJacksonCodec(type, redisObjectMapper);
    }


   /**
     * 为Map创建带类型的编解码器
     *
     * @param keyClass key的类型
     * @param valueClass value的类型
     * @return 编解码器
     */
    public <K, V> TypedJsonJacksonCodec createMapCodec(Class<K> keyClass, Class<V> valueClass) {
        return new TypedJsonJacksonCodec(keyClass, valueClass, redisObjectMapper);
    }
} 