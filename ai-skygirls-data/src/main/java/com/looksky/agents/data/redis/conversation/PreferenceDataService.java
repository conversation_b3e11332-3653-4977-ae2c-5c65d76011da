package com.looksky.agents.data.redis.conversation;

import cn.hutool.core.util.StrUtil;
import com.looksky.agents.sdk.agent.preference.ExtractedEntityObject;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.infrastructure.context.Context;
import java.time.Duration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.stereotype.Service;

/**
 * @ClassName PreferenceDataService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/30 下午3:29
 * @Version 1.0
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PreferenceDataService {

    private final RedissonClient redissonClient;

    /**
     * 获取用户会话偏好
     * @return
     */
    public ExtractedEntityObject getPreference() {
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());
        String userId = input.getUserId();
        String conversationId = input.getConversationId();
        try {
            String preferenceKey = StrUtil.format(RedisKeyConstants.preferenceKey(userId, conversationId));
            RBucket<ExtractedEntityObject> bucket = redissonClient.getBucket(preferenceKey, new TypedJsonJacksonCodec(ExtractedEntityObject.class));

            ExtractedEntityObject preference = bucket.get();
            preference = preference != null ? preference : new ExtractedEntityObject();
            Context.put(Context.Name.PREFERENCE.getName(), preference);
            return preference;

        } catch (Exception e) {
            log.error("获取用户会话偏好异常, userId={}, conversationId={}", userId, conversationId, e);
            return new ExtractedEntityObject();
        }
    }

    /**
     * 设置用户会话偏好
     */
    public void savePreference(ExtractedEntityObject preference) {
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());
        String userId = input.getUserId();
        String conversationId = input.getConversationId();

        try {
            String preferenceKey = StrUtil.format(RedisKeyConstants.preferenceKey(userId, conversationId));
            RBucket<ExtractedEntityObject> bucket = redissonClient.getBucket(preferenceKey, new TypedJsonJacksonCodec(ExtractedEntityObject.class));
            bucket.setAsync(preference, Duration.ofDays(1));
        } catch (Exception e) {
            log.error("设置用户会话偏好异常, userId={}, conversationId={}, preference={}",
                    userId, conversationId, preference, e);
        }
    }
}
