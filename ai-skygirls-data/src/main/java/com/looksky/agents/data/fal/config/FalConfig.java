package com.looksky.agents.data.fal.config;

import ai.fal.client.ClientConfig;
import ai.fal.client.CredentialsResolver;
import ai.fal.client.FalClient;
import com.looksky.agents.common.model.tryOn.TryOnFalKeyConfig;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class FalConfig {

    @Resource
    private TryOnFalKeyConfig tryOnFalKeyConfig;

    @Bean
    public FalClient falClient() {
        return FalClient.withConfig(ClientConfig.withCredentials(CredentialsResolver.fromApiKey(tryOnFalKeyConfig.getApiKeyList().getFirst())));
    }

}
