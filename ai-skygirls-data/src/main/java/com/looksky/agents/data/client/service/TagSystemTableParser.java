package com.looksky.agents.data.client.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.skygirls.biz.product.model.Item;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class TagSystemTableParser {

    private JsonNode tagTable;

    // 存放的是所有标签和标签值  key 为 标签名  value 为 标签值列表
    @Getter
    private final Map<String, List<String>> allTagAndTagValueMap = new HashMap<>();
    // 存放的是所有二级品类
    @Getter
    private final List<String> subCategoryList = new ArrayList<>();
    // 存放的所有一级品类
    @Getter
    private final List<String> firstCategoryList = new ArrayList<>();
    // 存放的是所有一级品类和二级品类的映射关系 key 为 一级品类, value 为 二级品类列表
    @Getter
    private final Map<String, List<String>> firstSubCategoryMap = new HashMap<>();
    // 存放的是所有二级品类和标签以及标签值的映射关系  Map<二级品类, Map<标签名, 标签值列表>>
    @Getter
    private final Map<String, Map<String, List<String>>> subCategoryTagValueMap = new HashMap<>();
    // 存放的是所有一级品类和标签以及标签值的映射关系  Map<一级品类, Map<标签名, 标签值列表>>
    @Getter
    private final Map<String, Map<String, List<String>>> firstCategoryTagValueMap = new HashMap<>();
    // 存放的是所有二级品类和一级品类的映射关系  Map<二级品类, 一级品类>
    @Getter
    private final Map<String, String> subCategoryToFirstCategoryMap = new HashMap<>();
    // 存放的是所有标签和父标签的映射关系  Map<标签名, 父标签名>
    @Getter
    private final Map<String, String> tagToParentTagMap = new HashMap<>();

    private List<Item> getTagTree() {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(getTagPart("tag_tree").toString(), new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private Map<String, List<String>> getTagParent() {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(getTagPart("tag_parents").toString(), new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private JsonNode getTagPart(String part) {
        return tagTable.get(part);
    }

    private List<Item> getTagList() {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(getTagPart("tag_list").toString(), new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 获取一级品类和二级品类的映射关系
     */
    private void getFirstSubCategoryMap(List<Item> tagTree) {

        // 获取category节点
        Item categoryNode = tagTree.stream()
                .filter(item -> "category".equals(item.getName()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("未找到category节点"));

        // 获取first_category节点
        categoryNode.getValues().stream()
                .flatMap(value -> value.getChildren().stream())
                .filter(item -> "first_category".equals(item.getName()))
                .forEach(firstCategoryNode -> {
                    // 遍历每个一级品类
                    firstCategoryNode.getValues().forEach(firstCategory -> {
                        String firstCategoryName = firstCategory.getValue();
                        // 获取该一级品类下的所有二级品类
                        List<String> subCategories = firstCategory.getChildren().stream()
                                .flatMap(subCategory -> subCategory.getValues().stream())
                                .map(Item::getValue)
                                .sorted()
                                .collect(Collectors.toList());
                        firstSubCategoryMap.put(firstCategoryName, subCategories);
                    });
                });
    }

    /**
     * 解析标签体系，移除 tag_list 中品类相关的标签
     */
    private void removeCategoryTagsValue(List<Item> tagList) {
        // 构建标签映射, 取出所有标签和标签值
        tagList.forEach(tag -> allTagAndTagValueMap.put(tag.getName(), tag.getChildren().stream()
                .map(Item::getName)
                .collect(Collectors.toList())));

        // 移除品类相关标签并保存二级品类列表
        List<String> subCategory = allTagAndTagValueMap.remove("sub_category");
        subCategoryList.addAll(subCategory != null ? subCategory : Collections.emptyList());
        List<String> firstCategory = allTagAndTagValueMap.remove("first_category");
        firstCategoryList.addAll(firstCategory != null ? firstCategory : Collections.emptyList());

    }

    /**
     * 判断是否为标签
     */
    private boolean isTag(String tagName) {
        return allTagAndTagValueMap.containsKey(tagName);
    }

    /**
     * 判断是否为二级品类
     */
    private boolean isSubCategory(String tagName) {
        return subCategoryList.contains(tagName);
    }

    /**
     * 解析二级品类的标签
     */
    private Map<String, Map<String, List<String>>> parseSubCategoryTag(List<Item> tagTree) {
        Set<SubCategoryTagValue> subCategoryTagValueSet = new HashSet<>();
        Map<String, Map<String, List<String>>> result = new HashMap<>();

        parseNodeForSubCategory(tagTree, null, null, subCategoryTagValueSet);

        // 构建结果映射
        for (SubCategoryTagValue value : subCategoryTagValueSet) {
            result.computeIfAbsent(value.subCategory, k -> new HashMap<>())
                    .computeIfAbsent(value.tag, k -> new ArrayList<>())
                    .add(value.tagValue);
        }

        return result;
    }

    private void parseNodeForSubCategory(List<Item> items, String subCategory, String parentTag,
                                         Set<SubCategoryTagValue> resultSet) {
        for (Item item : items) {
            if (subCategory != null && parentTag != null && item.getValue() != null) {
                resultSet.add(new SubCategoryTagValue(subCategory, parentTag, item.getValue()));
            }

            if (isSubCategory(item.getValue())) {
                subCategory = item.getValue();
            }
            if (isTag(item.getName())) {
                parentTag = item.getName();
            }

            if (item.getValues() != null) {
                parseNodeForSubCategory(item.getValues(), subCategory, parentTag, resultSet);
            }
            if (item.getChildren() != null) {
                parseNodeForSubCategory(item.getChildren(), subCategory, parentTag, resultSet);
            }
        }
    }

    /**
     * 解析通用品类标签
     */
    private Map<String, List<String>> parseCommonCategoryTag(List<Item> tagTree) {
        Set<CommonCategoryTagValue> commonTagValueSet = new HashSet<>();
        Map<String, List<String>> result = new HashMap<>();

        parseNodeForCommonCategory(tagTree, null, commonTagValueSet);

        // 构建结果映射
        for (CommonCategoryTagValue value : commonTagValueSet) {
            result.computeIfAbsent(value.tag, k -> new ArrayList<>())
                    .add(value.tagValue);
        }

        return result;
    }

    private void parseNodeForCommonCategory(List<Item> items, String parentTag,
                                            Set<CommonCategoryTagValue> resultSet) {
        for (Item item : items) {
            if (!"first_category".equals(item.getName())) {
                if (parentTag != null && item.getValue() != null) {
                    resultSet.add(new CommonCategoryTagValue(parentTag, item.getValue()));
                }

                if (isTag(item.getName())) {
                    parentTag = item.getName();
                }

                if (item.getValues() != null) {
                    parseNodeForCommonCategory(item.getValues(), parentTag, resultSet);
                }
                if (item.getChildren() != null) {
                    parseNodeForCommonCategory(item.getChildren(), parentTag, resultSet);
                }
            }
        }
    }

    /**
     * 获取二级品类与标签
     */
    private void getSubCategoryTagMap(List<Item> tagTree) {
        Map<String, Map<String, List<String>>> subCategoryTagValueMap = parseSubCategoryTag(tagTree);
        Map<String, List<String>> commonCategoryTagValueDict = parseCommonCategoryTag(tagTree);

        // 合并通用标签到每个二级品类中
        for (Map<String, List<String>> value : subCategoryTagValueMap.values()) {
            value.putAll(commonCategoryTagValueDict);
        }
        this.subCategoryTagValueMap.putAll(subCategoryTagValueMap);
    }

    // 获取一级品列的标签 map
    private void getFirstCategoryTagMap() {
        // 处理一级品类标签
        Map<String, Map<String, Set<String>>> firstCategoryTagMap = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : firstSubCategoryMap.entrySet()) {
            String firstCategory = entry.getKey();
            firstCategoryTagMap.put(firstCategory, new HashMap<>());

            for (String subCategory : entry.getValue()) {
                Map<String, List<String>> subCategoryTags = subCategoryTagValueMap.get(subCategory);
                if (subCategoryTags != null) {
                    for (Map.Entry<String, List<String>> tagEntry : subCategoryTags.entrySet()) {
                        firstCategoryTagMap.get(firstCategory)
                                .computeIfAbsent(tagEntry.getKey(), k -> new HashSet<>())
                                .addAll(tagEntry.getValue());
                    }
                }
            }
        }

        // 转换Set为List并输出一级品类标签
        for (Map.Entry<String, Map<String, Set<String>>> entry : firstCategoryTagMap.entrySet()) {
            Map<String, List<String>> tagListMap = new HashMap<>();
            for (Map.Entry<String, Set<String>> tagEntry : entry.getValue().entrySet()) {
                tagListMap.put(tagEntry.getKey(), new ArrayList<>(tagEntry.getValue()));
            }

            firstCategoryTagValueMap.put(entry.getKey(), tagListMap);
        }
    }

    // 获取二级品类到一级品类的映射
    private void getSucCategoryToFirstCategoryMap() {
        for (Map.Entry<String, List<String>> entry : firstSubCategoryMap.entrySet()) {
            for (String subCategory : entry.getValue()) {
                subCategoryToFirstCategoryMap.put(subCategory, entry.getKey());
            }
        }
    }

    // 获取标签的父标签
    private void getTagToParentTagMap(Map<String, List<String>> tagParents) {
        List<String> specialTagList = Arrays.asList("closure_type", "pattern_type", "size_type", "design_detail_type", "fabric_type", "pocket_type", "trims_type");
        tagParents.forEach((key, value) -> {
            String parent = value.size() >= 2 ? value.getLast() : specialTagList.contains(key) ? value.getFirst() : key;
            tagToParentTagMap.put(key, parent);
        });
        // 去除 品类相关的 tag
        tagToParentTagMap.remove("first_category");
        tagToParentTagMap.remove("sub_category");
    }

    private static class SubCategoryTagValue {
        String subCategory;
        String tag;
        String tagValue;

        SubCategoryTagValue(String subCategory, String tag, String tagValue) {
            this.subCategory = subCategory;
            this.tag = tag;
            this.tagValue = tagValue;
        }
    }

    @EqualsAndHashCode
    private static class CommonCategoryTagValue {
        String tag;
        String tagValue;

        CommonCategoryTagValue(String tag, String tagValue) {
            this.tag = tag;
            this.tagValue = tagValue;
        }
    }


    /**
     * 初始化参数标签系统表
     */
    public void parser(JsonNode tagTable) {
        this.tagTable = tagTable;

        List<Item> tagTree = getTagTree();
        List<Item> tagList = getTagList();
        Map<String, List<String>> tagParent = getTagParent();

        // 先移除 tag_list 中的品类相关标签
        removeCategoryTagsValue(tagList);

        // 获取一级品类和二级品类映射
        getFirstSubCategoryMap(tagTree);

        // 获取二级品类和标签的映射
        getSubCategoryTagMap(tagTree);

        // 获取一级品类和标签的映射
        getFirstCategoryTagMap();

        // 输出根据二级品类获取对应的一级品类映射
        getSucCategoryToFirstCategoryMap();

        // 获取标签的父标签
        getTagToParentTagMap(tagParent);
    }



    public void test() {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 输出所有一级品类和二级品类映射
            System.out.println("所有一级品类和二级品类映射: " + objectMapper.writeValueAsString(firstSubCategoryMap));

            // 输出根据一级品类获取对应的二级品类映射
            for (Map.Entry<String, List<String>> entry : firstSubCategoryMap.entrySet()) {
                System.out.println("根据一级品类获取对应的二级品类映射: " +
                        entry.getKey() + " -> " + objectMapper.writeValueAsString(entry.getValue()));
            }

            for (Map.Entry<String, String> entry : subCategoryToFirstCategoryMap.entrySet()) {
                System.out.println("根据二级品类获取对应的一级品类映射: " +
                        entry.getKey() + " -> " + entry.getValue());
            }

            System.out.println("所有标签映射: " + objectMapper.writeValueAsString(allTagAndTagValueMap));

            // 输出二级品类标签
            for (Map.Entry<String, Map<String, List<String>>> entry : subCategoryTagValueMap.entrySet()) {
                System.out.println("二级品类标签: " + entry.getKey() + " -> " +
                        objectMapper.writeValueAsString(entry.getValue()));
            }

            // 输出一级品类标签
            for (Map.Entry<String, Map<String, List<String>>> entry : firstCategoryTagValueMap.entrySet()) {
                System.out.println("一级品类标签: " + entry.getKey() + " -> " +
                        objectMapper.writeValueAsString(entry.getValue()));
            }

            // 输出标签的父标签
            System.out.println("标签的父标签: " + objectMapper.writeValueAsString(tagToParentTagMap));

        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON处理错误", e);
        }
    }
}