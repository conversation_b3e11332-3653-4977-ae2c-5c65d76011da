package com.looksky.agents.data.redis.conversation.utils;

import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.RoleTypeEnum;
import com.looksky.agents.sdk.agent.conversation.Event;
import com.skygirls.biz.im.dto.MessageRestDTO;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 历史相关工具
 *
 * <AUTHOR>
 * @since 1.1.14
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MessageUtils {

    /**
     * 过滤消息
     *
     * @param all     需要过滤的消息
     * @param current true: 获取当轮消息, false, 获取历史消息
     * @return 过滤后的event
     */
    public static List<Event> filterMessage(List<Event> all, boolean current) {
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());
        var messageIdOptional = Optional.ofNullable(input).map(RequestInputDTO::getMessageId);
        if (messageIdOptional.isPresent()) {
            return all.stream().filter(e -> current == messageIdOptional.get().equals(e.getMessageId())).collect(Collectors.toCollection(ArrayList::new));
        }
        return all;

    }


    /**
     * 构建 message, 自动填充 MessageId 和 时间
     *
     * @param roleTypeEnum 消息类型
     * @param message      消息内容
     * @return 消息对象
     */
    public static Event buildMessage(RoleTypeEnum roleTypeEnum, String message) {
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());
        return Event.builder().role(roleTypeEnum).messageId(input.getMessageId()).content(message).time(LocalDateTime.now()).build();
    }

    /**
     * 获取当轮用户发送的消息
     * @return 消息对象
     */
    public static Event userSendMessage() {
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());
        String message = Optional.ofNullable(input).map(RequestInputDTO::getEventDict).map(MessageRestDTO.EventDict::getText).orElse("");
        return Event.builder().role(RoleTypeEnum.USER).messageId(Optional.ofNullable(input).map(RequestInputDTO::getMessageId).orElse("")).content(message).time(LocalDateTime.now()).build();
    }
}
