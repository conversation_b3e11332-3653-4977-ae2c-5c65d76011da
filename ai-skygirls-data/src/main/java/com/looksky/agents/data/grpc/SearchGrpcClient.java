package com.looksky.agents.data.grpc;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.data.grpc.convertor.SearchConvertor;
import com.looksky.agents.infrastructure.collect.annotation.CollectEvent;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.interceptor.GrpcClientHeaderInterceptor;
import com.looksky.agents.infrastructure.interceptor.GrpcResponseHeaderInterceptor;
import com.looksky.agents.infrastructure.trace.annotation.TraceMethod;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import com.looksky.agents.sdk.recommend.search.dto.response.SearchResponseDTO;
import com.westyle.recm.GirlsAgentSearchRecom;
import com.westyle.recm.GirlsAgentSearchRecommendServiceGrpc;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.client.inject.GrpcClient;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SearchGrpcClient {

    @GrpcClient("girlsAgentSearchRecommendService")
    private GirlsAgentSearchRecommendServiceGrpc.GirlsAgentSearchRecommendServiceBlockingStub stub;


    @Resource
    private SearchConvertor grpcConvertor;

    private static final long DEADLINES = 15;

    @PostConstruct
    public void init() {
        // 在初始化时添加拦截器
        stub = stub.withInterceptors(new GrpcClientHeaderInterceptor(), new GrpcResponseHeaderInterceptor());
    }

    private GirlsAgentSearchRecom.GirlsAgentSearchResponse request(GirlsAgentSearchRecom.GirlsAgentSearchRequest request) {
        try {
            log.info("xxxxxxxx, {}", request);
            return stub.withDeadlineAfter(DEADLINES, TimeUnit.SECONDS)
                    .girlsAgentSearchRecommend(request);
        } catch (Exception e) {
            log.error("GRPC 调用发生异常: {}", e.getMessage());
            throw e;
        }
    }

    @Retryable(
            retryFor = Exception.class,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    @CollectEvent
    @TraceMethod(description = "请求推荐 Search 接口")
    public SearchResponseDTO search(SearchRequestDTO request) {
        try {
            log.info("请求 search recommend GRPC 接口 -----------> : {}", JSONUtil.toJsonStr(request));
            GirlsAgentSearchRecom.GirlsAgentSearchResponse protoResponse = request(grpcConvertor.toGrpcSearchRequest(request));
            SearchResponseDTO recommendSearchResponseDTO = grpcConvertor.toSearchResponseDTO(protoResponse);
            Optional.ofNullable(GrpcResponseHeaderInterceptor.getAbTestFlag()).ifPresent(flag -> Context.put(GrpcResponseHeaderInterceptor.KEY_NAME, flag));
            log.info("search recommend GRPC 接口返回 <------------ : {}", JSONUtil.toJsonStr(recommendSearchResponseDTO));
            return recommendSearchResponseDTO;
        } catch (Exception e) {
            log.error("search recommend grpc 接口请求失败", e);
            throw e;
        }
    }

    @Recover
    public SearchResponseDTO recover(Exception e, SearchRequestDTO request) {
        log.error("search recommend GRPC 调用失败，重试 {} 次后仍然失败", 2, e);
        throw new RuntimeException("调用 search recommend GRPC 服务失败", e);
    }
}