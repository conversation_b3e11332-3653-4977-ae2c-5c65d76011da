package com.looksky.agents.data.client.business;

import com.fasterxml.jackson.databind.JsonNode;
import com.looksky.agents.infrastructure.httpapi.ApiServiceEnum;
import com.looksky.agents.infrastructure.httpapi.HttpApi;
import com.looksky.agents.sdk.tryon.colorseason.TryOnColorSeasonRequestV1;
import com.looksky.agents.sdk.tryon.runpod.CreateTaskRequest;
import com.looksky.agents.sdk.tryon.runpod.CreateTaskResponse;
import com.looksky.agents.sdk.tryon.runpod.TaskResultResponse;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.SwapClothInterruptRequest;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.SwapClothStatusRequest;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.SwapClothWorkflowRequest;
import com.looksky.agents.sdk.tryon.swapcloth.dto.request.changecolor.RunPodChangeColorRequest;
import com.looksky.agents.sdk.tryon.swapcloth.dto.response.FashnRunResponse;
import java.util.Map;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.PostExchange;

/**
 * 使用 Spring RestClient 实现的换装 HTTP 客户端
 */
@HttpApi(service = ApiServiceEnum.OTHER_SERVICE)
public interface TryOnClient {
    /**
     * 换装
     *
     * @param request 请求参数
     * @return 响应结果
     */
    @Deprecated(since = "1.1.8", forRemoval = true)
    @PostExchange("https://westyleai--tryon.modal.run")
    JsonNode swapClothWorkflow(@RequestBody SwapClothWorkflowRequest request);

    /**
     * 换装, 异步接口
     *
     * @param request 请求参数
     * @return 响应结果
     */
    @Deprecated(since = "1.1.8", forRemoval = true)
    @PostExchange("https://westyleai--tryon-request.modal.run")
    SwapClothStatusRequest swapClothWorkflowAsync(@RequestBody SwapClothWorkflowRequest request);

    /**
     * 中断换装工作流
     *
     * @param request 请求参数
     * @return 响应结果
     */
    @Deprecated(since = "1.1.8", forRemoval = true)
    @PostExchange("https://westyleai--interrupt.modal.run")
    Object interruptSwapClothWorkflow(@RequestBody SwapClothInterruptRequest request);

    /**
     * 查询任务状态
     *
     * @param clientId 请求 id
     * @param promptId 提示 id
     * @return 响应结果
     */
    @Deprecated(since = "1.1.8", forRemoval = true)
    @GetExchange("https://westyleai--tryon-status.modal.run")
    Object swapClothStatus(@RequestParam("client_id") String clientId, @RequestParam("prompt_id") String promptId);


    @PostExchange("https://api.fashn.ai/nightly/run")
    FashnRunResponse tryOnRun(@RequestHeader(name = "Authorization") String authorization, @RequestBody Map<String, Object> requestBody);

    @GetExchange("https://api.fashn.ai/v1/status/{id}")
    FashnRunResponse truOnStatus(@RequestHeader(name = "Authorization") String authorization, @PathVariable("id") String id);


    @Deprecated(since = "1.1.12")
    @PostExchange("https://prtog54rkijkse-8502.proxy.runpod.net/generate")
    JsonNode colorSeasonV1(@RequestBody TryOnColorSeasonRequestV1 request);


    @PostExchange("https://api.runpod.ai/v2/dig7m9naa7pa47/run")
    CreateTaskResponse tryOnWhiteT(@RequestHeader(name = "Authorization") String authorization, @RequestBody CreateTaskRequest requestBody);


    @GetExchange("https://api.runpod.ai/v2/dig7m9naa7pa47/status/{jobId}")
    TaskResultResponse tryOnWhiteTStatus(@RequestHeader(name = "Authorization") String authorization, @PathVariable("jobId") String jobId);

    @PostExchange("https://prtog54rkijkse-8507.proxy.runpod.net/sam_ai/change_color_by_prompt")
    JsonNode runPodChangeColor(@RequestBody RunPodChangeColorRequest request);

    @PostExchange("https://api.runpod.ai/v2/nvqsxfnvixu6mq/run")
    CreateTaskResponse runPodServerlessChangeColor(@RequestHeader(name = "Authorization") String authorization, @RequestBody CreateTaskRequest requestBody);

    @GetExchange("https://api.runpod.ai/v2/nvqsxfnvixu6mq/status/{jobId}")
    TaskResultResponse runPodServerlessChangeColorStatus(@RequestHeader(name = "Authorization") String authorization, @PathVariable("jobId") String jobId);


}