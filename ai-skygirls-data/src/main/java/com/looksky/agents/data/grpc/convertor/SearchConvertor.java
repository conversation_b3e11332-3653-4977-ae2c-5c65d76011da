package com.looksky.agents.data.grpc.convertor;

import com.google.protobuf.BoolValue;
import com.google.protobuf.DoubleValue;
import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import com.looksky.agents.sdk.recommend.search.dto.response.SearchResponseDTO;
import com.westyle.recm.AgentSearchTerm;
import com.westyle.recm.GirlsAgentSearchRecom;
import com.westyle.recm.ItemOuterClass;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import java.util.ArrayList;
import java.util.List;

@Mapper(
    componentModel = MappingConstants.ComponentModel.SPRING,
    uses = {BoolValueMapper.class, DoubleValueMapper.class, CommonConvertor.class},
    collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface SearchConvertor {

    /**
     * 将 SearchRequestDTO 转换为 GRPC 对象
     * @param request SearchRequestDTO 对象
     * @return GRPC 对象
     */
    @Mapping(target = "userId", source = "userId")
    @Mapping(target = "season", source = "season", ignore = true)
    @Mapping(target = "strategyTermList", ignore = true)
    GirlsAgentSearchRecom.GirlsAgentSearchRequest toGrpcSearchRequest(SearchRequestDTO request);

    /**
     * 在映射完成后处理SearchRequest中的strategyTerms字段
     * @param builder 目标GirlsAgentSearchRequest.Builder对象
     * @param request 源SearchRequestDTO对象
     */
    @AfterMapping
    default void handleSearchRequest(@MappingTarget GirlsAgentSearchRecom.GirlsAgentSearchRequest.Builder builder, SearchRequestDTO request) {

        if (request.getSeason() != null) {
            builder.setSeason(request.getSeason());
        }

        if (request.getStrategyTerms() != null && !request.getStrategyTerms().isEmpty()) {
            for (SearchRequestDTO.GirlsStrategyTerm strategyTerm : request.getStrategyTerms()) {
                GirlsAgentSearchRecom.GirlsSingleStrategyTerm.Builder strategyTermBuilder = GirlsAgentSearchRecom.GirlsSingleStrategyTerm.newBuilder();
                
                // 处理searchTerm
                if (strategyTerm.getSearchTerm() != null) {
                    AgentSearchTerm.SingleAgentSearchTerm.Builder searchTermBuilder = AgentSearchTerm.SingleAgentSearchTerm.newBuilder();
                    SearchRequestDTO.SearchTerm searchTerm = strategyTerm.getSearchTerm();
                    
                    // 设置searchStrategy
                    if (searchTerm.getSearchStrategy() != null) {
                        searchTermBuilder.setSearchStrategy(searchTerm.getSearchStrategy());
                    }
                    
                    // 处理categories列表
                    if (searchTerm.getCategories() != null && !searchTerm.getCategories().isEmpty()) {
                        searchTermBuilder.addAllCategory(searchTerm.getCategories());
                    }
                    
                    // 处理brands列表
                    if (searchTerm.getBrands() != null && !searchTerm.getBrands().isEmpty()) {
                        searchTermBuilder.addAllBrand(searchTerm.getBrands());
                    }
                    
                    // 处理priceRange
                    if (searchTerm.getPriceRange() != null) {
                        AgentSearchTerm.PriceRangeModel.Builder priceRangeBuilder = AgentSearchTerm.PriceRangeModel.newBuilder();
                        
                        if (searchTerm.getPriceRange().getMinPrice() != null) {
                            priceRangeBuilder.setMinPrice(DoubleValue.newBuilder().setValue(searchTerm.getPriceRange().getMinPrice()).build());
                        }
                        
                        if (searchTerm.getPriceRange().getMaxPrice() != null) {
                            priceRangeBuilder.setMaxPrice(DoubleValue.newBuilder().setValue(searchTerm.getPriceRange().getMaxPrice()).build());
                        }
                        
                        if (searchTerm.getPriceRange().getNormPrice() != null) {
                            priceRangeBuilder.setNormPrice(DoubleValue.newBuilder().setValue(searchTerm.getPriceRange().getNormPrice()).build());
                        }
                        
                        searchTermBuilder.setPriceRange(priceRangeBuilder.build());
                    }
                    
                    // 处理userPreferences列表
                    if (searchTerm.getUserPreferences() != null && !searchTerm.getUserPreferences().isEmpty()) {
                        for (SearchRequestDTO.UserPreference userPref : searchTerm.getUserPreferences()) {
                            AgentSearchTerm.UserPreferenceModel.Builder userPrefBuilder = AgentSearchTerm.UserPreferenceModel.newBuilder();
                            
                            if (userPref.getTagType() != null) {
                                userPrefBuilder.setTagType(userPref.getTagType());
                            }
                            
                            if (userPref.getLike() != null && !userPref.getLike().isEmpty()) {
                                userPrefBuilder.addAllLike(userPref.getLike());
                            }
                            
                            if (userPref.getDisLike() != null && !userPref.getDisLike().isEmpty()) {
                                userPrefBuilder.addAllDisLike(userPref.getDisLike());
                            }
                            
                            if (userPref.getRecommend() != null && !userPref.getRecommend().isEmpty()) {
                                userPrefBuilder.addAllRecommend(userPref.getRecommend());
                            }
                            
                            searchTermBuilder.addUserPreference(userPrefBuilder.build());
                        }
                    }
                    
                    // 处理布尔值字段
                    if (searchTerm.getIsFreePostage() != null) {
                        searchTermBuilder.setIsFreePostage(BoolValue.newBuilder().setValue(searchTerm.getIsFreePostage()).build());
                    }
                    
                    if (searchTerm.getIsCanReturn() != null) {
                        searchTermBuilder.setIsCanReturn(BoolValue.newBuilder().setValue(searchTerm.getIsCanReturn()).build());
                    }
                    
                    if (searchTerm.getIsDiscount() != null) {
                        searchTermBuilder.setIsDiscount(BoolValue.newBuilder().setValue(searchTerm.getIsDiscount()).build());
                    }
                    
                    // 处理其他字符串列表字段
                    if (searchTerm.getPositiveElements() != null && !searchTerm.getPositiveElements().isEmpty()) {
                        searchTermBuilder.addAllPositiveElement(searchTerm.getPositiveElements());
                    }
                    
                    if (searchTerm.getNegativeElements() != null && !searchTerm.getNegativeElements().isEmpty()) {
                        searchTermBuilder.addAllNegativeElement(searchTerm.getNegativeElements());
                    }
                    
                    if (searchTerm.getClothesSizes() != null && !searchTerm.getClothesSizes().isEmpty()) {
                        searchTermBuilder.addAllClothesSize(searchTerm.getClothesSizes());
                    }
                    
                    if (searchTerm.getSizeTypes() != null && !searchTerm.getSizeTypes().isEmpty()) {
                        searchTermBuilder.addAllSizeType(searchTerm.getSizeTypes());
                    }
                    
                    if (searchTerm.getDislikeBrands() != null && !searchTerm.getDislikeBrands().isEmpty()) {
                        searchTermBuilder.addAllDislikeBrand(searchTerm.getDislikeBrands());
                    }
                    
                    if (searchTerm.getDislikeCategories() != null && !searchTerm.getDislikeCategories().isEmpty()) {
                        searchTermBuilder.addAllDislikeCategory(searchTerm.getDislikeCategories());
                    }
                    
                    if (searchTerm.getShouldElements() != null && !searchTerm.getShouldElements().isEmpty()) {
                        searchTermBuilder.addAllShouldElement(searchTerm.getShouldElements());
                    }
                    
                    // 处理vectorQueries列表
                    if (searchTerm.getVectorQueries() != null && !searchTerm.getVectorQueries().isEmpty()) {
                        for (SearchRequestDTO.VectorQuery query : searchTerm.getVectorQueries()) {
                            AgentSearchTerm.VectorQueryModel vectorQueryModel = CommonConvertor.INSTANCE.toGrpcVectorQueryModel(query);
                            if (vectorQueryModel != null) {
                                searchTermBuilder.addVectorQuery(vectorQueryModel);
                            }
                        }
                    }
                    
                    // 处理组合偏好字段
                    handleCombinationPreferences(searchTermBuilder, searchTerm);
                    
                    strategyTermBuilder.setSearchTerm(searchTermBuilder.build());
                }
                
                // 处理step1VectorQueries
                if (strategyTerm.getStep1VectorQueries() != null && !strategyTerm.getStep1VectorQueries().isEmpty()) {
                    for (SearchRequestDTO.VectorQuery query : strategyTerm.getStep1VectorQueries()) {
                        AgentSearchTerm.VectorQueryModel vectorQueryModel = CommonConvertor.INSTANCE.toGrpcVectorQueryModel(query);
                        if (vectorQueryModel != null) {
                            strategyTermBuilder.addStep1VectorQuery(vectorQueryModel);
                        }
                    }
                }
                
                // 处理step2VectorQueries
                if (strategyTerm.getStep2VectorQueries() != null && !strategyTerm.getStep2VectorQueries().isEmpty()) {
                    for (SearchRequestDTO.VectorQuery query : strategyTerm.getStep2VectorQueries()) {
                        AgentSearchTerm.VectorQueryModel vectorQueryModel = CommonConvertor.INSTANCE.toGrpcVectorQueryModel(query);
                        if (vectorQueryModel != null) {
                            strategyTermBuilder.addStep2VectorQuery(vectorQueryModel);
                        }
                    }
                }
                
                // 处理step3VectorQueries
                if (strategyTerm.getStep3VectorQueries() != null && !strategyTerm.getStep3VectorQueries().isEmpty()) {
                    for (SearchRequestDTO.VectorQuery query : strategyTerm.getStep3VectorQueries()) {
                        AgentSearchTerm.VectorQueryModel vectorQueryModel = CommonConvertor.INSTANCE.toGrpcVectorQueryModel(query);
                        if (vectorQueryModel != null) {
                            strategyTermBuilder.addStep3VectorQuery(vectorQueryModel);
                        }
                    }
                }

                if (strategyTerm.getTagsVectorQuery() != null && !strategyTerm.getTagsVectorQuery().isEmpty()) {
                    for (SearchRequestDTO.VectorQuery query : strategyTerm.getTagsVectorQuery()) {
                        AgentSearchTerm.VectorQueryModel vectorQueryModel = CommonConvertor.INSTANCE.toGrpcVectorQueryModel(query);
                        if (vectorQueryModel != null) {
                            strategyTermBuilder.addTagsVectorQuery(vectorQueryModel);
                        }
                    }
                }
                builder.addStrategyTerm(strategyTermBuilder.build());
            }
        }
    }
    
    /**
     * 处理组合偏好字段
     * @param searchTermBuilder 目标SingleAgentSearchTerm.Builder对象
     * @param searchTerm 源SearchTerm对象
     */
    default void handleCombinationPreferences(AgentSearchTerm.SingleAgentSearchTerm.Builder searchTermBuilder, SearchRequestDTO.SearchTerm searchTerm) {
        // 处理combinationPreferenceMust列表
        if (searchTerm.getCombinationPreferenceMust() != null && !searchTerm.getCombinationPreferenceMust().isEmpty()) {
            for (SearchRequestDTO.CombinationPreference pref : searchTerm.getCombinationPreferenceMust()) {
                AgentSearchTerm.combinationPreference.Builder prefBuilder = AgentSearchTerm.combinationPreference.newBuilder();
                
                if (pref.getParentTagType() != null) {
                    prefBuilder.setParentTagType(pref.getParentTagType());
                }
                
                if (pref.getPreferenceNodes() != null && !pref.getPreferenceNodes().isEmpty()) {
                    for (SearchRequestDTO.CombinationPreferenceNode node : pref.getPreferenceNodes()) {
                        AgentSearchTerm.combinationPreferenceNode.Builder nodeBuilder = AgentSearchTerm.combinationPreferenceNode.newBuilder();
                        
                        if (node.getTagType() != null) {
                            nodeBuilder.setTagType(node.getTagType());
                        }
                        
                        if (node.getTagValues() != null && !node.getTagValues().isEmpty()) {
                            nodeBuilder.addAllTagValue(node.getTagValues());
                        }
                        
                        prefBuilder.addPreferenceNode(nodeBuilder.build());
                    }
                }
                
                searchTermBuilder.addCombinationPreferenceMust(prefBuilder.build());
            }
        }
        
        // 处理combinationPreferenceMustNot列表
        if (searchTerm.getCombinationPreferenceMustNot() != null && !searchTerm.getCombinationPreferenceMustNot().isEmpty()) {
            for (SearchRequestDTO.CombinationPreference pref : searchTerm.getCombinationPreferenceMustNot()) {
                AgentSearchTerm.combinationPreference.Builder prefBuilder = AgentSearchTerm.combinationPreference.newBuilder();
                
                if (pref.getParentTagType() != null) {
                    prefBuilder.setParentTagType(pref.getParentTagType());
                }
                
                if (pref.getPreferenceNodes() != null && !pref.getPreferenceNodes().isEmpty()) {
                    for (SearchRequestDTO.CombinationPreferenceNode node : pref.getPreferenceNodes()) {
                        AgentSearchTerm.combinationPreferenceNode.Builder nodeBuilder = AgentSearchTerm.combinationPreferenceNode.newBuilder();
                        
                        if (node.getTagType() != null) {
                            nodeBuilder.setTagType(node.getTagType());
                        }
                        
                        if (node.getTagValues() != null && !node.getTagValues().isEmpty()) {
                            nodeBuilder.addAllTagValue(node.getTagValues());
                        }
                        
                        prefBuilder.addPreferenceNode(nodeBuilder.build());
                    }
                }
                
                searchTermBuilder.addCombinationPreferenceMustNot(prefBuilder.build());
            }
        }
        
        // 处理combinationPreferenceShould列表
        if (searchTerm.getCombinationPreferenceShould() != null && !searchTerm.getCombinationPreferenceShould().isEmpty()) {
            for (SearchRequestDTO.CombinationPreference pref : searchTerm.getCombinationPreferenceShould()) {
                AgentSearchTerm.combinationPreference.Builder prefBuilder = AgentSearchTerm.combinationPreference.newBuilder();
                
                if (pref.getParentTagType() != null) {
                    prefBuilder.setParentTagType(pref.getParentTagType());
                }
                
                if (pref.getPreferenceNodes() != null && !pref.getPreferenceNodes().isEmpty()) {
                    for (SearchRequestDTO.CombinationPreferenceNode node : pref.getPreferenceNodes()) {
                        AgentSearchTerm.combinationPreferenceNode.Builder nodeBuilder = AgentSearchTerm.combinationPreferenceNode.newBuilder();
                        
                        if (node.getTagType() != null) {
                            nodeBuilder.setTagType(node.getTagType());
                        }
                        
                        if (node.getTagValues() != null && !node.getTagValues().isEmpty()) {
                            nodeBuilder.addAllTagValue(node.getTagValues());
                        }
                        
                        prefBuilder.addPreferenceNode(nodeBuilder.build());
                    }
                }
                
                searchTermBuilder.addCombinationPreferenceShould(prefBuilder.build());
            }
        }
    }

    /**
     * 将 GRPC 对象转换为 SearchResponseDTO 对象
     * @param response GRPC 对象
     * @return SearchResponseDTO 对象
     */
    @Mapping(target = "requestId", source = "requestId")
    @Mapping(target = "isDefault", source = "isDefault")
    @Mapping(target = "strategyResponses", source = "strategyV2ResponsesList")
    SearchResponseDTO toSearchResponseDTO(GirlsAgentSearchRecom.GirlsAgentSearchResponse response);
    
    /**
     * 在映射完成后处理SearchResponse中的strategyResponses列表
     * @param responseDTO 目标SearchResponseDTO对象
     * @param response 源GirlsAgentSearchResponse对象
     */
    @AfterMapping
    default void handleSearchResponse(@MappingTarget SearchResponseDTO responseDTO, GirlsAgentSearchRecom.GirlsAgentSearchResponse response) {
        if (!response.getStrategyV2ResponsesList().isEmpty()) {
            List<SearchResponseDTO.StrategyResponse> strategyResponses = new ArrayList<>();
            
            for (GirlsAgentSearchRecom.GirlsSingleStrategyResponse strategyResponse : response.getStrategyV2ResponsesList()) {
                List<ItemDTO> itemDTOList = new ArrayList<>();
                
                for (ItemOuterClass.Item item : strategyResponse.getItemsList()) {
                    ItemDTO itemDTO = CommonConvertor.INSTANCE.toItemDTO(item);
                    if (itemDTO != null) {
                        itemDTOList.add(itemDTO);
                    }
                }
                
                SearchResponseDTO.StrategyResponse strategyResponseDTO = SearchResponseDTO.StrategyResponse.builder()
                    .searchStrategy(strategyResponse.getSearchStrategy())
                    .items(itemDTOList)
                    .size(strategyResponse.getSize())
                    .build();
                
                strategyResponses.add(strategyResponseDTO);
            }
            
            responseDTO.setStrategyResponses(strategyResponses);
        }
    }
}
