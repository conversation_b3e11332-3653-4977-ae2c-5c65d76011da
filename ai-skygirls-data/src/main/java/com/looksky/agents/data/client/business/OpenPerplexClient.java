package com.looksky.agents.data.client.business;

import com.looksky.agents.sdk.perplex.dto.response.PerplexSearchResponse;
import com.looksky.agents.infrastructure.httpapi.ApiServiceEnum;
import com.looksky.agents.infrastructure.httpapi.HttpApi;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;

/**
 * @ClassName OpenPerplexClient
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/2 下午8:55
 * @Version 1.0
 **/
@HttpApi(service = ApiServiceEnum.OPEN_PERPLEX)
public interface OpenPerplexClient {
    @GetExchange("/search")
    PerplexSearchResponse search(
            @RequestHeader(name = "X-API-Key", defaultValue = "${http.third-party.headers.open-perplex.api-key}") String apiKey,
            @RequestParam(name = "query") String query,
            @RequestParam(name = "date_context", required = false) String dateContext,
            @RequestParam(name = "location", required = false) String location,
            @RequestParam(name = "pro_mode", required = false) Boolean proMode,
            @RequestParam(name = "response_language", required = false) String responseLanguage,
            @RequestParam(name = "answer_type", required = false) String answerType,
            @RequestParam(name = "search_type", required = false) String searchType,
            @RequestParam(name = "verbose_mode", required = false) Boolean verboseMode,
            @RequestParam(name = "return_citations", required = false) Boolean returnCitations,
            @RequestParam(name = "return_sources", required = false) Boolean returnSources,
            @RequestParam(name = "return_images", required = false) Boolean returnImages
    );
}
