package com.looksky.agents.data.grpc.convertor;

import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import com.looksky.agents.sdk.recommend.search.dto.request.SearchRequestDTO;
import com.looksky.agents.sdk.recommend.similar.dto.request.SimilarRequestDTO;
import com.looksky.agents.sdk.recommend.similar.dto.response.SimilarResponseDTO;
import com.westyle.recm.AgentSearchTerm;
import com.westyle.recm.GirlsAgentSimilarRecom;
import com.westyle.recm.ItemOuterClass;
import java.util.ArrayList;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = MappingConstants.ComponentModel.SPRING,
    uses = {BoolValueMapper.class, DoubleValueMapper.class, CommonConvertor.class},
    collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface SimilarConvertor {

    /**
     * 将 SimilarRequestDTO 转换为 GRPC 对象
     * @param request SimilarRequestDTO 对象
     * @return GRPC 对象
     */
    @Mapping(target = "userId", source = "userId")
    @Mapping(target = "itemId", source = "itemId")
    @Mapping(target = "season", ignore = true)
    @Mapping(target = "searchTerm", ignore = true)
    GirlsAgentSimilarRecom.GirlsAgentSimilarRequest toGrpcSimilarRequest(SimilarRequestDTO request);
    
    /**
     * 在映射完成后处理SimilarRequest中的字段
     * @param builder 目标GirlsAgentSimilarRequest.Builder对象
     * @param request 源SimilarRequestDTO对象
     */
    @AfterMapping
    default void handleSimilarRequest(@MappingTarget GirlsAgentSimilarRecom.GirlsAgentSimilarRequest.Builder builder, SimilarRequestDTO request) {
        // 只有当season不为null时才设置
        if (request.getSeason() != null) {
            builder.setSeason(request.getSeason());
        }
        
        //// 处理searchTerm字段
        //if (request.getSearchTerm() != null) {
        //    AgentSearchTerm.SingleAgentSearchTerm.Builder searchTermBuilder = AgentSearchTerm.SingleAgentSearchTerm.newBuilder();
        //
        //    // 设置searchStrategy
        //    if (request.getSearchTerm().getSearchStrategy() != null) {
        //        searchTermBuilder.setSearchStrategy(request.getSearchTerm().getSearchStrategy());
        //    }
        //
        //    // 处理categories列表
        //    if (request.getSearchTerm().getCategories() != null && !request.getSearchTerm().getCategories().isEmpty()) {
        //        searchTermBuilder.addAllCategory(request.getSearchTerm().getCategories());
        //    }
        //
        //    // 处理brands列表
        //    if (request.getSearchTerm().getBrands() != null && !request.getSearchTerm().getBrands().isEmpty()) {
        //        searchTermBuilder.addAllBrand(request.getSearchTerm().getBrands());
        //    }
        //
        //    // 处理priceRange
        //    if (request.getSearchTerm().getPriceRange() != null) {
        //        AgentSearchTerm.PriceRangeModel.Builder priceRangeBuilder = AgentSearchTerm.PriceRangeModel.newBuilder();
        //
        //        if (request.getSearchTerm().getPriceRange().getMinPrice() != null) {
        //            priceRangeBuilder.setMinPrice(DoubleValue.newBuilder().setValue(request.getSearchTerm().getPriceRange().getMinPrice()).build());
        //        }
        //
        //        if (request.getSearchTerm().getPriceRange().getMaxPrice() != null) {
        //            priceRangeBuilder.setMaxPrice(DoubleValue.newBuilder().setValue(request.getSearchTerm().getPriceRange().getMaxPrice()).build());
        //        }
        //
        //        if (request.getSearchTerm().getPriceRange().getNormPrice() != null) {
        //            priceRangeBuilder.setNormPrice(DoubleValue.newBuilder().setValue(request.getSearchTerm().getPriceRange().getNormPrice()).build());
        //        }
        //
        //        searchTermBuilder.setPriceRange(priceRangeBuilder.build());
        //    }
        //
        //    // 处理userPreferences列表
        //    if (request.getSearchTerm().getUserPreferences() != null && !request.getSearchTerm().getUserPreferences().isEmpty()) {
        //        for (SearchRequestDTO.UserPreference userPref : request.getSearchTerm().getUserPreferences()) {
        //            AgentSearchTerm.UserPreferenceModel.Builder userPrefBuilder = AgentSearchTerm.UserPreferenceModel.newBuilder();
        //
        //            if (userPref.getTagType() != null) {
        //                userPrefBuilder.setTagType(userPref.getTagType());
        //            }
        //
        //            if (userPref.getLike() != null && !userPref.getLike().isEmpty()) {
        //                userPrefBuilder.addAllLike(userPref.getLike());
        //            }
        //
        //            if (userPref.getDisLike() != null && !userPref.getDisLike().isEmpty()) {
        //                userPrefBuilder.addAllDisLike(userPref.getDisLike());
        //            }
        //
        //            if (userPref.getRecommend() != null && !userPref.getRecommend().isEmpty()) {
        //                userPrefBuilder.addAllRecommend(userPref.getRecommend());
        //            }
        //
        //            searchTermBuilder.addUserPreference(userPrefBuilder.build());
        //        }
        //    }
        //
        //    // 处理布尔值字段
        //    if (request.getSearchTerm().getIsFreePostage() != null) {
        //        searchTermBuilder.setIsFreePostage(BoolValue.newBuilder().setValue(request.getSearchTerm().getIsFreePostage()).build());
        //    }
        //
        //    if (request.getSearchTerm().getIsCanReturn() != null) {
        //        searchTermBuilder.setIsCanReturn(BoolValue.newBuilder().setValue(request.getSearchTerm().getIsCanReturn()).build());
        //    }
        //
        //    if (request.getSearchTerm().getIsDiscount() != null) {
        //        searchTermBuilder.setIsDiscount(BoolValue.newBuilder().setValue(request.getSearchTerm().getIsDiscount()).build());
        //    }
        //
        //    // 处理其他字符串列表字段
        //    if (request.getSearchTerm().getPositiveElements() != null && !request.getSearchTerm().getPositiveElements().isEmpty()) {
        //        searchTermBuilder.addAllPositiveElement(request.getSearchTerm().getPositiveElements());
        //    }
        //
        //    if (request.getSearchTerm().getNegativeElements() != null && !request.getSearchTerm().getNegativeElements().isEmpty()) {
        //        searchTermBuilder.addAllNegativeElement(request.getSearchTerm().getNegativeElements());
        //    }
        //
        //    if (request.getSearchTerm().getClothesSizes() != null && !request.getSearchTerm().getClothesSizes().isEmpty()) {
        //        searchTermBuilder.addAllClothesSize(request.getSearchTerm().getClothesSizes());
        //    }
        //
        //    if (request.getSearchTerm().getSizeTypes() != null && !request.getSearchTerm().getSizeTypes().isEmpty()) {
        //        searchTermBuilder.addAllSizeType(request.getSearchTerm().getSizeTypes());
        //    }
        //
        //    if (request.getSearchTerm().getDislikeBrands() != null && !request.getSearchTerm().getDislikeBrands().isEmpty()) {
        //        searchTermBuilder.addAllDislikeBrand(request.getSearchTerm().getDislikeBrands());
        //    }
        //
        //    if (request.getSearchTerm().getDislikeCategories() != null && !request.getSearchTerm().getDislikeCategories().isEmpty()) {
        //        searchTermBuilder.addAllDislikeCategory(request.getSearchTerm().getDislikeCategories());
        //    }
        //
        //    if (request.getSearchTerm().getShouldElements() != null && !request.getSearchTerm().getShouldElements().isEmpty()) {
        //        searchTermBuilder.addAllShouldElement(request.getSearchTerm().getShouldElements());
        //    }
        //
        //    // 处理vectorQueries列表
        //    if (request.getSearchTerm().getVectorQueries() != null && !request.getSearchTerm().getVectorQueries().isEmpty()) {
        //        for (SearchRequestDTO.VectorQuery query : request.getSearchTerm().getVectorQueries()) {
        //            AgentSearchTerm.VectorQueryModel vectorQueryModel = CommonConvertor.INSTANCE.toGrpcVectorQueryModel(query);
        //            if (vectorQueryModel != null) {
        //                searchTermBuilder.addVectorQuery(vectorQueryModel);
        //            }
        //        }
        //    }
        //
        //    // 处理组合偏好字段
        //    handleCombinationPreferences(searchTermBuilder, request.getSearchTerm());
        //
        //    builder.setSearchTerm(searchTermBuilder.build());
        //}
    }
    
    /**
     * 处理组合偏好字段
     * @param searchTermBuilder 目标SingleAgentSearchTerm.Builder对象
     * @param searchTerm 源SearchTerm对象
     */
    default void handleCombinationPreferences(AgentSearchTerm.SingleAgentSearchTerm.Builder searchTermBuilder, SearchRequestDTO.SearchTerm searchTerm) {
        // 处理combinationPreferenceMust列表
        if (searchTerm.getCombinationPreferenceMust() != null && !searchTerm.getCombinationPreferenceMust().isEmpty()) {
            for (SearchRequestDTO.CombinationPreference pref : searchTerm.getCombinationPreferenceMust()) {
                AgentSearchTerm.combinationPreference.Builder prefBuilder = AgentSearchTerm.combinationPreference.newBuilder();
                
                if (pref.getParentTagType() != null) {
                    prefBuilder.setParentTagType(pref.getParentTagType());
                }
                
                if (pref.getPreferenceNodes() != null && !pref.getPreferenceNodes().isEmpty()) {
                    for (SearchRequestDTO.CombinationPreferenceNode node : pref.getPreferenceNodes()) {
                        AgentSearchTerm.combinationPreferenceNode.Builder nodeBuilder = AgentSearchTerm.combinationPreferenceNode.newBuilder();
                        
                        if (node.getTagType() != null) {
                            nodeBuilder.setTagType(node.getTagType());
                        }
                        
                        if (node.getTagValues() != null && !node.getTagValues().isEmpty()) {
                            nodeBuilder.addAllTagValue(node.getTagValues());
                        }
                        
                        prefBuilder.addPreferenceNode(nodeBuilder.build());
                    }
                }
                
                searchTermBuilder.addCombinationPreferenceMust(prefBuilder.build());
            }
        }
        
        // 处理combinationPreferenceMustNot列表
        if (searchTerm.getCombinationPreferenceMustNot() != null && !searchTerm.getCombinationPreferenceMustNot().isEmpty()) {
            for (SearchRequestDTO.CombinationPreference pref : searchTerm.getCombinationPreferenceMustNot()) {
                AgentSearchTerm.combinationPreference.Builder prefBuilder = AgentSearchTerm.combinationPreference.newBuilder();
                
                if (pref.getParentTagType() != null) {
                    prefBuilder.setParentTagType(pref.getParentTagType());
                }
                
                if (pref.getPreferenceNodes() != null && !pref.getPreferenceNodes().isEmpty()) {
                    for (SearchRequestDTO.CombinationPreferenceNode node : pref.getPreferenceNodes()) {
                        AgentSearchTerm.combinationPreferenceNode.Builder nodeBuilder = AgentSearchTerm.combinationPreferenceNode.newBuilder();
                        
                        if (node.getTagType() != null) {
                            nodeBuilder.setTagType(node.getTagType());
                        }
                        
                        if (node.getTagValues() != null && !node.getTagValues().isEmpty()) {
                            nodeBuilder.addAllTagValue(node.getTagValues());
                        }
                        
                        prefBuilder.addPreferenceNode(nodeBuilder.build());
                    }
                }
                
                searchTermBuilder.addCombinationPreferenceMustNot(prefBuilder.build());
            }
        }
        
        // 处理combinationPreferenceShould列表
        if (searchTerm.getCombinationPreferenceShould() != null && !searchTerm.getCombinationPreferenceShould().isEmpty()) {
            for (SearchRequestDTO.CombinationPreference pref : searchTerm.getCombinationPreferenceShould()) {
                AgentSearchTerm.combinationPreference.Builder prefBuilder = AgentSearchTerm.combinationPreference.newBuilder();
                
                if (pref.getParentTagType() != null) {
                    prefBuilder.setParentTagType(pref.getParentTagType());
                }
                
                if (pref.getPreferenceNodes() != null && !pref.getPreferenceNodes().isEmpty()) {
                    for (SearchRequestDTO.CombinationPreferenceNode node : pref.getPreferenceNodes()) {
                        AgentSearchTerm.combinationPreferenceNode.Builder nodeBuilder = AgentSearchTerm.combinationPreferenceNode.newBuilder();
                        
                        if (node.getTagType() != null) {
                            nodeBuilder.setTagType(node.getTagType());
                        }
                        
                        if (node.getTagValues() != null && !node.getTagValues().isEmpty()) {
                            nodeBuilder.addAllTagValue(node.getTagValues());
                        }
                        
                        prefBuilder.addPreferenceNode(nodeBuilder.build());
                    }
                }
                
                searchTermBuilder.addCombinationPreferenceShould(prefBuilder.build());
            }
        }
    }

    /**
     * 将 GRPC 对象转换为 SimilarResponseDTO 对象
     * @param response GRPC 对象
     * @return SimilarResponseDTO 对象
     */
    @Mapping(target = "size", source = "size")
    @Mapping(target = "requestId", source = "requestId")
    SimilarResponseDTO toSimilarResponseDTO(GirlsAgentSimilarRecom.GirlsAgentSimilarResponse response);
    
    /**
     * 在映射完成后处理SimilarResponse中的items列表
     * @param responseDTO 目标SimilarResponseDTO对象
     * @param response 源GirlsAgentSimilarResponse对象
     */
    @AfterMapping
    default void handleSimilarResponse(@MappingTarget SimilarResponseDTO responseDTO, GirlsAgentSimilarRecom.GirlsAgentSimilarResponse response) {
        // 处理items列表
        if (response.getItemsCount() > 0) {
            List<ItemDTO> itemDTOList = new ArrayList<>();
            for (ItemOuterClass.Item item : response.getItemsList()) {
                ItemDTO itemDTO = CommonConvertor.INSTANCE.toItemDTO(item);
                if (itemDTO != null) {
                    itemDTOList.add(itemDTO);
                }
            }
            responseDTO.setItems(itemDTOList);
        }
    }
}
