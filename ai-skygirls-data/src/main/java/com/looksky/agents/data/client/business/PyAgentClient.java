package com.looksky.agents.data.client.business;


import com.looksky.agents.infrastructure.httpapi.ApiServiceEnum;
import com.looksky.agents.infrastructure.httpapi.HttpApi;
import com.looksky.agents.sdk.agent.common.dto.PyAgentRedisDataDTO;
import com.skygirls.biz.im.dto.MessageRestDTO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.PostExchange;

@HttpApi(service = ApiServiceEnum.PY_AGENT)
public interface PyAgentClient {

    @PostExchange("/agent/chat/")
    void sendAgentMessage(@RequestBody MessageRestDTO messageRestDTO);


    @GetExchange("/agent/redis")
    PyAgentRedisDataDTO redisData(@RequestParam String key);

}