package com.looksky.agents.data.opensearch.config;

import jakarta.annotation.PostConstruct;
import jakarta.validation.constraints.Positive;
import java.io.IOException;
import java.time.Duration;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.json.jackson.JacksonJsonpMapper;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.transport.aws.AwsSdk2Transport;
import org.opensearch.client.transport.aws.AwsSdk2TransportOptions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;
import software.amazon.awssdk.http.SdkHttpClient;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;

/**
 * @Author：ch
 * @Date：2024/8/14 14:17
 * @Description
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "opensearch.aws")
@Validated
public class OpensSearchClientConfig {

    private String accessKeyId;
    private String secretAccessKey;
    private String endpoint;
    private String signingServiceName;
    private String region;
    @Positive
    private Long socketTimeout = 30000L;  // 默认30秒
    @Positive
    private Long connectionTimeout = 10000L;  // 默认10秒
    @Positive
    private Integer maxConnections = 100;  // 默认最大连接数

    @PostConstruct
    public void init(){

        log.info("开始设置aws - openSearch 系统参数");
        System.setProperty("aws.accessKeyId", accessKeyId);
        System.setProperty("aws.secretAccessKey", secretAccessKey);

    }

    @Bean
    public SdkHttpClient awsSdkHttpClient() {
        return ApacheHttpClient.builder()
                .connectionTimeout(Duration.ofMillis(connectionTimeout))
                .connectionAcquisitionTimeout(Duration.ofMillis(connectionTimeout))
                .socketTimeout(Duration.ofMillis(socketTimeout))
                .maxConnections(maxConnections)
                .useIdleConnectionReaper(true)  // 启用空闲连接收割器
                .connectionTimeToLive(Duration.ofMinutes(5))  // 连接最大存活时间
                .build();
    }

    @Bean
    public OpenSearchClient openSearchClient(SdkHttpClient awsSdkHttpClient) throws IOException {

        AwsSdk2TransportOptions transportOptions = AwsSdk2TransportOptions.builder()
                .setMapper(new JacksonJsonpMapper())
                .addHeader("Accept-Encoding", "gzip")
                .build();

        AwsSdk2Transport transport = new AwsSdk2Transport(
                awsSdkHttpClient,
                endpoint,
                signingServiceName,
                Region.of(region), // signing service region
                transportOptions
        );

        log.info("OpenSearchClient 开始创建");
        return new OpenSearchClient(transport);

    }

}
