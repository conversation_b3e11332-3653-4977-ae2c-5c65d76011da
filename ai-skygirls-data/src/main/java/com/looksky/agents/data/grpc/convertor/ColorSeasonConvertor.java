package com.looksky.agents.data.grpc.convertor;

import com.looksky.agents.sdk.recommend.colorseason.request.ColorSeasonRequestDTO;
import com.looksky.agents.sdk.recommend.colorseason.response.ColorSeasonResponseDTO;
import com.looksky.agents.sdk.recommend.common.dto.ItemDTO;
import com.looksky.agents.sdk.recommend.common.dto.VectorRecallModelDTO;
import com.westyle.recm.GirlsColorSeasonRecm;
import com.westyle.recm.ItemOuterClass;
import com.westyle.recm.VectorRecall;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import java.util.ArrayList;
import java.util.List;

/**
 * 颜色季节转换器
 */
@Mapper(
    componentModel = MappingConstants.ComponentModel.SPRING,
    uses = {BoolValueMapper.class, DoubleValueMapper.class, CommonConvertor.class},
    collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface ColorSeasonConvertor {
    /**
     * 转换 ColorSeasonRequest 为 GRPC 对象
     * @param request ColorSeason DTO 对象
     * @return ColorSeason GRPC 对象
     */
    @Mapping(target = "userId", source = "userId")
    @Mapping(target = "vectorQueryVersion", source = "vectorQueryVersion")
    @Mapping(target = "clientDayTime", source = "date")
    GirlsColorSeasonRecm.GirlsColorSeasonRequest toGrpcColorSeasonRequest(ColorSeasonRequestDTO request);

    /**
     * 在映射完成后处理ColorSeasonRequest中的vectorRecall列表
     * @param builder 目标GirlsColorSeasonRequest.Builder对象
     * @param request 源ColorSeasonRequestDTO对象
     */
    @AfterMapping
    default void handleColorSeasonRequest(@MappingTarget GirlsColorSeasonRecm.GirlsColorSeasonRequest.Builder builder, ColorSeasonRequestDTO request) {
        // 处理vectorRecall列表
        if (request.getVectorRecall() != null && !request.getVectorRecall().isEmpty()) {
            for (VectorRecallModelDTO dto : request.getVectorRecall()) {
                VectorRecall.VectorRecallModel model = CommonConvertor.INSTANCE.toGrpcVectorRecallModel(dto);
                if (model != null) {
                    builder.addVectorRecall(model);
                }
            }
        }
    }

    /**
     * 将 GRPC 对象转换为 ColorSeasonResponse DTO 对象
     * @param response ColorSeason GRPC 对象
     * @return ColorSeason DTO 对象
     */
    @Mapping(target = "size", source = "size")
    @Mapping(target = "requestId", source = "requestId")
    @Mapping(target = "newDay", source = "newDay")
    ColorSeasonResponseDTO toColorSeasonResponseDTO(GirlsColorSeasonRecm.GirlsColorSeasonResponse response);
    
    /**
     * 在映射完成后处理ColorSeasonResponse中的items列表
     * @param responseDTO 目标ColorSeasonResponseDTO对象
     * @param response 源GirlsColorSeasonResponse对象
     */
    @AfterMapping
    default void handleColorSeasonResponse(@MappingTarget ColorSeasonResponseDTO responseDTO, GirlsColorSeasonRecm.GirlsColorSeasonResponse response) {
        // 处理items列表
        if (response.getItemsCount() > 0) {
            List<ItemDTO> itemDTOList = new ArrayList<>();
            for (ItemOuterClass.Item item : response.getItemsList()) {
                ItemDTO itemDTO = CommonConvertor.INSTANCE.toItemDTO(item);
                if (itemDTO != null) {
                    itemDTOList.add(itemDTO);
                }
            }
            responseDTO.setItems(itemDTOList);
        }
    }
}
