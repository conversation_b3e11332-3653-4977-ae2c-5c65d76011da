package com.looksky.agents.data.client.utils;

import cn.hutool.core.util.IdUtil;
import com.graecove.common.ABTestFlagResp;
import com.looksky.agents.common.utils.ABTestFlagUtils;
import com.looksky.agents.common.utils.StringMatchUtils;
import com.looksky.agents.data.client.service.GirlsDataService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.infrastructure.interceptor.GrpcResponseHeaderInterceptor;
import com.looksky.agents.infrastructure.thread.VirtualCompletableFuture;
import com.looksky.agents.infrastructure.trace.annotation.TraceMethod;
import com.looksky.agents.models.parseOutput.ThinkTextBuffer;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.EventTypeEnum;
import com.looksky.agents.sdk.agent.communication.ReasoningMessage;
import com.looksky.agents.sdk.agent.ext.AgentMessageResp;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 通信工具
 *
 * <AUTHOR>
 * @since 1.1.14
 **/
@Component
@RequiredArgsConstructor
public class CommunicationHelper {
    private final GirlsDataService girlsDataService;

    public ReasoningMessage handlerDeepSeekThink(Flux<String> strFlux) {
        AgentMessageResp reasoningContent = initMessage(EventTypeEnum.REASONING_CONTENT);
        return handlerDeepSeekThink(strFlux, reasoningContent);
    }

    public ReasoningMessage handlerDeepSeekThink(Flux<String> strFlux, AgentMessageResp reasoningContent) {
        AgentMessageResp reasoningPlans = initMessage(EventTypeEnum.REASONING_PLANS);
        return new DeepSeekRecommendPlansHandler(reasoningContent, reasoningPlans).handler(strFlux);
    }


    public VirtualCompletableFuture<Void> sendErrorMessageAsync(AgentMessageResp message, String errorContent) {
        return VirtualCompletableFuture.runAsync(() -> {
            message.setContentValue(null).setIsFinished(true).setCode(1).setErrorMessage(errorContent);
            girlsDataService.sendAgentMessage(message);
        });
    }


    /**
     * 发送消息
     *
     * @param type    消息的 type
     * @param content 消息的内容
     */
    @TraceMethod(description = "给后端发送消息")
    public void sendMessage(EventTypeEnum type, Object content) {
        sendMessage(type, content, true);
    }

    /**
     * 发送消息
     *
     * @param type       消息的 type
     * @param content    消息的内容
     * @param isFinished 是否是结束消息
     */
    public AgentMessageResp sendMessage(EventTypeEnum type, Object content, boolean isFinished) {
        // 构建消息
        AgentMessageResp message = initMessage(type);

        // 发送内容
        sendContentAsync(message, content, isFinished);

        return message;
    }

    /**
     * 异步发送内容
     *
     * @param message    消息对象
     * @param content    内容
     * @param isFinished 是否是结束消息
     */
    public void sendContentAsync(AgentMessageResp message, Object content, boolean isFinished) {
        VirtualCompletableFuture.runAsync(() -> {
            message.setContentValue(content);
            message.setIsFinished(isFinished);
            girlsDataService.sendAgentMessage(message);
        });
    }


    /**
     * 初始化消息
     *
     * @param type 消息类型
     * @return AgentMessageResp 对象
     */
    public AgentMessageResp initMessage(EventTypeEnum type) {
        // 构建消息
        AgentMessageResp message = buildMessage(type);

        // 发送初始消息
        girlsDataService.sendAgentMessage(message);

        // 将请求 ID 置空
        message.setRequestId(null);

        return message;
    }


    /**
     * 构建消息
     *
     * @param type 消息类型
     * @return AgentMessageResp 对象
     */
    public static AgentMessageResp buildMessage(EventTypeEnum type) {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());
        Boolean intentChange = Context.get(Context.Name.NEW_CONVERSATION_STATUS.getName());

        AgentMessageResp message = new AgentMessageResp();
        message.setMessageId(IdUtil.getSnowflakeNextIdStr())
            .setConversationId(requestInput.getConversationId())
            .setEventName(requestInput.getEvent().getEventName())
            .setUserId(requestInput.getUserId())
            .setZone(requestInput.getZone())
            .setConnectionId(requestInput.getConnectionId())
            .setPage(requestInput.getPage())
            .setContentType(type.getType())
            .setRequestId(requestInput.getRequestId())
            .setEnterPoint(requestInput.getEnterPointEnum().getValue())
            .setContentOrder(1)
            .setAbTestFlagResp(getAbTestFlag())
            .setIsFinished(false);

        Optional.ofNullable(requestInput.getEventDict()).ifPresent(eventDict -> message.setSkuId(eventDict.getSkuId()));
        Optional.ofNullable(requestInput.getEventDict()).ifPresent(eventDict -> message.setSkcId(eventDict.getSkcId()));
        Optional.ofNullable(requestInput.getEventDict()).ifPresent(eventDict -> message.setMessageType(eventDict.getMessageType()));

        message.setIntentChange(intentChange);
        return message;
    }


    /**
     * 获取 AB 测试信息
     *
     * @return AB 测试信息
     */
    public static ABTestFlagResp getAbTestFlag() {
        Object abTestFlag = Context.get(GrpcResponseHeaderInterceptor.KEY_NAME);
        return ABTestFlagUtils.parse(abTestFlag);
    }

    /**
     * 发送 Loading 事件
     */
    public void sendLoading() {
        sendMessage(EventTypeEnum.LOADING, null);
    }


    class DeepSeekRecommendPlansHandler {
        AgentMessageResp reasoningContent;
        AgentMessageResp reasoningPlans;
        ThinkTextBuffer buffer;


        DeepSeekRecommendPlansHandler(AgentMessageResp reasoningContent, AgentMessageResp reasoningPlans) {
            this.reasoningContent = reasoningContent;
            this.reasoningPlans = reasoningPlans;
            this.buffer = new ThinkTextBuffer();
        }

        public ReasoningMessage handler(Flux<String> strFlux) {
            return strFlux.doOnNext(this::next)
                .doOnError(error -> error(error.getMessage()))
                .doOnComplete(this::complete)
                .then(Mono.fromCallable(this::finalResult)).block();
        }


        void next(String chunk) {

            buffer.appendChunk(StringMatchUtils.filterDeepSeekOutput(chunk));

            if (buffer.hasEnoughNewWords()) {
                buffer.processAndUpdate();
                // 获取当前是哪个内容
                if (buffer.getNormalTextStarted()) {
                    if (buffer.isGetThinkFinishMessage()) {
                        reasoningContent.setContentValue(buffer.getThinkFinalText());
                        reasoningContent.setIsFinished(true);
                        girlsDataService.sendAsyncAgentMessage(reasoningContent);
                    }
                    reasoningPlans.setContentValue(buffer.getCurrentText());
                    girlsDataService.sendAsyncAgentMessage(reasoningPlans);

                } else {
                    reasoningContent.setContentValue(buffer.getThinkText());
                    girlsDataService.sendAsyncAgentMessage(reasoningContent);
                }
            }
        }

        void error(String error) {
            sendErrorMessageAsync(initMessage(EventTypeEnum.TEXT), error);
        }

        void complete() {
            reasoningPlans.setContentValue(buffer.getFinalText());
            reasoningPlans.setIsFinished(true);
            girlsDataService.sendAsyncAgentMessage(reasoningPlans);
        }

        ReasoningMessage finalResult() {
            return new ReasoningMessage(buffer.getThinkText(), buffer.getCurrentText());
        }
    }
}
