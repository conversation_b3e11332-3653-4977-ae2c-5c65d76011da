package com.looksky.agents.data.client.business;


import com.fasterxml.jackson.databind.JsonNode;
import com.graecove.common.ApiResp;
import com.looksky.agents.infrastructure.httpapi.ApiServiceEnum;
import com.looksky.agents.infrastructure.httpapi.HttpApi;
import com.looksky.agents.sdk.product.ProductInfoResp;
import com.looksky.agents.sdk.recommend.reason.dto.RecommendReasonDTO;
import com.skygirls.biz.im.dto.AgentMessageRespV3;
import com.skygirls.biz.product.dto.BatchGetSkcByIdsReq;
import com.skygirls.biz.report.IosUserInfoDto;
import com.skygirls.biz.user.tryon.dto.SearchTryOnStatusReq;
import com.skygirls.biz.user.tryon.model.TryOnReportModel;
import java.util.List;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.PostExchange;

@HttpApi(service = ApiServiceEnum.SKYGIRLS_API, path = "/skygirls")
public interface GirlsClient {

    @PostExchange("/chat/receiveAgentMsgV3")
    ApiResp<Void> sendAgentMessage(@RequestBody AgentMessageRespV3 agentMessageRespV3);

    @GetExchange("/user/page/userinfo/{userId}")
    ApiResp<IosUserInfoDto> userInfo(@PathVariable String userId);

    @PostExchange("/product/v2/batchGetSkcByIds")
    ApiResp<ProductInfoResp> batchGetSkcByIds(@RequestBody BatchGetSkcByIdsReq batchGetSkcByIdsReq);

    @GetExchange("/user/page/userinfo/python/{userId}")
    ApiResp<IosUserInfoDto> userInfoPython(@PathVariable String userId);

    @PostExchange("/product/info/recommend/callback")
    void recomReasonCallback(@RequestBody List<RecommendReasonDTO> reasonModels);

    @GetExchange("/user/page/temp/{userId}")
    ApiResp<IosUserInfoDto> userInfoPreRegistration(@PathVariable String userId);

    @PostExchange("/sign/agent/tryon/colorseason")
    ApiResp<String> colorSeason(@RequestHeader String userid, @RequestHeader String sign);

    @PostExchange("/tryon-agent/callback")
    ApiResp<String> tryonCallback(@RequestBody SearchTryOnStatusReq swapClothCallbackRequest);

    @PostExchange("/tryon-agent/search/status")
    ApiResp<TryOnReportModel> tryOnHomeCard(@RequestBody SearchTryOnStatusReq swapClothCallbackRequest);

    @GetExchange("/category/getTagsConfigV2?publicKey=225a44b332f0492bbbc04b4ab5378f42")
    ApiResp<JsonNode> getTagSystemTable();

}