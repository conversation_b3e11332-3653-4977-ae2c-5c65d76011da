package com.looksky.agents.data.mysql.service.impl;

import com.looksky.agents.sdk.agent.event.model.EventModel;
import com.looksky.agents.data.mysql.mapper.EventMapper;
import com.looksky.agents.data.mysql.service.IEventService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22 14:04:33
 */
@Service
public class EventServiceImpl extends ServiceImpl<EventMapper, EventModel> implements IEventService {

    @Override
    @Cacheable(value = "event:eventModel", key = "#name")
    public EventModel getEventByName(String name) {
        return lambdaQuery().eq(EventModel::getName, name).oneOpt().orElse(null);
    }
}
