package com.looksky.agents.data.client.service;


import cn.hutool.core.util.ObjectUtil;
import com.graecove.common.ApiResp;
import com.looksky.agents.sdk.product.ProductInfoResp;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.data.client.business.LookSkyClient;
import com.looksky.agents.infrastructure.context.Context;
import com.skygirls.biz.im.dto.AgentMessageRespV3;
import com.skygirls.biz.product.dto.BatchGetSkcByIdsReq;
import com.skygirls.biz.report.IosUserInfoDto;
import com.skygirls.biz.user.dto.VisitHistoryDTO;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName UserDataService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/12 下午2:18
 * @Version 1.0
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class LookSkyDataService {
    private final LookSkyClient lookSkyClient;
    private final GirlsClient girlsClient;

    public ProductInfoResp getProductInfo(String skcId) {
        return getProductInfo(List.of(skcId));

    }

    public ProductInfoResp getProductInfo(List<String> skcIds) {

        BatchGetSkcByIdsReq batchGetSkcByIdsReq = new BatchGetSkcByIdsReq();
        batchGetSkcByIdsReq.setIds(skcIds);
        batchGetSkcByIdsReq.setUserId("1");
        batchGetSkcByIdsReq.setNotRecommend(1);


        ApiResp<ProductInfoResp> response = girlsClient.batchGetSkcByIds(batchGetSkcByIdsReq);

        ProductInfoResp productInfo = new ProductInfoResp();

        if (response != null && !ObjectUtil.isEmpty(response.getData())) {
            productInfo =  response.getData();
        } else {
            log.error("获取商品信息失败, skcId: {}, response: {}", skcIds, response);
        }

        Context.put(Context.Name.PRODUCT_INFO.getName(), productInfo);

        return productInfo;

    }



    public IosUserInfoDto getUserInfo(String userId) {
        IosUserInfoDto userInfoDto;
        try {
            userInfoDto = girlsClient.userInfo(userId).getData();
            if (userInfoDto == null) {
                userInfoDto = new IosUserInfoDto();
            }
        } catch (Exception e) {
            log.info("获取用户信息失败, userId: {}", userId, e);
            userInfoDto = new IosUserInfoDto();
        }

        Context.put(Context.Name.USER_INFO.getName(), userInfoDto);

        return userInfoDto;
    }


    public void sendAgentMessage(AgentMessageRespV3 agentMessageRespV3) {
        try {
            girlsClient.sendAgentMessage(agentMessageRespV3);
        } catch (Exception e) {
            log.error("消息发送失败: {}", e.getMessage());
        }
    }


    @Deprecated
    public VisitHistoryDTO visitHistory(String userId) {
        ApiResp<VisitHistoryDTO> response = lookSkyClient.appVisitHistory(userId);
        return response.getData();
    }
}
