package com.looksky.agents.data.redis.product;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.data.client.business.PyAgentClient;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.sdk.agent.common.dto.PyAgentRedisDataDTO;
import com.looksky.agents.sdk.product.ProductDTO;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;

/**
 * @ClassName ProductService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/8 下午1:40
 * @Version 1.0
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductClient {
    private final RedissonClient redissonClient;
    private final ObjectMapper objectMapper;

    private final PyAgentClient client;

    private static final String SKC_INFO = "all:skc:%s";
    private static final String BRAND_INFO = "all:br:%s";
    private static final String TOPICS = "topics";


    /**
     * 获取商品的详细信息
     * @param skcId 商品的 skcId
     * @return 商品的详细信息
     */
    public String getSkcInfo(String skcId) {
        PyAgentRedisDataDTO pyAgentRedisDataDTO = client.redisData(String.format(SKC_INFO, skcId));
        return pyAgentRedisDataDTO.getValue();
    }


    /**
     * 获取商品的品牌名称
     * @param skcId 商品的 skcId
     * @return 商品的品牌名称
     */
    @SneakyThrows
    public String getBrandName(String skcId) {
        String skcInfo = getSkcInfo(skcId);
        
        if (skcInfo == null || skcInfo.isEmpty()) {
            return null;
        }
        
        JsonNode rootNode = objectMapper.readTree(skcInfo);

        // 从 topics 数组中查找 brand 类型的对象
        if (rootNode.has(TOPICS) && rootNode.get(TOPICS).isArray()) {
            JsonNode topicsNode = rootNode.get(TOPICS);
            for (JsonNode topic : topicsNode) {
                if (topic.has("type") && "brand".equals(topic.get("type").asText()) &&
                    topic.has("value")) {
                    return topic.get("value").asText();
                }
            }
        }

        return null;
    }


    /**
     * 根据 skcId 获取品牌信息 里面包含品牌的支付方式, 折扣消息, 尺码消息等
     * @param skcId 商品的 skcId
     * @return 品牌信息
     */
    public String getBrandInfoBySkcId(String skcId) {
        String brandName = getBrandName(skcId);
        return this.getBrandInfoByBrandName(brandName);
    }


    /**
     * 根据 skcId 获取品牌信息 里面包含品牌的支付方式, 折扣消息, 尺码消息等
     * @return 品牌信息
     */
    public String getBrandInfoByBrandName(String brandName) {
        return client.redisData(String.format(BRAND_INFO, brandName)).getValue();
    }


    /**
     * 获取商品的销售政策 --> 包邮政策
     * @param skcId
     * @return
     */
    public String getShippingInfoBySkcId(String skcId) {
        String brandName = getBrandName(skcId);
        return this.getShippingInfoByBrandName(brandName);
    }

    @SneakyThrows
    public String getShippingInfoByBrandName(String brandName) {
        String brandInfo = this.getBrandInfoByBrandName(brandName);
        JsonNode rootNode = objectMapper.readTree(brandInfo);
        JsonNode infoNode = rootNode.path("info");
        if (!infoNode.isMissingNode()) {
            JsonNode shippingInfo = infoNode.path("shipping_info");
            if (!shippingInfo.isMissingNode()) {
                return shippingInfo.asText();
            }
        }
        return null;
    }

    public String getBrandInfo(String skcId) {
        String brandName = getBrandName(skcId);
        return getBrandInfoByBrandName(brandName);
    }



    // ============================== 下面这些方法都应该废弃 ==============================
    public ProductDTO getProductByProductId(String productId) {
        String productStr = getProductByProductIdFromRedis(productId);
        try {
            return objectMapper.readValue(productStr, ProductDTO.class);
        } catch (JsonProcessingException e) {
            log.error("解析商品信息时发生错误: {}", productStr, e);
            throw new RuntimeException(e);
        }
    }

    public ProductDTO getProductBySkcId(String skcId) {
        String productStr = getProductBySkcIdFromRedis(skcId);
        try {
            return objectMapper.readValue(productStr, ProductDTO.class);
        } catch (JsonProcessingException e) {
            log.error("解析商品信息时发生错误: {}", productStr, e);
            throw new RuntimeException(e);
        }
    }

    private String getProductByProductIdFromRedis(String productId) {
        RBucket<String> bucket = redissonClient.getBucket(CharSequenceUtil.format(RedisKeyConstants.PRODUCT_PRODUCT_INFO, productId), StringCodec.INSTANCE);
        return bucket.get();
    }

    private String getProductBySkcIdFromRedis(String skcId) {
        RBucket<String> bucket = redissonClient.getBucket(CharSequenceUtil.format(RedisKeyConstants.PRODUCT_SKC_INFO, skcId), StringCodec.INSTANCE);
        return bucket.get();
    }

}
