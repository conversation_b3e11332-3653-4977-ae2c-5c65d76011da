package com.looksky.agents.data.mysql.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.looksky.agents.sdk.agent.prompt.model.PromptMacrosModel;
import com.looksky.agents.data.mysql.mapper.PromptMacrosMapper;
import com.looksky.agents.data.mysql.service.IPromptMacrosService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-21 14:14:29
 */
@Service
public class PromptMacrosServiceImpl extends ServiceImpl<PromptMacrosMapper, PromptMacrosModel> implements IPromptMacrosService {

    @Override
    @Cacheable(value = "prompt:macros", key = "#name")
    public String getMacrosContent(String name) {
        PromptMacrosModel promptMacrosModel = getMacrosByName(name);
        return promptMacrosModel == null ? null : promptMacrosModel.getContent();
    }

    @Override
    public PromptMacrosModel getMacrosByName(String name) {
        return lambdaQuery().eq(PromptMacrosModel::getName, name).oneOpt().orElse(null);
    }
}
