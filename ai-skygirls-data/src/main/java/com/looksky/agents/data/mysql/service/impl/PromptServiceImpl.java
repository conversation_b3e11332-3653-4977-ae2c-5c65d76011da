package com.looksky.agents.data.mysql.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.looksky.agents.data.mysql.mapper.PromptMapper;
import com.looksky.agents.data.mysql.service.IPromptService;
import com.looksky.agents.sdk.agent.prompt.model.PromptModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-20 21:48:15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PromptServiceImpl extends ServiceImpl<PromptMapper, PromptModel> implements IPromptService {

    @Override
    //@Cacheable(value = "prompt:promptModel", key = "#name")
    public PromptModel getPromptModelByName(String name) {
        return lambdaQuery()
                .eq(PromptModel::getName, name)
                .oneOpt()
                .orElseGet(() -> { 
                    log.error("没有找到 prompt, prompt name: {}", name);
                    return new PromptModel();
                });
    }

}
