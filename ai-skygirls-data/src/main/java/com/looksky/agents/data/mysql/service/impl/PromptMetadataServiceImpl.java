package com.looksky.agents.data.mysql.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.looksky.agents.sdk.agent.prompt.model.PromptMetadataModel;
import com.looksky.agents.data.mysql.mapper.PromptMetadataMapper;
import com.looksky.agents.data.mysql.service.IPromptMetadataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-21 14:14:56
 */
@Slf4j
@Service
public class PromptMetadataServiceImpl extends ServiceImpl<PromptMetadataMapper, PromptMetadataModel> implements IPromptMetadataService {

    @Override
    @Cacheable(value = "prompt:metadata", key = "#promptId")
    public List<PromptMetadataModel> getMetaDataByPromptId(String promptId) {
        return lambdaQuery()
                .eq(PromptMetadataModel::getPromptId, promptId)
                .list();
    }

    @Override
    @Cacheable(
            value = "prompt:metadata",
            key = "#metadataModel.metadataKey + ':' + #metadataModel.metadataValue",
            condition = "#metadataModel != null"
    )
    public PromptMetadataModel findMetadata(PromptMetadataModel metadataModel) {
        if (metadataModel == null) {
            log.warn("元数据为空");
            return null;
        }
        List<PromptMetadataModel> metadataList = findMetadataList(metadataModel);
        if (metadataList.isEmpty()) {
            log.warn("未找到元数据, metadataModel:{}", JSONUtil.toJsonStr(metadataModel));
            return null;
        }
        if (metadataList.size() > 1) {
            log.warn("找到多个元数据, metadataModel:{}, 找到的元数据:{}", JSONUtil.toJsonStr(metadataModel), JSONUtil.toJsonStr(metadataList));
        }
        return metadataList.getFirst();
    }

    public List<PromptMetadataModel> findMetadataList(PromptMetadataModel metadataModel) {
        return lambdaQuery()
                .eq(PromptMetadataModel::getMetadataKey, metadataModel.getMetadataKey())
                .eq(PromptMetadataModel::getMetadataValue, metadataModel.getMetadataValue())
                .list();
    }
}
