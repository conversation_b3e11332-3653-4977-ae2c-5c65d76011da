package com.looksky.agents.data.client.business;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.graecove.common.ApiResp;
import com.looksky.agents.infrastructure.httpapi.ApiServiceEnum;
import com.looksky.agents.infrastructure.httpapi.HttpApi;
import com.looksky.agents.sdk.datahub.message.dto.MsgReq;
import com.looksky.agents.sdk.datahub.version.response.UserClientUaResponse;
import com.looksky.agents.sdk.user.visit.dto.VisitHistoryDTO;
import com.skygirls.biz.im.model.MessageModel;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.PostExchange;

@HttpApi(service = ApiServiceEnum.DATA_HUB, path = "/datahub")
public interface DataHubClient {

    @GetExchange("/user/appVisitHistory")
    ApiResp<VisitHistoryDTO> appVisitHistory(@RequestParam String userId);

    @PostExchange("/girls/user/message/count")
    ApiResp<Page<MessageModel>> messageHistory(@RequestBody MsgReq msgReq);


    @GetExchange("/data/clientUa")
    ApiResp<UserClientUaResponse> clientUa(@RequestParam String userId);

}