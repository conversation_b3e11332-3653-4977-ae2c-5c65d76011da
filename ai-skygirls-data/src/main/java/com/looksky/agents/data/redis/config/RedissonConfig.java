package com.looksky.agents.data.redis.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redisson配置类
 */
@Configuration
public class RedissonConfig {

    @Value("${spring.data.redis.host}")
    private String host;

    @Value("${spring.data.redis.port}")
    private String port;

    @Value("${spring.data.redis.database}")
    private Integer database;

    @Value("${spring.data.redis.password}")
    private String password;

    @Value("${spring.data.redis.ssl.enabled:false}")
    private Boolean ssl;

    @Bean
    public ObjectMapper redisObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        // 注册Java8时间模块
        mapper.registerModule(new JavaTimeModule());
        // 配置序列化特性
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return mapper;
    }

    @Bean
    public RedissonClient redissonClient(ObjectMapper redisObjectMapper) {
        Config config = new Config();
        // 使用TypedJsonJacksonCodec，它不会包含类型信息
        config.setCodec(new TypedJsonJacksonCodec(Object.class, redisObjectMapper));
        
        String pre = ssl ? "rediss":"redis";
        config.useSingleServer()
                .setAddress(pre + "://" + host + ":" + port)
                .setPassword(password)
                .setDatabase(database)
                .setSslEnableEndpointIdentification(false)
                .setConnectionPoolSize(ssl?10:2)
                .setConnectionMinimumIdleSize(ssl?4:1);
                
        return Redisson.create(config);
    }
}