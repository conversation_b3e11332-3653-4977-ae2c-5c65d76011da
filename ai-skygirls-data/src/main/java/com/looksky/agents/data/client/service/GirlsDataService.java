package com.looksky.agents.data.client.service;

import com.graecove.common.ApiResp;
import com.looksky.agents.common.utils.SignUtils;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.data.opensearch.SkcSearchService;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.product.ProductInfo;
import com.skygirls.biz.im.dto.AgentMessageRespV3;
import com.skygirls.biz.report.IosUserInfoDto;
import com.skygirls.biz.user.tryon.dto.SearchTryOnStatusReq;
import com.skygirls.biz.user.tryon.model.TryOnReportModel;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @ClassName GirlsDataService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/16 上午11:57
 * @Version 1.0
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class GirlsDataService {
    private final GirlsClient girlsClient;
    @Value("${looksky.signature}")
    private String signature;
    private final SkcSearchService skcSearchService;


    public TryOnReportModel getTryOnStatusModel(String userId, String skcId, String skuId) {
        SearchTryOnStatusReq request = new SearchTryOnStatusReq();
        request.setUserId(userId);
        Optional.ofNullable(skcId).ifPresent(request::setSkcId);
        Optional.ofNullable(skuId).ifPresent(request::setSkuId);
        return girlsClient.tryOnHomeCard(request).getData();
    }


    @Retryable(retryFor = Exception.class, maxAttempts = 2)
    public String getTryOnColorSeasonUrl(String userId) {
        log.debug("colorSeason tryOn -> UserId: {}", userId);
        String sign = SignUtils.generateExpectedSign(userId, signature);
        log.debug("colorSeason tryOn -> signature: {}", sign);
        ApiResp<String> stringApiResp = girlsClient.colorSeason(userId, sign);
        return stringApiResp.getData();
    }

    public ProductInfo getProductInfo(String skcId) {
        return getProductInfo(List.of(skcId)).getFirst();
    }

    @SneakyThrows
    public List<ProductInfo> getProductInfo(List<String> skcIds){
        List<ProductInfo> productInfos = skcSearchService.searchSkc(skcIds);
        Context.put(Context.Name.PRODUCT_INFO.getName(), productInfos);
        return productInfos;
    }


    public IosUserInfoDto getUserInfo(String userId) {
        log.info("获取用户{}的用户信息", userId);
        IosUserInfoDto userInfoDto;
        try {
            userInfoDto = girlsClient.userInfo(userId).getData();
            if (userInfoDto == null) {
                userInfoDto = new IosUserInfoDto();
            }
        } catch (Exception e) {
            log.info("获取用户信息失败, userId: {}", userId, e);
            userInfoDto = new IosUserInfoDto();
        }

        Context.put(Context.Name.USER_INFO.getName(), userInfoDto);

        return userInfoDto;
    }

    public IosUserInfoDto userInfoPreRegistration(String userId) {
        log.info("获取用户{}的用户信息", userId);
        IosUserInfoDto userInfoDto;
        try {
            userInfoDto = girlsClient.userInfoPreRegistration(userId).getData();
            if (userInfoDto == null) {
                userInfoDto = new IosUserInfoDto();
            }
        } catch (Exception e) {
            log.info("获取用户信息失败, userId: {}", userId, e);
            userInfoDto = new IosUserInfoDto();
        }

        Context.put(Context.Name.USER_INFO.getName(), userInfoDto);

        return userInfoDto;
    }

    @Async
    public void sendAsyncAgentMessage(AgentMessageRespV3 agentMessageRespV3) {
        girlsClient.sendAgentMessage(agentMessageRespV3);
    }


    //    @Async
    public void sendAgentMessage(AgentMessageRespV3 agentMessageRespV3) {
        try {
            girlsClient.sendAgentMessage(agentMessageRespV3);
        } catch (Exception e) {
            log.error("消息发送失败: {}", e.getMessage());
        }
//        girlsClient.sendAgentMessage(agentMessageRespV3).subscribe(
//                result -> {log.debug(JSONUtil.toJsonStr(result));},
//                error -> {log.error(error.getMessage());}
//        );
    }


    public void initAgentMessage(AgentMessageRespV3 agentMessageRespV3) {
        try {
            girlsClient.sendAgentMessage(agentMessageRespV3);
        } catch (Exception e) {
            log.error("消息发送失败: {}", e.getMessage());
        }
//        girlsClient.sendAgentMessage(agentMessageRespV3).subscribe(
//                result -> {log.debug(JSONUtil.toJsonStr(result));},
//                error -> {log.error(error.getMessage());}
//        );
    }


}
