package com.looksky.agents.data.client.config;

import com.looksky.agents.data.client.business.DataHubClient;
import com.looksky.agents.data.client.business.GirlsClient;
import com.looksky.agents.data.client.business.LookSkyClient;
import com.looksky.agents.data.client.business.LookSkyClientLocal;
import com.looksky.agents.data.client.business.OpenPerplexClient;
import com.looksky.agents.data.client.business.PyAgentClient;
import com.looksky.agents.data.client.business.TryOnClient;
import com.looksky.agents.infrastructure.httpapi.ApiServiceEnum;
import com.looksky.agents.infrastructure.httpapi.HttpApi;
import com.looksky.agents.infrastructure.httpapi.ThirdPartyApiProperties;
import java.time.Duration;
import org.springframework.boot.web.client.ClientHttpRequestFactories;
import org.springframework.boot.web.client.ClientHttpRequestFactorySettings;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.support.RestClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

@Configuration
public class HttpInterfaceConfig {

    private <T> HttpServiceProxyFactory createProxyFactory(
        Class<T> clientClass,
        RestClient.Builder restClientBuilder,
        ThirdPartyApiProperties properties) {
        HttpApi annotation = clientClass.getAnnotation(HttpApi.class);
        if (annotation == null) {
            throw new IllegalArgumentException("No @HttpApi annotation found on " + clientClass.getName());
        }

        ApiServiceEnum serviceEnum = annotation.service();
        String baseUrl = properties.getBaseUrls().get(serviceEnum.getValue());
        if (baseUrl == null) {
            throw new IllegalArgumentException("No base URL configured for service: " + serviceEnum);
        }

        RestClient restClient = restClientBuilder.baseUrl(baseUrl).build();
        return HttpServiceProxyFactory.builderFor(RestClientAdapter.create(restClient))
            .build();
    }

    @Bean
    public LookSkyClient userClient(
        RestClient.Builder restClientBuilder,
        ThirdPartyApiProperties properties) {
        HttpServiceProxyFactory factory = createProxyFactory(
            LookSkyClient.class,
            restClientBuilder,
            properties
        );
        return factory.createClient(LookSkyClient.class);
    }

    @Bean
    public GirlsClient girlsClient(
        RestClient.Builder restClientBuilder,
        ThirdPartyApiProperties properties) {
        HttpServiceProxyFactory factory = createProxyFactory(
            GirlsClient.class,
            restClientBuilder,
            properties
        );
        return factory.createClient(GirlsClient.class);
    }

    @Bean
    public OpenPerplexClient openPerplexClientClient(
        RestClient.Builder restClientBuilder,
        ThirdPartyApiProperties properties) {
        HttpServiceProxyFactory factory = createProxyFactory(
            OpenPerplexClient.class,
            restClientBuilder,
            properties
        );
        return factory.createClient(OpenPerplexClient.class);
    }

    @Bean
    public PyAgentClient pyAgentClient(
        RestClient.Builder restClientBuilder,
        ThirdPartyApiProperties properties) {
        HttpServiceProxyFactory factory = createProxyFactory(
            PyAgentClient.class,
            restClientBuilder,
            properties
        );
        return factory.createClient(PyAgentClient.class);
    }

    @Bean
    public LookSkyClientLocal lookSkyClientLocal(
        RestClient.Builder restClientBuilder,
        ThirdPartyApiProperties properties) {
        HttpServiceProxyFactory factory = createProxyFactory(
            LookSkyClientLocal.class,
            restClientBuilder,
            properties
        );
        return factory.createClient(LookSkyClientLocal.class);
    }

    @Bean
    public DataHubClient dataHubClient(
        RestClient.Builder restClientBuilder,
        ThirdPartyApiProperties properties) {
        HttpServiceProxyFactory factory = createProxyFactory(
            DataHubClient.class,
            restClientBuilder,
            properties
        );
        return factory.createClient(DataHubClient.class);
    }

    @Bean
    public TryOnClient tryOnHttpClient(
        RestClient.Builder restClientBuilder,
        ThirdPartyApiProperties properties) {

        restClientBuilder.requestFactory(new BufferingClientHttpRequestFactory(
                ClientHttpRequestFactories.get(ClientHttpRequestFactorySettings.DEFAULTS
                    .withConnectTimeout(Duration.ofSeconds(5))
                    .withReadTimeout(Duration.ofMinutes(5)))))
            ;

        HttpServiceProxyFactory factory = createProxyFactory(
            TryOnClient.class,
            restClientBuilder,
            properties
        );
        return factory.createClient(TryOnClient.class);
    }
}