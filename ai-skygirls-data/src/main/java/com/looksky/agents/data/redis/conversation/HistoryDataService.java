package com.looksky.agents.data.redis.conversation;

import cn.hutool.json.JSONUtil;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.data.redis.config.RedisCodecFactory;
import com.looksky.agents.data.redis.conversation.utils.MessageUtils;
import com.looksky.agents.infrastructure.context.Context;
import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.agent.common.enums.RoleTypeEnum;
import com.looksky.agents.sdk.agent.conversation.Event;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

/**
 * @ClassName ConversationService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/11 下午8:35
 * @Version 1.0
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class HistoryDataService {
    private final RedissonClient redissonClient;
    private final RedisCodecFactory factory;

    /**
     * 获取非本轮对话的历史消息
     * @return 对话历史
     */
    public List<Event> getPrevHistory() {
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());
        List<Event> eventHistory = getHistory(input.getUserId(), input.getConversationId());
        List<Event> history = MessageUtils.filterMessage(eventHistory, false);
        Context.put(Context.Name.EVENT_LIST.getName(), history);
        return history;
    }

    /**
     * 获取用户会话消息
     *
     * @return
     */
    private List<Event> getHistory(String userId, String conversationId) {
        String conversationKey = RedisKeyConstants.historyKey(userId, conversationId);
        RList<Event> list = redissonClient.getList(conversationKey, factory.createCodec(Event.class));
        List<Event> events = list.readAll();

        if (events.isEmpty()) {
            return new ArrayList<>();
        }
        return events;
    }

    /**
     * 保存历史消息
     * @param event
     */
    public void saveHistory(Event event) {
        saveHistory(List.of(event));
    }

    public void saveHistory(List<Event> event) {
        log.debug("保存历史消息: {}", JSONUtil.toJsonStr(event));
        RequestInputDTO input = Context.get(Context.Name.REQUEST_INPUT.getName());
        RList<Event> list = redissonClient.getList(
            RedisKeyConstants.historyKey(input.getUserId(), input.getConversationId()), 
            factory.createCodec(Event.class));
        list.addAll(event);
        list.expire(Duration.ofDays(1));
    }

    /**
     * 保存历史
     * @param roleTypeEnum 消息类型
     * @param message 消息内容
     */
    public void saveHistory(RoleTypeEnum roleTypeEnum, String message) {
        saveHistory(MessageUtils.buildMessage(roleTypeEnum, message));
    }

    /**
     * 保存用户历史
     * @param message 消息内容
     */
    public void saveUserHistory(String message) {
        saveHistory(MessageUtils.buildMessage(RoleTypeEnum.USER, message));
    }

    /**
     * 保存 agent 的历史
     * @param message 消息内容
     */
    public void saveAgentHistory(String message) {
        saveHistory(MessageUtils.buildMessage(RoleTypeEnum.AGENT, message));
    }
}
