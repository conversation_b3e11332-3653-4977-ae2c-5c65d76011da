package com.looksky.agents.data.client.service;

import com.looksky.agents.data.client.business.OpenPerplexClient;
import com.looksky.agents.infrastructure.httpapi.ThirdPartyApiProperties;
import com.looksky.agents.sdk.perplex.dto.request.PerplexSearchRequest;
import com.looksky.agents.sdk.perplex.dto.response.PerplexSearchResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @ClassName OpenPerplexService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/2 下午9:20
 * @Version 1.0
 **/
@Service
@RequiredArgsConstructor
public class OpenPerplexService {
    private final OpenPerplexClient openPerplexClient;
    private final ThirdPartyApiProperties thirdPartyApiProperties;

    public PerplexSearchResponse search(PerplexSearchRequest searchRequest) {

        String apiKey = thirdPartyApiProperties.getHeaders().get("open-perplex").get("api-key");

        return openPerplexClient.search(
            apiKey,
            searchRequest.getQuery(),
            searchRequest.getDateContext(),
            searchRequest.getLocation(),
            searchRequest.getProMode(),
            searchRequest.getResponseLanguage(),
            searchRequest.getAnswerType(),
            searchRequest.getSearchType(),
            searchRequest.getVerboseMode(),
            searchRequest.getReturnCitations(),
            searchRequest.getReturnSources(),
            searchRequest.getReturnImages()
        );
    }
}
