package com.looksky.agents.data.redis.recommend;

import com.looksky.agents.sdk.agent.common.dto.RequestInputDTO;
import com.looksky.agents.sdk.product.RecommendProducts;
import com.looksky.agents.data.redis.RedisKeyConstants;
import com.looksky.agents.infrastructure.context.Context;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName RecommendProductsService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/18 上午11:07
 * @Version 1.0
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class RecommendProductsDataService {
    private final RedissonClient redissonClient;

    @Deprecated
    public List<RecommendProducts> getRecommendProducts(String userId, String conversationId) {
        RBucket<List<RecommendProducts>> bucket = redissonClient.getBucket(RedisKeyConstants.recommendProductKey(userId, conversationId), new TypedJsonJacksonCodec(RecommendProducts.class));
        return bucket.get();
    }


    public void save(List<String> skcIds) {
        RList<String> list = getRList();
        list.clear();
        list.addAllAsync(skcIds);
    }

    public String get(int index) {
        if (index <= 0 || index > 20) {
            return null;
        }

        RList<String> list = getRList();
        if (list.isEmpty()) {
            return null;
        }
        return list.get(index - 1);
    }

    public List<String> getAll() {
        RList<String> list = getRList();
        if (list.isEmpty()) {
            return null;
        }
        return list.readAll();
    }

    private RList<String> getRList() {
        RequestInputDTO requestInput = Context.get(Context.Name.REQUEST_INPUT.getName());
        return redissonClient.getList(RedisKeyConstants.recommendProductKey(requestInput.getUserId(), requestInput.getConversationId()));
    }
}
