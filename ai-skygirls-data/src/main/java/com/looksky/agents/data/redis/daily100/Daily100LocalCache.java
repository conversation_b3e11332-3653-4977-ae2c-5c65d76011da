package com.looksky.agents.data.redis.daily100;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.looksky.agents.sdk.recommend.common.dto.ColorSeasonCacheDataDTO;
import com.looksky.agents.sdk.recommend.common.dto.Face2HairCacheDataDTO;
import com.looksky.agents.sdk.recommend.common.dto.UserPageKibbeCacheDataDTO;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class Daily100LocalCache {

    private final ObjectMapper objectMapper;

    private Map<String, UserPageKibbeCacheDataDTO> kibbeCache;
    private Map<String, ColorSeasonCacheDataDTO> colorSeasonCache;
    private List<Face2HairCacheDataDTO> face2HairCache;

    @PostConstruct
    @SneakyThrows
    public void init() {
        // 加载kibbe数据
        try (InputStream kibbeStream = new ClassPathResource("cache/agent-cache-kibbe.json").getInputStream()) {
            kibbeCache = objectMapper.readValue(kibbeStream, new TypeReference<>() {});
            log.info("Loaded {} kibbe types", kibbeCache.size());
        }

        // 加载color season数据
        try (InputStream colorSeasonStream = new ClassPathResource("cache/agent-cache-colorSeason.json").getInputStream()) {
            colorSeasonCache = objectMapper.readValue(colorSeasonStream, new TypeReference<>() {});
            log.info("Loaded {} color seasons", colorSeasonCache.size());
        }

        // 加载face2hair数据
        try (InputStream face2HairStream = new ClassPathResource("cache/agent-cache-face2hair.json").getInputStream()) {
            face2HairCache = objectMapper.readValue(face2HairStream, new TypeReference<>() {});
            log.info("Loaded {} face2hair mappings", face2HairCache.size());
        }
    }

    @SneakyThrows
    public UserPageKibbeCacheDataDTO getKbType(String kbType) {
        return kibbeCache.get(kbType);
    }

    @SneakyThrows
    public ColorSeasonCacheDataDTO getColorSeason(String colorSeason) {
        return colorSeasonCache.get(colorSeason);
    }

    @SneakyThrows
    public Face2HairCacheDataDTO getFace2Hair(String faceType) {
        return face2HairCache.stream()
                .filter(face2HairDTO -> face2HairDTO.getFaceType().equals(faceType))
                .findFirst()
                .orElse(null);
    }
} 