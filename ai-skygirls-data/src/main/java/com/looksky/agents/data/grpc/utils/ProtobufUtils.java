package com.looksky.agents.data.grpc.utils;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.StringValue;
import com.google.protobuf.util.JsonFormat;

public class ProtobufUtils {
    public static final JsonFormat.Printer printer;
    public static final JsonFormat.Parser parser;

    static {
        JsonFormat.TypeRegistry registry = JsonFormat.TypeRegistry.newBuilder()
            .add(StringValue.getDescriptor())
            .build();

        printer = JsonFormat
            .printer()
            .usingTypeRegistry(registry)
            .includingDefaultValueFields()
            .omittingInsignificantWhitespace();

        parser = JsonFormat
            .parser()
            .usingTypeRegistry(registry);
    }

    /**
     * 将Protobuf消息对象转换为JSON字符串
     *
     * @param message Protobuf消息对象
     * @return JSON字符串
     */
    public static String toJson(Message message) {
        try {
            return printer.print(message);
        } catch (InvalidProtocolBufferException e) {
            throw new RuntimeException("Failed to convert protobuf message to JSON", e);
        }
    }

    /**
     * 将JSON字符串解析为Protobuf消息对象
     *
     * @param json JSON字符串
     * @param builder Protobuf消息构建器
     * @param <T> Protobuf消息类型
     * @return 解析后的Protobuf消息对象
     */
    public static <T extends Message> T fromJson(String json, Message.Builder builder) {
        try {
            parser.merge(json, builder);
            @SuppressWarnings("unchecked")
            T message = (T) builder.build();
            return message;
        } catch (InvalidProtocolBufferException e) {
            throw new RuntimeException("Failed to parse JSON to protobuf message", e);
        }
    }
}