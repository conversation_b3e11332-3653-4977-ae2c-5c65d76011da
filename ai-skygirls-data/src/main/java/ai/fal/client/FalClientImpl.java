package ai.fal.client;

import ai.fal.client.http.ClientProxyInterceptor;
import ai.fal.client.http.CredentialsInterceptor;
import ai.fal.client.http.HttpClient;
import ai.fal.client.queue.QueueClient;
import ai.fal.client.queue.QueueClientImpl;
import ai.fal.client.queue.QueueResultOptions;
import ai.fal.client.queue.QueueSubmitOptions;
import ai.fal.client.queue.QueueSubscribeOptions;
import jakarta.annotation.Nonnull;
import java.util.concurrent.TimeUnit;
import okhttp3.OkHttpClient;

@SuppressWarnings("all")
public class FalClientImpl implements FalClient {

    private final HttpClient httpClient;
    private final QueueClient queueClient;

    FalClientImpl(@Nonnull ClientConfig config) {
        final var builder = new OkHttpClient.Builder().addInterceptor(new CredentialsInterceptor(config));
        if (config.getProxyUrl() != null) {
            builder.addInterceptor(new ClientProxyInterceptor(config));
        }
        builder.callTimeout(1, TimeUnit.MINUTES)
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS);
        this.httpClient = new HttpClient(config, builder.build());
        this.queueClient = new QueueClientImpl(this.httpClient);
    }

    @Override
    @Nonnull
    public <O> Output<O> run(String endpointId, RunOptions<O> options) {
        final var url = "https://fal.run/" + endpointId;
        final var request = httpClient.prepareRequest(url, options);
        final var response = httpClient.executeRequest(request);
        return httpClient.wrapInResult(response, options.getResultType());
    }

    @Override
    @Nonnull
    public <O> Output<O> subscribe(String endpointId, SubscribeOptions<O> options) {
        final var enqueued = queueClient.submit(
                endpointId,
                QueueSubmitOptions.builder()
                        .input(options.getInput())
                        .webhookUrl(options.getWebhookUrl())
                        .build());

        final var completed = queueClient.subscribeToStatus(
                endpointId,
                QueueSubscribeOptions.builder()
                        .requestId(enqueued.getRequestId())
                        .logs(options.getLogs())
                        .onQueueUpdate(options.getOnQueueUpdate())
                        .build());

        return queueClient.result(
                endpointId,
                QueueResultOptions.<O>builder()
                        .requestId(completed.getRequestId())
                        .resultType(options.getResultType())
                        .build());
    }

    @Override
    public QueueClient queue() {
        return this.queueClient;
    }
}
